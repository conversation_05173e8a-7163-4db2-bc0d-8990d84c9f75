<template>
  <div class="chart-container" ref="chartContainer">
    <div class="form-wrapper">
      <el-form :model="queryParams" label-width="90px" v-show="showSearch" :inline="true" label-position="left">
        <el-form-item label="日期范围:">
          <el-date-picker v-model="queryParams.dateRange" :type="datePickerType" range-separator="-"
            start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
            @change="debouncedHandleQuery" style="width: 300px;" />
        </el-form-item>
        <el-form-item label="计量单位:">
          <el-select v-model="queryParams.timeUnit" placeholder="请选择计量单位" style="width: 150px;"
            @change="handleChangeTimeUnit">
            <el-option label="日" value="day"></el-option>
            <el-option label="月" value="month"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div class="charts-wrapper">
      <div ref="hashrateChartContainer" class="chart-item"></div>
      <div ref="residueChartContainer" class="chart-item"></div>
      <div v-if="!consumptionList.length" class="no-data">暂无数据</div>
    </div>
  </div>
</template>

<script setup name="Statistics">
import { ref, onMounted, watch, computed, nextTick, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { fetchConsumptionData, processHashrateForChart, processResidueForChart } from '@/utils/consumptionService';
import { debounce } from '@/utils/index';

const props = defineProps({
  userData: Array,
  userName: String
});
const showSearch = ref(true);
const consumptionList = ref([]);
const data = reactive({
  queryParams: {
    dateRange: null,
    timeUnit: 'day'
  }
});
const { queryParams } = toRefs(data);
const hashrateChartContainer = ref(null);
const residueChartContainer = ref(null);
let hashrateChart = null;
let residueChart = null;
const datePickerType = computed(() => queryParams.value.timeUnit === 'day' ? 'daterange' : 'monthrange');
const chartContainer = ref(null);
let resizeObserver = null;
const isDataReady = ref(false);
const isInitialized = ref(false);

async function fetchAndSetConsumption(userName) {
  try {
    if (!userName) {
      consumptionList.value = [];
      clearCharts();
      return;
    }

    const data = await fetchConsumptionData({ ...queryParams.value, userName });
    consumptionList.value = Array.isArray(data)
      ? data.filter(item => item && item.consumptionStaus === "1")
      : [];

    isDataReady.value = true;

    if (consumptionList.value.length && !isInitialized.value) {
      await nextTick();
      initCharts();
    } else if (consumptionList.value.length) {
      updateCharts();
    } else {
      clearCharts();
    }
  } catch (error) {
    console.error('获取消费数据失败:', error);
    consumptionList.value = [];
    clearCharts();
  }
}

function initCharts() {
  if (!isDataReady.value || !hashrateChartContainer.value?.offsetWidth || !residueChartContainer.value?.offsetWidth) {
    console.warn('数据或容器尺寸未就绪，延迟初始化');
    return;
  }

  try {
    if (!hashrateChart && hashrateChartContainer.value) {
      hashrateChart = echarts.init(hashrateChartContainer.value);
    }
    if (!residueChart && residueChartContainer.value) {
      residueChart = echarts.init(residueChartContainer.value);
    }

    isInitialized.value = true;
    addResizeListener();
    updateCharts();
  } catch (error) {
    console.error('初始化图表失败:', error);
    clearCharts();
  }
}

function clearCharts() {
  if (hashrateChart) {
    try {
      hashrateChart.clear();
      hashrateChart.dispose();
    } catch (e) {
      console.error('清理 hashrate 图表失败:', e);
    }
    hashrateChart = null;
  }
  if (residueChart) {
    try {
      residueChart.clear();
      residueChart.dispose();
    } catch (e) {
      console.error('清理 residue 图表失败:', e);
    }
    residueChart = null;
  }
  isInitialized.value = false;
}

function addResizeListener() {
  window.addEventListener('resize', handleResize);

  resizeObserver = new ResizeObserver(() => {
    handleResize();
  });

  if (chartContainer.value) {
    resizeObserver.observe(chartContainer.value);
  }

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    if (resizeObserver) {
      resizeObserver.disconnect();
    }
    clearCharts();
  });
}

const handleResize = debounce(() => {
  if (!isInitialized.value || !isDataReady.value) return;

  nextTick(() => {
    try {
      if (hashrateChart && hashrateChartContainer.value?.offsetWidth > 0) {
        hashrateChart.resize();
      }
      if (residueChart && residueChartContainer.value?.offsetWidth > 0) {
        residueChart.resize();
      }
    } catch (error) {
      console.error('Resize 图表失败:', error);
    }
  });
}, 200);

function updateCharts() {
  if (!hashrateChart || !residueChart || !consumptionList.value.length) return;

  try {
    if (!hashrateChartContainer.value?.offsetWidth || !residueChartContainer.value?.offsetWidth) {
      console.warn('图表容器尺寸异常');
      return;
    }

    const hashrateData = processHashrateForChart(consumptionList.value, queryParams.value.timeUnit);
    const residueData = processResidueForChart(consumptionList.value, queryParams.value.timeUnit);

    hashrateChart.setOption(createChartOption(hashrateData.keys, hashrateData.values, 'hashrate'));
    residueChart.setOption(createChartOption(residueData.keys, residueData.values, 'residue'));

    hashrateChart.resize();
    residueChart.resize();
  } catch (error) {
    console.error('更新图表失败:', error);
    clearCharts();
  }
}

function createChartOption(keys, values, chartType) {
  if (!keys.length || !values.length) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        textStyle: { fontSize: 18, fontWeight: 'bold', color: '#333' }
      }
    };
  }

  const titleText = chartType === 'hashrate' ? '算力花费情况' : '剩余算力点下降趋势';
  const seriesName = chartType === 'hashrate' ? '花费算力点' : '剩余算力点';
  const yAxisName = chartType === 'hashrate' ? '算力点花费' : '剩余算力点';

  const coordinates = values.map((value, index) => {
    return [index, typeof value === 'number' ? value : 0];
  }).filter(coord => coord[1] !== null && !isNaN(coord[1]));

  const baseOption = {
    title: {
      text: `${keys[0]?.substring(0, 4)}年-${(queryParams.value.timeUnit === 'day' ? '每日' : '每月')}${titleText}`,
      left: 'center',
      textStyle: { fontSize: 18, fontWeight: 'bold', color: '#333' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: params => `${params[0].name}<br>${seriesName}: ${params[0].value.toFixed(2)}`,
      axisPointer: { type: 'cross', label: { backgroundColor: '#6a7985' } }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: keys.map(key => key.substring(queryParams.value.timeUnit === 'day' ? 5 : 0)),
      axisLabel: { rotate: 0, interval: 0 }
    },
    yAxis: {
      type: 'value',
      name: yAxisName
    },
    series: [
      {
        name: seriesName,
        type: 'line',
        data: values,
        smooth: true,
        lineStyle: {
          width: 2,
          color: '#0077C2'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(89, 165, 245, 0.15)' },
              { offset: 1, color: 'rgba(100, 150, 255, 0)' }
            ]
          }
        },
        showSymbol: true,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#6495ED',
          borderWidth: 2,
          borderColor: '#FFF'
        }
      }
    ]
  };

  if (coordinates.length > 1) {
    baseOption.series.push({
      type: 'lines',
      coordinateSystem: 'cartesian2d',
      polyline: true,
      effect: {
        show: true,
        constantSpeed: 80,
        trailLength: 0.7,
        symbol: 'circle',
        symbolSize: 6,
        color: 'rgba(0, 191, 255, 0.1)',
        shadowBlur: 0,
        shadowColor: 'rgba(0, 191, 255, 0.1)'
      },
      lineStyle: {
        opacity: 0
      },
      data: [{
        coords: coordinates
      }]
    });
  }

  return baseOption;
}

function handleChangeTimeUnit() {
  queryParams.value.dateRange = null;
  debouncedHandleQuery();
}

function handleQuery() {
  const { dateRange } = queryParams.value;
  queryParams.value.createTime = dateRange?.[0] ?? null;
  queryParams.value.updateTime = dateRange?.[1] ?? null;
  fetchAndSetConsumption(props.userName);
}

const debouncedHandleQuery = debounce(handleQuery, 100, false);

onMounted(() => {
  setTimeout(() => {
    if (props.userName) {
      fetchAndSetConsumption(props.userName);
    }
  }, 300);
});

watch([() => queryParams.value.dateRange, () => queryParams.value.timeUnit], ([newDateRange, newTimeUnit]) => {
  if ((Array.isArray(newDateRange) && newDateRange.length === 2) || newTimeUnit) {
    debouncedHandleQuery();
  }
}, { deep: true });

watch(() => props.userData, (newUserData) => {
  if (!newUserData) return;

  consumptionList.value = newUserData;
  isDataReady.value = true;

  nextTick(() => {
    if (!isInitialized.value) {
      initCharts();
    } else {
      updateCharts();
    }
  });
}, { deep: true, immediate: true });
</script>

<style scoped lang="scss">
// 变量定义
$chart-border-radius: 18px;
$chart-padding: 20px;
$chart-gap: 20px;
$chart-border-color: #e6e6e6;

// 图表容器
.chart-container {
  position: relative;
  width: 100%;
  height: auto;
  min-height: 720px;

  // 表单包装器
  .form-wrapper {
    display: flex;
    justify-content: flex-end;
    margin-bottom: $chart-gap;
    z-index: 1;
    position: relative;
  }
}

// 图表区域包装器
.charts-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: $chart-gap;
  width: 100%;

  // 单个图表容器
  .chart-item {
    position: relative;
    width: 100%;
    height: 360px;
    background-color: #fff;
    border-radius: $chart-border-radius;
    border: 1px solid $chart-border-color;
    padding: $chart-padding;

    // 阴影效果
    box-shadow:
      0 2px 12px 0 rgba(0, 0, 0, 0.05),
      0 1px 2px 0 rgba(0, 0, 0, 0.02);

    // 过渡效果
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 4px 16px 0 rgba(0, 0, 0, 0.08),
        0 2px 4px 0 rgba(0, 0, 0, 0.03);
    }
  }

  // 无数据状态
  .no-data {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: #999;
    font-size: 14px;

    &::before {
      content: '暂无数据';
      display: block;
      text-align: center;
      font-size: 16px;
      margin-bottom: 8px;
    }
  }
}

// 响应式布局
@media screen and (max-width: 768px) {
  .chart-container {
    .form-wrapper {
      flex-direction: column;
      gap: 10px;
    }

    .charts-wrapper {
      gap: 15px;

      .chart-item {
        height: 300px;
      }
    }
  }
}
</style>