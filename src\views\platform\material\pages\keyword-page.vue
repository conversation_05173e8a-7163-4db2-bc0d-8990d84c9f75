<script setup>
import { listKeyword, getKeyword, delKeyword, addKeyword, updateKeyword } from "@/api/platform/keyword";
import { watch, computed, onMounted, onBeforeUnmount } from "vue";
import { ElMessage } from "element-plus";
import { ArrowDown } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const props = defineProps({
  categoryId: {
    type: Number,
  }
})
const loading = ref(false)
const keywordList = ref([])
const total = ref(0);

const queryParams = ref({
  pageNum: 1,
  pageSize: 29,
  categoryId: null,
})
watch(() => props.categoryId, getList, { immediate: true });

//查询关键词列表
function getList() {
  queryParams.value.categoryId = props.categoryId;
  if (!props.categoryId) return
  loading.value = true;
  listKeyword(queryParams.value).then((response) => {
    keywordList.value = response.rows;
    keywordList.value.forEach(item => item.type = generateType());
    total.value = response.total;
    loading.value = false;
  });
}

//删除关键词
const ids = ref([]);
function handleClose(tag) {
  const _keywordIds = tag.keywordId || ids.value;
  delKeyword(_keywordIds).then(() => {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  });
}
//添加关键词
const inputValue = ref('')
const inputVisible = ref(false)
const inputRef = ref()
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value.input.focus()
  })
}

const handleAdd = () => {
  if (inputValue.value) {
    if (!props.categoryId || props.categoryId == 0 || props.categoryId == -1) {
      proxy.$modal.msgError("请先选择分类");
      return
    }
    if (inputValue.value.length > 20) {
      proxy.$modal.msgError("关键词长度不能超过20个字符");
      return
    }
    addKeyword({ keywordName: inputValue.value, categoryId: props.categoryId }).then(res => {
      ElMessage({ message: '新增成功', plain: true, grouping: true, type: 'success', })
      getList();
    })
  }
  inputValue.value = ''
}
function closeAdd() {
  handleAdd()
  inputVisible.value = false
}
//修改关键词
const editVisible = ref([])
const editRef = ref([])
function openEdit(data, index) {
  editVisible.value[index] = true
  nextTick(() => {
    editRef.value[index].input.focus()
  })
}
function handleEdit(data, index) {
  if (data.keywordName) {
    updateKeyword({ keywordName: data.keywordName, keywordId: data.keywordId }).then(res => {
      ElMessage({ message: '修改成功', plain: true, grouping: true, type: 'success', })
      getList();
    })
  }
  editVisible.value[index] = false
}

// 批量添加关键词相关变量和方法
const batchDialogVisible = ref(false)
const batchKeywords = ref('')
const batchAddLoading = ref(false)

// 打开批量添加对话框
function openBatchAdd() {
  batchDialogVisible.value = true
  batchKeywords.value = ''
}

// 批量添加
function handleBatchAdd() {
  if (!batchKeywords.value) {
    ElMessage({ message: '请输入关键词', type: 'warning' })
    return
  }
  
  if (!props.categoryId || props.categoryId == 0 || props.categoryId == -1) {
    proxy.$modal.msgError("请先选择分类");
    return
  }
  
  // 将输入的关键词按逗号、空格或换行符分割
  const keywords = batchKeywords.value.split(/[,，\s\n]+/).filter(item => item.trim() !== '')
  
  if (keywords.length === 0) {
    ElMessage({ message: '请输入有效的关键词', type: 'warning' })
    return
  }
  
  // 检查关键词长度
  const invalidKeywords = keywords.filter(item => item.length > 20)
  if (invalidKeywords.length > 0) {
    proxy.$modal.msgError(`以下关键词长度超过20个字符：${invalidKeywords.join(', ')}`)
    return
  }
  
  batchAddLoading.value = true
  
  const addPromises = keywords.map(keyword => {
    return addKeyword({ keywordName: keyword, categoryId: props.categoryId })
  })
  
  Promise.all(addPromises)
    .then(() => {
      ElMessage({ message: `成功添加${keywords.length}个关键词`, type: 'success', plain: true, grouping: true })
      batchDialogVisible.value = false
      batchKeywords.value = ''
      getList()
    })
    .catch(error => {
      proxy.$modal.msgError("批量添加失败，请重试")
      console.error("批量添加失败:", error)
    })
    .finally(() => {
      batchAddLoading.value = false
    })
}

//随机关键词标签背景
const types = ['primary', 'success', 'info', 'warning', 'danger'];
function generateType() {
  const index = Math.floor(Math.random() * types.length);
  return types[index];
}
</script>
<template>
  <div class="container">
    <div class="head-item">
      <el-input v-if="inputVisible" ref="inputRef" v-model="inputValue" size="small" maxlength="20" show-word-limit
        @keyup.enter="handleAdd" @blur="closeAdd" />
      <el-dropdown v-else trigger="click">
        <el-button size="small" type="primary" class="add-button">
          添加关键词 <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="showInput">单个添加</el-dropdown-item>
            <el-dropdown-item @click="openBatchAdd">批量添加</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="list-container">
      <div v-for="(tag, index) in keywordList" :key="tag.keywordId" class="list-item">
        <el-input v-if="editVisible[index]" :ref="el => { if (el) editRef[index] = el }" v-model="tag.keywordName"
          size="small" @keyup.enter="$event.target.blur()" @blur="handleEdit(tag, index)" />
        <el-tooltip v-else effect="dark" :content="tag.keywordName" placement="top" :hide-after="0">
          <el-tag closable :type="tag.type" @close="handleClose(tag)" @click="openEdit(tag, index)"
            :disable-transitions="true">
            {{ tag.keywordName }}
          </el-tag>
        </el-tooltip>
      </div>
    </div>
    
    <!-- 批量添加关键词对话框 -->
    <el-dialog
      title="批量添加关键词"
      v-model="batchDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form>
        <el-form-item label="关键词" label-width="80px">
          <el-input
            v-model="batchKeywords"
            type="textarea"
            :rows="6"
            placeholder="请输入多个关键词，用逗号、空格或换行分隔"
          />
        </el-form-item>
        <div class="dialog-tips">
          <i class="el-icon-info"></i>
          <span>提示：每个关键词最多20个字符，多个关键词可用逗号、空格或换行分隔</span>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleBatchAdd" :loading="batchAddLoading">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.container {
  height: 100%;

  .head-item {
    width: 100%;
    display: flex;
    justify-content: center;
    height: 32px;
    margin-bottom: 10px;

    .el-button,
    .el-input,
    .el-tag {
      height: 32px;
      line-height: 32px;
      font-size: 16px;
    }
    
    .add-button {
      width: 140px;
    }
  }

  .list-container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    height: calc(100% - 42px);
    overflow-y: auto;

    .list-item {
      margin: 5px;

      .el-button,
      .el-input,
      .el-tag {
        height: 32px;
        line-height: 32px;
        font-size: 16px;
      }

      .el-tag {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        text-align: center;
      }

      :deep(.el-tag__content) {
        flex-grow: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 15px;
        max-width: 120px;
      }

      :deep(.el-tag__close) {
        position: absolute;
        right: 3px;
      }
    }
  }

  .dialog-tips {
    color: #909399;
    font-size: 12px;
    margin-top: -15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 5px;
  }
}
</style>