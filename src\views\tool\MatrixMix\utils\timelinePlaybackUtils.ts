/**
 * @file timelinePlaybackUtils.ts
 * @description 时间轴播放校验相关的工具函数集合
 *              提供播放时长计算、播放校验、播放状态管理等功能
 *              从 TimelinePlayer.vue 和 useVideoEditor.ts 中抽取，提供统一的播放控制逻辑
 */

import type { Timeline } from '../types/videoEdit';

/**
 * @interface PlaybackValidationResult
 * @description 播放校验结果
 */
export interface PlaybackValidationResult {
  isValid: boolean;
  shouldPause: boolean;
  shouldResetToStart: boolean;
  actualSeekTime?: number;
  message?: string;
}

/**
 * @interface TrackDurationInfo
 * @description 轨道时长信息
 */
export interface TrackDurationInfo {
  maxDuration: number;
  trackDetails: {
    videoTracks: number[];
    audioTracks: number[];
    subtitleTracks: number[];
  };
}

/**
 * 计算时间轴中所有轨道的最长持续时间
 * @param timeline - 时间轴数据
 * @returns 轨道时长信息，包含最长时长和各轨道详情
 */
export function calculateMaxTrackDuration(timeline: Timeline | null): TrackDurationInfo {
  if (!timeline) {
    return {
      maxDuration: 0,
      trackDetails: {
        videoTracks: [],
        audioTracks: [],
        subtitleTracks: []
      }
    };
  }
  
  let maxDuration = 0;
  const trackDetails = {
    videoTracks: [] as number[],
    audioTracks: [] as number[],
    subtitleTracks: [] as number[]
  };
  
  // 检查视频轨道
  if (timeline.VideoTracks) {
    timeline.VideoTracks.forEach((track, trackIndex) => {
      let trackDuration = 0;
      if (track.VideoTrackClips) {
        track.VideoTrackClips.forEach(clip => {
          const clipEnd = (clip.TimelineIn || 0) + (clip.Duration || 0);
          if (clipEnd > trackDuration) {
            trackDuration = clipEnd;
          }
          if (clipEnd > maxDuration) {
            maxDuration = clipEnd;
          }
        });
      }
      trackDetails.videoTracks.push(trackDuration);
    });
  }
  
  // 检查音频轨道
  if (timeline.AudioTracks) {
    timeline.AudioTracks.forEach((track, trackIndex) => {
      let trackDuration = 0;
      if (track.AudioTrackClips) {
        track.AudioTrackClips.forEach(clip => {
          const clipEnd = (clip.TimelineIn || 0) + (clip.Duration || 0);
          if (clipEnd > trackDuration) {
            trackDuration = clipEnd;
          }
          if (clipEnd > maxDuration) {
            maxDuration = clipEnd;
          }
        });
      }
      trackDetails.audioTracks.push(trackDuration);
    });
  }
  
  // 检查字幕轨道
  if (timeline.SubtitleTracks) {
    timeline.SubtitleTracks.forEach((track, trackIndex) => {
      let trackDuration = 0;
      if (track.SubtitleTrackClips) {
        track.SubtitleTrackClips.forEach(clip => {
          const clipEnd = (clip.TimelineIn || 0) + (clip.Duration || 0);
          if (clipEnd > trackDuration) {
            trackDuration = clipEnd;
          }
          if (clipEnd > maxDuration) {
            maxDuration = clipEnd;
          }
        });
      }
      trackDetails.subtitleTracks.push(trackDuration);
    });
  }
  
  console.log('📏 计算得出的最长轨道时长:', maxDuration, '轨道详情:', trackDetails);
  return {
    maxDuration,
    trackDetails
  };
}

/**
 * 播放时间校验 - 检查当前播放时间是否超过最长轨道时长
 * @param currentTime - 当前播放时间
 * @param maxDuration - 最长轨道时长
 * @returns 校验结果
 */
export function validatePlaybackTime(currentTime: number, maxDuration: number): PlaybackValidationResult {
  if (maxDuration <= 0) {
    return {
      isValid: true,
      shouldPause: false,
      shouldResetToStart: false
    };
  }
  
  if (currentTime >= maxDuration) {
    return {
      isValid: false,
      shouldPause: true,
      shouldResetToStart: false,
      message: `播放到达最长轨道结束时间，自动暂停 (${currentTime}s >= ${maxDuration}s)`
    };
  }
  
  return {
    isValid: true,
    shouldPause: false,
    shouldResetToStart: false
  };
}

/**
 * 播放开始前校验 - 检查是否需要重置到开始位置
 * @param currentTime - 当前播放时间
 * @param maxDuration - 最长轨道时长
 * @returns 校验结果
 */
export function validatePlayStart(currentTime: number, maxDuration: number): PlaybackValidationResult {
  if (maxDuration <= 0) {
    return {
      isValid: true,
      shouldPause: false,
      shouldResetToStart: false
    };
  }
  
  if (currentTime >= maxDuration) {
    return {
      isValid: false,
      shouldPause: false,
      shouldResetToStart: true,
      message: `播放时间已到达结尾，重置到开始位置 (${currentTime}s >= ${maxDuration}s)`
    };
  }
  
  return {
    isValid: true,
    shouldPause: false,
    shouldResetToStart: false
  };
}

/**
 * 跳转位置校验 - 限制跳转位置不能超过最长轨道时长
 * @param requestedTime - 请求跳转的时间
 * @param maxDuration - 最长轨道时长
 * @returns 校验结果，包含实际跳转时间
 */
export function validateSeekPosition(requestedTime: number, maxDuration: number): PlaybackValidationResult {
  if (maxDuration <= 0) {
    return {
      isValid: true,
      shouldPause: false,
      shouldResetToStart: false,
      actualSeekTime: requestedTime
    };
  }
  
  const actualSeekTime = Math.min(requestedTime, maxDuration);
  
  if (actualSeekTime !== requestedTime) {
    return {
      isValid: false,
      shouldPause: false,
      shouldResetToStart: false,
      actualSeekTime,
      message: `跳转位置超过最长轨道时长，限制到最大时长 (${requestedTime}s -> ${actualSeekTime}s)`
    };
  }
  
  return {
    isValid: true,
    shouldPause: false,
    shouldResetToStart: false,
    actualSeekTime
  };
}

/**
 * 创建播放校验管理器
 * @param timeline - 时间轴数据
 * @returns 播放校验管理器对象
 */
export function createPlaybackValidator(timeline: Timeline | null) {
  const trackInfo = calculateMaxTrackDuration(timeline);
  
  return {
    /** 获取最长轨道时长 */
    getMaxDuration: () => trackInfo.maxDuration,
    
    /** 获取轨道详情 */
    getTrackDetails: () => trackInfo.trackDetails,
    
    /** 播放时间校验 */
    validateTime: (currentTime: number) => validatePlaybackTime(currentTime, trackInfo.maxDuration),
    
    /** 播放开始校验 */
    validateStart: (currentTime: number) => validatePlayStart(currentTime, trackInfo.maxDuration),
    
    /** 跳转位置校验 */
    validateSeek: (requestedTime: number) => validateSeekPosition(requestedTime, trackInfo.maxDuration),
    
    /** 更新时间轴数据 */
    updateTimeline: (newTimeline: Timeline | null) => {
      const newTrackInfo = calculateMaxTrackDuration(newTimeline);
      trackInfo.maxDuration = newTrackInfo.maxDuration;
      trackInfo.trackDetails = newTrackInfo.trackDetails;
    }
  };
}

/**
 * 播放校验状态管理器
 * 提供播放状态的集中管理和校验功能
 */
export class PlaybackValidationManager {
  private timeline: Timeline | null = null;
  private currentTime: number = 0;
  private validator: ReturnType<typeof createPlaybackValidator>;
  
  constructor(timeline: Timeline | null = null) {
    this.timeline = timeline;
    this.validator = createPlaybackValidator(timeline);
  }
  
  /** 更新时间轴数据 */
  updateTimeline(timeline: Timeline | null): void {
    this.timeline = timeline;
    this.validator.updateTimeline(timeline);
  }
  
  /** 更新当前播放时间 */
  updateCurrentTime(time: number): void {
    this.currentTime = time;
  }
  
  /** 获取最长轨道时长 */
  getMaxDuration(): number {
    return this.validator.getMaxDuration();
  }
  
  /** 获取当前播放时间 */
  getCurrentTime(): number {
    return this.currentTime;
  }
  
  /** 播放时间校验 */
  validateCurrentTime(): PlaybackValidationResult {
    return this.validator.validateTime(this.currentTime);
  }
  
  /** 播放开始校验 */
  validatePlayStart(): PlaybackValidationResult {
    return this.validator.validateStart(this.currentTime);
  }
  
  /** 跳转位置校验 */
  validateSeekPosition(requestedTime: number): PlaybackValidationResult {
    return this.validator.validateSeek(requestedTime);
  }
  
  /** 获取轨道详情信息 */
  getTrackDetails() {
    return this.validator.getTrackDetails();
  }
}
