<template>
  <el-dialog
    v-model="visibleProxy"
    title="创建新模板"
    width="700px"
    :close-on-click-modal="false"
    class="create-template-dialog"
    @close="handleClose"
  >
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
        v-loading="formLoading"
        class="template-form"
      >
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          <el-form-item label="模板名称" prop="name" class="form-item">
            <el-input
              v-model="form.name"
              placeholder="请输入模板名称，如：企业宣传片模板"
              maxlength="50"
              show-word-limit
              class="form-input"
            />
          </el-form-item>

          <el-form-item label="模板类型" class="form-item">
            <el-select v-model="form.type" disabled class="form-select">
              <el-option label="Timeline 普通模板" value="Timeline">
                <div class="option-content">
                  <span class="option-label">Timeline 普通模板</span>
                  <span class="option-desc">基于时间线的视频模板</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <div class="form-section">
          <h3 class="section-title">模板配置</h3>
          <el-form-item label="模板内容" prop="config" class="form-item config-item">
            <ConfigBuilder v-model:config="form.config" class="config-builder"/>
          </el-form-item>
        </div>

        <div class="form-section">
          <h3 class="section-title">封面设置</h3>
          <el-form-item label="封面图片" class="form-item">
            <div class="cover-upload-container">
              <el-upload
                class="cover-uploader"
                action="#"
                :http-request="handleCoverUpload"
                :show-file-list="false"
                :before-upload="beforeCoverUpload"
                list-type="picture-card"
              >
                <div v-if="form.coverUrl" class="cover-preview">
                  <img :src="form.coverUrl" class="cover-image"/>
                  <div class="cover-overlay">
                    <el-icon class="overlay-icon"><Edit /></el-icon>
                    <span class="overlay-text">更换封面</span>
                  </div>
                </div>
                <div v-else class="cover-placeholder">
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <div class="upload-text">上传封面</div>
                  <div class="upload-hint">支持 JPG、PNG 格式，建议尺寸 16:9</div>
                </div>
              </el-upload>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog" class="cancel-btn">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirmCreate"
          :loading="formLoading"
          class="confirm-btn"
        >
          <el-icon v-if="!formLoading"><Check /></el-icon>
          {{ formLoading ? '创建中...' : '确认创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
  
<script setup lang="ts">
import { ref, watch, defineEmits, defineProps } from 'vue';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { Plus, Edit, Check } from '@element-plus/icons-vue';
import type { UploadRawFile, UploadRequestOptions } from 'element-plus';
import { addTemplate } from '../../api/template';
import { uploadFileUnified } from '@/api/file/info.js';
import ConfigBuilder from './ConfigBuilder.vue';

const props = defineProps<{ visible: boolean }>();
const emit = defineEmits(['update:visible', 'success']);

const visibleProxy = ref(props.visible);
watch(() => props.visible, v => (visibleProxy.value = v));
watch(visibleProxy, v => emit('update:visible', v));

const formRef = ref<FormInstance>();
const form = ref({
  name: '',
  type: 'Timeline',
  config: '',
  coverUrl: ''
});

const formRules: FormRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '模板名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  config: [
    { required: true, message: '请配置模板内容', trigger: 'blur' }
  ]
};

const formLoading = ref(false);
  
const closeDialog = () => {
  visibleProxy.value = false;
};

const handleClose = () => {
  // 重置表单
  form.value = {
    name: '',
    type: 'Timeline',
    config: '',
    coverUrl: ''
  };
  formRef.value?.resetFields();
};

const handleConfirmCreate = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
  } catch (error) {
    ElMessage.warning('请完善表单信息');
    return;
  }

  formLoading.value = true;
  try {
    // 转换为后端期望的格式 - 使用大写字段名匹配 @JsonProperty 注解
    const templateData = {
      Name: form.value.name,        // @JsonProperty("Name")
      Type: form.value.type,        // @JsonProperty("Type")
      Config: form.value.config,    // @JsonProperty("Config")
      CoverUrl: form.value.coverUrl // @JsonProperty("CoverUrl")
    };

    console.log('🚀 发送创建模板请求:', templateData);
    const res = await addTemplate(templateData);
    if (res.code === 200) {
      ElMessage.success('模板创建成功');
      closeDialog();
      emit('success');
    } else {
      ElMessage.error(`创建失败: ${res.msg}`);
    }
  } catch (error) {
    console.error('创建模板错误:', error);
    ElMessage.error('创建模板时发生网络错误');
  } finally {
    formLoading.value = false;
  }
};
  
  const beforeCoverUpload = (rawFile: UploadRawFile) => {
    const isImage = rawFile.type.startsWith('image/');
    if (!isImage) {
      ElMessage.error('请上传图片格式文件!');
      return false;
    }
    const isLt512K = rawFile.size / 1024 < 512;
    if (!isLt512K) {
      ElMessage.error('上传封面图片大小不能超过 512KB!');
      return false;
    }
    return true;
  };
  
  const handleCoverUpload = async (options: UploadRequestOptions) => {
    const { file } = options;
    try {
      const storageType = 'oss';
      const clientName = 'default';
      const res = await uploadFileUnified({ storageType, clientName, file });
      if (res.code === 200 && res.data && res.data.url) {
        form.value.coverUrl = res.data.url;
        ElMessage.success('封面上传成功');
      } else {
        ElMessage.error(res.msg || '封面上传失败');
      }
    } catch (error) {
      ElMessage.error('封面上传时发生网络错误');
    }
  };
  </script>
  
<style lang="scss" scoped>
.create-template-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 32px;
    margin: 0;

    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: white;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: rgba(255, 255, 255, 0.8);
        font-size: 18px;

        &:hover {
          color: white;
        }
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 0;
    background: #f8fafc;
  }

  :deep(.el-dialog__footer) {
    padding: 0;
    background: white;
  }
}

.dialog-content {
  padding: 32px;
  background: #f8fafc;
}

.template-form {
  .form-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid #e2e8f0;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 20px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid #e2e8f0;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 40px;
      height: 2px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }

  .form-item {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #374151;
    }
  }

  .form-input, .form-select {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #d1d5db;
      transition: all 0.2s ease;

      &:hover {
        border-color: #9ca3af;
      }

      &.is-focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
  }

  .config-item {
    .config-builder {
      border-radius: 8px;
      overflow: hidden;
    }
  }
}

.option-content {
  display: flex;
  flex-direction: column;

  .option-label {
    font-weight: 500;
    color: #1e293b;
  }

  .option-desc {
    font-size: 12px;
    color: #64748b;
    margin-top: 2px;
  }
}

.cover-upload-container {
  .cover-uploader {
    :deep(.el-upload) {
      width: 200px;
      height: 120px;
      border: 2px dashed #d1d5db;
      border-radius: 12px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      background: #f9fafb;

      &:hover {
        border-color: #667eea;
        background: #f0f4ff;
      }
    }
  }

  .cover-preview {
    width: 100%;
    height: 100%;
    position: relative;

    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 10px;
    }

    .cover-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 10px;

      &:hover {
        opacity: 1;
      }

      .overlay-icon {
        font-size: 24px;
        color: white;
        margin-bottom: 4px;
      }

      .overlay-text {
        color: white;
        font-size: 12px;
      }
    }
  }

  .cover-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;

    .upload-icon {
      font-size: 32px;
      color: #9ca3af;
      margin-bottom: 8px;
    }

    .upload-text {
      font-size: 14px;
      color: #374151;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .upload-hint {
      font-size: 12px;
      color: #6b7280;
      text-align: center;
      line-height: 1.4;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 32px;
  background: white;
  border-top: 1px solid #e2e8f0;

  .cancel-btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
  }

  .confirm-btn {
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .el-icon {
      margin-right: 6px;
    }
  }
}
</style>