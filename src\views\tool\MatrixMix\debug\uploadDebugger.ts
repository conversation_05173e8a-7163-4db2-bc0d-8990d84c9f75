/**
 * 一体化媒资上传调试工具
 * 
 * 用于调试和测试一体化媒资上传接口的问题
 * 可以分析文件名、FormData构造、网络请求等各个环节
 */

import { uploadAndRegisterMedia } from '../api/media';

export class UploadDebugger {
  private static instance: UploadDebugger;
  private logs: Array<{ timestamp: string; level: string; message: string; data?: any }> = [];
  
  static getInstance(): UploadDebugger {
    if (!UploadDebugger.instance) {
      UploadDebugger.instance = new UploadDebugger();
    }
    return UploadDebugger.instance;
  }
  
  private log(level: 'info' | 'warn' | 'error' | 'debug', message: string, data?: any) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data
    };
    this.logs.push(logEntry);
    console[level](`[UploadDebugger] ${message}`, data || '');
  }
  
  /**
   * 分析文件是否适合上传
   */
  analyzeFile(file: File) {
    this.log('info', '🔍 文件分析开始');
    
    const analysis = {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: new Date(file.lastModified).toISOString(),
      hasSpecialChars: /[^\w\u4e00-\u9fa5.-]/.test(file.name),
      specialChars: file.name.match(/[^\w\u4e00-\u9fa5.-]/g) || [],
      extension: file.name.substring(file.name.lastIndexOf('.')),
      nameWithoutExt: file.name.substring(0, file.name.lastIndexOf('.')),
      sizeFormatted: this.formatFileSize(file.size),
      potentialIssues: [] as Array<{
        type: string;
        issue: string;
        chars?: string[];
        size?: string;
        recommendation: string;
      }>
    };
    
    // 检查潜在问题
    if (analysis.hasSpecialChars) {
      analysis.potentialIssues.push({
        type: 'fileName',
        issue: '文件名包含特殊字符',
        chars: analysis.specialChars,
        recommendation: '建议使用字母、数字、中文、点和横线'
      });
    }
    
    if (file.size > 100 * 1024 * 1024) { // 100MB
      analysis.potentialIssues.push({
        type: 'fileSize',
        issue: '文件较大',
        size: analysis.sizeFormatted,
        recommendation: '可能需要较长上传时间'
      });
    }
    
    if (!file.type) {
      analysis.potentialIssues.push({
        type: 'mimeType',
        issue: '文件类型未识别',
        recommendation: '检查文件是否损坏或格式不支持'
      });
    }
    
    this.log('info', '📋 文件分析结果', analysis);
    return analysis;
  }
  
  /**
   * 生成安全的文件名
   */
  sanitizeFileName(fileName: string): string {
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    const extension = fileName.substring(fileName.lastIndexOf('.'));
    
    const safeName = nameWithoutExt
      .replace(/[^\w\u4e00-\u9fa5.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .replace(/^_+|_+$/g, '');
    
    const result = safeName + extension;
    this.log('info', `🔧 文件名处理: "${fileName}" -> "${result}"`);
    return result;
  }
  
  /**
   * 测试上传流程
   */
  async testUpload(file: File, category: string = 'general', metadata?: any) {
    this.log('info', '🚀 开始测试上传流程');
    
    try {
      // 1. 文件分析
      const fileAnalysis = this.analyzeFile(file);
      
      // 2. 元数据准备（不修改文件名，让后端API统一处理）
      const defaultMetadata = {
        title: metadata?.title || file.name.split('.').slice(0, -1).join('.'),
        description: metadata?.description || `${category}文件: ${file.name}`,
        tags: metadata?.tags || `${category},media`,
        businessType: metadata?.businessType || category
      };
      
      this.log('info', '📋 上传参数', {
        originalFile: { name: file.name, size: file.size, type: file.type },
        category,
        metadata: defaultMetadata
      });
      
      // 3. 执行上传（直接使用原始文件，让后端API处理文件名）
      const startTime = Date.now();
      const result = await uploadAndRegisterMedia(file, category, defaultMetadata);
      const endTime = Date.now();
      
      this.log('info', '✅ 上传成功', {
        duration: `${endTime - startTime}ms`,
        result
      });
      
      return {
        success: true,
        result,
        duration: endTime - startTime,
        fileAnalysis
      };
      
    } catch (error: any) {
      this.log('error', '❌ 上传失败', {
        error: error.message,
        response: error.response?.data,
        status: error.response?.status,
        stack: error.stack
      });
      
      return {
        success: false,
        error: error.message,
        details: error.response?.data,
        status: error.response?.status
      };
    }
  }
  
  /**
   * 检查网络连接和后端状态
   */
  async checkBackendHealth() {
    this.log('info', '🔍 检查后端健康状态');
    
    try {
      // 可以添加一个简单的健康检查接口
      const response = await fetch('/api/health', { method: 'GET' });
      const isHealthy = response.ok;
      
      this.log(isHealthy ? 'info' : 'warn', `后端状态: ${isHealthy ? '正常' : '异常'}`, {
        status: response.status,
        statusText: response.statusText
      });
      
      return isHealthy;
    } catch (error) {
      this.log('error', '后端健康检查失败', error);
      return false;
    }
  }
  
  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  /**
   * 获取所有日志
   */
  getLogs() {
    return this.logs;
  }
  
  /**
   * 清空日志
   */
  clearLogs() {
    this.logs = [];
    this.log('info', '🧹 日志已清空');
  }
  
  /**
   * 导出调试报告
   */
  exportDebugReport() {
    const report = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      logs: this.logs
    };
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `upload-debug-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    this.log('info', '📥 调试报告已导出');
  }
}

// 全局导出到window，方便在浏览器控制台使用
declare global {
  interface Window {
    uploadDebugger: UploadDebugger;
  }
}

if (typeof window !== 'undefined') {
  window.uploadDebugger = UploadDebugger.getInstance();
}

export default UploadDebugger;

// 使用示例:
// window.uploadDebugger.testUpload(file, 'audio')
// window.uploadDebugger.analyzeFile(file)
// window.uploadDebugger.getLogs()
// window.uploadDebugger.exportDebugReport()
