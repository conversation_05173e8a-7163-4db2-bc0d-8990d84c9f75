<template>
    <div class="timeline-container">
        <div class="timeline-header">
            <div class="timeline-controls">
                <button class="timeline-btn" @click="playVideo">
                    <i class="el-icon-video-play"></i>
                </button>
                <button class="timeline-btn" @click="pauseVideo">
                    <i class="el-icon-video-pause"></i>
                </button>
                <button class="timeline-btn" @click="resetVideo">
                    <i class="el-icon-refresh-left"></i>
                </button>
                <span class="time-display">{{ currentTime }} / {{ duration }}</span>
            </div>
            <div class="timeline-tools">
                <button class="tool-btn" @click="cutClip">剪切</button>
                <button class="tool-btn" @click="copyClip">复制</button>
                <button class="tool-btn" @click="deleteClip">删除</button>
            </div>
        </div>
        <div class="timeline-ruler">
            <div class="ruler-marks">
                <div v-for="n in 21" :key="n" class="ruler-mark">
                    <span class="ruler-time">{{ String(n - 1).padStart(2, '0') }}:00</span>
                    <div class="ruler-line"></div>
                </div>
            </div>
        </div>
        <div class="timeline-tracks">
            <div class="track-labels">
                <div class="track-label">视频轨道</div>
                <div class="track-label">音频轨道</div>
                <div class="track-label">字幕轨道</div>
            </div>
            <div class="track-content">
                <div class="track video-track">
                    <div 
                        v-for="(clip, index) in videoClips" 
                        :key="`video-${index}`"
                        class="clip video-clip" 
                        :style="{ left: clip.left + 'px', width: clip.width + 'px' }"
                        @click="selectClip('video', index)"
                        :class="{ selected: selectedClip.type === 'video' && selectedClip.index === index }"
                    >
                        <div class="clip-content">{{ clip.name }}</div>
                    </div>
                </div>
                <div class="track audio-track">
                    <div 
                        v-for="(clip, index) in audioClips" 
                        :key="`audio-${index}`"
                        class="clip audio-clip" 
                        :style="{ left: clip.left + 'px', width: clip.width + 'px' }"
                        @click="selectClip('audio', index)"
                        :class="{ selected: selectedClip.type === 'audio' && selectedClip.index === index }"
                    >
                        <div class="clip-content">{{ clip.name }}</div>
                    </div>
                </div>
                <div class="track subtitle-track">
                    <div 
                        v-for="(clip, index) in subtitleClips" 
                        :key="`subtitle-${index}`"
                        class="clip subtitle-clip" 
                        :style="{ left: clip.left + 'px', width: clip.width + 'px' }"
                        @click="selectClip('subtitle', index)"
                        :class="{ selected: selectedClip.type === 'subtitle' && selectedClip.index === index }"
                    >
                        <div class="clip-content">{{ clip.name }}</div>
                    </div>
                </div>
            </div>
            <div class="playhead" :style="{ left: playheadPosition + 'px' }"></div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, defineEmits, defineProps } from 'vue'

// 定义 props
const props = defineProps({
    currentTime: {
        type: String,
        default: '00:00'
    },
    duration: {
        type: String,
        default: '10:00'
    },
    playheadPosition: {
        type: Number,
        default: 100
    }
})

// 定义 emits
const emit = defineEmits(['play', 'pause', 'reset', 'cut', 'copy', 'delete', 'clip-select'])

// 响应式数据
const selectedClip = ref({
    type: null,
    index: null
})

// 示例数据 - 可以通过 props 传入
const videoClips = ref([
    { name: 'videoC.mp4', left: 10, width: 200 },
    { name: 'user2.mp4', left: 220, width: 150 }
])

const audioClips = ref([
    { name: '音频 1', left: 10, width: 200 },
    { name: '音频 2', left: 220, width: 150 }
])

const subtitleClips = ref([
    { name: '字幕 1', left: 50, width: 100 }
])

// 方法
const playVideo = () => {
    emit('play')
}

const pauseVideo = () => {
    emit('pause')
}

const resetVideo = () => {
    emit('reset')
}

const cutClip = () => {
    if (selectedClip.value.type) {
        emit('cut', selectedClip.value)
    }
}

const copyClip = () => {
    if (selectedClip.value.type) {
        emit('copy', selectedClip.value)
    }
}

const deleteClip = () => {
    if (selectedClip.value.type) {
        emit('delete', selectedClip.value)
        selectedClip.value = { type: null, index: null }
    }
}

const selectClip = (type, index) => {
    selectedClip.value = { type, index }
    emit('clip-select', { type, index })
}
</script>

<style lang="scss" scoped>
.timeline-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    color: #ffffff;

    .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 20px;
        background-color: #2a2a2a;
        border-bottom: 1px solid #444;

        .timeline-controls {
            display: flex;
            align-items: center;
            gap: 10px;

            .timeline-btn {
                background-color: #3a3a3a;
                border: none;
                color: #ffffff;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                
                &:hover {
                    background-color: #4a4a4a;
                }
            }

            .time-display {
                font-family: monospace;
                font-size: 14px;
                color: #ccc;
                margin-left: 10px;
            }
        }

        .timeline-tools {
            display: flex;
            gap: 8px;

            .tool-btn {
                background-color: #1378f9;
                border: none;
                color: #ffffff;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;

                &:hover {
                    background-color: #0d6efd;
                }
            }
        }
    }

    .timeline-ruler {
        background-color: #2a2a2a;
        border-bottom: 1px solid #444;
        height: 30px;

        .ruler-marks {
            display: flex;
            position: relative;

            .ruler-mark {
                position: relative;
                width: 60px;
                height: 30px;
                display: flex;
                align-items: flex-end;

                .ruler-time {
                    font-size: 10px;
                    color: #888;
                    position: absolute;
                    bottom: 2px;
                    left: 2px;
                }

                .ruler-line {
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    width: 1px;
                    height: 15px;
                    background-color: #666;
                }
            }
        }
    }

    .timeline-tracks {
        flex: 1;
        display: flex;
        position: relative;
        overflow-x: auto;
        overflow-y: hidden;

        .track-labels {
            width: 120px;
            background-color: #2a2a2a;
            border-right: 1px solid #444;
            flex-shrink: 0;

            .track-label {
                height: 60px;
                display: flex;
                align-items: center;
                padding: 0 10px;
                border-bottom: 1px solid #444;
                font-size: 12px;
                color: #ccc;
            }
        }

        .track-content {
            flex: 1;
            position: relative;

            .track {
                height: 60px;
                border-bottom: 1px solid #444;
                position: relative;
                background-color: #1a1a1a;

                &.video-track {
                    background-color: #1a2332;
                }

                &.audio-track {
                    background-color: #2d1a32;
                }

                &.subtitle-track {
                    background-color: #1a3232;
                }

                .clip {
                    position: absolute;
                    height: 50px;
                    top: 5px;
                    border-radius: 4px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 2px solid transparent;
                    transition: all 0.2s ease;

                    &:hover {
                        border-color: #1378f9;
                    }

                    &.selected {
                        border-color: #ffd700;
                        box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
                    }

                    .clip-content {
                        font-size: 11px;
                        color: #ffffff;
                        text-align: center;
                        padding: 0 5px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    &.video-clip {
                        background-color: #4a90e2;
                    }

                    &.audio-clip {
                        background-color: #9b59b6;
                    }

                    &.subtitle-clip {
                        background-color: #1abc9c;
                    }
                }
            }
        }

        .playhead {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #ff4444;
            z-index: 10;
            pointer-events: none;
            transition: left 0.1s ease;

            &::before {
                content: '';
                position: absolute;
                top: -5px;
                left: -5px;
                width: 12px;
                height: 12px;
                background-color: #ff4444;
                border-radius: 50%;
            }
        }
    }
}
</style>
