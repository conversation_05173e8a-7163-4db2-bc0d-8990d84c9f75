<template>
  <div class="template-factory-container">
    <!-- 顶部标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <div class="title-icon"><el-icon><Film /></el-icon></div>
          <div class="title-text">
            <h1>模板工厂</h1>
            <p>管理和创建您的视频模板</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="primary" size="large" @click="createTemplate" class="create-btn"><el-icon><Plus /></el-icon>创建模板</el-button>
        </div>
      </div>
    </div>

    <!-- 主内容卡片 -->
    <div class="main-content">
      <el-card class="content-card">
        <!-- 搜索工具栏 -->
        <div class="search-toolbar">
          <div class="search-left">
            <el-input v-model="queryParams.keyword" placeholder="搜索模板名称或ID" style="width: 280px;" clearable @keyup.enter="handleQuery" @clear="handleQuery" class="search-input">
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template></el-input>
            <el-select v-model="queryParams.status" placeholder="选择状态" style="width: 260px;" clearable @change="handleQuery" class="status-select">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button type="primary" @click="handleQuery" class="search-btn"><el-icon><Search /></el-icon>搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn"><el-icon><Refresh /></el-icon>重置</el-button>
          </div>
          <div class="search-right">
            <el-button :disabled="state.multiple" type="danger" plain @click="handleDelete()" class="batch-delete-btn"><el-icon><Delete /></el-icon>批量删除 ({{ multipleSelection.length }})</el-button>
          </div>
        </div>

        <!-- 模板表格 -->
        <div class="table-container">
          <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange" class="template-table" :row-class-name="tableRowClassName">
            <el-table-column type="selection" width="60" align="center" />

            <el-table-column label="模板信息" min-width="280">
              <template #default="{ row }">
                <div class="template-card">
                  <div class="template-cover">
                    <el-image v-if="row.CoverURL" :src="row.CoverURL" fit="cover" class="cover-image" :preview-src-list="[row.CoverURL]"><template #error><div class="image-slot"><el-icon><Film /></el-icon></div></template></el-image>
                    <div v-else class="image-slot"><el-icon><Film /></el-icon></div>
                  </div>
                  <div class="template-info">
                    <div class="template-name">{{ row.Name }}</div>
                    <div class="template-id">ID: {{ row.TemplateId }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="状态" width="140" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusConfig(row.Status).type" size="small" round>{{ getStatusConfig(row.Status).label }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="CreateSource" label="创建方式" width="140" align="center" />

            <el-table-column label="创建时间" width="180" align="center">
              <template #default="scope">
                <div class="time-display">
                  <el-icon class="time-icon"><Clock /></el-icon>
                  <span>{{ parseTime(scope.row.CreationTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="更新时间" width="180" align="center">
              <template #default="scope">
                <div class="time-display">
                  <el-icon class="time-icon"><Clock /></el-icon>
                  <span>{{ parseTime(scope.row.ModifiedTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right" align="center">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button link type="primary" @click="editTemplate(row)" class="action-btn edit-btn"><el-icon><Edit /></el-icon>编辑</el-button>
                  <el-button link type="danger" @click="handleDelete(row)" class="action-btn delete-btn"><el-icon><Delete /></el-icon>删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination v-if="total > 0" v-model:current-page="queryParams.pageNo" v-model:page-size="queryParams.pageSize" :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="fetchTemplateList" @current-change="fetchTemplateList" class="pagination" />
        </div>
      </el-card>
    </div>

    <!-- 创建模板弹窗子组件 -->
    <CreateTemplateDialog v-model:visible="dialogVisible" @success="fetchTemplateList" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, toRefs } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { Search, Refresh, Film, Plus, Delete, Edit, Clock } from '@element-plus/icons-vue';
import { listTemplates, getTemplate, deleteTemplate as deleteTemplateApi } from '../api/template';
import type { TemplateInfo, ListTemplatesRequest } from '../types/template';
import { useRouter } from 'vue-router';
import { parseTime } from '@/utils/ruoyi';
import CreateTemplateDialog from './components/CreateTemplateDialog.vue';

const loading = ref(false);
const templateList = ref<any[]>([]);
const multipleSelection = ref<any[]>([]);
const total = ref(0);
const dialogVisible = ref(false);

// 状态管理 - 参考 videoEditList.vue
const state = reactive({
  ids: [] as string[],
  multiple: true,
});

// 状态配置
const statusConfig = {
  base: {
    Available: { label: '正常', type: 'success', effect: 'light' },
    UploadFailed: { label: '上传失败', type: 'danger', effect: 'light' },
    ProcessFailed: { label: '高级模版分析失败', type: 'danger', effect: 'light' },
    Uploading: { label: '上传中', type: 'warning', effect: 'light' },
    Created: { label: '已创建，还不能使用', type: 'info', effect: 'light' },
    Processing: { label: '高级模板分析中', type: 'warning', effect: 'light' }
  },
  // 生成选项列表 - 使用英文值作为value
  get options() {
    return [
      { label: '全部', value: undefined },
      ...Object.entries(this.base).map(([value, item]) => ({ label: item.label, value }))
    ];
  },
  // 生成映射表
  get mapping() {
    return {
      ...this.base,
      default: { label: '未知', type: 'info', effect: 'light' }
    };
  }
};

// 导出状态选项供模板使用
const statusOptions = statusConfig.options;

// 获取状态配置
function getStatusConfig(status: string) {
  return statusConfig.mapping[status as keyof typeof statusConfig.mapping] || statusConfig.mapping.default;
}

const data = reactive<{
  queryParams: ListTemplatesRequest
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 5,
    keyword: '',
    status: undefined, // 默认不筛选状态
    type: 'Timeline', // 默认只查询Timeline类型的模板
  }
});

const { queryParams } = toRefs(data);
const router = useRouter();

const fetchTemplateList = async () => {
  loading.value = true;
  try {
    const res = await listTemplates(queryParams.value);
    if (res.code === 200) {
      // 兼容后端未分页和已分页两种情况
      const allTemplates = res.data.Templates || [];
      const totalCount = res.data.TotalCount || allTemplates.length;
      total.value = totalCount;
      const { pageNo = 1, pageSize = 10 } = queryParams.value;
      // 如果后端返回的Templates数量大于pageSize，且totalCount大于pageSize，说明未分页，需要前端slice
      if (allTemplates.length > pageSize && totalCount > pageSize) {
        const start = (pageNo - 1) * pageSize;
        const end = start + pageSize;
        templateList.value = allTemplates.slice(start, end);
      } else {
        // 后端已分页，直接用
        templateList.value = allTemplates;
      }
    } else {
      ElMessage.error(`获取模板列表失败: ${res.msg}`);
      templateList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取模板列表时发生错误:', error);
    ElMessage.error('获取模板列表时发生错误');
    templateList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

const handleQuery = () => {
  queryParams.value.pageNo = 1; // 重置页码为1
  fetchTemplateList();
};

const resetQuery = () => {
  queryParams.value.pageNo = 1;
  queryParams.value.keyword = '';
  queryParams.value.status = undefined; // 重置状态筛选
  fetchTemplateList();
};

const createTemplate = () => {
  dialogVisible.value = true;
};

const editTemplate = (row: TemplateInfo) => {
  if (!row.TemplateId) {
    ElMessage.error('模板ID缺失，无法编辑。');
    return;
  }

  // 直接跳转到模板编辑页，传递模板ID
  router.push({
    path: `/tool/matrix-mix/video-edit/${row.TemplateId}`,
    query: { mode: 'template' }
  });
};

/** 删除按钮操作 */
async function handleDelete(row?: any) {
  const templateIds = row ? [row.TemplateId] : state.ids;
  if (templateIds.length === 0) {
    ElMessage.warning('请选择要删除的模板');
    return;
  }
  try {
    await ElMessageBox.confirm(
      `是否确认删除模板ID为 "${templateIds.join(', ')}" 的数据项？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    await deleteTemplateApi(templateIds);
    ElMessage.success('删除成功');
    fetchTemplateList(); // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
}

// 表格行样式
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  return rowIndex % 2 === 1 ? 'table-row-even' : '';
};

/** 多选框选中数据 */
function handleSelectionChange(selection: any[]) {
  multipleSelection.value = selection;
  state.ids = selection.map(item => item.TemplateId);
  state.multiple = !selection.length;
}

onMounted(() => {
  fetchTemplateList();
});
</script>

<style lang="scss" scoped>
.template-factory-container {
  padding: 24px;
  background: #f8fafc;
  height: 700px;
  position: relative;
}

.page-header {
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .title-section {
    display: flex;
    align-items: center;
    gap: 16px;

    .title-icon {
      width: 56px;
      height: 56px;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
      }
    }

    .title-text {
      h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 700;
        color: #1e293b;
        line-height: 1.2;
      }

      p {
        margin: 4px 0 0 0;
        font-size: 14px;
        color: #64748b;
        line-height: 1.4;
      }
    }
  }

  .header-actions {
    .create-btn {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border: none;
      color: white;
      font-weight: 600;
      padding: 12px 24px;
      border-radius: 12px;
      transition: all 0.3s ease;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

      &:hover {
        background: linear-gradient(135deg, #2563eb, #1e40af);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
      }
    }
  }
}

.main-content {
  
  position: relative;
  z-index: 1;

  .content-card {
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;
    background: white;
    animation: slideInUp 0.8s ease-out 0.6s both;

    :deep(.el-card__body) {
      padding: 32px;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
}

.search-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .search-left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;

    .search-input, .status-select {
      :deep(.el-input__wrapper) {
        border-radius: 8px;
        border: 1px solid #d1d5db;
        transition: all 0.2s ease;
        box-shadow: none;

        &:hover {
          border-color: #3b82f6;
        }

        &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }
    }

    .search-btn {
      background: #3b82f6;
      border: none;
      border-radius: 8px;
      padding: 10px 16px;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        background: #2563eb;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
      }
    }

    .reset-btn {
      background: #f8fafc;
      border: 1px solid #d1d5db;
      color: #64748b;
      border-radius: 8px;
      padding: 10px 16px;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        background: #f1f5f9;
        border-color: #94a3b8;
        transform: translateY(-1px);
      }
    }
  }

  .search-right {
    .batch-delete-btn {
      background: #ef4444;
      border: none;
      color: white;
      border-radius: 8px;
      padding: 10px 16px;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background: #dc2626;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
      }

      &:disabled {
        background: #f1f5f9;
        color: #94a3b8;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }
  }
}

.table-container {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .template-table {
    :deep(.el-table__header) {
      th {
        background: #f8fafc;
        color: #374151;
        font-weight: 600;
        font-size: 14px;
        border-bottom: 1px solid #e2e8f0;
        padding: 16px;
      }
    }

    :deep(.el-table__body) {
      tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #f1f5f9;

        &:hover {
          background: #f8fafc;
        }

        &.table-row-even {
          background: #fafbfc;
        }

        &:last-child {
          border-bottom: none;
        }

        td {
          border: none;
          padding: 16px;
          vertical-align: middle;
        }
      }
    }
  }
}

.template-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px;
  border-radius: 8px;
  transition: all 0.2s ease;

  .template-cover {
    width: 56px;
    height: 56px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;

    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .image-slot {
      width: 100%;
      height: 100%;
      background: #f1f5f9;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #94a3b8;
      font-size: 20px;
    }
  }

  .template-info {
    flex: 1;
    min-width: 0;

    .template-name {
      font-size: 14px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 4px;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .template-id {
      font-size: 11px;
      color: #64748b;
      background: #f1f5f9;
      padding: 2px 6px;
      border-radius: 4px;
      display: inline-block;
      font-family: 'Monaco', 'Menlo', monospace;
    }
  }
}

.time-display {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #64748b;

  .time-icon {
    color: #94a3b8;
    font-size: 12px;
  }
}

.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;

  .action-btn {
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 3px;

    &.edit-btn {
      color: #3b82f6;

      &:hover {
        background: #3b82f6;
        color: white;
      }
    }

    &.delete-btn {
      color: #ef4444;

      &:hover {
        background: #ef4444;
        color: white;
      }
    }
  }
}

.pagination-wrapper {
  margin-top: 20px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .pagination {
    :deep(.el-pagination) {
      justify-content: center;

      .el-pager li {
        border-radius: 6px;
        margin: 0 2px;
        min-width: 32px;
        height: 32px;
        line-height: 32px;
        font-weight: 500;
        transition: all 0.2s ease;
        border: 1px solid #e2e8f0;

        &:hover {
          background: #3b82f6;
          color: white;
          border-color: #3b82f6;
        }

        &.is-active {
          background: #3b82f6;
          color: white;
          border-color: #3b82f6;
        }
      }

      .btn-prev, .btn-next {
        border-radius: 6px;
        margin: 0 4px;
        width: 32px;
        height: 32px;
        transition: all 0.2s ease;
        border: 1px solid #e2e8f0;

        &:hover {
          background: #3b82f6;
          color: white;
          border-color: #3b82f6;
        }
      }

      .el-pagination__total,
      .el-pagination__jump {
        color: #64748b;
        font-weight: 500;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .template-factory-container {
    padding: 16px;
  }

  .page-header {
    padding: 24px;

    .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }
  }
}

@media (max-width: 768px) {
  .template-factory-container {
    padding: 12px;

    .page-header {
      padding: 20px;

      .title-section {
        flex-direction: column;
        gap: 12px;

        .title-icon {
          width: 48px;
          height: 48px;
          font-size: 20px;
        }

        .title-text h1 {
          font-size: 24px;
        }
      }
    }

    .main-content .content-card :deep(.el-card__body) {
      padding: 16px;
    }

    .search-toolbar {
      flex-direction: column;
      gap: 12px;
      padding: 16px;

      .search-left {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
      }

      .search-right {
        width: 100%;
        text-align: center;
      }
    }

    .template-card {
      .template-cover {
        width: 48px;
        height: 48px;
      }

      .template-info .template-name {
        font-size: 13px;
      }
    }

    .action-buttons {
      flex-direction: column;
      gap: 4px;

      .action-btn {
        font-size: 11px;
        padding: 5px 8px;
      }
    }
  }
}
</style>