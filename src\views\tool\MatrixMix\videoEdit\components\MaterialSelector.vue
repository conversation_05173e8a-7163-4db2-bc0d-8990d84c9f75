<template>
  <el-drawer v-model="drawerVisible" title="添加素材" direction="rtl" size="40%"
    style="background-color:rgba(44, 44, 44, 0.8) ;" class="material-selector-drawer" :before-close="handleClose">
    <template #header>
      <div class="drawer-header">
        <h3>{{ headerTitle }}</h3>
        <div class="header-actions">
          <el-button type="primary" :icon="Upload" @click="handleUpload">
            上传{{ mediaTypeLabel }}
          </el-button>
        </div>
      </div>
    </template>

    <div class="material-content">
      <!-- 搜索栏 -->
      <div class="search-bar material-selector-scoped">
        <el-select v-model="sortBy" placeholder="排序方式" style="width: 120px" @change="handleSortChange"
          class="material-selector-select">
          <el-option label="文件名" value="title:Desc" />
          <el-option label="创建时间" value="utcCreate:Desc" />
        </el-select>
        
        <template v-if="sortBy === 'title:Desc'">
          <el-input v-model="searchKeyword" placeholder="搜索素材名称..." :prefix-icon="Search" clearable
            @change="handleSearch" style="flex:1" class="material-selector-input" />
        </template>
        <template v-else-if="sortBy === 'utcCreate:Desc'">
          <el-date-picker v-model="searchDateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="YYYY-MM-DD" value-format="x" @change="handleSearch" style="flex:1"
            class="material-selector-date" />
        </template>

        <span class="go-media-lib-link" @click="handleGoToMediaLib">
          去媒资库上传
        </span>
      </div>

      <!-- 媒体类型标签 -->
      <div class="media-type-tabs">
        <div v-for="tab in mediaTypeTabs" :key="tab.value" class="media-tab"
          :class="{ active: currentMediaTypeFilter === tab.value }" @click="handleMediaTypeChange(tab.value)">
          {{ tab.label }}
        </div>
      </div>

      <!-- 素材网格 -->
      <div v-loading="loading" class="material-grid">
        <div v-for="material in materialList" :key="material.mediaId" class="material-item"
          :class="{ selected: selectedMaterials.includes(material.mediaId) }" @click="toggleSelect(material)">
          <div class="material-preview">
            <img v-if="material.coverUrl" :src="material.coverUrl" :alt="material.title" @error="handleImageError" />
            <div v-else class="no-preview">
              <el-icon><Picture /></el-icon>
            </div>

            <div v-if="material.mediaType === 'video' && material.duration" class="duration">
              {{ formatDuration(material.duration) }}
            </div>

            <div v-if="selectedMaterials.includes(material.mediaId)" class="selected-overlay">
              <el-icon class="check-icon"><Check /></el-icon>
            </div>
          </div>

          <div class="material-info">
            <div class="material-title" :title="material.title">{{ material.title }}</div>
            <div class="material-meta">
              <span v-if="material.size">{{ formatFileSize(material.size) }}</span>
              <span v-if="material.createTime">{{ formatTime(material.createTime) }}</span>
            </div>
          </div>
        </div>

        <div v-if="!loading && materialList.length === 0" class="empty-state">
          <el-empty description="暂无素材" />
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="total > 0" class="pagination">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
          :page-sizes="[20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handlePageChange" />
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <span class="selected-count">已选择 {{ selectedMaterialsCache.length }} 个素材</span>
        <div>
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" :disabled="selectedMaterialsCache.length === 0" @click="handleConfirm">
            确定添加
          </el-button>
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Search, Picture, Check } from '@element-plus/icons-vue'
import { iceNPMManager } from '../utils/iceNPMManager'
import { formatDuration, formatFileSize } from '@/views/tool/MatrixMix/utils/commonUtils'

// 显示用接口
interface Material {
  mediaId: string
  title: string
  coverUrl?: string
  duration?: number
  size?: number
  createTime: string
  mediaType: 'video' | 'audio' | 'image' | 'text'
}

interface Props {
  visible: boolean
  mediaType: 'video' | 'audio' | 'image' | 'text'
  mode: 'project' | 'template'
  editorId: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'select', materials: any[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 数据状态
const loading = ref(false)
const materialList = ref<Material[]>([])
const selectedMaterials = ref<string[]>([])
const selectedMaterialsCache = ref<any[]>([])
const rawMaterialsMap = ref<Map<string, any>>(new Map())
const searchKeyword = ref('')
const searchDateRange = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const currentMediaTypeFilter = ref('')
const sortBy = ref('title:Desc')

// 抽屉状态
const drawerVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 媒体类型标签
const mediaTypeTabs = ref([
  { label: '所有', value: '' },
  { label: '视频', value: 'video' },
  { label: '图片', value: 'image' },
  { label: '音频', value: 'audio' },
  { label: '文本', value: 'text' }
])

// 计算属性
const headerTitle = computed(() => {
  const typeMap: Record<string, string> = {
    video: '添加视频素材',
    audio: '添加音频素材',
    image: '添加图片素材',
    text: '添加文本素材'
  }
  return typeMap[props.mediaType] || '添加素材'
})

const mediaTypeLabel = computed(() => {
  const labelMap: Record<string, string> = {
    video: '视频',
    audio: '音频',
    image: '图片',
    text: '文本'
  }
  return labelMap[props.mediaType] || '素材'
})

// 监听抽屉打开
watch(drawerVisible, (visible) => {
  if (visible) {
    resetData()
    loadMaterials()
  }
})

// 重置数据
const resetData = () => {
  selectedMaterials.value = []
  selectedMaterialsCache.value = []
  rawMaterialsMap.value.clear()
  materialList.value = []
  currentPage.value = 1
  searchKeyword.value = ''
  searchDateRange.value = ''
  currentMediaTypeFilter.value = ''
  total.value = 0
}

// 加载素材列表
const loadMaterials = async () => {
  loading.value = true
  try {
    const matchConditions: string[] = []

    // 媒体类型过滤
    const mediaType = currentMediaTypeFilter.value || props.mediaType
    if (mediaType && mediaType !== '') {
      matchConditions.push(`mediaType == '${mediaType}'`)
    }

    // 搜索关键词过滤
    if (searchKeyword.value) {
      matchConditions.push(`title = '${searchKeyword.value}'`)
    }

    // 日期范围过滤
    if (searchDateRange.value && Array.isArray(searchDateRange.value) && searchDateRange.value.length === 2) {
      const [startTime, endTime] = searchDateRange.value
      matchConditions.push(`utcCreate >= ${startTime} and utcCreate <= ${endTime}`)
    }

    const params: any = {
      PageSize: pageSize.value,
      PageNo: currentPage.value,
      SortBy: sortBy.value
    }

    if (matchConditions.length > 0) {
      params.Match = matchConditions.join(' and ')
    }

    const response = await iceNPMManager.SearchMedia(params)

    if (response?.MediaInfoList) {
      const { MediaInfoList = [], Total = 0 } = response

      // 存储原始API数据映射
      rawMaterialsMap.value.clear()
      MediaInfoList.forEach((item: any) => {
        rawMaterialsMap.value.set(item.MediaId, item)
      })

      // 转换为显示数据
      const materials: Material[] = MediaInfoList.map((item: any) => ({
        mediaId: item.MediaId,
        title: item.MediaBasicInfo?.Title || item.FileInfoList?.[0]?.FileBasicInfo?.FileName || '未知素材',
        coverUrl: item.MediaBasicInfo?.CoverURL,
        duration: item.FileInfoList?.[0]?.FileBasicInfo?.Duration ? parseFloat(String(item.FileInfoList[0].FileBasicInfo.Duration)) : undefined,
        size: item.FileInfoList?.[0]?.FileBasicInfo?.FileSize ? parseInt(String(item.FileInfoList[0].FileBasicInfo.FileSize)) : undefined,
        createTime: item.MediaBasicInfo?.CreateTime || item.FileInfoList?.[0]?.FileBasicInfo?.CreateTime || '',
        mediaType: (item.MediaBasicInfo?.MediaType || props.mediaType || 'video') as 'video' | 'audio' | 'image' | 'text'
      }))

      materialList.value = materials
      total.value = Total
    }
  } catch (error) {
    ElMessage.error('加载素材失败')
  } finally {
    loading.value = false
  }
}

// 切换选择
const toggleSelect = (material: Material) => {
  const index = selectedMaterials.value.indexOf(material.mediaId)
  if (index > -1) {
    selectedMaterials.value.splice(index, 1)
    const cacheIndex = selectedMaterialsCache.value.findIndex(item => item.MediaId === material.mediaId)
    if (cacheIndex > -1) {
      selectedMaterialsCache.value.splice(cacheIndex, 1)
    }
  } else {
    selectedMaterials.value.push(material.mediaId)
    const rawData = rawMaterialsMap.value.get(material.mediaId)
    if (rawData) {
      selectedMaterialsCache.value.push(rawData)
    }
  }
}

// 处理上传
const handleUpload = () => {
  ElMessage.info('上传功能待实现')
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  loadMaterials()
}

// 处理排序变化
const handleSortChange = () => {
  currentPage.value = 1
  loadMaterials()
}

// 处理跳转到媒资库
const handleGoToMediaLib = () => {
  // 待实现
}

// 处理媒体类型变化
const handleMediaTypeChange = (mediaType: string) => {
  currentMediaTypeFilter.value = mediaType
  currentPage.value = 1
  loadMaterials()
}

// 处理分页
const handlePageChange = () => {
  loadMaterials()
}

const handleSizeChange = () => {
  currentPage.value = 1
  loadMaterials()
}

// 处理图片错误
const handleImageError = (e: Event) => {
  const target = e.target as HTMLImageElement
  target.style.display = 'none'
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleDateString()
}

// 处理关闭
const handleClose = () => {
  drawerVisible.value = false
}

// 处理确认 - 返回原始API数据
const handleConfirm = () => {
  emit('select', selectedMaterialsCache.value)
  handleClose()
}
</script>

<style lang="scss" scoped>
.material-selector-drawer {
  :deep(.el-drawer) {
    background: #2c2c2c;
    border: none;
    box-shadow: -2px 0 12px rgba(0, 0, 0, 0.4);
    min-height: 600px;
  }

  :deep(.el-drawer__body) {
    padding: 0;
    display: flex;
    background: #2c2c2c;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 0 !important;
    background: #2c2c2c;
    border-bottom: 1px solid #3a3a3a;
  }

  :deep(.el-drawer__close-btn) {
    color: #cccccc;

    &:hover {
      color: #ffffff;
    }
  }
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 16px 20px;
  background: #2c2c2c;
  border-bottom: 1px solid #3a3a3a;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    flex: 1;
  }

  .header-actions {
    display: flex;
    align-items: center;

    .el-button {
      background: #409eff;
      border-color: #409eff;
      color: #ffffff;
      font-size: 13px;
      padding: 6px 12px;
      border-radius: 4px;

      &:hover {
        background: #337ecc;
        border-color: #337ecc;
      }

      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

.material-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #2c2c2c;
  min-height: 500px;

  .go-media-lib-link {
    color: #409eff;
    font-size: 13px;
    cursor: pointer;
    margin-left: 8px;
    border-radius: 4px;
    padding: 0 4px;
    transition: background 0.2s;

    &:hover {
      background: rgba(64, 158, 255, 0.08);
      text-decoration: underline;
    }
  }
}

.search-bar {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  align-items: center;

  .el-select {
    width: 120px;

    :deep(.el-input__wrapper) {
      background-color: #3a3a3a;
      border: 1px solid #4a4a4a;
      box-shadow: none;
      border-radius: 6px;
      transition: border-color 0.2s;
    }

    :deep(.el-input__inner) {
      color: #ffffff;
      font-size: 13px;
      background: transparent;

      &::placeholder {
        color: #999999;
      }
    }

    :deep(.el-input__suffix-inner) {
      color: #999999;
    }

    :deep(.el-select-dropdown) {
      background: #2c2c2c;
      border: 1px solid #3a3a3a;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
      padding: 4px 0;

      .el-scrollbar {
        --el-scrollbar-opacity: 0.5 !important;
        --el-scrollbar-bg-color: #666666 !important;
        --el-scrollbar-hover-opacity: 0.8 !important;
        --el-scrollbar-hover-bg-color: #888888 !important;
      }
    }

    :deep(.el-select-dropdown__item) {
      color: #cccccc;
      font-size: 13px;
      background: transparent;
      transition: background 0.2s, color 0.2s;

      &.selected,
      &.hover,
      &:hover {
        background: #409eff;
        color: #fff;
      }
    }
  }

  // 日期选择器组件样式
  .el-date-editor {
    background-color: #3a3a3a !important;
    border: 1px solid #4a4a4a !important;

    :deep(.el-select__wrapper) {
      background-color: #3a3a3a !important;
      box-shadow: 0 0 0 1px #4a4a4a inset !important;
      border: none !important;
    }

    :deep(.el-input__wrapper) {
      background-color: #3a3a3a !important;
      border: none !important;
      box-shadow: none !important;
    }

    :deep(.el-input__inner) {
      background-color: transparent !important;
      color: #ffffff !important;

      &::placeholder {
        color: #999999 !important;
      }
    }

    :deep(.el-range-separator) {
      color: #cccccc !important;
      background-color: transparent !important;
    }

    :deep(.el-range-input) {
      background-color: transparent !important;
      color: #ffffff !important;

      &::placeholder {
        color: #999999 !important;
      }
    }

    :deep(.el-input__suffix) {
      background-color: transparent !important;
    }

    :deep(.el-input__suffix-inner) {
      color: #999999 !important;
    }
  }

  .el-input {
    flex: 1;

    :deep(.el-input__wrapper) {
      background-color: #3a3a3a;
      border: 1px solid #4a4a4a;
      box-shadow: none;
      border-radius: 6px;
    }

    :deep(.el-input__inner) {
      color: #ffffff;
      font-size: 13px;

      &::placeholder {
        color: #999999;
      }
    }

    :deep(.el-input__prefix-inner) {
      color: #999999;
    }
  }

  .el-button {
    font-size: 12px;
    height: 32px;
    padding: 6px 12px;
    border-radius: 6px;
    white-space: nowrap;

    &.el-button--primary {
      background-color: #409eff;
      border-color: #409eff;

      &:hover {
        background-color: #337ecc;
        border-color: #337ecc;
      }
    }
  }
}

.media-type-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  padding: 0 2px;
}

.media-tab {
  flex: 1;
  padding: 8px 12px;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #cccccc;
  background-color: #3a3a3a;
  border: 1px solid #4a4a4a;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #4a4a4a;
    border-color: #5a5a5a;
    color: #ffffff;
  }

  &.active {
    background-color: #409eff;
    border-color: #409eff;
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
  }
}

.material-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
  overflow-y: auto;
  padding: 0 4px 16px 0;
  min-height: 300px;

  // Loading状态样式
  :deep(.el-loading-mask) {
    background-color: rgba(44, 44, 44, 0.8);

    .el-loading-spinner {
      .el-loading-text {
        color: #ffffff;
      }

      .circular {
        .path {
          stroke: #409eff;
        }
      }
    }
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.25);
    }
  }
}

.material-item {
  border: 1px solid #3a3a3a;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #3a3a3a;
  position: relative;

  &:hover {
    border-color: #4a4a4a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transform: translateY(-1px);
  }

  &.selected {
    border-color: #409eff;
    box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.3);
  }
}

.material-preview {
  position: relative;
  width: 100%;
  height: 68px;
  background: #2a2a2a;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .no-preview {
    color: #666666;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .duration {
    position: absolute;
    bottom: 3px;
    right: 3px;
    background: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: 1px 3px;
    border-radius: 2px;
    font-size: 9px;
    font-weight: 500;
    line-height: 1.2;
  }

  .selected-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(64, 158, 255, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;

    .check-icon {
      color: #409eff;
      font-size: 14px;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 50%;
      padding: 2px;
    }
  }
}

.material-info {
  padding: 6px 8px;
  background: #3a3a3a;
}

.material-title {
  font-weight: 400;
  color: #ffffff;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 11px;
  line-height: 1.3;
}

.material-meta {
  font-size: 9px;
  color: #999999;
  display: flex;
  justify-content: space-between;
  line-height: 1.2;

  span {
    &:first-child {
      color: #cccccc;
    }
  }
}

.empty-state {
  grid-column: 1 / -1;
  padding: 60px 20px;
  text-align: center;

  :deep(.el-empty) {
    .el-empty__image {
      width: 80px;
      height: 80px;

      svg {
        fill: #666666;
      }
    }

    .el-empty__description {
      color: #999999;
      font-size: 14px;
    }
  }
}

.pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  background: #2c2c2c;
  padding: 12px 0;
  border-top: 1px solid #3a3a3a;

  :deep(.el-pagination) {

    .el-pagination__total,
    .el-pagination__jump {
      color: #cccccc;
      font-size: 12px;
    }

    .btn-prev,
    .btn-next,
    .el-pager li {
      font-size: 12px;
      min-width: 28px;
      height: 28px;
      line-height: 26px;
      background-color: #3a3a3a;
      color: #ffffff;
      border: 1px solid #4a4a4a;
      border-radius: 4px;
      margin: 0 2px;

      &:hover {
        background-color: #4a4a4a;
        border-color: #5a5a5a;
      }

      &.is-active {
        background-color: #409eff;
        border-color: #409eff;
        color: #ffffff;
      }
    }

    .el-pagination__sizes {
      .el-select {
        .el-input {
          :deep(.el-input__wrapper) {
            background-color: #3a3a3a;
            border: 1px solid #4a4a4a;
          }
        }
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #3a3a3a;
  background: #2c2c2c;

  .selected-count {
    color: #cccccc;
    font-size: 13px;
  }

  .el-button {
    padding: 6px 16px;
    font-size: 13px;
    border-radius: 4px;

    &:not(.el-button--primary) {
      background-color: #3a3a3a;
      border-color: #4a4a4a;
      color: #ffffff;

      &:hover {
        background-color: #4a4a4a;
        border-color: #5a5a5a;
      }
    }

    &.el-button--primary {
      background-color: #409eff;
      border-color: #409eff;

      &:hover {
        background-color: #337ecc;
        border-color: #337ecc;
      }

      &:disabled {
        background-color: #53a8ff;
        border-color: #53a8ff;
        opacity: 0.5;
      }
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .material-grid {
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
  }
}

@media (max-width: 768px) {
  .material-grid {
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: 6px;
  }

  .material-preview {
    height: 60px;
  }

  .material-info {
    padding: 4px 6px;
  }

  .material-title {
    font-size: 10px;
  }

  .material-meta {
    font-size: 8px;
  }
}
</style>

<!-- 限制作用域的样式 - 只在当前组件生效 -->
<style lang="scss">
// 只针对当前组件的样式，使用特定的类名前缀
.material-selector-drawer {
  // 重写 Element Plus 的 CSS 变量 - 只在抽屉内生效
  --el-fill-color-blank: #3a3a3a;
  --el-border-color: #4a4a4a;
  --el-border-color-hover: #5a5a5a;
  --el-text-color-regular: #ffffff;
  --el-text-color-placeholder: #999999;
  --el-text-color-secondary: #cccccc;
  --el-bg-color: #3a3a3a;
  --el-disabled-text-color: #666666;
  --el-disabled-border-color: #555555;
  --el-color-primary: #409eff;

  // 日期选择器样式
  .material-selector-date {
    --el-fill-color-blank: #3a3a3a !important;
    --el-border-color: #4a4a4a !important;

    .el-input__wrapper {
      background-color: #3a3a3a !important;
      border: 1px solid #4a4a4a !important;
      box-shadow: none !important;
    }

    .el-input__inner {
      background-color: transparent !important;
      color: #ffffff !important;

      &::placeholder {
        color: #999999 !important;
      }
    }

    .el-range-separator {
      color: #cccccc !important;
      background-color: transparent !important;
    }

    .el-range-input {
      background-color: transparent !important;
      color: #ffffff !important;

      &::placeholder {
        color: #999999 !important;
      }
    }

    .el-input__suffix-inner {
      color: #999999 !important;
    }
  }
}

// 使用属性选择器精确匹配当前组件的选择框
.material-selector-drawer .material-selector-select {
  --el-select-border-color-hover: #5a5a5a !important;
  --el-select-disabled-color: #666666 !important;
  --el-select-disabled-border: #555555 !important;
  --el-select-close-hover-color: #cccccc !important;
  --el-select-input-color: #999999 !important;
  --el-select-multiple-input-color: #ffffff !important;
  --el-select-input-focus-border-color: #409eff !important;

  .el-input__wrapper {
    background-color: #3a3a3a !important;
    border: 1px solid #4a4a4a !important;
    box-shadow: none !important;

    &:hover {
      border-color: #5a5a5a !important;
    }

    &.is-focus {
      border-color: #409eff !important;
    }
  }

  .el-input__inner {
    background-color: transparent !important;
    color: #ffffff !important;

    &::placeholder {
      color: #999999 !important;
    }
  }

  .el-input__suffix-inner {
    color: #999999 !important;
  }

  .el-select__caret {
    color: #999999 !important;
  }
}

// 使用属性选择器精确匹配当前组件的输入框
.material-selector-drawer .material-selector-input {
  .el-input__wrapper {
    background-color: #3a3a3a !important;
    border: 1px solid #4a4a4a !important;
    box-shadow: none !important;
  }

  .el-input__inner {
    background-color: transparent !important;
    color: #ffffff !important;

    &::placeholder {
      color: #999999 !important;
    }
  }

  .el-input__prefix-inner {
    color: #999999 !important;
  }
}

// 下拉框弹窗样式 - 使用更精确的选择器
.el-select-dropdown[aria-describedby] {
  &[class*="material-selector"] {
    background: #2c2c2c !important;
    border: 1px solid #3a3a3a !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25) !important;

    .el-select-dropdown__item {
      background: transparent !important;
      color: #cccccc !important;

      &:hover,
      &.hover {
        background: #409eff !important;
        color: #ffffff !important;
      }

      &.selected {
        background: #409eff !important;
        color: #ffffff !important;
      }
    }

    .el-scrollbar {
      --el-scrollbar-opacity: 0.5 !important;
      --el-scrollbar-bg-color: #666666 !important;
      --el-scrollbar-hover-opacity: 0.8 !important;
      --el-scrollbar-hover-bg-color: #888888 !important;
    }
  }
}

// 日期选择器弹窗样式 - 使用更精确的选择器
.el-popper.el-picker__popper {
  &[data-popper-reference-hidden="false"] {
    background: #2c2c2c !important;
    border: 1px solid #3a3a3a !important;
    color: #fff !important;

    .el-picker-panel {
      background: #2c2c2c !important;
      border: none !important;
      color: #fff !important;

      .el-picker-panel__body-wrapper {
        background: #2c2c2c !important;
      }

      .el-date-range-picker__header {
        background: #2c2c2c !important;
        color: #fff !important;
      }

      .el-picker-panel__header {
        background: #2c2c2c !important;
        color: #fff !important;
      }

      .el-date-table th,
      .el-date-table td {
        color: #cccccc !important;
      }

      .el-date-table td.available {
        color: #fff !important;

        &:hover {
          background: #409eff !important;
          color: #fff !important;
        }
      }

      .el-date-table td.today {
        color: #409eff !important;
      }

      .el-picker-panel__footer {
        background: #2c2c2c !important;
        border-top: 1px solid #3a3a3a !important;
      }

      .el-button {
        background: #3a3a3a !important;
        color: #fff !important;
        border: 1px solid #4a4a4a !important;

        &:hover {
          background: #409eff !important;
          color: #fff !important;
          border-color: #409eff !important;
        }
      }

      .el-scrollbar {
        --el-scrollbar-opacity: 0.5 !important;
        --el-scrollbar-bg-color: #666666 !important;
        --el-scrollbar-hover-opacity: 0.8 !important;
        --el-scrollbar-hover-bg-color: #888888 !important;
      }
    }
  }
}
</style>