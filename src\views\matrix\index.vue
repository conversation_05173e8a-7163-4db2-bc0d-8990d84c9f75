<template>
  <div class="app-container">
    <header>
      <h1>山东数智宝AI矩阵网站应用功能介绍</h1>
    </header>

    <main>
      <section>
        <h2>一、应用核心功能</h2>
        <p>
          本应用是一款专为企业内部宣传部门设计的抖音账号内容管理工具。它旨在简化和高效化多账号的内容创作与发布流程。核心功能包括：
        </p>
        <ul>
          <li>
            <strong>素材管理：</strong>
            在电脑端统一管理视频、图片、文案等创作素材，方便团队协作与内容复用。
          </li>
          <li>
            <strong>内容预编：</strong>
            在网站上提前编辑和排版待发布的内容，为多个抖音账号准备发布计划。
          </li>
          <li>
            <strong>扫码发布：</strong>
            内容编辑完成后，系统会生成一个专属二维码。运营人员使用手机抖音App扫描该二维码，即可将预设内容一键发布或跳转至抖音编辑页面进行最后调整，极大提升发布效率。
          </li>
        </ul>
      </section>

      <section>
        <h2>二、适用人群</h2>
        <p>本应用主要服务于企业内部的<strong>宣传、市场或新媒体运营团队</strong>，帮助他们更便捷地管理公司的官方抖音账号矩阵。</p>
      </section>

      <section>
        <h2>三、预期接入开放平台的功能 & 使用场景</h2>
        <p>为实现上述核心功能，应用计划接入抖音开放平台的以下能力：</p>

        <div class="feature-card">
          <h3>1. 抖音授权</h3>
          <p>
            <strong>功能描述：</strong>
            通过接入抖音授权能力，获取用户的接口调用凭证（access_token），以安全合规的方式管理用户账号。
          </p>
          <p><strong>使用场景：</strong></p>
          <ul>
            <li>用户首次使用时，需通过抖音授权登录，将抖音账号与本应用绑定。</li>
            <li>应用将支持增量授权，在需要新权限时引导用户进行授权，提升用户体验。</li>
            <li>通过授权动态续期，确保用户登录状态的持久性，避免频繁要求用户重新授权。</li>
          </ul>
        </div>

        <div class="feature-card">
          <h3>2. 投稿能力</h3>
          <p>
            <strong>功能描述：</strong>
            允许用户从本应用向抖音发布内容，是实现“扫码发布”功能的核心。
          </p>
          <p><strong>使用场景：</strong></p>
          <ul>
            <li>
              运营人员在电脑端完成内容编辑后，通过手机扫码，应用将调用发布能力，将内容推送至手机抖音，用户可选择直接发布或进入编辑页。
            </li>
            <li>此场景将利用H5或APP的发布能力，并获取openTicket作为发布凭证，确保流程的顺畅与安全。</li>
          </ul>
        </div>

        <div class="feature-card">
          <h3>3. 经营任务系统能力</h3>
          <p>
            <strong>功能描述：</strong>
            在取得用户授权的前提下，获取用户特定事件（如关注、点赞）的完成状态信息，用于开展激励活动。
          </p>
          <p><strong>使用场景：</strong></p>
          <ul>
            <li>
              可发起“关注官方账号”的推广活动。开发者设定任务后，可以查询到哪些用户在规定时间内完成了关注操作。
            </li>
            <li>对于完成任务的用户，企业可通过应用内系统给予奖励，有效提升用户互动和粉丝增长。</li>
          </ul>
        </div>
      </section>
    </main>
  </div>
</template>

<style scoped>
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  line-height: 1.7;
  color: #333;
  background: linear-gradient(to right top, #f2f6f8, #e9f1f6, #e0ecf4, #d8e7f2, #d0e2f0);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-y: auto; 
}

.container {
  max-width: 960px;
  margin: 40px auto 80px;
  padding: 40px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  animation: fadeIn 0.5s ease-out;
}

header {
  text-align: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 20px;
  margin-bottom: 40px;
}

header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, #42b983, #2c3e50);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

h2 {
  color: #34495e;
  font-size: 1.8rem;
  margin-top: 40px;
  padding-bottom: 10px;
  border-bottom: 3px solid;
  border-image: linear-gradient(to right, #42b983, #86d993) 1;
}

h3 {
  color: #2c3e50;
  font-weight: 600;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

section {
  margin-bottom: 40px;
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
}

section:nth-of-type(1) {
  animation-delay: 0.1s;
}
section:nth-of-type(2) {
  animation-delay: 0.2s;
}
section:nth-of-type(3) {
  animation-delay: 0.3s;
}

ul {
  padding-left: 25px;
  list-style-type: '✨';
}

li {
  margin-bottom: 12px;
  padding-left: 10px;
}

p {
  margin-bottom: 15px;
  color: #555;
}

.feature-card {
  background-color: #fff;
  border: 1px solid #eef2f5;
  border-radius: 12px;
  padding: 25px;
  margin-top: 20px;
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  border-top: 4px solid #42b983;
  opacity: 0;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
}

.feature-card:nth-of-type(1) {
  animation: slideInUp 0.5s ease-out 0.4s forwards;
}
.feature-card:nth-of-type(2) {
  animation: slideInUp 0.5s ease-out 0.5s forwards;
}
.feature-card:nth-of-type(3) {
  animation: slideInUp 0.5s ease-out 0.6s forwards;
}

strong {
  color: #34495e;
  font-weight: 600;
}
</style>
