import request from '@/utils/request'

// 查询音频列表
export function listAudio(query) {
  return request({
    url: '/platform/audio/list',
    method: 'get',
    params: query
  })
}

// 查询音频详细
export function getAudio(audioId) {
  return request({
    url: '/platform/audio/' + audioId,
    method: 'get'
  })
}

// 新增音频
export function addAudio(data) {
  return request({
    url: '/platform/audio',
    method: 'post',
    data: data
  })
}

// 修改音频
export function updateAudio(data) {
  return request({
    url: '/platform/audio',
    method: 'put',
    data: data
  })
}

// 删除音频
export function delAudio(audioId) {
  return request({
    url: '/platform/audio/' + audioId,
    method: 'delete'
  })
}

//上传音频
export function uploadAudio(data, param, signal) {
  return request({
    url: '/platform/audio/uploadAudio',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
      'repeatSubmit': false
    },
    // headers: { 'content-type': 'application/x-www-form-urlencoded'},
    data: data,
    signal: signal,
    onUploadProgress: (event) => {
      param.file.percent = event.loaded / event.total * 100
      console.log("percent", param)
      param.onProgress(param.file)
    },

  })
}


//下载音频
export function downloadAudio(id) {
  return request({
    url: '/platform/audio/downloadAudioId',
    method: 'get',
    headers: {
      'Content-Type': 'application/octet-stream',
      'repeatSubmit': false
    },
    params: {
      audioId: id
    },
    responseType: 'blob'
  })
}

//根据声音Id下载参考音频
export function downloadSoundRefBySoundId(id) {
  return request({
    url: '/platform/sound/downloadSoundRefBySoundId',
    method: 'get',
    headers: {
      'Content-Type': 'application/octet-stream',
      'repeatSubmit': false
    },
    params: {
      soundId: id
    },
    responseType: 'blob'
  })
}


// 根据音频路径获取可访问的URL
export function getUrlByAudioPath(audioPath) {
  return request({
    url: '/platform/audio/getUrlByAudioPath',
    method: 'get',
    params: {
      audioPath: audioPath
    }
  })
}

// 根据分类ID查询文本转音频数据
export function audioAndText(params) {
  return request({
    url: '/platform/audio/audioAndText',
    method: 'get',
    params: params
  })
}

// 根据音频地址生成临时凭证
export function audioDetail(audioPath) {
  return request({
    url: '/platform/audio/audioDetail',
    method: 'get',
    params: { audioPath }
  })
}