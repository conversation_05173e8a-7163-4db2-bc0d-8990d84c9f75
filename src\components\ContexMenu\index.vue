<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
const props = defineProps<{
    menu: { label: string }[]
}>()
const emit = defineEmits(["select"])
const x = ref(0)
const y = ref(0)
const visible = ref(false)
const containerRef = ref<HTMLElement | null>(null)
function openMenu(e: MouseEvent) {
    e.preventDefault()
    e.stopPropagation()
    visible.value = true
    x.value = e.clientX
    y.value = e.clientY
}
function closeMenu() {
    visible.value = false
}
onMounted(() => {
    containerRef.value!.addEventListener('contextmenu', openMenu)
    window.addEventListener('click', closeMenu)
    window.addEventListener('contextmenu', closeMenu)
})
onUnmounted(() => {
    containerRef.value!.removeEventListener('contextmenu', openMenu)
    window.removeEventListener('click', closeMenu)
    window.removeEventListener('contextmenu', closeMenu)
})
function handleClick(item: any) {
    visible.value = false
    emit('select', item)
}
function handleBeforeEnter(el: HTMLElement) {
    el.style.height = '0'
}
function handleEnter(el: HTMLElement) {
    el.style.height = 'auto'
    const h = el.clientHeight
    el.style.height = '0'
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            el.style.height = h + 'px'
            el.style.transition = '0.5s'
        })
    })
}
function handleAfterEnter(el: HTMLElement) {
    el.style.transition = 'none'
}
</script>
<template>
    <div class="container" ref="containerRef">
        <slot></slot>
        <Teleport to="body" @beforEnter="handleBeforeEnter" @enter="handleEnter" @afterEnter="handleAfterEnter">
            <div class="context-menu" v-if="visible" :style="{
                left: x + 'px',
                top: y + 'px'
            }">
                <div class="menu-list">
                    <div class="menu-item" v-for="(item, i) in menu" :key="item.label" @click="handleClick(item)">
                        {{ item.label }}
                    </div>
                </div>
            </div>
        </Teleport>
    </div>
</template>
<style scoped></style>