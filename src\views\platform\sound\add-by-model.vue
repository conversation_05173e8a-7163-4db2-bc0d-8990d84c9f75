<template>
    <el-dialog center @closed="resetForm" :style="{ top: '110px' }">
        <el-form :model="form" :rules="rules" ref="soundRef">
            <el-form-item label="声音名称" label-width="100px" prop="soundName">
                <el-input v-model="form.soundName" placeholder="请输入声音名称" />
            </el-form-item>
            <el-form-item label="GPT模型" label-width="100px" prop="gptModel">
                <el-row align="middle">
                    <el-col :span="6">
                        <el-upload 
                            ref="gptUploadRef"
                            class="upload-demo" 
                            :show-file-list="false" 
                            :limit="1" 
                            :on-exceed="handleExceed('gptModel')"
                            :auto-upload="false" 
                            :on-change="handleChange('gptModel')"
                        >
                            <el-button type="primary">请选择文件</el-button>
                        </el-upload>
                    </el-col>
                    <el-col :span="16">
                        <div style="width: 500px;">
                            <el-tag type="primary">{{ form.gptModel ? form.gptModel.name : '请上传模型' }}</el-tag>
                        </div>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="Sovits模型" label-width="100px" prop="sovitsModel">
                <el-row align="middle">
                    <el-col :span="6">
                        <el-upload 
                            ref="sovitsUploadRef"
                            class="upload-demo" 
                            :show-file-list="false" 
                            :limit="1" 
                            :on-exceed="handleExceed('sovitsModel')"
                            :auto-upload="false" 
                            :on-change="handleChange('sovitsModel')"
                        >
                            <el-button type="primary">请选择文件</el-button>
                        </el-upload>
                    </el-col>
                    <el-col :span="16">
                        <div style="width: 500px;">
                            <el-tag type="primary">{{ form.sovitsModel ? form.sovitsModel.name : '请上传模型' }}</el-tag>
                        </div>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="参考音频" label-width="100px" prop="refAudio">
                <el-row align="middle">
                    <el-col :span="12">
                        <el-upload 
                            ref="refAudioUploadRef"
                            class="upload-demo" 
                            :show-file-list="false" 
                            :limit="1" 
                            :on-exceed="handleExceed('refAudio')"
                            :auto-upload="false" 
                            :on-change="handleChange('refAudio')"
                        >
                            <el-button type="primary">请选择文件</el-button>
                        </el-upload>
                    </el-col>
                    <audio v-if="refAudioURL" :src="refAudioURL" controls style="height: 33px;margin-top: 20px;margin-left: -10px"></audio>
                </el-row>
            </el-form-item>
            <el-form-item label="参考文本" label-width="100px" prop="refText">
                <el-input type="textarea" v-model="form.refText" placeholder="请确保参考文本与参考音频内容完全一致，以免影响文案转音频的效果。"></el-input>
            </el-form-item>
            <el-form-item label="部门列表" prop="deptId" v-hasPermi="['platform:sound:editDeptId']" label-width="100px">
                <el-tree-select 
                    v-model="form.deptId"  
                    :props="{value: 'deptId',label: 'deptName', children: 'children' }" 
                    :data="deptOptions" 
                    check-strictly 
                    placeholder="请选择部门列表"/>
            </el-form-item>
            <el-form-item label="数据范围" prop="soundFiltration" v-hasPermi="['platform:sound:editSoundFiltration']" label-width="100px">
                <el-select v-model="form.soundFiltration" placeholder="请选择数据范围">
                    <el-option v-for="dict in platform_sound_sound_filtration" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label-width="100px">
                <div v-if="uploadProgress.gptModel > 0" class="progress-item">
                    <span class="progress-label">GPT模型:</span>
                    <el-progress :percentage="uploadProgress.gptModel" :color="progressColor(uploadProgress.gptModel)" />
                </div>
                <div v-if="uploadProgress.sovitsModel > 0" class="progress-item">
                    <span class="progress-label">Sovits模型:</span>
                    <el-progress :percentage="uploadProgress.sovitsModel" :color="progressColor(uploadProgress.sovitsModel)" />
                </div>
                <div v-if="uploadProgress.refAudio > 0" class="progress-item">
                    <span class="progress-label">参考音频:</span>
                    <el-progress :percentage="uploadProgress.refAudio" :color="progressColor(uploadProgress.refAudio)" />
                </div>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="success" @click="submit" :disabled="isUploading">
                {{ isUploading ? '上传中...' : '提交' }}
            </el-button>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { initSoundUpload, uploadSoundChunk, completeSoundUpload } from '@/api/platform/sound'
import { fetchDeptOptions } from '@/utils/deptUtils'
import modal from '@/plugins/modal'

const { proxy } = getCurrentInstance();
const { platform_sound_sound_filtration } = proxy.useDict("platform_sound_sound_filtration");

// 表单数据
const form = ref({
    soundName: '',
    gptModel: null,
    sovitsModel: null,
    refAudio: null,
    refText: '',
    deptId: null,
    soundFiltration: null
})

// 表单验证规则
const rules = ref({
    soundName: [{ required: true, message: '请输入声音名称', trigger: 'blur' }],
    gptModel: [
        { required: true, message: '请上传GPT模型', trigger: 'blur' },
        { validator: (_, value, callback) => {
            if (value && !value.name.toLowerCase().endsWith('.ckpt')) {
                callback(new Error('GPT模型必须是.ckpt格式'));
            } else {
                callback();
            }
        }, trigger: 'change' }
    ],
    sovitsModel: [
        { required: true, message: '请上传Sovits模型', trigger: 'blur' },
        { validator: (_, value, callback) => {
            if (value && !value.name.toLowerCase().endsWith('.pth')) {
                callback(new Error('Sovits模型必须是.pth格式'));
            } else {
                callback();
            }
        }, trigger: 'change' }
    ],
    refAudio: [
        { required: true, message: '请上传参考音频', trigger: 'blur' },
        { validator: (_, value, callback) => {
            if (value && !/\.wav$|\.mp3$|\.flac$|\.m4a$/i.test(value.name)) {
                callback(new Error('参考音频必须是.wav/.mp3/.flac/.m4a格式'));
            } else {
                callback();
            }
        }, trigger: 'change' }
    ],
    refText: [{ required: true, message: '请输入参考文本', trigger: 'blur' }],
    deptId: [{ required: true, message: '请选择部门', trigger: 'blur' }],
    soundFiltration: [{ required: true, message: '请选择数据范围', trigger: 'blur' }]
})

// 状态管理
const soundRef = ref(null)
const gptUploadRef = ref(null)
const sovitsUploadRef = ref(null)
const refAudioUploadRef = ref(null)
const deptOptions = ref([])

// 上传状态
const isUploading = ref(false)

// 上传进度
const uploadProgress = ref({
    gptModel: 0,
    sovitsModel: 0,
    refAudio: 0
})

// 参考音频URL
const refAudioURL = computed(() => form.value.refAudio ? URL.createObjectURL(form.value.refAudio) : null)

// 重置表单
const resetForm = () => {
    form.value = {
        soundName: '',
        gptModel: null,
        sovitsModel: null,
        refAudio: null,
        refText: '',
        deptId: null,
        soundFiltration: null
    }
    refAudioURL.value = null
    uploadProgress.value = { gptModel: 0, sovitsModel: 0, refAudio: 0 }
    isUploading.value = false
    
    // 清理上传组件
    gptUploadRef.value?.clearFiles()
    sovitsUploadRef.value?.clearFiles()
    refAudioUploadRef.value?.clearFiles()
}

// 处理文件超出限制
const handleExceed = (field) => (files) => {
    const uploadRef = getUploadRef(field)
    uploadRef.value?.clearFiles()
    
    const file = files[0]
    file.uid = Date.now()
    uploadRef.value?.handleStart(file)
    
    form.value[field] = file
}

// 处理文件变化
const handleChange = (field) => (file) => {
    if (file.raw) {
        form.value[field] = file.raw
        if (field === 'refAudio') {
            refAudioURL.value = URL.createObjectURL(file.raw)
        }
    }
}

// 获取上传组件引用
const getUploadRef = (field) => {
    switch(field) {
        case 'gptModel': return gptUploadRef
        case 'sovitsModel': return sovitsUploadRef
        case 'refAudio': return refAudioUploadRef
        default: throw new Error('无效的字段名')
    }
}

// 进度条颜色动态变化
const progressColor = (percentage) => {
    if (percentage < 30) return '#909399'
    if (percentage < 70) return '#e6a23c'
    return '#67c23a'
}

// 分片上传方法
const submit = async () => {
    if (isUploading.value) return // 防止重复提交
    
    if (!soundRef.value) return
    soundRef.value.validate(async valid => {
        if (valid) {
            try {
                isUploading.value = true
                
                // 1. 初始化上传会话
                const initData = {
                    soundName: form.value.soundName,
                    gptFileName: form.value.gptModel?.name || '',
                    sovitsFileName: form.value.sovitsModel?.name || '',
                    refAudioFileName: form.value.refAudio?.name || '',
                    gptFileSize: form.value.gptModel?.size || 0,
                    sovitsFileSize: form.value.sovitsModel?.size || 0,
                    refAudioFileSize: form.value.refAudio?.size || 0,
                    refText: form.value.refText,
                    deptId: form.value.deptId,
                    soundFiltration: form.value.soundFiltration
                }
                
                const { data: initResult } = await initSoundUpload(initData)
                const { uploadId, gptFilePath, sovitsFilePath, refAudioFilePath } = initResult
                
                // 2. 上传所有文件的分片
                await Promise.all([
                    uploadChunks('gptModel', form.value.gptModel, uploadId, gptFilePath, 'gpt'),
                    uploadChunks('sovitsModel', form.value.sovitsModel, uploadId, sovitsFilePath, 'sovits'),
                    uploadChunks('refAudio', form.value.refAudio, uploadId, refAudioFilePath, 'refAudio') 
                ])
                
                // 3. 完成上传，合并分片
                const completeData = {
                    uploadId,
                    gptFilePath,
                    sovitsFilePath,
                    refAudioFilePath,
                    soundName: form.value.soundName,
                    refText: form.value.refText,
                    deptId: form.value.deptId,
                    soundFiltration: form.value.soundFiltration
                }
                await completeSoundUpload(completeData)
                
                modal.msgSuccess('上传成功')
                resetForm()
                emit('uploadByModelSuccess')
            } catch (error) {
                console.error('上传失败:', error)
                modal.msgError('上传失败，请检查文件格式或网络')
            } finally {
                isUploading.value = false
            }
        }
    })
}

// 上传单个文件的所有分片
const uploadChunks = async (field, file, uploadId, filePath, fileType) => {
    if (!file) return
    
    const CHUNK_SIZE = 5 * 1024 * 1024 // 5MB
    const fileSize = file.size
    const chunkCount = Math.ceil(fileSize / CHUNK_SIZE)

    for (let i = 0; i < chunkCount; i++) {
        const start = i * CHUNK_SIZE
        const end = Math.min(start + CHUNK_SIZE, fileSize)
        const chunkBlob = file.slice(start, end)
        
        const formData = new FormData()
        formData.append('uploadId', uploadId)
        formData.append('fileType', fileType)
        formData.append('filePath', filePath)
        formData.append('chunkIndex', i)
        formData.append('chunk', chunkBlob)

        await uploadSoundChunk(formData)
        
        // 更新进度
        uploadProgress.value[field] = Math.round(((i + 1) / chunkCount) * 100)
    }
}

// 获取部门列表和字典数据
onMounted(async () => {
    try {
        deptOptions.value = await fetchDeptOptions()
    } catch (error) {
        console.error('获取初始化数据失败:', error)
    }
})

// 定义触发事件
const emit = defineEmits(['uploadByModelSuccess'])
</script>

<style scoped>
.progress-item {
    margin-bottom: 10px;
}

.progress-label {
    display: inline-block;
    width: 80px;
    margin-right: 10px;
}
</style>