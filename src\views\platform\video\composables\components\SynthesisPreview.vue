<template>
  <div class="preview-section">
    <h4 class="section-title">合成预览</h4>
    <div class="preview-grid">
      <div class="preview-item">
        <div class="preview-label">参与数字人</div>
        <div class="preview-value">{{ humanCount }} 个</div>
      </div>
      <div class="preview-item">
        <div class="preview-label">对话内容</div>
        <div class="preview-value">{{ dialogueCount }} 条</div>
      </div>
      <div class="preview-item">
        <div class="preview-label">合成版本</div>
        <div class="preview-value">{{ getVersionName(version) }}</div>
      </div>
      <div class="preview-item">
        <div class="preview-label">预计时长</div>
        <div class="preview-value">{{ estimatedDuration }} 秒</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  digitalHumans: {
    type: Array,
    default: () => []
  },
  dialogueContent: {
    type: Array,
    default: () => []
  },
  version: {
    type: String,
    default: ''
  }
})

const humanCount = computed(() => props.digitalHumans.length)
const dialogueCount = computed(() => props.dialogueContent.length)

const estimatedDuration = computed(() => {
  const avgWordsPerSecond = 3
  const totalWords = props.dialogueContent.reduce((sum, dialogue) => {
    return sum + (dialogue.text ? dialogue.text.length : 0)
  }, 0)
  return Math.max(10, Math.ceil(totalWords / avgWordsPerSecond))
})

const getVersionName = (version) => {
  const versionMap = {
    'M': 'M版 - 标准版',
    'V': 'V版 - 增强版',
    'H': 'H版 - 高清版'
  }
  return versionMap[version] || '未选择'
}
</script>

<style scoped>
.preview-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.preview-item {
  padding: 16px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
  }
}

.preview-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  font-weight: 500;
}

.preview-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
</style>
