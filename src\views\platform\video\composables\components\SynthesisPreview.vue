<template>
  <div class="preview-section">
    <h4 class="section-title">合成预览</h4>
    <div class="preview-grid">
      <div class="preview-item">
        <div class="preview-label">参与数字人</div>
        <div class="preview-value">{{ humanCount }} 个</div>
      </div>
      <div class="preview-item">
        <div class="preview-label">对话内容</div>
        <div class="preview-value">{{ dialogueCount }} 条</div>
      </div>
      <div class="preview-item">
        <div class="preview-label">合成版本</div>
        <div class="preview-value">{{ getVersionName(version) }}</div>
      </div>
      <div class="preview-item">
        <div class="preview-label">预计时长</div>
        <div class="preview-value">{{ estimatedDuration }} 秒</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'

const props = defineProps({
  digitalHumans: {
    type: Array,
    default: () => []
  },
  dialogueContent: {
    type: Array,
    default: () => []
  },
  version: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['preview-updated'])

const humanCount = computed(() => props.digitalHumans.length)
const dialogueCount = computed(() => props.dialogueContent.length)

// 更精确的时长计算
const estimatedDuration = computed(() => {
  if (!props.dialogueContent.length) return 0

  // 基于不同语言和内容类型的语速
  const avgWordsPerSecond = 3.5 // 中文平均语速
  const pauseBetweenDialogues = 0.5 // 对话间停顿

  const totalWords = props.dialogueContent.reduce((sum, dialogue) => {
    const text = dialogue.text || dialogue.content || ''
    return sum + text.length
  }, 0)

  const baseDuration = totalWords / avgWordsPerSecond
  const pauseDuration = (props.dialogueContent.length - 1) * pauseBetweenDialogues

  return Math.max(10, Math.ceil(baseDuration + pauseDuration))
})

const getVersionName = (version) => {
  const versionMap = {
    'M': 'M版 - 标准版',
    'V': 'V版 - 增强版',
    'H': 'H版 - 高清版'
  }
  return versionMap[version] || '未选择'
}

// 监听数据变化，通知父组件
watch([humanCount, dialogueCount, estimatedDuration], () => {
  emit('preview-updated', {
    humanCount: humanCount.value,
    dialogueCount: dialogueCount.value,
    estimatedDuration: estimatedDuration.value,
    version: props.version
  })
}, { immediate: true })
</script>

<style lang="scss" scoped>
.preview-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
  }
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.preview-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
  }
}

.preview-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.preview-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

@media (max-width: 768px) {
  .preview-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
