<template>
  <div class="notes-search-container">
    <!-- 搜索按钮 -->
    <div v-if="!showSearchBox" class="search-btn-wrapper">
      <el-button
        @click="toggleSearch"
        type="primary"
        :icon="Search"
        circle
        size="small"
        class="search-toggle-btn"
        title="搜索笔记内容"
      />
      <!-- 粒子效果容器 -->
      <div class="particles-container">
        <div
          v-for="i in 8"
          :key="i"
          class="particle"
          :style="{
            '--delay': `${i * 0.15}s`,
            '--angle': `${i * 45}deg`
          }"
        ></div>
      </div>
      <!-- 脉冲环效果 -->
      <div class="pulse-ring"></div>
      <div class="pulse-ring pulse-ring-delayed"></div>
    </div>

    <!-- 搜索框区域 -->
    <div v-if="showSearchBox" class="search-box-container">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索笔记内容..."
        size="small"
        class="search-input"
        clearable
        @input="performSearch"
        @keyup.enter="performSearch"
        @clear="clearSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>

      <!-- 搜索导航 -->
      <div v-if="searchResults.length > 0" class="search-navigation">
        <span class="search-count">{{ currentSearchIndex + 1 }}/{{ searchResults.length }}</span>
        <div class="search-nav-buttons">
          <el-button
            @click="previousResult"
            :disabled="currentSearchIndex <= 0"
            size="small"
            :icon="ArrowUp"
            circle
          />
          <el-button
            @click="nextResult"
            :disabled="currentSearchIndex >= searchResults.length - 1"
            size="small"
            :icon="ArrowDown"
            circle
          />
        </div>
      </div>

      <!-- 关闭按钮 -->
      <el-button
        @click="toggleSearch"
        type="text"
        size="small"
        :icon="Close"
        class="close-search-btn"
        title="关闭搜索"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Search, ArrowUp, ArrowDown, Close } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  notes: {
    type: Array,
    required: true,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'search-result-change',
  'scroll-to-note',
  'search-keyword-change'
])

// 搜索相关状态
const showSearchBox = ref(false) // 控制搜索框显示
const searchKeyword = ref('') // 搜索关键词
const searchResults = ref([]) // 搜索结果
const currentSearchIndex = ref(-1) // 当前搜索结果索引

// 搜索相关方法
const toggleSearch = () => {
  showSearchBox.value = !showSearchBox.value
  if (!showSearchBox.value) {
    clearSearch()
  }
}

const performSearch = () => {
  if (!searchKeyword.value.trim()) {
    clearSearch()
    return
  }

  const keyword = searchKeyword.value.toLowerCase()
  const results = []

  props.notes.forEach((note, index) => {
    if (note.notes.toLowerCase().includes(keyword)) {
      results.push({
        noteIndex: index,
        note: note
      })
    }
  })

  searchResults.value = results
  currentSearchIndex.value = results.length > 0 ? 0 : -1

  // 发送搜索结果变化事件
  emit('search-result-change', {
    results: results,
    currentIndex: currentSearchIndex.value,
    keyword: searchKeyword.value
  })

  // 如果有搜索结果，滚动到第一个结果
  if (results.length > 0) {
    scrollToSearchResult(0)
  }
}

const clearSearch = () => {
  searchKeyword.value = ''
  searchResults.value = []
  currentSearchIndex.value = -1

  // 发送搜索结果变化事件
  emit('search-result-change', {
    results: [],
    currentIndex: -1,
    keyword: ''
  })
}

const scrollToSearchResult = (index) => {
  if (index >= 0 && index < searchResults.value.length) {
    const noteIndex = searchResults.value[index].noteIndex
    emit('scroll-to-note', noteIndex)
  }
}

const previousResult = () => {
  if (currentSearchIndex.value > 0) {
    currentSearchIndex.value--
    scrollToSearchResult(currentSearchIndex.value)

    // 更新搜索结果状态
    emit('search-result-change', {
      results: searchResults.value,
      currentIndex: currentSearchIndex.value,
      keyword: searchKeyword.value
    })
  }
}

const nextResult = () => {
  if (currentSearchIndex.value < searchResults.value.length - 1) {
    currentSearchIndex.value++
    scrollToSearchResult(currentSearchIndex.value)

    // 更新搜索结果状态
    emit('search-result-change', {
      results: searchResults.value,
      currentIndex: currentSearchIndex.value,
      keyword: searchKeyword.value
    })
  }
}

// 监听搜索关键词变化
watch(searchKeyword, (newKeyword) => {
  emit('search-keyword-change', newKeyword)
})

// 暴露方法供外部调用
const setSearchKeyword = (keyword) => {
  showSearchBox.value = true
  searchKeyword.value = keyword
  performSearch()
}

// 暴露给父组件的方法
defineExpose({
  setSearchKeyword,
  clearSearch,
  toggleSearch
})
</script>

<style scoped>
/* 搜索功能样式 */
.notes-search-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.1);
  margin-bottom: 16px;
}

.search-btn-wrapper {
  position: relative;
  display: inline-block;
}

.search-toggle-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  overflow: hidden;
  width: 32px;
  height: 32px;
}

.search-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

/* 粒子效果 */
.particles-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 3px;
  height: 3px;
  background: linear-gradient(45deg, #8b5cf6, #3b82f6);
  border-radius: 50%;
  transform-origin: 0 20px;
  animation: particleOrbit 3s linear infinite;
  animation-delay: var(--delay);
  opacity: 0.7;
}

@keyframes particleOrbit {
  0% {
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(-20px) scale(0.5);
    opacity: 0;
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(calc(var(--angle) + 180deg)) translateY(-20px) scale(1);
  }
  100% {
    transform: translate(-50%, -50%) rotate(calc(var(--angle) + 360deg)) translateY(-20px) scale(0.5);
    opacity: 0;
  }
}

/* 脉冲环效果 */
.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-radius: 50%;
  animation: pulseRing 2s ease-out infinite;
}

.pulse-ring-delayed {
  animation-delay: 1s;
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

.search-box-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  animation: searchBoxSlideIn 0.3s ease-out;
}

@keyframes searchBoxSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.search-input {
  flex: 1;
  max-width: 200px;
}

.search-input :deep(.el-input__wrapper) {
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 8px;
  box-shadow: none;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
}

.search-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(139, 92, 246, 0.4);
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
}

.search-input :deep(.el-input__inner) {
  font-size: 13px;
  color: #374151;
}

.search-navigation {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0 6px;
  border-left: 1px solid rgba(139, 92, 246, 0.2);
}

.search-count {
  font-size: 11px;
  color: #6b7280;
  white-space: nowrap;
  min-width: 35px;
  text-align: center;
}

.search-nav-buttons {
  display: flex;
  gap: 2px;
}

.search-nav-buttons .el-button {
  padding: 2px;
  width: 24px;
  height: 24px;
  border: 1px solid rgba(139, 92, 246, 0.2);
  background: rgba(255, 255, 255, 0.8);
  color: #8b5cf6;
  transition: all 0.2s ease;
}

.search-nav-buttons .el-button:hover:not(.is-disabled) {
  background: rgba(139, 92, 246, 0.1);
  border-color: #8b5cf6;
}

.search-nav-buttons .el-button.is-disabled {
  opacity: 0.4;
}

.close-search-btn {
  color: #6b7280;
  padding: 4px;
  transition: all 0.2s ease;
}

.close-search-btn:hover {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}
</style>
