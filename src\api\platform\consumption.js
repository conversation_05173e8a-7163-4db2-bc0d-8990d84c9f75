import request from '@/utils/request'

// 查询用户算力点数变化记录列表
export function listConsumption(query) {
  return request({
    url: '/platform/consumption/list',
    method: 'get',
    params: query
  })
}

// 查询用户算力点数变化记录详细
export function getConsumption(consumptionId) {
  return request({
    url: '/platform/consumption/' + consumptionId,
    method: 'get'
  })
}

// 新增用户算力点数变化记录
export function addConsumption(data) {
  return request({
    url: '/platform/consumption',
    method: 'post',
    data: data
  })
}

// 修改用户算力点数变化记录
export function updateConsumption(data) {
  return request({
    url: '/platform/consumption',
    method: 'put',
    data: data
  })
}

// 删除用户算力点数变化记录
export function delConsumption(consumptionId) {
  return request({
    url: '/platform/consumption/' + consumptionId,
    method: 'delete'
  })
}

// 刷新算力缓存
export function refreshCache() {
  return request({
    url: '/platform/consumption/refreshCache',
    method: 'delete'
  })
}

// 首页展示当前用户算力点数变化记录列表
export function userConsumption(query) {
  return request({
    url: '/platform/consumption/userConsumption',
    method: 'get',
    params: query
  })
}

// 获取用户算力点使用占比
export function getUserHashratePercent() {
  return request({
    url: '/platform/consumption/getUserHashratePercent',
    method: 'get'
  })
}

// 获取算力消费统计数据
export function getStatistics() {
  return request({
    url: '/platform/consumption/statistics',
    method: 'get'
  })
}