/**
 * 阿里云ICE Web SDK TypeScript类型定义
 * 基于官方文档：https://help.aliyun.com/zh/ims/developer-reference/extended-functionality
 */

// Timeline相关类型
export interface TimelineData {
  videoTracks?: VideoTrack[];
  audioTracks?: AudioTrack[];
  duration?: number;
  [key: string]: any;
}

export interface VideoTrack {
  id: string;
  clips: VideoClip[];
  [key: string]: any;
}

export interface AudioTrack {
  id: string;
  clips: AudioClip[];
  [key: string]: any;
}

export interface VideoClip {
  id: string;
  materialId: string;
  startTime: number;
  duration: number;
  timelineIn: number;
  timelineOut: number;
  [key: string]: any;
}

export interface AudioClip {
  id: string;
  materialId: string;
  startTime: number;
  duration: number;
  timelineIn: number;
  timelineOut: number;
  volume?: number;
  [key: string]: any;
}

// 素材相关类型
export interface ProjectMaterial {
  id: string;
  type: 'video' | 'audio' | 'image' | 'subtitle';
  url: string;
  duration?: number;
  title?: string;
  [key: string]: any;
}

// 项目相关类型
export interface EditingProject {
  id: string;
  title: string;
  timeline: TimelineData;
  materials: ProjectMaterial[];
  settings?: ProjectSettings;
  [key: string]: any;
}

export interface ProjectSettings {
  width?: number;
  height?: number;
  fps?: number;
  aspectRatio?: string;
  [key: string]: any;
}

// SDK配置类型
export interface AliyunVideoEditorConfig {
  container: HTMLElement;
  locale?: 'zh-CN' | 'en-US';
  
  // 必需的回调函数
  getEditingProject: () => Promise<EditingProject>;
  updateEditingProject: (project: EditingProject) => Promise<void>;
  getEditingProjectMaterials: () => Promise<ProjectMaterial[]>;
  searchMedia: (keyword?: string) => Promise<ProjectMaterial[]>;
  deleteEditingProjectMaterials: (mediaIds: string[]) => Promise<void>;
  produceEditingProjectVideo: (params: ProduceVideoParams) => Promise<ProduceVideoResult>;
  
  // 可选配置
  defaultAspectRatio?: string;
  defaultSubtitleText?: string;
  customTexts?: {
    importButton?: string;
    updateButton?: string;
    produceButton?: string;
    logoUrl?: string;
  };
  
  // 事件回调
  onBackButtonClick?: () => void;
  onProjectChange?: (project: EditingProject) => void;
  onTimelineChange?: (timeline: TimelineData) => void;
  onProjectTimelineChange?: (timeline: TimelineData) => void;
}

export interface ProduceVideoParams {
  templateId?: string;
  watermarkConfig?: any;
  outputConfig?: {
    width?: number;
    height?: number;
    videoBitrate?: number;
    fps?: number;
  };
  [key: string]: any;
}

export interface ProduceVideoResult {
  jobId: string;
  status: string;
  [key: string]: any;
}

// 编辑器实例类型
export interface AliyunVideoEditorInstance {
  // 基础控制
  destroy: (keepState?: boolean) => boolean;
  
  // Timeline操作
  getProjectTimeline?: () => TimelineData;
  setProjectTimeline?: (timeline: TimelineData) => void;
  
  // 素材操作
  addProjectMaterials?: (materials: ProjectMaterial[]) => void;
  updateProjectMaterials?: (updateFn: (materials: ProjectMaterial[]) => ProjectMaterial[]) => void;
  
  // 播放控制 (如果支持)
  getCurrentTime?: () => number;
  setCurrentTime?: (time: number) => void;
  getDuration?: () => number;
  play?: () => void;
  pause?: () => void;
  stop?: () => void;
  
  // 其他功能
  [key: string]: any;
}

// 全局AliyunVideoEditor对象类型
export interface AliyunVideoEditorGlobal {
  version: string;
  
  // 初始化方法
  init: (config: AliyunVideoEditorConfig) => AliyunVideoEditorInstance;
  
  // 静态方法 (如果有)
  getProjectTimeline?: () => TimelineData;
  setProjectTimeline?: (timeline: TimelineData) => void;
  getCurrentTime?: () => number;
  setCurrentTime?: (time: number) => void;
  getDuration?: () => number;
  destroy?: (keepState?: boolean) => boolean;
  addProjectMaterials?: (materials: ProjectMaterial[]) => void;
  updateProjectMaterials?: (updateFn: (materials: ProjectMaterial[]) => ProjectMaterial[]) => void;
  
  // 其他功能
  [key: string]: any;
}

// 扩展Window接口
declare global {
  interface Window {
    AliyunVideoEditor: AliyunVideoEditorGlobal;
  }
}

export {};
