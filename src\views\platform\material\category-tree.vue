<script setup>
import { listCategory, delCategory, addCategory, updateCategory, checkCategoryHasResources } from "@/api/platform/category";
import { useRoute, useRouter } from "vue-router";
import { computed, nextTick, ref, reactive, toRefs, getCurrentInstance } from "vue";
const route = useRoute();
const model = defineModel()
const { proxy } = getCurrentInstance();
const isTableShow = ref(true);
const projectId = computed(() => route.params.projectId);
const data = reactive({
  rules: {
    categoryTitle: [
      { required: true, message: "分类名称不能为空", trigger: "blur" },
      { max: 15, message: "分类名称不能超过15个字", trigger: "blur" }
    ],
    projectId: [{ required: true, message: "项目ID不能为空", trigger: "blur" }],
  },
  categoryForm: {
    categoryId: null,
    categoryTitle: null,
    projectId: projectId.value,
    projectTitle: null,
    remark: null,
  },
});
const { rules, categoryForm } = toRefs(data);
const categoryOptions = ref(undefined);
const router = useRouter();
const categoryOpen = ref(false);

// 左侧菜单树部分
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

const handleNodeClick = (data) => {
  model.value = data.categoryId
};

// 查询分类
const getCategoryTree = async () => {
  let query = { projectId: projectId.value };
  return listCategory(query).then((response) => {
    categoryOptions.value = response.rows;
    if (categoryOptions.value.length == 0) {
      isTableShow.value = false;
      model.value = -1
    } else {
      // 默认选中第一个分组
      nextTick(() => {
        categoryForm.value.projectTitle = categoryOptions.value[0].projectTitle
        proxy.$refs.tree.setCurrentKey(categoryOptions.value[0].categoryId);
        model.value = categoryOptions.value[0].categoryId;
      });
    }
  });
};

// 添加和修改分类
const updateNode = (node, data) => {
  categoryOpen.value = true;
  let { projectTitle, categoryTitle, categoryId } = { ...data };
  categoryForm.value = { ...categoryForm.value, projectTitle, categoryTitle, categoryId };
};

const categoryAdd = () => {
  categoryForm.value = {
    categoryId: null,
    categoryTitle: '',
    projectId: projectId.value,
    projectTitle: route.query.projectTitle.slice(0, -2),
    remark: null
  }
  categoryOpen.value = true
};

const submitCategoryForm = () => {
  proxy.$refs["categoryRef"].validate((valid) => {
    if (valid) {
      if (categoryForm.value.categoryId != null) {
        updateCategory(categoryForm.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          categoryOpen.value = false;
          getCategoryTree();
        });
      } else {
        addCategory(categoryForm.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          categoryOpen.value = false;
          getCategoryTree();
        });
      }
    }
  });
};

// 删除分类
const remove = (node, data) => {
  checkCategoryHasResources(data.categoryId).then((hasResources) => {
    if (hasResources) {
      // 有资源，询问是否强制删除
      proxy.$modal.confirm("该分类下还有子项，确定要强制删除该分类及其所有相关数据吗？").then(() => {
        const isForceDelete = true; // 设置为强制删除
        return delCategory(data.categoryId, isForceDelete);
      }).then(() => {
        proxy.$modal.msgSuccess("删除成功");
        getCategoryTree(); // 刷新分类列表
      }).catch(() => {
        proxy.$modal.msgInfo("已取消删除");
      });
    } else {
      // 没有资源，直接删除
      proxy.$modal.confirm("确定要删除该分类吗？").then(() => {
        const isForceDelete = false; // 不是强制删除
        return delCategory(data.categoryId, isForceDelete);
      }).then(() => {
        proxy.$modal.msgSuccess("删除成功");
        getCategoryTree(); // 刷新分类列表
      }).catch(() => {
        proxy.$modal.msgInfo("已取消删除");
      });
    }
  }).catch((error) => {
    console.error("检查分类资源时发生错误:", error);
    proxy.$modal.msgError("检查分类资源时发生错误，请稍后再试。");
  });
};

getCategoryTree();
</script>

<template>
  <div class="tree-container">
    <div class="head-container">
      <el-button icon="ArrowLeft" link @click="router.push('/project')" />
      <div style="font-size: 0.8em;font-weight: bolder;">分类</div>
      <el-button icon="Plus" link @click="categoryAdd()" />
    </div>
    <div class="body-container">
      <el-tree ref="tree" :data="categoryOptions" :props="{ label: 'categoryTitle', children: 'children' }"
        :expand-on-click-node="true" :filter-node-method="filterNode" node-key="categoryId" highlight-current
        default-expand-all @node-click="handleNodeClick">
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <div>{{ node.label }}</div>
            <div class="custom-tree-node-button">
              <el-button link type="primary" icon="edit" @click="updateNode(node, data)" />
              <el-button link type="primary" icon="Delete" @click="remove(node, data)" />
            </div>
          </div>
        </template>
      </el-tree>
    </div>
    <!-- 添加或修改分类对话框 -->
    <el-dialog v-model="categoryOpen" width="500px" append-to-body>
      <el-form ref="categoryRef" :model="categoryForm" :rules="rules" label-width="80px">
        <el-form-item label="项目名称">
          {{ categoryForm.projectTitle }}
        </el-form-item>
        <el-form-item label="分类名称" prop="categoryTitle">
          <el-input v-model="categoryForm.categoryTitle" maxlength="15" placeholder="请输入分类名称" @keydown.enter.prevent />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCategoryForm">确 定</el-button>
          <el-button @click="categoryOpen = false;">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.head-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}

.body-container {
  overflow-y: auto;
  height: 100%;
}

.custom-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .custom-tree-node-button {
    width: 50px;

    :deep(.el-button+.el-button) {
      margin-left: 2px;
    }
  }
}
</style>