/**
 * 检查两条消息是否重复
 * 
 * 本函数通过比较两条消息的处理后形式，以及利用相似性比较和多次重复检查来判断消息是否应被视为重复
 * 
 * @param {string} msg1 - 第一条消息
 * @param {string} msg2 - 第二条消息
 * @returns {boolean} - 如果消息重复返回true，否则返回false
 */
function isRepeat(msg1, msg2) {
    const dealMessage = (msg) => msg.toLowerCase().replace(/[^\w\s]/g, '').trim();

    const message1 = dealMessage(msg1);
    const message2 = dealMessage(msg2);
    if (message1 === message2) {
        return true;
    }
    if (isSimilar(message1, message2)) {
        return true;
    }
    if (isManyRepeat(message1) || isManyRepeat(message2)) {
        return true;
    }
    return false;
}

/**
 * 判断两条消息是否相似
 * 相似的定义是基于莱文斯坦距离（Levenshtein distance），即两个字符串之间，由一个转换成另一个所需的最少编辑操作次数
 * 如果两个字符串的莱文斯坦距离不超过某个阈值，则认为它们是相似的
 * 
 * @param {string} msg1 第一条消息
 * @param {string} msg2 第二条消息
 * @returns {boolean} 如果两条消息相似返回true，否则返回false
 */
function isSimilar(msg1, msg2) {
    const msgDistance = (s1, s2) => {
        const m = s1.length;
        const n = s2.length;
        const d = Array(m + 1).fill(null).map(() => Array(n + 1).fill(0));
        for (let i = 0; i <= m; i++) {
            d[i][0] = i;
        }
        for (let j = 0; j <= n; j++) {
            d[0][j] = j;
        }
        for (let j = 1; j <= n; j++) {
            for (let i = 1; i <= m; i++) {
                if (s1[i - 1] === s2[j - 1]) {
                    d[i][j] = d[i - 1][j - 1];
                } else {
                    d[i][j] = Math.min(d[i - 1][j], d[i][j - 1], d[i - 1][j - 1]) + 1;
                }
            }
        }
        return d[m][n];
    };
    const maxDistance = Math.max(msg1.length, msg2.length) * 0.1;
    return msgDistance(msg1, msg2) <= maxDistance;
}
/**
 * 判断给定消息中是否有单词重复出现超过三次
 * 
 * @param {string} message - 待检查的消息字符串
 * @returns {boolean} - 如果有单词重复超过三次则返回true，否则返回false
 */
function isManyRepeat(message) {
    const words = message.split(/\s+/).filter(word => word !== '');
    const wordCount = {};
    for (const word of words) {
        if (wordCount[word]) {
            wordCount[word]++;
        } else {
            wordCount[word] = 1;
        }
    }
    for (const count of Object.values(wordCount)) {
        if (count > 3) {
            return true;
        }
    }
    return false;
}
/**
 * 过滤重复的弹幕消息
 * 本函数通过比较弹幕内容的处理后版本，来确定是否为重复的弹幕
 * 主要目的是去除那些内容相似或相同的弹幕，以减少垃圾信息
 * 
 * @param {Array} barrages - 弹幕消息的数组，每个元素代表一条弹幕消息
 * @returns {Array} - 返回过滤后的弹幕消息数组，其中不包含重复的弹幕
 */
function isFilterRepeat(barrages) {
    const filterBarrages = [];
    const seenMessages = new Set();
    for (const barrage of barrages) {
        const dealMessage = (msg) => msg.toLowerCase().replace(/[^\w\s]/g, '').trim();
        if (!seenMessages.has(dealMessage(barrage.content))) {
            let isSpam = false;
            for (const seenMsg of seenMessages) {
                if (isRepeat(seenMsg, dealMessage(barrage.content))) {
                    isSpam = true;
                    break;
                }
            }
            if (!isSpam) {
                filterBarrages.push(barrage);
                seenMessages.add(dealMessage(barrage.content));
            }
        }
    }
    return filterBarrages;
}

export default isFilterRepeat