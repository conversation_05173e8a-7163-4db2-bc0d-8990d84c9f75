<template>
    <div>
        <el-dialog :model-value="visible" title="上传本地音视频文件" width="800px" :align-center="true"
            :close-on-click-modal="false" :close-on-press-escape="true" @close="handleClose"
            @update:model-value="(val) => emits('update:visible', val)">
            <div class="dialog-body">
                <div class="dialog-left">
                    <div class="upload-box">
                        <el-upload ref="upload" :auto-upload="false" :on-change="handleChange" :on-remove="handleRemove"
                            :file-list="fileList" :limit="1"
                            accept=".mp4,.wmv,.m4v,.flv,.rmvb,.dat,.mov,.mkv,.webm,.avi,.mpeg,.3gp,.ogg,.mp3,.wav,.m4a,.wma,.aac,.ogg,.amr,.flac,.aiff"
                            drag style="width: 100%; height: 100%;">
                            <div v-if="!fileUploaded" class="upload-empty-state">
                                <div class="upload-desc">
                                    <span class="upload-link">点击</span> / <span class="upload-link">拖拽</span> 本地音视频文件到这里
                                </div>
                                <ul class="upload-tips">
                                    <li>单个文件最长6小时，仅支持上传1个文件。</li>
                                    <li>视频支持：mp4/wmv/m4v/flv/rmvb/dat/mov/ <br />mkv/webm/avi/mpeg/3gp/ogg，单个最大6G；</li>
                                    <li>音频支持：mp3/wav/m4a/wma/aac/ogg/amr<br />/flac/aiff，单个最大500M。</li>
                                </ul>
                            </div>

                        </el-upload>

                    </div>
                </div>
                <div class="dialog-right">
                    <el-form :model="form" ref="formRef">
                        <el-row style="display: flex; flex-direction: column;">
                            <el-form-item label="音视频语言"></el-form-item>
                            <el-radio-group v-model="form.language">
                                <el-radio v-for="item in langOptions" :key="item.value" :label="item.value"
                                    size="middle" border>
                                    {{ item.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-row>
                        <el-row style="display: flex; flex-direction: column;">
                            <el-form-item label="翻译"></el-form-item>
                            <el-select v-model="form.translate" size="middle">
                                <el-option v-for="item in translateOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-row>
                        <el-row style="display: flex; flex-direction: column;">
                            <el-form-item label="区分发言人"></el-form-item>
                            <el-radio-group v-model="form.speakerType">
                                <el-radio v-for="item in speakerOptions" :key="item.value" :label="item.value"
                                    size="middle" border>
                                    {{ item.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-row>
                        <el-row style="display: flex; flex-direction: column;">
                            <el-form-item label="词表选择"></el-form-item>
                            <el-select v-model="form.phraseId" size="middle" placeholder="请选择词表" clearable
                                :loading="phraseLoading" class="phrase-select">
                                <el-option v-for="item in phraseList" :key="item.phraseId" :label="item.name"
                                    :value="item.phraseId" class="phrase-option">
                                    <div class="option-content">
                                        <span class="option-name">{{ item.name }}</span>
                                    </div>
                                </el-option>
                            </el-select>
                        </el-row>
                    </el-form>
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <div class="footer-actions">
                        <el-button @click="handleClose" class="cancel-btn">
                            <span>取消</span>
                        </el-button>
                        <el-button type="primary" @click="onSubmit" :disabled="!fileUploaded" class="submit-btn">
                            <el-icon class="submit-icon">
                                <VideoPlay />
                            </el-icon>
                            <span>开始转写</span>
                        </el-button>
                    </div>
                </div>
            </template>

        </el-dialog>
    </div>
</template>

<script setup>
import { reactive, toRefs, defineProps, defineEmits, ref, watch, onMounted } from 'vue'
import { VideoPlay } from '@element-plus/icons-vue'

import { summitOfflineTask } from '@/api/tingwu/record'
import { listPhrase } from '@/api/tingwu/phrase'

import { ElMessage } from 'element-plus'

const props = defineProps({
    visible: Boolean
})

// 添加监听器来调试visible属性变化
watch(() => props.visible, (newVal) => {
    console.log('UploadMediaDialog visible changed to:', newVal)
})
const emits = defineEmits(['update:visible', 'refresh-data'])

const translateOptions = [
    { value: '0', label: '不翻译' },
    { value: '1', label: '英语' },
    { value: '2', label: '日语' },
]

const langOptions = [
    { label: '中英文自由说', value: '4' },
    { label: '中文', value: '0' },
    { label: '英语', value: '1' },
    { label: '日语', value: '2' },
    { label: '粤语', value: '3' },

]
const speakerOptions = [
    { label: '暂不体验', value: '0' },
    { label: '单人演讲', value: '1' },
    { label: '2人对话', value: '2' },
    { label: '多人讨论', value: '3' }
]

// 文件上传相关逻辑
const fileUploaded = ref(false)
const fileList = ref([])

const formRef = ref()

// 词表相关数据
const phraseList = ref([])
const phraseLoading = ref(false)

const data = reactive({
    form: {
        language: '4',
        translate: '0', // 默认不翻译
        speakerType: '2',
        phraseId: '' // 添加词表选择字段
    }
})

const { form } = toRefs(data)

// 获取词表列表
const getPhraseList = async () => {
    try {
        phraseLoading.value = true
        const response = await listPhrase({ pageNum: 1, pageSize: 999 })
        phraseList.value = response.rows || []
    } catch (error) {
        console.error('获取词表列表失败:', error)
        ElMessage.error('获取词表列表失败')
    } finally {
        phraseLoading.value = false
    }
}

// 组件挂载时获取词表数据
onMounted(() => {
    getPhraseList()
})

/**文件被改变 */
const handleChange = (file, fileListRaw) => {
    fileList.value = fileListRaw
    changeFileUploadedValue()
}

function handleClose() {
    // 重置表单和文件列表
    resetForm()
    // 关闭对话框
    emits('update:visible', false)
    // 通知父组件刷新数据
    emits('refresh-data')
}

/**删除文件 */
const handleRemove = (file, fileListRaw) => {
    fileList.value = fileListRaw
    changeFileUploadedValue()
}

/**修改数据 */
const changeFileUploadedValue = () => {
    fileList.value.length > 0 ? fileUploaded.value = true : fileUploaded.value = false
}

/**重置表单 */
const resetForm = () => {
    // 重置表单字段
    if (formRef.value) {
        formRef.value.resetFields()
    }

    // 手动重置表单值到默认值
    form.value.language = '4'
    form.value.translate = '0'
    form.value.speakerType = '2'
    form.value.phraseId = ''

    // 清空文件列表
    fileList.value = []
    fileUploaded.value = false
}

const onSubmit = async () => {
    try {
        const formData = new FormData()
        if (fileList.value.length > 0) {
            formData.append('files', fileList.value[0].raw)
        }
        formData.append('language', form.value.language)
        formData.append('translate', form.value.translate)
        formData.append('speakerType', form.value.speakerType)
        if (form.value.phraseId) {
            formData.append('phraseId', form.value.phraseId)
        }
        await summitOfflineTask(formData)
        ElMessage.success('上传成功')
        // openTask();
    } catch (error) {
        ElMessage.error('上传失败')
    } finally {
        resetForm()
        handleClose()
    }
}
</script>

<style lang="scss" scoped>
@import '../styles/upload-media-dialog.scss';
</style>
