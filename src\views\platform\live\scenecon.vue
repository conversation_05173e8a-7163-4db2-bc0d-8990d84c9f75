<script setup>
import { listScenecon, getScenecon, delScenecon, addScenecon, updateScenecon } from "@/api/platform/scenecon";
import { useRoute, useRouter } from "vue-router";
import modal from "@/plugins/modal";
import { ref } from "vue";
/** 查询场控管理列表 */
const props = defineProps({
  categorys: {
    type: Object,
    default: () => { return {} }
  }
})
const { proxy } = getCurrentInstance();
const route = useRoute()
const sceneconList = ref([]);
const open = ref(false);
const loading = ref(true);
const total = ref(0);
const title = ref("");
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: null,
    sceneconName: null,
    sceneconInteractionId: [],
    sceneconQuestionsId: [],
  },
  rules: {
    sceneconInteractionId: [
      { required: true, message: "请选择互动分类", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          console.log("===>", value);

          if (value.length <= 0) {
            return callback(new Error("请选择互动分类"))
          } else {
            callback()
          }
        }, trigger: "blur"
      }
    ],
    sceneconQuestionsId: [
      { required: true, message: "请选择问答分类", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (value.length <= 0) {
            return callback(new Error("请选择互动分类"))
          } else {
            callback()
          }
        }
      }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);
async function getList() {
  loading.value = true;
  queryParams.value.projectId = route.params.projectId
  const response = await listScenecon(queryParams.value)
  sceneconList.value = response.rows;
  loading.value = false;
}
function handleAdd() {
  open.value = true;
  title.value = "添加场控";
}
function submitForm() {
  proxy.$refs["sceneconRef"].validate((valid, fields) => {
    if (valid) {
      if (form.value.sceneconId != null) {
        updateScenecon(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        form.value.projectId = parseInt(route.params.projectId)
        addScenecon(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    } else {
      for (let field in fields) {
        fields[field].forEach(i => {
          proxy.$modal.msgError(i.message)
        })
      }
    }
  });
}
async function handleUpdate(row) {
  const res = await getScenecon(row.sceneconId).then(res => res.data)
  form.value = res
  open.value = true;
  title.value = "修改场控";
}
function handleDelete(row) {
  delScenecon(row.sceneconId).then(res => {
    getList();
    modal.msgSuccess("删除成功");
  })
}

getList()
</script>
<template>
  <el-card class="scene-control-panel" body-style="overflow-y: auto; height:calc(100% - 50px); padding: 0;">
    <template #header>
      <div class="scene-control-header">
        <div class="scene-control-title">
          <div class="main-title"><i class="el-icon-video-play"></i> 场控管理</div>
          <div class="sub-title">为直播间配置智能场控</div>
        </div>
        <el-button type="primary" class="add-button" @click="handleAdd">
          <i class="el-icon-plus"></i> 创建场控
        </el-button>
      </div>
    </template>
    
    <div class="scene-control-body">
      <el-empty description="暂无场控配置" v-if="sceneconList.length <= 0" />
      
      <div v-loading="loading" class="scene-grid-container" v-else>
        <div class="scene-grid">
          <div v-for="scenecon in sceneconList" :key="scenecon.sceneconId" class="scene-card-wrapper">
            <div class="scene-card">
              <div class="scene-card-header">
                <div class="scene-name">{{ scenecon.sceneconName }}</div>
                <el-tag size="small" class="scene-tag">场控</el-tag>
              </div>
              
              <div class="scene-card-content">
                <div class="content-section">
                  <div class="section-row">
                    <span class="section-label"><i class="el-icon-magic-stick"></i> 互动：</span>
                    <div class="tags-container">
                      <el-tag 
                        v-for="(item, index) in scenecon.sceneconInteractionId" 
                        :key="`inter-${index}`"
                        class="category-tag" 
                        size="small"
                        type="success" 
                        effect="plain"
                      >
                        {{ categorys[item] }}
                      </el-tag>
                      <span class="no-data" v-if="scenecon.sceneconInteractionId.length === 0">暂无</span>
                    </div>
                  </div>
                  
                  <div class="section-row">
                    <span class="section-label"><i class="el-icon-chat-dot-square"></i> 问答：</span>
                    <div class="tags-container">
                      <el-tag 
                        v-for="(item, index) in scenecon.sceneconQuestionsId" 
                        :key="`ques-${index}`"
                        class="category-tag" 
                        size="small"
                        type="info" 
                        effect="plain"
                      >
                        {{ categorys[item] }}
                      </el-tag>
                      <span class="no-data" v-if="scenecon.sceneconQuestionsId.length === 0">暂无</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="scene-card-footer">
                <el-button type="primary" size="small" @click="handleUpdate(scenecon)">
                  <i class="el-icon-edit"></i> 编辑
                </el-button>
                <el-button type="danger" size="small" @click="handleDelete(scenecon)">
                  <i class="el-icon-delete"></i> 删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <el-dialog 
      :title="title" 
      v-model="open" 
      width="800px" 
      append-to-body 
      @update:modelValue="form = {}" 
      destroy-on-close
      class="scene-dialog"
    >
      <el-form ref="sceneconRef" :model="form" :rules="rules" label-width="120px" class="scene-form">
        <el-form-item label="场控名称：" prop="sceneconName">
          <el-input v-model="form.sceneconName" placeholder="请输入场控名称" class="custom-input" />
        </el-form-item>
        
        <el-form-item label="场控互动分类：" prop="sceneconInteractionId">
          <div class="category-select-container">
            <div class="category-description">
              <i class="el-icon-connection"></i> 会主动触发所选分类下的音频
            </div>
            <el-checkbox-group v-model="form.sceneconInteractionId" class="checkbox-group">
              <el-checkbox 
                v-for="(k, v) in categorys" 
                :key="`form-inter-${v}`" 
                :label="k" 
                :value="+v" 
                class="category-checkbox"
              />
            </el-checkbox-group>
            <div v-if="JSON.stringify(categorys)=='{}'" class="no-category-data">
              <i class="el-icon-warning"></i> 暂无分类数据
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="场控问答分类：" prop="sceneconQuestionsId">
          <div class="category-select-container">
            <div class="category-description">
              <i class="el-icon-chat-line-round"></i> 弹幕中涉及所选分类关键词时会触发该分类下的音频
            </div>
            <el-checkbox-group v-model="form.sceneconQuestionsId" class="checkbox-group">
              <el-checkbox 
                v-for="(k, v) in categorys" 
                :key="`form-ques-${v}`" 
                :label="k" 
                :value="+v" 
                class="category-checkbox"
              />
            </el-checkbox-group>
            <div v-if="JSON.stringify(categorys)=='{}'" class="no-category-data">
              <i class="el-icon-warning"></i> 暂无分类数据
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="备注：" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" class="custom-textarea" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open = false" class="cancel-btn">取 消</el-button>
          <el-button type="primary" @click="submitForm" class="submit-btn">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </el-card>
</template>

<style lang="scss" scoped>
.scene-control-panel { height: 80vh; border-radius: 8px; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06); border: 1px solid #e8eaec; }
.scene-control-header { display: flex; justify-content: space-between; align-items: center; padding: 12px 16px; background: linear-gradient(to right, #f0f5ff 0%, #edf2fc 100%); border-bottom: 1px solid #e1e6ef; }
.scene-control-title { display: flex; flex-direction: column; }
.main-title { font-size: 16px; font-weight: 600; color: #303133; display: flex; align-items: center; gap: 6px; }
.main-title i { font-size: 16px; color: #409EFF; background-color: rgba(64, 158, 255, 0.1); padding: 4px; border-radius: 4px; }
.sub-title { font-size: 12px; color: #606266; margin-top: 2px; }
.add-button { padding: 6px 12px; border-radius: 4px; font-weight: 500; transition: all 0.2s; background: #409EFF; border: none; }
.add-button:hover { background: #66b1ff; transform: translateY(-2px); box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25); }
.scene-control-body { padding: 12px; background-color: #f8f8fc; }
.scene-grid-container { min-height: 200px; transition: all 0.3s ease; }
.scene-grid { display: flex; flex-wrap: wrap; gap: 16px; }
.scene-card-wrapper { position: relative; margin-bottom: 5px; }
.scene-card { 
  width: 270px; 
  border-radius: 6px; 
  background: white; 
  box-shadow: 0 2px 8px rgba(0,0,0,0.04); 
  transition: all 0.25s ease; 
  height: 100%; 
  display: flex; 
  flex-direction: column; 
  border: 1px solid #ebeef5; 
  position: relative;
  overflow: hidden;
}
.scene-card:hover { 
  transform: translateY(-3px); 
  box-shadow: 0 6px 12px rgba(0,0,0,0.08); 
  border-color: #c6e2ff; 
}
.scene-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #409EFF;
  opacity: 0.6;
}
.scene-card-header { 
  padding: 10px 12px; 
  border-bottom: 1px solid #ebeef5; 
  display: flex; 
  justify-content: space-between; 
  align-items: center; 
  background: linear-gradient(to right, #f5f7fa, #f8f9fc);
}
.scene-name { 
  font-size: 14px; 
  font-weight: 600; 
  color: #303133; 
  overflow: hidden; 
  text-overflow: ellipsis; 
  white-space: nowrap; 
  flex: 1; 
}
.scene-tag { 
  font-size: 11px; 
  padding: 0 6px; 
  height: 20px; 
  line-height: 18px; 
  color: #fff; 
  background-color: #409EFF; 
  border: none;
  border-radius: 10px;
}
.scene-card-content { padding: 10px 12px; flex: 1; background-color: #ffffff; }
.content-section { display: flex; flex-direction: column; gap: 10px; }
.section-row { display: flex; align-items: flex-start; margin-bottom: 2px; }
.section-label { 
  font-size: 12px; 
  font-weight: 500; 
  color: #606266; 
  white-space: nowrap; 
  display: flex; 
  align-items: center; 
  gap: 4px; 
  width: 55px; 
  background: rgba(64, 158, 255, 0.05);
  padding: 2px 4px;
  border-radius: 3px;
}
.section-label i { font-size: 12px; color: #409EFF; }
.tags-container { flex: 1; display: flex; flex-wrap: wrap; gap: 4px; align-items: center; }
.category-tag { 
  margin: 1px 2px; 
  font-size: 11px; 
  padding: 0 6px; 
  height: 18px; 
  line-height: 16px; 
  border-radius: 9px;
  border: none;
}
.category-tag.el-tag--success { 
  background-color: rgba(103, 194, 58, 0.1); 
  color: #67c23a; 
}
.category-tag.el-tag--info { 
  background-color: rgba(144, 147, 153, 0.1); 
  color: #909399; 
}
.no-data { 
  font-size: 11px; 
  color: #909399; 
  font-style: italic; 
  display: inline-block;
  padding: 2px 6px;
  background: #f5f7fa;
  border-radius: 3px;
}
.scene-card-footer { 
  display: flex; 
  justify-content: space-between; 
  gap: 8px; 
  padding: 8px 12px; 
  border-top: 1px solid #ebeef5; 
  background: #f8f9fc; 
}
.scene-card-footer .el-button {
  flex: 1;
  font-size: 12px;
  padding: 6px 0;
  border-radius: 4px;
}
.scene-card-footer .el-button--primary {
  background-color: #409EFF;
  border-color: #409EFF;
}
.scene-card-footer .el-button--primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25);
}
.scene-card-footer .el-button--danger {
  background-color: #fff;
  border-color: #f56c6c;
  color: #f56c6c;
}
.scene-card-footer .el-button--danger:hover {
  background-color: #f56c6c;
  color: #fff;
}
</style>