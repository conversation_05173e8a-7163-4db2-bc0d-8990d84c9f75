import type { Router } from 'vue-router'

import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isHttp } from '@/utils/validate'
import { isRelogin } from '@/utils/request'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'
import { isPathMatch } from '@/utils/validate'
import { RoutesAlias } from '../routesAlias'

const whiteList: string[] = [RoutesAlias.Login, RoutesAlias.Register]
const isWhiteList = (path: string) => {
  if (whiteList.some(pattern => isPathMatch(pattern, path))) {
    return true
  }
  // 放行视频编辑独立页面及其子路由
  if (path.startsWith('/tool/MatrixMix/videoEdit')) {
    return true
  }
  return false
}

/**
 * 路由全局前置守卫
 * 处理进度条、获取菜单列表、动态路由注册、404 检查、工作标签页及页面标题设置
 */
export function setupBeforeEachGuard(router: Router): void {
  router.beforeEach((to, _from, next) => {
    let title: string = typeof to.meta.title === 'function' ? to.meta.title(to) : to.meta.title ?? ''
    useSettingsStore().setTitle(title)
    NProgress.start()

    // 白名单路由直接放行
    if (isWhiteList(to.path)) {
      next()
      return
    }

    // 检查是否有token
    const token = getToken()
    if (!token) {
      // 没有token，跳转到登录页
      const redirectPath = to.fullPath ? `${RoutesAlias.Login}?redirect=${encodeURIComponent(to.fullPath)}` : RoutesAlias.Login
      next(redirectPath)
      return
    }

    // 有token，检查用户信息是否已加载
    const userStore = useUserStore()
    if (userStore.roles.length === 0) {
      // 显示重新登录状态，但不阻塞
      isRelogin.show = true

      // 获取用户信息
      userStore.getInfo().then(() => {
        isRelogin.show = false

        // 生成动态路由
        return usePermissionStore().generateRoutes()
      }).then(accessRoutes => {
        // 添加动态路由
        accessRoutes.forEach(route => {
          if (!isHttp(route.path)) {
            router.addRoute(route as any) // 类型转换以兼容Vue Router
          }
        })

        // 确保路由添加完成后再跳转
        next({ ...to, replace: true })
      }).catch(err => {
        console.error('获取用户信息失败:', err)
        isRelogin.show = false

        // 清除用户信息并跳转到登录页
        userStore.logOut().then(() => {
          ElMessage.error(err.message || '登录状态已过期，请重新登录')
          const redirectPath = to.fullPath ? `${RoutesAlias.Login}?redirect=${encodeURIComponent(to.fullPath)}` : RoutesAlias.Login
          next(redirectPath)
        }).catch(() => {
          // 如果登出也失败，直接跳转
          const redirectPath = to.fullPath ? `${RoutesAlias.Login}?redirect=${encodeURIComponent(to.fullPath)}` : RoutesAlias.Login
          next(redirectPath)
        })
      })
    } else {
      // 用户信息已存在，直接放行
      next()
    }
  })
}