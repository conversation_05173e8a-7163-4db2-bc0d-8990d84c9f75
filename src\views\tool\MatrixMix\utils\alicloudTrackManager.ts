/**
 * 阿里云轨道管理器
 * 专门处理轨道的创建、删除、移动等操作
 */

import { TimelineValidator, globalTrackIdManager, generateUniqueTrackId, type ValidationResult } from './timelineValidationUtils';

export interface TrackCreationResult {
  success: boolean;
  timeline?: any;
  newTrackId?: string;
  error?: string;
  validationLog?: string[];
}

export interface ClipMoveResult {
  success: boolean;
  timeline?: any;
  error?: string;
}

/**
 * 阿里云轨道管理器类
 */
export class AlicloudTrackManager {
  
  /**
   * 生成唯一轨道ID（使用全局ID管理器）
   * @param timeline Timeline数据
   * @param trackType 轨道类型
   * @param insertPosition 插入位置
   * @returns 唯一轨道ID
   */
  private generateTrackId(timeline: any, trackType: 'Video' | 'Audio' | 'Subtitle', insertPosition: 'top' | 'bottom' = 'bottom'): string {
    // 使用全局ID管理器生成唯一ID
    return generateUniqueTrackId(timeline, trackType, insertPosition);
  }
  
  /**
   * 创建空轨道（不包含任何片段）
   * @param timeline 当前timeline数据
   * @param trackType 轨道类型
   * @param insertPosition 插入位置
   */
  createEmptyTrack(
    timeline: any,
    trackType: 'Video' | 'Audio' | 'Subtitle',
    insertPosition: 'top' | 'bottom' = 'bottom'
  ): TrackCreationResult {
    try {
      if (!timeline) {
        throw new Error('Timeline data is required');
      }

      console.log(`🎬 创建${trackType}空轨道, 插入位置: ${insertPosition}`);
      
      // 第一步：校验当前Timeline数据完整性
      const timelineValidation = TimelineValidator.validateTimeline(timeline);
      const validationLog: string[] = [];
      
      if (!timelineValidation.isValid) {
        console.warn('⚠️ Timeline数据存在问题:', timelineValidation.errors);
        validationLog.push('Timeline校验警告: ' + timelineValidation.errors.join(', '));
      }
      
      // 第二步：生成安全的轨道数据（使用全局ID管理器）
      const newTrackId = this.generateTrackId(timeline, trackType, insertPosition);
      
      validationLog.push(`✅ 生成新轨道ID: ${newTrackId}`);
      
      // 第三步：构建轨道数据结构
      const newTrack: any = {
        Id: newTrackId,
        Type: trackType
      };
      
      // 添加轨道类型特有的片段数组
      if (trackType === 'Video') {
        newTrack.VideoTrackClips = [];
      } else if (trackType === 'Audio') {
        newTrack.AudioTrackClips = [];
      } else if (trackType === 'Subtitle') {
        newTrack.SubtitleTrackClips = [];
      }
      
      console.log('🎯 创建的新轨道数据:', newTrack);
      
      // 第四步：根据插入位置将轨道添加到Timeline
      const tracksKey = `${trackType}Tracks`;
      if (!timeline[tracksKey]) {
        timeline[tracksKey] = [];
      }
      
      if (insertPosition === 'top') {
        timeline[tracksKey].unshift(newTrack);
        validationLog.push(`⬆️ 轨道已插入到顶部`);
      } else {
        timeline[tracksKey].push(newTrack);
        validationLog.push(`⬇️ 轨道已插入到底部`);
      }
      
      // 第五步：校验完整的Timeline数据
      const finalValidation = TimelineValidator.validateTimeline(timeline);
      if (!finalValidation.isValid) {
        throw new Error(`Timeline最终校验失败: ${finalValidation.errors.join(', ')}`);
      }
      
      console.log('✅ 空轨道创建成功，ID:', newTrackId);
      console.log('📊 创建后轨道统计:', {
        VideoTracks: timeline.VideoTracks?.length || 0,
        AudioTracks: timeline.AudioTracks?.length || 0,
        SubtitleTracks: timeline.SubtitleTracks?.length || 0
      });
      
      return {
        success: true,
        timeline,
        newTrackId,
        validationLog
      };
      
    } catch (error: any) {
      console.error('❌ 创建空轨道失败:', error);
      return {
        success: false,
        error: error?.message || '创建空轨道失败'
      };
    }
  }
  
  /**
   * 创建新轨道并添加片段
   * @param timeline 当前timeline数据
   * @param trackType 轨道类型
   * @param insertPosition 插入位置
   * @param clipData 片段数据
   * @param startTime 开始时间
   */
  createNewTrackWithClip(
    timeline: any,
    trackType: 'Video' | 'Audio' | 'Subtitle',
    insertPosition: 'top' | 'bottom',
    clipData: any,
    startTime: number = 0
  ): TrackCreationResult {
    try {
      if (!timeline) {
        throw new Error('Timeline data is required');
      }
      
      if (!clipData) {
        throw new Error('Clip data is required');
      }

      console.log(`🎬 创建${trackType}轨道并添加片段, 插入位置: ${insertPosition}`);
      console.log('📎 片段数据:', clipData);
      
      // 第一步：先创建空轨道
      const emptyTrackResult = this.createEmptyTrack(timeline, trackType, insertPosition);
      if (!emptyTrackResult.success) {
        throw new Error(`创建空轨道失败: ${emptyTrackResult.error}`);
      }
      
      const newTrackId = emptyTrackResult.newTrackId!;
      const validationLog = emptyTrackResult.validationLog || [];
      
      // 第二步：构建片段数据
      const clipProperties = {
        In: startTime.toString(),
        Out: (startTime + (clipData.duration || 5)).toString(),
        TimelineIn: startTime.toString(),
        TimelineOut: (startTime + (clipData.duration || 5)).toString()
      };
      
      // 根据轨道类型构建不同的片段数据
      let clipToAdd;
      if (trackType === 'Video') {
        clipToAdd = {
          MediaId: clipData.MediaId || clipData.id,
          Type: 'Video',
          ...clipProperties
        };
      } else if (trackType === 'Audio') {
        clipToAdd = {
          MediaId: clipData.MediaId || clipData.id,
          Type: 'Audio',
          ...clipProperties
        };
      } else if (trackType === 'Subtitle') {
        clipToAdd = {
          MediaId: clipData.MediaId || clipData.id,
          Type: 'Subtitle',
          Content: clipData.content || '字幕内容',
          ...clipProperties
        };
      }
      
      console.log('🎯 构建的片段数据:', clipToAdd);
      
      // 第三步：将片段添加到新创建的轨道
      const tracksKey = `${trackType}Tracks`;
      const tracks = timeline[tracksKey];
      const targetTrack = tracks.find((track: any) => track.Id === newTrackId);
      
      if (!targetTrack) {
        throw new Error(`未找到新创建的轨道: ${newTrackId}`);
      }
      
      const clipsKey = `${trackType}TrackClips`;
      if (!targetTrack[clipsKey]) {
        targetTrack[clipsKey] = [];
      }
      
      targetTrack[clipsKey].push(clipToAdd);
      validationLog.push(`📎 片段已添加到轨道 ${newTrackId}`);
      
      // 第四步：最终校验
      const finalValidation = TimelineValidator.validateTimeline(timeline);
      if (!finalValidation.isValid) {
        console.warn('⚠️ Timeline最终校验有警告:', finalValidation.errors);
        validationLog.push('最终校验警告: ' + finalValidation.errors.join(', '));
      }
      
      console.log('✅ 轨道创建并添加片段成功，轨道ID:', newTrackId);
      console.log('📊 最终轨道统计:', {
        VideoTracks: timeline.VideoTracks?.length || 0,
        AudioTracks: timeline.AudioTracks?.length || 0,
        SubtitleTracks: timeline.SubtitleTracks?.length || 0
      });
      
      return {
        success: true,
        timeline,
        newTrackId,
        validationLog
      };
      
    } catch (error: any) {
      console.error('❌ 创建轨道并添加片段失败:', error);
      return {
        success: false,
        error: error?.message || '创建轨道失败'
      };
    }
  }
  
  /**
   * 移动片段到其他轨道
   * @param timeline Timeline数据
   * @param sourceTrackId 源轨道ID
   * @param targetTrackId 目标轨道ID
   * @param clipId 片段ID
   * @param newStartTime 新的开始时间
   */
  moveClipToTrack(
    timeline: any,
    sourceTrackId: string,
    targetTrackId: string,
    clipId: string,
    newStartTime: number
  ): ClipMoveResult {
    try {
      if (!timeline) {
        throw new Error('Timeline data is required');
      }

      console.log(`📦 移动片段: ${clipId} 从轨道 ${sourceTrackId} 到轨道 ${targetTrackId}`);
      
      // 寻找源轨道和片段
      let sourceTrack: any = null;
      let clipToMove: any = null;
      let clipsArray: any[] = [];
      let clipIndex = -1;
      
      // 搜索所有轨道类型
      const trackTypes = ['VideoTracks', 'AudioTracks', 'SubtitleTracks'];
      for (const trackType of trackTypes) {
        if (timeline[trackType]) {
          const track = timeline[trackType].find((t: any) => t.Id === sourceTrackId);
          if (track) {
            sourceTrack = track;
            const clipsKey = trackType.replace('Tracks', 'TrackClips');
            clipsArray = track[clipsKey] || [];
            clipIndex = clipsArray.findIndex((clip: any) => clip.MediaId === clipId);
            if (clipIndex !== -1) {
              clipToMove = clipsArray[clipIndex];
              break;
            }
          }
        }
      }
      
      if (!sourceTrack || !clipToMove) {
        throw new Error(`未找到源轨道或片段: 轨道=${sourceTrackId}, 片段=${clipId}`);
      }
      
      // 寻找目标轨道
      let targetTrack: any = null;
      let targetClipsArray: any[] = [];
      
      for (const trackType of trackTypes) {
        if (timeline[trackType]) {
          const track = timeline[trackType].find((t: any) => t.Id === targetTrackId);
          if (track) {
            targetTrack = track;
            const clipsKey = trackType.replace('Tracks', 'TrackClips');
            targetClipsArray = track[clipsKey] || [];
            if (!track[clipsKey]) {
              track[clipsKey] = [];
              targetClipsArray = track[clipsKey];
            }
            break;
          }
        }
      }
      
      if (!targetTrack) {
        throw new Error(`未找到目标轨道: ${targetTrackId}`);
      }
      
      // 更新片段时间
      const duration = parseFloat(clipToMove.Out) - parseFloat(clipToMove.In);
      clipToMove.TimelineIn = newStartTime.toString();
      clipToMove.TimelineOut = (newStartTime + duration).toString();
      
      // 从源轨道移除片段
      clipsArray.splice(clipIndex, 1);
      
      // 添加到目标轨道
      targetClipsArray.push(clipToMove);
      
      console.log('✅ 片段移动成功');
      
      return {
        success: true,
        timeline
      };
      
    } catch (error: any) {
      console.error('❌ 片段移动失败:', error);
      return {
        success: false,
        error: error?.message || '片段移动失败'
      };
    }
  }
  
  /**
   * 删除轨道
   * @param timeline Timeline数据
   * @param trackId 要删除的轨道ID
   */
  deleteTrack(timeline: any, trackId: string): TrackCreationResult {
    try {
      if (!timeline) {
        throw new Error('Timeline data is required');
      }

      console.log(`🗑️ 删除轨道: ${trackId}`);
      
      let deleted = false;
      const trackTypes = ['VideoTracks', 'AudioTracks', 'SubtitleTracks'];
      
      for (const trackType of trackTypes) {
        if (timeline[trackType]) {
          const trackIndex = timeline[trackType].findIndex((track: any) => track.Id === trackId);
          if (trackIndex !== -1) {
            timeline[trackType].splice(trackIndex, 1);
            deleted = true;
            console.log(`✅ 从${trackType}中删除轨道: ${trackId}`);
            break;
          }
        }
      }
      
      if (!deleted) {
        throw new Error(`未找到要删除的轨道: ${trackId}`);
      }
      
      return {
        success: true,
        timeline
      };
      
    } catch (error: any) {
      console.error('❌ 删除轨道失败:', error);
      return {
        success: false,
        error: error?.message || '删除轨道失败'
      };
    }
  }
}

// 导出单例实例
export const alicloudTrackManager = new AlicloudTrackManager();
