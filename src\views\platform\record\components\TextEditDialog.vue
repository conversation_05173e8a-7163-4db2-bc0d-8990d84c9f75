<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑原文"
    width="700px"
    :before-close="handleBeforeClose"
    class="text-edit-dialog"
    append-to-body
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    destroy-on-close
    :show-close="false"
  >
    <div class="dialog-content">
      <!-- 对话信息 -->
      <div class="conversation-info">
        <div class="speaker-info">
          <div class="speaker-id-circle" :style="{ backgroundColor: getSpeakerColor(conversationData.speakerId) }">
            {{ conversationData.speakerId }}
          </div>
          <div class="speaker-details">
            <div class="speaker-name" :style="{ color: getSpeakerColor(conversationData.speakerId) }">
              {{ conversationData.speakerName }}
            </div>
            <div class="timestamp">
              {{ formatTimestamp(conversationData.startTime) }} - {{ formatTimestamp(conversationData.endTime) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 原文编辑区域 -->
      <div class="text-edit-section">
        <div class="section-title">
          <el-icon><Edit /></el-icon>
          <!-- 保存状态指示器 -->
          <div class="save-status" v-if="showSaveStatus">
            <el-icon v-if="autoSaving" class="saving-icon"><Loading /></el-icon>
            <el-icon v-else-if="lastSaveSuccess" class="saved-icon"><Check /></el-icon>
            <span class="status-text">
              {{ getSaveStatusText() }}
            </span>
          </div>
        </div>
        <el-input
          v-model="editText"
          type="textarea"
          :rows="8"
          placeholder="请输入原文内容..."
          class="text-textarea"
          @keydown.esc="handleEscapeClose"
          @input="handleTextChange"
          ref="textTextareaRef"
        />
        <div class="edit-tips">
          <span class="tip-text">原文不能为空 • Esc 取消 • 点击外部区域自动保存并关闭</span>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { Edit, Check, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 简单的防抖函数实现
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  conversationData: {
    type: Object,
    default: () => ({})
  },
  conversationIndex: {
    type: Number,
    default: -1
  }
})

const emit = defineEmits(['update:visible', 'save-text'])

// 响应式数据
const dialogVisible = ref(false)
const editText = ref('')
const originalEditText = ref('') // 保存原始内容，用于比较是否有变化
const autoSaving = ref(false)
const lastSaveSuccess = ref(false)
const showSaveStatus = ref(false)
const textTextareaRef = ref(null)
const hasUnsavedChanges = ref(false)

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    // 弹窗打开时初始化文本内容
    const initialText = props.conversationData.text || ''
    editText.value = initialText
    originalEditText.value = initialText
    hasUnsavedChanges.value = false
    showSaveStatus.value = false
    lastSaveSuccess.value = false

    // 聚焦到文本框
    nextTick(() => {
      if (textTextareaRef.value) {
        textTextareaRef.value.focus()
      }
    })
  }
})

// 监听dialogVisible变化，同步到父组件
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  if (!newVal) {
    // 弹窗关闭时重置状态
    resetDialogState()
  }
})

// 格式化时间显示（毫秒转换为 MM:SS 格式）
const formatTimestamp = (milliseconds) => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 获取说话人颜色
const getSpeakerColor = (speakerId) => {
  const colors = [
    '#1890ff', // 蓝色
    '#52c41a', // 绿色
    '#fa8c16', // 橙色
    '#eb2f96', // 粉色
    '#722ed1', // 紫色
    '#13c2c2', // 青色
    '#f5222d', // 红色
    '#faad14', // 黄色
  ]
  const index = parseInt(speakerId) % colors.length
  return colors[index]
}

// 获取保存状态文本
const getSaveStatusText = () => {
  if (autoSaving.value) {
    return '保存中...'
  }
  if (lastSaveSuccess.value) {
    return '已保存'
  }
  return ''
}

// 重置弹窗状态
const resetDialogState = () => {
  editText.value = ''
  originalEditText.value = ''
  hasUnsavedChanges.value = false
  autoSaving.value = false
  lastSaveSuccess.value = false
  showSaveStatus.value = false
}

// 自动保存方法
const autoSave = async () => {
  if (!hasUnsavedChanges.value || autoSaving.value) {
    return
  }

  const currentText = editText.value.trim()

  // 原文不能为空
  if (!currentText) {
    ElMessage.warning('原文内容不能为空')
    return
  }

  try {
    autoSaving.value = true
    showSaveStatus.value = true
    lastSaveSuccess.value = false

    // 发送保存事件给父组件
    await new Promise((resolve, reject) => {
      emit('save-text', {
        index: props.conversationIndex,
        newText: currentText,
        conversation: props.conversationData,
        resolve,
        reject
      })
    })

    // 保存成功
    originalEditText.value = editText.value
    hasUnsavedChanges.value = false
    lastSaveSuccess.value = true

    // 2秒后隐藏保存状态
    setTimeout(() => {
      showSaveStatus.value = false
    }, 2000)

  } catch (error) {
    console.error('自动保存失败:', error)
    lastSaveSuccess.value = false
    ElMessage.error('保存失败，请重试')
  } finally {
    autoSaving.value = false
  }
}

// 防抖的自动保存
const debouncedAutoSave = debounce(autoSave, 1000)

// 处理文本变化
const handleTextChange = () => {
  const currentText = editText.value.trim()
  const originalText = originalEditText.value.trim()

  hasUnsavedChanges.value = currentText !== originalText

  // 如果有内容且有变化，触发自动保存
  if (currentText && hasUnsavedChanges.value) {
    debouncedAutoSave()
  }
}

// 处理ESC键关闭（直接关闭，不保存）
const handleEscapeClose = () => {
  dialogVisible.value = false
}

// 处理弹窗关闭前的逻辑（点击外部区域或其他关闭方式）
const handleBeforeClose = async (done) => {
  const currentText = editText.value.trim()

  // 如果没有内容，提示用户原文不能为空
  if (!currentText) {
    ElMessage.warning('原文内容不能为空，按ESC键可直接关闭')
    return
  }

  // 如果没有未保存的变化，直接关闭
  if (!hasUnsavedChanges.value) {
    done()
    return
  }

  // 如果有未保存的变化，先保存再关闭
  try {
    await autoSave()
    done()
  } catch (error) {
    // 保存失败，询问用户是否强制关闭
    ElMessage.warning('保存失败，按ESC键可直接关闭')
  }
}
</script>

<style lang="scss" scoped>
.text-edit-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #606266 0%, #4a4e52 100%);
    padding: 24px 32px 20px;
    border-bottom: none;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
    }

    .el-dialog__title {
      font-weight: 700;
      font-size: 20px;
      color: white;
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      gap: 12px;

      &::before {
        content: '✏️';
        font-size: 24px;
      }
    }

    .el-dialog__headerbtn {
      position: relative;
      z-index: 1;

      .el-dialog__close {
        color: white;
        font-size: 20px;
        font-weight: bold;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 32px;
    background: #fafbfc;
  }

  :deep(.el-dialog__footer) {
    padding: 20px 32px 32px;
    border-top: 1px solid #f0f2f5;
    background: white;
  }

  .dialog-content {
    .conversation-info {
      background: white;
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 24px;
      border: 1px solid #f0f0f0;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      position: relative;
      overflow: hidden;

      .speaker-info {
        display: flex;
        align-items: center;

        .speaker-id-circle {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          font-size: 16px;
          margin-right: 16px;
          flex-shrink: 0;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          position: relative;

          &::after {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 50%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            z-index: -1;
          }
        }

        .speaker-details {
          flex: 1;

          .speaker-name {
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 6px;
          }

          .timestamp {
            font-size: 13px;
            color: #4a4e52;
            font-weight: 500;
            padding: 4px 12px;
            background: #f0f0f0;
            border-radius: 20px;
            display: inline-block;
          }
        }
      }
    }

    .text-edit-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      border: 1px solid #f0f0f0;

      .section-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        font-weight: 700;
        font-size: 16px;
        color: #2c3e50;

        .el-icon {
          margin-right: 12px;
          color: #606266;
          font-size: 20px;
        }

        .save-status {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 13px;
          font-weight: 500;
          color: #606266;

          .saving-icon {
            color: #409eff;
            animation: rotate 1s linear infinite;
          }

          .saved-icon {
            color: #67c23a;
          }

          .status-text {
            font-size: 12px;
          }
        }
      }

      .text-textarea {
        margin-bottom: 12px;

        :deep(.el-textarea__inner) {
          border-radius: 12px;
          border: 2px solid #e2e8f0;
          font-size: 15px;
          line-height: 1.6;
          padding: 16px 20px;
          transition: all 0.3s ease;
          background: #fafbfc;
          resize: vertical;
          min-height: 160px;

          &:focus {
            border-color: #606266;
            box-shadow: 0 0 0 4px rgba(96, 98, 102, 0.1);
            background: white;
          }

          &::placeholder {
            color: #a0aec0;
            font-style: italic;
          }
        }
      }

      .edit-tips {
        text-align: right;
        margin-top: 8px;

        .tip-text {
          font-size: 12px;
          color: #4a4e52;
          font-weight: 500;
          padding: 6px 12px;
          background: #f0f0f0;
          border-radius: 20px;
          display: inline-block;
        }
      }
    }
  }


}

// 添加一些动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.text-edit-dialog {
  :deep(.el-dialog) {
    animation: slideInUp 0.3s ease-out;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .text-edit-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto !important;
    }

    :deep(.el-dialog__body) {
      padding: 20px;
    }

    :deep(.el-dialog__footer) {
      padding: 16px 20px 24px;
    }

    .dialog-content {
      .conversation-info,
      .text-edit-section {
        padding: 16px;
      }
    }
  }
}


</style>
