<template>
  <div class="hashrate-container">
    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-title"><el-icon><search /></el-icon><span>算力用户查询</span></div>
          <el-button link type="primary" @click="showSearch = !showSearch">
            {{ showSearch ? '收起筛选' : '展开筛选' }}
            <el-icon><arrow-up v-if="showSearch" /><arrow-down v-else /></el-icon>
          </el-button>
        </div>
      </template>
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" @submit.prevent class="search-form">
        <div class="form-item-group">
          <el-form-item label="算力用户" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable @keyup.enter="handleQuery" class="search-input" prefix-icon="User" />
          </el-form-item>
          <div class="form-buttons">
            <el-button type="primary" @click="handleQuery" :icon="Search" class="action-btn query-btn">搜 索</el-button>
            <el-button @click="resetQuery" :icon="Refresh" class="action-btn reset-btn">重 置</el-button>
          </div>
        </div>
      </el-form>
    </el-card>

    <!-- 数据概览卡片 -->
    <div class="stat-cards">
      <el-card class="stat-card" shadow="hover">
        <div class="stat-icon users-icon"><el-icon><user /></el-icon></div>
        <div class="stat-content">
          <div class="stat-value">{{ hashrateList.length }}</div>
          <div class="stat-label">算力用户</div>
        </div>
      </el-card>
      <el-card class="stat-card" shadow="hover">
        <div class="stat-icon balance-icon"><el-icon><wallet /></el-icon></div>
        <div class="stat-content">
          <div class="stat-value">{{ totalBalance }}</div>
          <div class="stat-label">总算力余额</div>
        </div>
      </el-card>
      <el-card class="stat-card" shadow="hover">
        <div class="stat-icon today-icon"><el-icon><calendar /></el-icon></div>
        <div class="stat-content">
          <div class="stat-value">{{ todayOperations }}</div>
          <div class="stat-label">今日操作</div>
        </div>
      </el-card>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-toolbar">
      <div class="left-buttons">
        <el-button type="danger" :icon="Delete" :disabled="multiple" @click="handDelete" class="action-btn">批量删除</el-button>
        <el-button type="warning" :icon="Download" @click="handleExport" class="action-btn">导出数据</el-button>
        <el-button type="primary" :icon="Plus" @click="handleOpenDialog" class="action-btn">批量创建卡号</el-button>
      </div>
      <div class="right-info">
        <el-tag effect="light" class="info-tag">{{ formatDate(new Date(), '{y}-{m}-{d}') }}</el-tag>
        <el-tag type="success" class="info-tag">{{ hashrateList.length }} 位用户</el-tag>
      </div>
    </div>

    <!-- 卡片视图区域 -->
    <el-card class="data-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-title"><el-icon><grid /></el-icon><span>算力用户列表</span></div>
          <div class="view-actions">
            <el-button :icon="Refresh" circle plain @click="getList" title="刷新数据"></el-button>
            <el-switch v-model="viewMode" active-text="卡片视图" inactive-text="表格视图" style="margin-left: 10px;" />
          </div>
        </div>
      </template>

      <!-- 视图切换 -->
      <div v-if="viewMode" class="users-grid">
        <div v-if="loading" class="loading-overlay">
          <el-icon class="loading-icon"><loading /></el-icon>
          <span>正在加载数据...</span>
        </div>
        <el-empty v-else-if="hashrateList.length === 0" description="暂无算力用户数据" />
        <div v-for="item in hashrateList" :key="item.hashrateId" class="user-card" :class="{ 'is-selected': selectedUsers.includes(item.userName) }">
          <div class="select-area">
            <el-checkbox v-model="item.isSelected" @change="(val) => handleCardSelect(val, item)"></el-checkbox>
          </div>
          <div class="user-card-content">
            <div class="user-card-header">
              <el-avatar :size="48" :src="userAvatar(item.userName)" class="user-card-avatar"></el-avatar>
              <div class="user-card-info">
                <div class="user-card-name">{{ item.userName }}</div>
                <div class="user-card-id">ID: {{ item.hashrateId }}</div>
              </div>
              <div class="user-card-balance">
                <span class="balance-label">余额</span>
                <span class="balance-amount">{{ item.hashrateBalance }}</span>
              </div>
            </div>
            <el-divider />
            <div class="user-card-details">
              <div class="detail-item" v-if="item.createBy">
                <el-icon><user /></el-icon>
                <span>操作人: {{ item.createBy }}</span>
              </div>
              <div class="detail-item">
                <el-icon><Timer /></el-icon>
                <span>{{ parseTime(item.updateTime) }}</span>
              </div>
            </div>
            <div class="user-card-footer">
              <div class="card-actions">
                <el-button type="primary" size="small" @click="openStatistics(item)">
                  <el-icon><data-analysis /></el-icon>消费详情
                </el-button>
                <el-button type="success" size="small" @click="handleAdd(item)">
                  <el-icon><plus /></el-icon>充值
                </el-button>
                <el-button type="danger" size="small" @click="handDelete(item)">
                  <el-icon><delete /></el-icon>删除
                </el-button>
              </div>
            </div>
          </div>
          <div class="card-tag" :class="getActivityClass(item)">
            {{ getActivityStatus(item) }}
          </div>
        </div>
      </div>
      <div v-else>
        <el-table v-loading="loading" :data="hashrateList" @selection-change="handleSelectionChange" class="data-table">
          <el-table-column type="selection" width="45" align="center" />
          <el-table-column label="#" type="index" width="50" align="center" />
          <el-table-column label="用户名" align="center" prop="userName" min-width="120">
            <template #default="scope">
              <div class="user-cell">
                <el-avatar :size="28" :src="userAvatar(scope.row.userName)" class="user-avatar"></el-avatar>
                <el-button link type="primary" @click="openStatistics(scope.row)" class="username-link">
                  {{ scope.row.userName }}
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="算力余额" align="center" prop="hashrateBalance" min-width="100">
            <template #default="scope">
              <div class="balance-cell">
                <el-icon><wallet /></el-icon>
                <span class="balance-value">{{ scope.row.hashrateBalance }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="最后操作时间" align="center" prop="createTime" min-width="160">
            <template #default="scope">
              <div class="time-cell">
                <el-icon><time /></el-icon>
                <span class="time-value">{{ parseTime(scope.row.updateTime) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="180">
            <template #default="scope">
              <div class="action-cell">
                <el-button link type="primary" @click="handleAdd(scope.row)" class="table-btn">
                  <el-icon><plus /></el-icon>充值
                </el-button>
                <el-divider direction="vertical" />
                <el-button link type="danger" @click="handDelete(scope.row)" class="table-btn">
                  <el-icon><delete /></el-icon>删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-wrapper">
        <pagination v-if="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
        <div v-else class="no-more">没有更多数据</div>
      </div>
    </el-card>

    <!-- 统计信息对话框 -->
    <el-dialog v-model="statisticsOpen" :title="title" width="70%" destroy-on-close custom-class="stats-dialog">
      <Statistics :user-data="selectedUser" :user-name="selectedUserName" style="height: 630px; width: 100%;" />
    </el-dialog>

    <!-- 引入充值子组件 -->
    <Recharge :open="rechargeDialogOpen" :form="form" :title="title"  @close="rechargeDialogOpen = false"
      @rechargeSuccess="getList"/>

    <!-- 批量创建卡号对话框 -->
    <el-dialog v-model="dialogVisible" title="批量创建卡号" width="600px" @close="handleDialogClose">
      <AddNumber v-if="dialogVisible"  @save="handleBatchCreate" @close="handleDialogClose" @createSuccess="onCreateSuccess" />
    </el-dialog>
  </div>
</template>

<script setup>
import { userConsumption } from "@/api/platform/consumption";
import { deleteByUserName, getHashrate, listHashrate } from "@/api/platform/hashrate";
import Statistics from "../consumption/statistics.vue";
import { getCurrentInstance, ref, reactive, toRefs, computed } from 'vue';
import { Search, Refresh, Delete, Download, Timer, ArrowDown, ArrowUp, Grid, DataAnalysis, Calendar, Loading, Plus, User, Wallet } from '@element-plus/icons-vue';
import AddNumber from './consumption/addNumber.vue'; 
import Recharge from './consumption/recharge.vue'; 

const { proxy } = getCurrentInstance();
const hashrateList = ref([]);
const rechargeDialogOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const ids = ref([]);
const total = ref(0);
const title = ref("");
const statisticsOpen = ref(false);
const selectedUser = ref([]);
const selectedUserName = ref(null);
const viewMode = ref(true);
const selectedUsers = ref([]);

const totalBalance = computed(() => {
  return hashrateList.value.reduce((sum, item) => sum + parseFloat(item.hashrateBalance || 0), 0).toFixed(2);
});

const todayOperations = computed(() => {
  const today = new Date().toDateString();
  return hashrateList.value.filter(item => {
    return new Date(item.updateTime).toDateString() === today;
  }).length;
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 8,
    userName: null,
  }
});

const { queryParams, form } = toRefs(data);
const dialogVisible = ref(false);

function handleOpenDialog() { dialogVisible.value = true; }
function handleDialogClose() { dialogVisible.value = false; }
function userAvatar(username) {
  if (!username) return '';
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#9B59B6', '#3498DB'];
  const index = username.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&background=${colors[index].substring(1)}&color=fff`;
}

function getList() {
  loading.value = true;
  listHashrate(queryParams.value).then(response => {
    hashrateList.value = response.rows.map(item => ({
      ...item,
      isSelected: selectedUsers.value.includes(item.userName)
    }));
    total.value = response.total;
    loading.value = false;
  });
}

function reset() {
  form.value = {
    hashrateId: null,
    userName: null,
    hashrateBalance: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    rechargedHashrate: 100,
    remark: null
  };
  proxy.resetForm("hashrateRef");
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.userName);
  selectedUsers.value = ids.value;
  single.value = selection.length !== 1;
  multiple.value = selection.length === 0;
  hashrateList.value.forEach(item => {
    item.isSelected = selectedUsers.value.includes(item.userName);
  });
}

function handleAdd(row) {
  reset();
  const _hashrateId = row.hashrateId || ids.value
  getHashrate(_hashrateId).then(response => {
    form.value = response.data;
    form.value.rechargedHashrate = 100;
    rechargeDialogOpen.value = true;
    title.value = '给 ' + row.userName + ' 充值算力点';
  });
};

function handDelete(row){
  const _userName = row?.userName || ids.value;
  proxy.$modal.confirm('是否确认删除算力用户 "' + _userName + '" 以及相关的数据？').then(function(){
    return deleteByUserName(_userName);
  }).then(()=>{
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function handleExport() {
  proxy.download('platform/hashrate/export', {
    ...queryParams.value
  }, `hashrate_${new Date().getTime()}.xlsx`)
}

async function openStatistics(row) {
  try {
    title.value = '算力用户 ' + row.userName + ' 消费详情';
    let params = {userName: row.userName };
    const response = await userConsumption(params); 
    selectedUser.value = response.rows;
    selectedUserName.value = row.userName; 
    statisticsOpen.value = true; 
  } catch (error) {
    console.error('未找到该用户的信息', error);
  }
}

function formatDate(date, format) { 
  return proxy.parseTime(date, format); 
}

function handleCardSelect(checked, row) {
  const userName = row.userName;
  if (checked) {
    if (!selectedUsers.value.includes(userName)) {
      selectedUsers.value.push(userName);
    }
  } else {
    const index = selectedUsers.value.indexOf(userName);
    if (index > -1) {
      selectedUsers.value.splice(index, 1);
    }
  }
  ids.value = selectedUsers.value;
  multiple.value = selectedUsers.value.length === 0;
  single.value = selectedUsers.value.length !== 1;
}

function getActivityStatus(item) {
  const now = new Date();
  const updateTime = new Date(item.updateTime);
  const diffDays = Math.floor((now - updateTime) / (1000 * 60 * 60 * 24));
  if (diffDays < 7) return '活跃';
  if (diffDays < 30) return '一般';
  return '不活跃';
}

function getActivityClass(item) {
  const status = getActivityStatus(item);
  switch (status) {
    case '活跃': return 'active';
    case '一般': return 'normal';
    default: return 'inactive';
  }
}

function onCreateSuccess() {
  proxy.$modal.msgSuccess("成功创建卡号");
  getList();
}

getList();
</script>       

<style lang="scss" scoped>
/* 容器样式 */
.hashrate-container { padding: 24px; background: #f5f7fa; min-height: calc(100vh - 60px); }

/* 卡片通用样式 */
.search-card, .data-card, .stat-card { border-radius: 12px; overflow: hidden; transition: all 0.3s ease; margin-bottom: 20px; &:hover { box-shadow: 0 8px 24px rgba(31, 45, 61, 0.08); } }

/* 卡片标题样式 */
.card-header { display: flex; justify-content: space-between; align-items: center; .header-title { display: flex; align-items: center; font-size: 16px; font-weight: 600; color: #303133; .el-icon { margin-right: 8px; font-size: 18px; color: #409EFF; } } }

/* 搜索区域样式 */
.search-form { .form-item-group { display: flex; flex-wrap: wrap; align-items: center; .search-input { width: 260px; } } .form-buttons { margin-left: auto; display: flex; gap: 10px; } }

/* 按钮样式 */
.action-btn { border-radius: 8px; padding: 8px 16px; transition: all 0.3s; &:hover { transform: translateY(-2px); } &.query-btn { background: linear-gradient(to right, #409EFF, #53a8ff); color: #fff; border: none; } &.reset-btn { color: #606266; } }

/* 统计卡片区域 */
.stat-cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); gap: 20px; margin-bottom: 20px; .stat-card { display: flex; align-items: center; padding: 24px; border-left: 4px solid transparent; &:nth-child(1) { border-left-color: #409EFF; } &:nth-child(2) { border-left-color: #67C23A; } &:nth-child(3) { border-left-color: #E6A23C; } .stat-icon { width: 50px; height: 50px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 16px; &.users-icon { background: rgba(64, 158, 255, 0.1); color: #409EFF; } &.balance-icon { background: rgba(103, 194, 58, 0.1); color: #67C23A; } &.today-icon { background: rgba(230, 162, 60, 0.1); color: #E6A23C; } .el-icon { font-size: 24px; } } .stat-content { .stat-value { font-size: 24px; font-weight: 600; color: #303133; line-height: 1.2; } .stat-label { font-size: 14px; color: #909399; } } } }

/* 操作工具栏 */
.action-toolbar { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; background: white; border-radius: 12px; padding: 12px 16px; .left-buttons { display: flex; gap: 10px; } .right-info { display: flex; align-items: center; gap: 10px; .info-tag { padding: 6px 12px; border-radius: 16px; } } }

/* 表格样式 */
.data-table { .user-cell, .balance-cell, .time-cell { display: flex; align-items: center; justify-content: center; gap: 8px; } .user-avatar { margin-right: 8px; } .username-link { font-weight: 500; } .balance-value { font-weight: 500; color: #67C23A; } .time-value { color: #909399; font-size: 13px; } .action-cell { display: flex; justify-content: center; align-items: center; .table-btn { font-size: 13px; } } }

/* 分页区域 */
.pagination-wrapper { display: flex; justify-content: center; padding: 16px 0; background: #f8f9fa; .no-more { color: #909399; font-size: 14px; } }

/* 统计对话框 */
:deep(.stats-dialog) { border-radius: 16px; overflow: hidden; .el-dialog__header { background: linear-gradient(to right, #409EFF, #53a8ff); padding: 16px 24px; margin: 0; .el-dialog__title { color: white; font-weight: 600; } } }

/* 优化的充值对话框样式 */
:deep(.recharge-dialog) { border-radius: 16px; overflow: hidden; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15); .el-dialog__header { background: linear-gradient(135deg, #67C23A, #85ce61); padding: 16px 24px; margin: 0; .el-dialog__title { color: white; font-weight: 600; font-size: 18px; } .el-dialog__headerbtn { top: 16px; right: 16px; .el-dialog__close { color: #fff; } } } .el-dialog__body { padding: 0; } .el-dialog__footer { display: none; } }

.recharge-container { padding: 0; .user-info-section { padding: 24px; display: flex; align-items: center; background: #f8f9fa; .user-avatar { border: 3px solid #fff; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); margin-right: 16px; } .user-details { flex: 1; .user-name { display: block; font-size: 18px; font-weight: 600; color: #303133; margin-bottom: 6px; } .balance-display { display: flex; align-items: center; color: #67C23A; font-size: 14px; .el-icon { margin-right: 6px; } b { font-weight: 600; margin-left: 4px; } } } } .amount-form { padding: 0 24px; .el-divider { margin: 16px 0; .el-divider__text { color: #909399; font-size: 14px; background: #fff; } } } .quick-amount-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; margin: 20px 0; .amount-option { height: 50px; display: flex; align-items: center; justify-content: center; background: #f5f7fa; border-radius: 8px; cursor: pointer; transition: all 0.2s ease; border: 2px solid transparent; &:hover { transform: translateY(-2px); background: #ecf5ff; } &.active { border-color: #67C23A; background: rgba(103, 194, 58, 0.1); .amount-value { color: #67C23A; } } &.custom { background: rgba(64, 158, 255, 0.1); .amount-value { color: #409EFF; } } .amount-value { font-weight: 600; font-size: 16px; color: #606266; &::before { content: '¥'; font-size: 14px; margin-right: 2px; } &::after { content: '点'; font-size: 14px; margin-left: 2px; } } } } .custom-input-section { margin: 16px 0; text-align: center; .custom-amount-input { width: 100%; :deep(.el-input__wrapper) { box-shadow: 0 0 0 1px #dcdfe6 inset; padding: 8px; &:focus-within { box-shadow: 0 0 0 1px #67C23A inset; } } } } .total-amount { background: #f8f9fa; padding: 16px; border-radius: 8px; margin: 20px 0; text-align: center; .label { font-size: 14px; color: #909399; margin-right: 8px; } .value { font-size: 20px; font-weight: 600; color: #67C23A; &::after { content: ''; font-size: 16px; } } } .action-buttons { padding: 16px 24px 24px; display: flex; justify-content: center; gap: 16px; .cancel-btn, .submit-btn { min-width: 120px; border-radius: 8px; padding: 10px 20px; font-weight: 500; transition: all 0.3s; } .submit-btn { background: linear-gradient(135deg, #67C23A, #85ce61); border: none; &:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3); } .el-icon { margin-right: 6px; } } } }

/* 卡片视图布局 */
.users-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; position: relative; padding: 10px; }

/* 加载状态覆盖层 */
.loading-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; background: rgba(255, 255, 255, 0.8); border-radius: 12px; z-index: 10; .loading-icon { font-size: 36px; color: #409EFF; animation: rotate 1.5s linear infinite; margin-bottom: 16px; } }

/* 用户卡片样式 */
.user-card { position: relative; background: white; border-radius: 12px; overflow: hidden; transition: all 0.3s ease; border: 1px solid #ebeef5; display: flex; align-items: stretch; &:hover { transform: translateY(-5px); box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); } &.is-selected { border-color: #409EFF; box-shadow: 0 0 0 1px #409EFF; } .select-area { display: flex; justify-content: center; align-items: flex-start; padding: 16px 0; background: #f8f9fa; width: 40px; } .user-card-content { flex: 1; padding: 16px; display: flex; flex-direction: column; } .user-card-header { display: flex; align-items: center; .user-card-avatar { border: 3px solid #fff; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); } .user-card-info { flex: 1; margin-left: 16px; .user-card-name { font-size: 16px; font-weight: 600; color: #303133; margin-bottom: 4px; } .user-card-id { font-size: 12px; color: #909399; } } .user-card-balance { display: flex; flex-direction: column; align-items: flex-end; .balance-label { font-size: 12px; color: #909399; margin-bottom: 4px; } .balance-amount { font-size: 18px; font-weight: 600; color: #67C23A; } } } .el-divider { margin: 12px 0; } .user-card-details { display: flex; flex-direction: column; gap: 8px; margin-bottom: 16px; .detail-item { display: flex; align-items: center; gap: 8px; font-size: 13px; color: #606266; .el-icon { color: #909399; font-size: 16px; } } } .user-card-footer { margin-top: auto; .card-actions { display: flex; justify-content: space-between; gap: 8px; .el-button { flex: 1; display: flex; align-items: center; justify-content: center; padding: 8px 0; .el-icon { margin-right: 4px; } } } } .card-tag { position: absolute; top: 12px; right: 0; padding: 4px 12px; font-size: 12px; color: white; border-radius: 12px 0 0 12px; &.active { background: #67C23A; } &.normal { background: #E6A23C; } &.inactive { background: #909399; } } }

/* 视图切换控件 */
.view-actions { display: flex; align-items: center; :deep(.el-switch__label) { font-size: 12px; &.is-active { color: #409EFF; } } }

/* 动画 */
@keyframes rotate { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }

/* 响应式调整 */
@media (max-width: 768px) { .hashrate-container { padding: 16px; } .stat-cards { grid-template-columns: 1fr; } .action-toolbar { flex-direction: column; gap: 12px; } .right-info { margin-top: 10px; } .users-grid { grid-template-columns: 1fr; } }
</style>
