<template>
  <div class="auth-form-wrapper">
    <h2 class="form-title">注册</h2>
    <el-form ref="registerRef" :model="registerForm" :rules="registerRules" class="auth-form">
      <el-form-item prop="username" v-if="emileEnabled">
        <el-input
          v-model="registerForm.username"
          placeholder="请输入邮箱"
          :prefix-icon="Message"
        />
      </el-form-item>
      <el-form-item prop="username" v-else-if="phoneEnabled">
        <el-input
          v-model="registerForm.username"
          placeholder="请输入手机号"
          :prefix-icon="Phone"
        />
      </el-form-item>
      <el-form-item prop="username" v-else>
        <el-input
          v-model="registerForm.username"
          placeholder="请输入账号"
          :prefix-icon="User"
        />
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="registerForm.password"
          type="password"
          placeholder="请输入密码"
          :prefix-icon="Lock"
          @keyup.enter="handleRegister"
        />
      </el-form-item>
      <el-form-item prop="confirmPassword">
        <el-input
          v-model="registerForm.confirmPassword"
          type="password"
          placeholder="请确认密码"
          :prefix-icon="Lock"
          @keyup.enter="handleRegister"
        />
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <div class="captcha-wrapper">
          <el-input
            v-model="registerForm.code"
            placeholder="验证码"
            :prefix-icon="Key"
            @keyup.enter="handleRegister"
          />
          <img :src="codeUrl" @click="getCode" class="captcha-img" />
        </div>
      </el-form-item>
      <el-form-item prop="code" v-if="emileEnabled || phoneEnabled">
        <div class="captcha-wrapper">
          <el-input
            v-model="registerForm.code"
            placeholder="验证码"
            :prefix-icon="Key"
            @keyup.enter="handleRegister"
          />
          <el-button class="captcha-btn" @click="sendCode">
            发送验证码
          </el-button>
        </div>
      </el-form-item>
      <el-button :loading="loading" type="primary" class="submit-btn" @click.prevent="handleRegister">
        {{ loading ? '注册中...' : '注册' }}
      </el-button>
      <div class="register-type-options">
        <el-link class="form-link" :underline="false" @click="useEmile()" v-if="!emileEnabled">使用邮箱注册</el-link>
        <el-link class="form-link" :underline="false" @click="usePhone()" v-if="!phoneEnabled">使用手机号注册</el-link>
        <el-link class="form-link" :underline="false" @click="useUsername()" v-if="emileEnabled || phoneEnabled">使用账号注册</el-link>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus"
import { getCodeImg, register, sendEmailCode, verifyEmailCode, sendPhoneCode, verifyPhoneCode } from "@/api/login"
import { getConfigKey } from '@/api/system/config'
import { ref } from "vue"
import { User, Lock, Message, Phone, Key } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { getCurrentInstance } from 'vue'

const emit = defineEmits(['switch-mode'])
const router = useRouter()
const { proxy } = getCurrentInstance()

const registerForm = ref({
  username: "",
  password: "",
  confirmPassword: "",
  code: "",
  uuid: ""
})

// 密码确认验证
const equalToPassword = (rule, value, callback) => {
  if (registerForm.value.password !== value) {
    callback(new Error("两次输入的密码不一致"))
  } else {
    callback()
  }
}

const registerRules = {
  username: [
    { required: true, trigger: "blur", message: "请输入账号" },
    { min: 2, max: 20, message: "账号长度必须介于 2 和 20 之间", trigger: "blur" }
  ],
  password: [
    { required: true, trigger: "blur", message: "请输入密码" },
    { min: 5, max: 20, message: "密码长度必须介于 5 和 20 之间", trigger: "blur" },
    { pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, trigger: "blur", message: "请确认密码" },
    { required: true, validator: equalToPassword, trigger: "blur" }
  ],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
}

const codeUrl = ref("")
const loading = ref(false)
const captchaEnabled = ref(true)
const emileEnabled = ref(false)
const phoneEnabled = ref(false)

// 切换注册方式
function useEmile() {
  captchaEnabled.value = false
  phoneEnabled.value = false
  emileEnabled.value = true
}

function useUsername() {
  getConfigKey("sys.account.captchaEnabled").then(res => {
    captchaEnabled.value = res.msg === 'true'
  })
  phoneEnabled.value = false
  emileEnabled.value = false
}

function usePhone() {
  captchaEnabled.value = false
  phoneEnabled.value = true
  emileEnabled.value = false
}

// 处理注册
async function handleRegister() {
  try {
    const valid = await proxy.$refs.registerRef.validate()
    if (!valid) return
    
    loading.value = true
    
    // 根据注册方式选择验证方法
    let handleRegister = register
    if (emileEnabled.value) {
      registerForm.value.email = registerForm.value.username
      handleRegister = verifyEmailCode
    } else if (phoneEnabled.value) {
      registerForm.value.phone = registerForm.value.username
      handleRegister = verifyPhoneCode
    }
    
    // 注册
    await handleRegister(registerForm.value)
    const username = registerForm.value.username
    
    await ElMessageBox.alert(
      `<font color='red'>恭喜你，您的账号 ${username} 注册成功！</font>`,
      "系统提示",
      {
        dangerouslyUseHTMLString: true,
        type: "success",
      }
    )
    
    emit('switch-mode')
  } catch (error) {
    loading.value = false
    if (captchaEnabled.value) {
      getCode()
    }
  }
}

// 获取验证码
function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img
      registerForm.value.uuid = res.uuid
    }
  })
}

// 发送验证码
function sendCode() {
  if (emileEnabled.value) {
    registerForm.value.email = registerForm.value.username
    sendEmailCode(registerForm.value)
  } else if (phoneEnabled.value) {
    registerForm.value.phonenumber = registerForm.value.username
    sendPhoneCode(registerForm.value)
  }
}

// 初始化
getCode()
</script>

<style lang="scss" scoped>
@use '@/assets/styles/auth-form.scss';
</style> 