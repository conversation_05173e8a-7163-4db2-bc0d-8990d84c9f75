/**
 * @file timeUtils.ts
 * @description 提供了时间格式化和时间线数据处理的核心工具函数。
 * 这些函数负责将来自不同源（如模板、云剪辑项目）的数据，适配成前端剪辑器可用的标准格式。
 */

import { getMediaInfo } from '../api/media';
import type { Timeline, TimelineClip } from '../types/videoEdit';
import type { TemplateInfo, TemplateConfig, RelatedMediaidsData, TemplateImageTrackClip, TemplateVideoTrackClip } from '../types/template';
import type { MediaInfoQuery } from '@/views/tool/MatrixMix/api/media'

/**
 * @method formatRulerTime
 * @description 格式化时间标尺上的时间显示。
 * @param {number} seconds - 秒数。
 * @returns {string} "MM:SS" 格式的字符串。
 */
export function formatRulerTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
}

/**
 * 将秒数格式化为 "MM:SS.ms" 的字符串格式。
 *
 * @description
 * 这个函数专门用于处理视频和音频时间显示，确保即使在秒数无效（如 NaN、负数）的情况下也能优雅地降级，返回 "00:00.000"。
 * 它的核心原理是：
 * 1.  **输入校验**: 首先检查传入的 `seconds` 是否为有效数字且大于等于0，防止后续计算出错。
 * 2.  **提取各部分**:
 *     - 分钟: `Math.floor(seconds / 60)`
 *     - 秒: `Math.floor(seconds % 60)`
 *     - 毫秒: `Math.round((seconds - Math.floor(seconds)) * 1000)`，通过取小数部分并乘以1000来得到。
 * 3.  **格式化补零**: `padStart()` 是关键步骤。它能确保个位数的分钟和秒被格式化为两位数（如"05"），
 *     同时确保毫秒部分始终为三位数（如 "023" 或 "120"），从而保持 "MM:SS.ms" 的对齐格式，提升视觉效果。
 *
 * @param {number} seconds - 需要被格式化的总秒数。
 * @returns {string} - 返回 "MM:SS.ms" 格式的时间字符串。
 */
export function formatTime(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) return '00:00.000';
  const min = Math.floor(seconds / 60);
  const sec = Math.floor(seconds % 60);
  const ms = Math.round((seconds - Math.floor(seconds)) * 1000);
  return `${String(min).padStart(2, '0')}:${String(sec).padStart(2, '0')}.${String(ms).padStart(3, '0')}`;
}

/**
 * 格式化用于UI显示的当前时间，实现了播放时"冻结"最后一位毫秒的视觉效果。
 *
 * @param currentTime 当前的精确时间（秒）。
 * @param previousTime 动画循环中上一帧的精确时间（秒）。
 * @param isPlaying 当前是否处于播放状态。
 * @returns {string} 返回处理后的 "MM:SS.ms" 格式的时间字符串。
 */
export function formatDisplayTime(currentTime: number, previousTime: number, isPlaying: boolean): string {
  // 1. 如果当前处于暂停状态，则直接返回当前时间
  if (!isPlaying) {
    return formatTime(currentTime);
  }
  // 2.如果处于播放状态,使用上一帧的时间来决定"冻结的数字"
  const previousTimeString = formatTime(previousTime);
  // 3.截取上一帧最后一位数字
  const frozenDigit = previousTimeString.slice(-1);
  // 4.获取当前帧的精确时间
  const currentTimeString = formatTime(currentTime);
  // 5.替换最后一位数字为冻结的数字,并返回
  return currentTimeString.slice(0, -1) + frozenDigit;
}

/**
 * 将模板的Config（菜谱）转换为可被剪辑器直接使用的标准Timeline（制作好的菜）。
 *
 * @description
 * 模板的 `Config` 是一个"抽象"的时间线，其中包含了变量（如 `$Video`）和带默认值的参数（如 `"$X:40"`）。
 * 此函数的核心职责就是用真实数据去"填充"这个模板，生成一个具体的、可播放的时间线。
 *
 * 原理：
 * 1.  **解析数据**: 首先，安全地解析模板中的 `Config` 和 `RelatedMediaids` 两个JSON字符串。`RelatedMediaids` 提供了模板关联的默认素材ID。
 * 2.  **建立替换表**: 创建一个简单的查找表 (substitutions)，用于快速替换像 `$Video` 这样的通用变量。
 * 3.  **递归遍历**: 定义一个递归函数 `replaceVariables`，它会深度遍历整个 `Config` 对象（深拷贝后）的每一个角落。
 * 4.  **执行替换**: 在遍历过程中，如果遇到一个字符串值：
 *     - **检查是否为变量**: 判断字符串是否以 `$` 开头。
 *     - **情况一：简单变量**: 如果是 `$Video` 这样的简单变量，直接在替换表中查找并替换。
 *     - **情况二：带默认值的变量**: 如果是 `"$X:40"` 这样的格式，通过分割字符串，提取出冒号后面的默认值进行替换。
 * 5.  **返回标准Timeline**: 经过替换后，整个对象不再含有变量，其结构与标准的 `Timeline` 完全兼容，可以直接返回给剪辑器使用。
 *
 * @param {TemplateInfo} template - 从API获取的完整模板信息对象。
 * @returns {Timeline} - 返回一个标准的、不含变量的Timeline对象。
 * @throws {Error} - 如果解析或转换过程中发生错误，则抛出异常，由调用方捕获处理。
 */
export function convertTemplateToTimeline(template: TemplateInfo): Timeline {
  if (!template.Config) {
    throw new Error("模板配置(Config)为空，无法进行转换。");
  }

  try {
    const config: TemplateConfig = JSON.parse(template.Config);
    const relatedMedia: RelatedMediaidsData = template.RelatedMediaids ? JSON.parse(template.RelatedMediaids) : {};

    const timeline = JSON.parse(JSON.stringify(config));

    const substitutions: Record<string, string | undefined> = {
      '$Video': relatedMedia.video?.[0],
      '$Audio': relatedMedia.audio?.[0],
      '$Image': relatedMedia.image?.[0],
    };

    function replaceVariables(obj: any) {
      if (!obj) return;
      for (const key in obj) {
        if (typeof obj[key] === 'object') {
          replaceVariables(obj[key]);
        } else if (typeof obj[key] === 'string') {
          const value = obj[key];
          if (value.startsWith('$')) {
            if (substitutions[value]) {
              obj[key] = substitutions[value];
            } else if (value.includes(':')) {
              obj[key] = value.split(':')[1];
            }
          }
        }
      }
    }

    replaceVariables(timeline);

    // TODO: 将来在这里处理更复杂的数组参数 (Sys_Type: 'ArrayItems')

    return timeline as Timeline;

  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : String(e);
    console.error("解析或转换模板配置失败:", errorMessage);
    throw new Error(`解析或转换模板配置失败: ${errorMessage}`);
  }
}

/**
 * "丰富化"一个时间线对象。
 * 它会获取时间线中所有片段的详细媒体信息（如文件名、播放URL），然后将这些信息附加回原始的时间线对象上。
 *
 * @description
 * 一个原始的时间线对象只包含 `MediaId`，这不足以进行播放和UI展示。
 * 此函数通过一个"抓取-附加"的过程，为时间线注入它所需的完整数据。
 *
 * 原理：
 * 1.  **收集ID**: 遍历时间线中的所有视频和音频轨道，提取出所有不重复的 `MediaId`。使用 `Set` 来自动去重。
 * 2.  **并行请求**: 将所有唯一的 `MediaId` 包装成 API 请求，然后使用 `Promise.all` 来并行（同时）发起所有请求。
 *     这大大提升了数据获取效率，避免了串行请求的漫长等待。
 * 3.  **创建查找表**: 将 `Promise.all` 返回的结果（一个媒体信息数组）转换成一个 `Map` 对象。
 *     这个 Map 的键是 `MediaId`，值是包含 `fileName` 和 `fileUrl` 的对象。Map 提供了近乎 O(1) 的高效查找。
 * 4.  **注入数据**: 再次遍历时间线的每一个片段，使用 `MediaId` 作为钥匙，在 Map 中查找对应的详细信息，
 *     然后将 `FileName` 和 `FileUrl` 这两个新属性动态地添加到片段对象上。
 *
 * @param {Timeline} timeline - 一个"原始"的，只包含基础ID的时间线对象。
 * @returns {Promise<Timeline>} - 返回一个"丰富"的，包含了额外媒体信息的同一个时间线对象。
 * @throws {Error} - 如果API请求失败或处理过程中发生错误，则抛出异常。
 */
export async function enrichTimelineWithMediaDetails(timeline: Timeline): Promise<Timeline> {
  const allClips: TimelineClip[] = [
    ...(timeline.VideoTracks?.[0]?.VideoTrackClips || []),
    ...(timeline.AudioTracks?.[0]?.AudioTrackClips || []),
  ];

  if (allClips.length === 0) return timeline;

  const uniqueMediaIds = [...new Set(
    allClips.map(clip => clip.MediaId)
      .filter(id => id && !id.startsWith('icepublic-'))
  )];
  if (uniqueMediaIds.length === 0) return timeline;

  try {
    const mediaInfos = await Promise.all(
      uniqueMediaIds.map(id => getMediaInfo({ mediaId: id } as MediaInfoQuery))
    );

    const mediaDetailsMap = new Map<string, { fileName: string; fileUrl: string }>();
    mediaInfos.forEach(info => {
      const mediaId = info.MediaInfo?.MediaId;
      const fileInfo = info.MediaInfo?.FileInfoList?.[0]?.FileBasicInfo;
      if (mediaId && fileInfo?.FileUrl && fileInfo?.FileName) {
        mediaDetailsMap.set(mediaId, {
          fileName: fileInfo.FileName,
          fileUrl: fileInfo.FileUrl,
        });
      }
    });

    const enrich = (clip: TimelineClip) => {
      const details = mediaDetailsMap.get(clip.MediaId);
      if (details) {
        (clip as any).FileName = details.fileName;
        (clip as any).FileUrl = details.fileUrl;
      }
    };

    timeline.VideoTracks?.[0]?.VideoTrackClips.forEach(enrich);
    timeline.AudioTracks?.[0]?.AudioTrackClips.forEach(enrich);

    return timeline;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("获取媒体素材详细信息失败:", errorMessage);
    throw new Error(`获取媒体素材详细信息失败: ${errorMessage}`);
  }
}

/**
 * @method convertTimelineToTemplateConfig
 * @description 将前端剪辑器使用的 `Timeline` 对象转换为后端模板服务所需的 `TemplateConfig` JSON 字符串。
 *              这个转换过程会移除前端为了显示和编辑方便而添加的临时属性（如 `FileName` 和 `FileUrl`），
 *              确保输出的配置只包含模板核心数据结构。
 *
 * @param {Timeline} timeline - 当前时间线数据，包含视频、音频、字幕、图片轨道及其片段信息。
 * @returns {string} - 转换后的 `TemplateConfig` JSON 字符串，可以直接作为后端API的 `Config` 参数。
 *
 * @principle
 * 1.  **深拷贝**: 避免直接修改传入的 `timeline` 对象，通过深拷贝创建一个全新的对象用于转换。
 * 2.  **属性清理**: 定义一个 `cleanClip` 辅助函数，用于从每个片段中删除前端特有的冗余属性。
 * 3.  **遍历映射**: 遍历时间线中的所有媒体轨道（视频、音频、字幕、图片），对每个轨道内的所有片段应用 `cleanClip` 函数，
 *     生成一个符合 `TemplateConfig` 结构的新片段数组。
 * 4.  **序列化**: 将清理后的时间线对象转换为 JSON 字符串。
 */
export function convertTimelineToTemplateConfig(timeline: Timeline): string {
  // 1. 深拷贝时间线对象
  const templateConfig: TemplateConfig = JSON.parse(JSON.stringify(timeline));

  // 2. 定义一个辅助函数 `cleanClip` 用于清理片段属性
  const cleanClip = (clip: TimelineClip) => {
    // 使用对象展开运算符 `{ ...clip }` 创建一个片段的浅拷贝。
    const cleanedClip: Partial<TimelineClip> = { ...clip };
    // 删除前端为了显示文件名而添加的 `FileName` 属性。
    delete cleanedClip.FileName;
    // 删除前端为了播放视频/音频而添加的 `FileUrl` 属性。
    delete cleanedClip.FileUrl;
    // 返回清理后的片段对象。
    return cleanedClip;
  };

  // 3. 遍历并清理所有轨道中的片段
  if (templateConfig.VideoTracks?.[0]?.VideoTrackClips) {
    templateConfig.VideoTracks[0].VideoTrackClips = templateConfig.VideoTracks[0].VideoTrackClips.map(cleanClip as (clip: any) => TemplateVideoTrackClip);
  }
  // 清理音频轨道片段
  if (templateConfig.AudioTracks?.[0]?.AudioTrackClips) {
    templateConfig.AudioTracks[0].AudioTrackClips = templateConfig.AudioTracks[0].AudioTrackClips.map(cleanClip as (clip: any) => TemplateVideoTrackClip);
  }
  // 清理字幕轨道片段
  if (templateConfig.SubtitleTracks?.[0]?.SubtitleTrackClips) {
    // 字幕片段可能也有 MediaId 或其他需要清理的属性。
    templateConfig.SubtitleTracks[0].SubtitleTrackClips = templateConfig.SubtitleTracks[0].SubtitleTrackClips.map(cleanClip as (clip: any) => TemplateVideoTrackClip);
  }
  // 清理图片轨道片段
  if (templateConfig.ImageTracks?.[0]?.ImageTrackClips) {
    // 图片片段可能包含 ImageId，且通常没有 FileUrl/FileName，但为了统一处理，也通过 cleanClip 处理。
    // 使用 `TemplateImageTrackClip` 进行类型断言，因为图片片段的结构可能与视频/音频有所不同（例如使用 ImageId 而不是 MediaId）。
    templateConfig.ImageTracks[0].ImageTrackClips = templateConfig.ImageTracks[0].ImageTrackClips.map(cleanClip as (clip: any) => TemplateImageTrackClip);
  }
  // 4. 将清理后的对象转换为 JSON 字符串
  return JSON.stringify(templateConfig);
}

/**
 * @method extractRelatedMediaids
 * @description 从前端剪辑器使用的 `Timeline` 对象中提取所有关联的媒体素材ID，
 *              并将其格式化为后端模板服务所需的 `RelatedMediaids` JSON 字符串。
 *              这个函数确保只收集唯一的媒体ID，并按类型（视频、音频、图片、字幕）进行分类。
 *
 * @param {Timeline} timeline - 当前时间线数据，包含视频、音频、字幕、图片轨道及其片段信息。
 * @returns {string} - 格式化后的 `RelatedMediaids` JSON 字符串，可以直接作为后端API的 `RelatedMediaids` 参数。
 */
export function extractRelatedMediaids(timeline: Timeline): string {
  // 1. 初始化 `relatedMediaids` 对象
  const relatedMediaids: RelatedMediaidsData = {
    video: [],
    audio: [],
    image: [],
    subtitle: [] // 包含字幕，因为字幕可能也关联媒体ID
  };

  // 2. 收集视频媒体ID
  const videoMediaIds = new Set<string>();
  if (timeline.VideoTracks?.[0]?.VideoTrackClips) {
    timeline.VideoTracks[0].VideoTrackClips.forEach(clip => {
      if (clip.MediaId) videoMediaIds.add(clip.MediaId);
    });
  }
  relatedMediaids.video = Array.from(videoMediaIds);

  // 3. 收集音频媒体ID (逻辑同视频媒体ID)
  const audioMediaIds = new Set<string>();
  if (timeline.AudioTracks?.[0]?.AudioTrackClips) {
    timeline.AudioTracks[0].AudioTrackClips.forEach(clip => {
      if (clip.MediaId) audioMediaIds.add(clip.MediaId);
    });
  }
  relatedMediaids.audio = Array.from(audioMediaIds);

  // 4. 收集字幕媒体ID (逻辑同视频/音频媒体ID)
  const subtitleMediaIds = new Set<string>();
  if (timeline.SubtitleTracks?.[0]?.SubtitleTrackClips) {
    timeline.SubtitleTracks[0].SubtitleTrackClips.forEach(clip => {
      if (clip.MediaId) subtitleMediaIds.add(clip.MediaId);
    });
  }
  relatedMediaids.subtitle = Array.from(subtitleMediaIds);

  // 5. 收集图片媒体ID (注意：图片片段使用 `ImageId`)
  const imageMediaIds = new Set<string>();
  if (timeline.ImageTracks?.[0]?.ImageTrackClips) {
    timeline.ImageTracks[0].ImageTrackClips.forEach(clip => {
      if (clip.ImageId) imageMediaIds.add(clip.ImageId);
    });
  }
  relatedMediaids.image = Array.from(imageMediaIds);

  // 6. 将最终的 `relatedMediaids` 对象转换为 JSON 字符串
  return JSON.stringify(relatedMediaids);
} 