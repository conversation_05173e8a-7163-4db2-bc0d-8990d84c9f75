/**
 * 时间轴片段处理工具类
 * 负责片段样式计算、选择状态等功能
 */

import type { ProcessedClip } from './timelineTrackUtils';

/**
 * 计算片段在时间轴上的样式
 */
export function getClipStyle(clip: ProcessedClip, PIXELS_PER_SECOND: number = 50): Record<string, string> {
  return {
    left: `${clip.start * PIXELS_PER_SECOND}px`,
    width: `${clip.duration * PIXELS_PER_SECOND}px`,
  };
}

/**
 * 检查片段是否被选中
 */
export function isClipSelected(
  type: 'video' | 'audio' | 'subtitle',
  trackIndex: number,
  clipIndex: number,
  selectedClip: { type: string | null; trackIndex: number | null; clipIndex: number | null }
): boolean {
  return selectedClip.type === type && 
         selectedClip.trackIndex === trackIndex && 
         selectedClip.clipIndex === clipIndex;
}

/**
 * 选中片段
 */
export function selectClipHandler(
  type: 'video' | 'audio' | 'subtitle',
  trackIndex: number,
  clipIndex: number,
  setSelectedClip: (payload: { type: string; trackIndex: number; clipIndex: number }) => void
) {
  setSelectedClip({ type, trackIndex, clipIndex });
}

/**
 * 检查是否可以切割片段
 */
export function canCutClip(selectedClip: { type: string | null; trackIndex: number | null; clipIndex: number | null }): boolean {
  return selectedClip.type !== null && 
         selectedClip.trackIndex !== null &&
         selectedClip.clipIndex !== null;
}

/**
 * 检查是否可以删除片段
 */
export function canDeleteClip(selectedClip: { type: string | null; trackIndex: number | null; clipIndex: number | null }): boolean {
  return selectedClip.type !== null && 
         selectedClip.trackIndex !== null &&
         selectedClip.clipIndex !== null;
}
