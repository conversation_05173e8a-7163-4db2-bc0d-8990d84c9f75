<script setup>
import { listGoods } from "@/api/platform/goods";
import { useRoute, useRouter } from "vue-router";
import { computed, nextTick } from "vue";
const projectId = computed(() => useRoute().params.projectId);
const router = useRouter();
const goodsList = ref([])
const total = ref(0)
const goodsTable = ref(null)
const props = defineProps({
    ids: {
        type: Array
    },
})
const queryParams = reactive({
  pageNum: 1,
  pageSize: 5,
  projectId:projectId.value
});
const idsSelect = ref({})
const emit = defineEmits(['select']);
function handleSelectionChange(select) {
    idsSelect.value = {}
    for (let x of select) {
        idsSelect.value[x.goodsId] = x.goodsName;
    }
    emit('select', idsSelect.value)
}
function getListGoods() {
    listGoods(queryParams).then(res => {
        console.log("Asas",res);
        
        goodsList.value = res.rows;
        total.value = res.total;
        handleIdsChange();
    });
}
function handleIdsChange() {
    goodsList.value.forEach(row=>{
        goodsTable.value.toggleRowSelection(row, false);
    })
    if(props.ids){
        props.ids.forEach(id => {
            goodsList.value.forEach(row => {
                if (row.goodsId === id) {
                    goodsTable.value.toggleRowSelection(row, true);
                }
            });
        });
        
    
    }
}

watch(() => props.ids, handleIdsChange, {immediate:true});
onMounted(() => {
    getListGoods();
});
</script>
<template>
        <el-table :data="goodsList" ref="goodsTable" @selection-change="handleSelectionChange" max-height="200" :row-key="(row)=>row.goodsId" >
            <el-table-column type="selection" width="80" align="center" reserve-selection />
            <el-table-column prop="goodsName" min-width="100" label="产品" />
        </el-table>
        <el-pagination
            class="scene"
            v-show="total>0"
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :size="size"
            :disabled="disabled"
            :background="background"
            layout="prev, pager, next"
            :total="total"
            @size-change="getListGoods"
            @current-change="getListGoods"
            />
</template>
<style lang="scss" scoped>
.scene{
    display: flex;
    justify-content: center;
}
</style>