import request from '@/utils/request'

// 查询机器管理列表
export function listCode(query) {
  return request({
    url: '/platform/code/list',
    method: 'get',
    params: query
  })
}

// 查询机器管理详细
export function getCode(machineCodeId) {
  return request({
    url: '/platform/code/' + machineCodeId,
    method: 'get'
  })
}

// 新增机器管理
export function addCode(data) {
  return request({
    url: '/platform/code',
    method: 'post',
    data: data
  })
}

// 修改机器管理
export function updateCode(data) {
  return request({
    url: '/platform/code',
    method: 'put',
    data: data
  })
}

// 删除机器管理
export function delCode(machineCodeId) {
  return request({
    url: '/platform/code/' + machineCodeId,
    method: 'delete'
  })
}
