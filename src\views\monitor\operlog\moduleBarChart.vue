<template>
    <!-- 平台模块堆叠柱状图 -->
    <el-card shadow="always" class="box-card hover-card">
        <template #header>
            <span>系统模块统计</span>
        </template>
        <div id="moduleBarChart" style="width: 100%; height: 400px;"></div>
    </el-card>
</template>
<script setup name="NewModuleBarChart">
import { ref, watch, onMounted, getCurrentInstance } from 'vue';
import * as echarts from 'echarts';
import { business } from "@/api/monitor/operlog";

const { proxy } = getCurrentInstance();
const props = defineProps({
    queryParams: Object,
    dateRange: Array
});
const moduleOperationStats = ref([]);
const operationTypes = {
    1: 'add',
    2: 'edit',
    3: 'delete',
    4: 'authorization',
    5: 'export',
    6: 'import',
    7: 'exit',
    8: 'generate',
    9: 'clean',
    0: 'else'
};

/** 查询业务信息 */
function getList() {
    business(proxy.addDateRange(props.queryParams, props.dateRange)).then(response => {
        moduleOperationStats.value = response.data.moduleOperationStats;
        updateModuleBarChart();
    });
}

/** 渲染堆叠柱状图 */
function updateModuleBarChart() {
    const moduleMap = new Map();

    // 遍历操作日志数据，统计各模块的操作次数
    moduleOperationStats.value.forEach(item => {
        if (!moduleMap.has(item.title)) {
            moduleMap.set(item.title, { 
                add: { success: 0, fail: 0 },  //默认初始都是0
                edit: { success: 0, fail: 0 }, 
                delete: { success: 0, fail: 0 }, 
                authorization: { success: 0, fail: 0 }, 
                export: { success: 0, fail: 0 }, 
                import: { success: 0, fail: 0 }, 
                exit: { success: 0, fail: 0 }, 
                generate: { success: 0, fail: 0 }, 
                clean: { success: 0, fail: 0 }, 
                else: { success: 0, fail: 0 } 
            });
        }
        const module = moduleMap.get(item.title);
        const type = operationTypes[item.business_type] || 'else';
        module[type].success += item.success_count || 0;
        module[type].fail += item.failure_count || 0;
    });

    const categories = Array.from(moduleMap.keys());
    const chartData = categories.map(category => moduleMap.get(category));

    // 初始化 ECharts 实例
    const chart = echarts.init(document.getElementById('moduleBarChart'));
    chart.setOption({
        tooltip: { 
            trigger: 'axis', 
            axisPointer: { 
                type: 'shadow',
                shadowStyle: { color: 'rgba(150, 150, 150 ,0.04)'}
            },
            confine: true,
            formatter: function (params) {
                let result = params[0].name + '<br/>';
                params.forEach(item => {
                    const data = item.data;
                    result += item.marker + item.seriesName + ': 成功 ' + data.success + ' 失败 ' + data.fail + '<br/>';
                });
                return result;
            }
        },
        legend: { data: ['新增', '修改', '删除', '授权', '导出', '导入', '强退', '生成代码', '清空数据', '其他'] },
        xAxis: { type: 'category', data: categories },
        yAxis: { type: 'value' },
        series: [
            { name: '新增', type: 'bar', stack: '总量', data: chartData.map(data => ({ value: data.add.success + data.add.fail, success: data.add.success, fail: data.add.fail })), itemStyle: { color: '#67C23A' }, emphasis: { focus: 'series' } },
            { name: '修改', type: 'bar', stack: '总量', data: chartData.map(data => ({ value: data.edit.success + data.edit.fail, success: data.edit.success, fail: data.edit.fail })), itemStyle: { color: '#1E90FF' }, emphasis: { focus: 'series' } },
            { name: '删除', type: 'bar', stack: '总量', data: chartData.map(data => ({ value: data.delete.success + data.delete.fail, success: data.delete.success, fail: data.delete.fail })), itemStyle: { color: '#ec0000' }, emphasis: { focus: 'series' } },
            { name: '授权', type: 'bar', stack: '总量', data: chartData.map(data => ({ value: data.authorization.success + data.authorization.fail, success: data.authorization.success, fail: data.authorization.fail })), itemStyle: { color: '#FF69B4' }, emphasis: { focus: 'series' } },
            { name: '导出', type: 'bar', stack: '总量', data: chartData.map(data => ({ value: data.export.success + data.export.fail, success: data.export.success, fail: data.export.fail })), itemStyle: { color: '#FFD700' }, emphasis: { focus: 'series' } },
            { name: '导入', type: 'bar', stack: '总量', data: chartData.map(data => ({ value: data.import.success + data.import.fail, success: data.import.success, fail: data.import.fail })), itemStyle: { color: '#8A2BE2' }, emphasis: { focus: 'series' } },
            { name: '强退', type: 'bar', stack: '总量', data: chartData.map(data => ({ value: data.exit.success + data.exit.fail, success: data.exit.success, fail: data.exit.fail })), itemStyle: { color: '#FF4500' }, emphasis: { focus: 'series' } },
            { name: '生成代码', type: 'bar', stack: '总量', data: chartData.map(data => ({ value: data.generate.success + data.generate.fail, success: data.generate.success, fail: data.generate.fail })), itemStyle: { color: '#2E8B57' }, emphasis: { focus: 'series' } },
            { name: '清空数据', type: 'bar', stack: '总量', data: chartData.map(data => ({ value: data.clean.success + data.clean.fail, success: data.clean.success, fail: data.clean.fail })), itemStyle: { color: '#000000' }, emphasis: { focus: 'series' } },
            { name: '其他', type: 'bar', stack: '总量', data: chartData.map(data => ({ value: data.else.success + data.else.fail, success: data.else.success, fail: data.else.fail })), itemStyle: { color: '#A9A9A9' }, emphasis: { focus: 'series' } }
        ]
    });
    chart.resize();
}

onMounted(() => { 
    getList();
});
watch([props.queryParams, props.dateRange], () => {  
    getList();
});
</script>
<style scoped>
.app-container {
    padding: 20px;
    background-color: #f0f2f5;
}

.box-card {
    margin-bottom: 20px;
    border-radius: 15px;
    transition: transform 0.3s, box-shadow 0.3s;
}

.box-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 2px 12px rgba(4, 116, 245, 0.3);
}
</style>
