@use './variables.module.scss';

#app {

  .main-container {
    min-height: 100%;
    transition: all .3s ease;
    position: relative;
    background-color: #f8f9fa;
    padding: 16px;
  }

  .sidebarHide {
    margin-left: 0!important;
  }

  .sidebar-container {
    -webkit-transition: width .28s;
    transition: width 0.28s;
    width: variables.$base-sidebar-width !important;
    background-color: #ffffff;
    height: 100%;
    font-size: 0px;
    z-index: 1001;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
      transition: all .3s ease;
      color: var(--el-menu-text-color);
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
      background: transparent;
    }

    .el-menu-item, .menu-title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      height: 44px;
      line-height: 44px;
      transition: all .3s ease;
      border-radius: 8px;
      color: var(--el-menu-text-color);
      
      &:hover {
        background-color: #f1f3f4 !important;
        color: var(--el-menu-active-color);
        
        .svg-icon, span {
          color: var(--el-menu-active-color);
        }
      }
      
      &.is-active {
        background-color: #e8f0fe !important;
        color: var(--el-menu-active-color);
        font-weight: 500;
        
        .svg-icon, span {
          color: var(--el-menu-active-color);
        }
      }
    }

    .el-menu-item .el-menu-tooltip__trigger {
      display: inline-block !important;
    }

    // menu hover
    .sub-menu-title-noDropdown,
    .el-sub-menu__title {
      height: 44px;
      line-height: 44px;
      border-radius: 8px;
      color: var(--el-menu-text-color);
      
      &:hover {
        background-color: #f1f3f4 !important;
        color: var(--el-menu-active-color);
        
        .svg-icon, span {
          color: var(--el-menu-active-color);
        }
      }

      &.is-active {
        background-color: #e8f0fe !important;
        color: var(--el-menu-active-color);
        font-weight: 500;
        
        .svg-icon, span {
          color: var(--el-menu-active-color);
        }
      }
    }

    & .theme-dark .is-active > .el-sub-menu__title {
      color: variables.$base-menu-color-active !important;
    }

    & .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      
      &:hover {
        background-color: #f1f3f4 !important;
        color: var(--el-menu-active-color);
        
        .svg-icon, span {
          color: var(--el-menu-active-color);
        }
      }
    }

    & .theme-dark .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: variables.$base-sub-menu-background !important;

      &:hover {
        background-color: variables.$base-sub-menu-hover !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .sub-menu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;
      }
    }

    .el-sub-menu {
      overflow: hidden;

      &>.el-sub-menu__title {
        padding: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .el-sub-menu__icon-arrow {
          display: none;
        }

        .svg-icon {
          margin: 0;
        }
      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        &>.el-sub-menu__title {
          .svg-icon {
            margin: 0;
            width: 24px;
            text-align: center;
          }
          
          >span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }

      .el-menu-item {
        .el-menu-tooltip__trigger {
          padding: 0 10px !important;
          justify-content: center;
        }
      }
    }
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: variables.$base-sidebar-width !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(0 - variables.$base-sidebar-width, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-sub-menu>.el-sub-menu__title,
  .el-menu-item {
    &:hover {
      background-color: #f1f3f4 !important;
      color: var(--el-menu-active-color);
    }
  }

  // the scroll bar appears when the sub-menu is too long
  >.el-menu--popup {
    max-height: calc(100vh - 100px);
    overflow-y: auto;
    background: #ffffff;
    border-radius: 4px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);

    .el-menu-item, .el-sub-menu__title {
      height: 44px;
      line-height: 44px;
      padding: 0 16px !important;
      margin: 4px;
      border-radius: 4px;
      min-width: auto !important;
      
      &:hover {
        background-color: #f1f3f4 !important;
        color: var(--el-menu-active-color);
      }
    }

    .el-sub-menu__title {
      padding-right: 34px !important;
    }

    .el-sub-menu .el-menu-item {
      padding-left: 40px !important;
    }

    &::-webkit-scrollbar-track-piece {
      background: #f1f3f4;
    }

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0,0,0,0.2);
      border-radius: 4px;
    }
  }
}
