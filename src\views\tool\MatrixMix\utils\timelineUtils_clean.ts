/**
 * 时间轴工具函数 - 精简版
 * 只保留实际使用的函数
 */

export type TrackType = 'video' | 'audio' | 'subtitle';

export interface DragCalculationResult {
  targetTrackIndex: number | null;
  isValidDrop: boolean;
  isNewTrack: boolean;
  insertPosition?: 'above' | 'below' | 'replace';
  snapZone?: 'top' | 'center' | 'bottom';
}

export interface ProcessedTrack {
  originalIndex: number;
  clips: any[];
  uiIndex?: number;
  isHighlighted?: boolean;
  insertPosition?: string;
}

/**
 * 轨道显示排序函数 - 按照视频编辑软件习惯排序
 * 字幕轨道和视频轨道显示在上方，轨道序号大的在上面（图层层级高）
 */
export function sortTracksForDisplay(tracks: ProcessedTrack[]): ProcessedTrack[] {
  // 深拷贝避免修改原数组
  const sortedTracks = [...tracks];
  
  // 按照originalIndex降序排列（序号大的在上面）
  return sortedTracks.sort((a, b) => {
    // 预览轨道也参与排序，根据其 originalIndex 排序
    return b.originalIndex - a.originalIndex;
  });
}

/**
 * 轨道逆序恢复函数 - 将显示顺序转换回原始数据顺序
 * 用于后续数据处理时恢复正常的数组索引顺序
 */
export function restoreOriginalTrackOrder(tracks: ProcessedTrack[]): ProcessedTrack[] {
  // 深拷贝避免修改原数组
  const restoredTracks = [...tracks];
  
  // 按照originalIndex升序排列（恢复原始顺序）
  return restoredTracks.sort((a, b) => {
    // 预览轨道也参与排序，根据其 originalIndex 排序
    return a.originalIndex - b.originalIndex;
  });
}

import { processTrackData } from './timelineTrackUtils';

export { processTrackData } from './timelineTrackUtils';

/**
 * 智能轨道拖拽计算，参考专业剪辑软件的交互逻辑
 */
export function smartTrackDragCalculation(params: {
  mouseY: number;
  trackType: TrackType;
  scrollAreaRect: DOMRect;
  scrollTop: number;
  trackHeight: number;
  displayTracks: any[];
  allTracks: any[];
  sourceTrackIndex?: number;
  dragStartY?: number;
  isDraggingFromLibrary?: boolean;
}): DragCalculationResult {
  const {
    mouseY,
    trackType,
    scrollAreaRect,
    scrollTop,
    trackHeight,
    displayTracks,
    allTracks,
    sourceTrackIndex,
    dragStartY,
    isDraggingFromLibrary = false
  } = params;

  const relativeY = mouseY - scrollAreaRect.top + scrollTop;
  
  // 如果没有任何轨道，创建第一个轨道
  if (displayTracks.length === 0) {
    return {
      targetTrackIndex: 0,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'below',
      snapZone: 'center'
    };
  }

  // 计算所有轨道的边界区域
  const trackRegions = displayTracks.map((track, uiIndex) => {
    const top = uiIndex * trackHeight;
    const bottom = top + trackHeight;
    const center = top + trackHeight / 2;
    
    return {
      uiIndex,          // DOM渲染位置索引（用于坐标计算）
      track,
      top,
      center, 
      bottom,
      originalIndex: track.originalIndex  // 原始轨道索引（用于业务逻辑）
    };
  });

  // 添加拖拽区域边界（用于创建新轨道）
  const topBoundary = trackRegions[0]?.top || 0;
  const bottomBoundary = trackRegions[trackRegions.length - 1]?.bottom || trackHeight;
  
  // 定义吸附阈值
  const snapThreshold = trackHeight * 0.25;
  const insertThreshold = trackHeight * 0.3;
  
  // 拖拽到轨道区域上方 - 创建新轨道到顶部
  if (relativeY < topBoundary - snapThreshold) {
    return {
      targetTrackIndex: 0,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'above',
      snapZone: 'top'
    };
  }
  
  // 拖拽到轨道区域下方 - 创建新轨道到底部
  if (relativeY > bottomBoundary + snapThreshold) {
    return {
      targetTrackIndex: allTracks.length,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'below',
      snapZone: 'bottom'
    };
  }

  // 在轨道区域内 - 寻找最佳匹配
  let bestMatch: any = null;
  let minDistance = Infinity;
  let insertMode = false;

  // 首先确定鼠标在哪个轨道区域内
  for (const region of trackRegions) {
    if (relativeY >= region.top && relativeY <= region.bottom) {
      bestMatch = region;
      break;
    }
  }

  // 如果没有找到精确匹配，找最近的轨道
  if (!bestMatch) {
    for (const region of trackRegions) {
      const distanceToRegion = Math.min(
        Math.abs(relativeY - region.top),
        Math.abs(relativeY - region.bottom)
      );
      
      if (distanceToRegion < minDistance) {
        minDistance = distanceToRegion;
        bestMatch = region;
      }
    }
  }

  if (!bestMatch) {
    return { targetTrackIndex: null, isValidDrop: false, isNewTrack: false };
  }

  // 计算拖拽意图 - 基于绝对坐标轴判定
  const dragDirection = sourceTrackIndex !== undefined && dragStartY !== undefined ?
    (mouseY > dragStartY ? 'down' : 'up') : null;
  
  // 使用绝对坐标判定插入位置，而不是相对于轨道内部的位置
  const trackUpperBoundary = bestMatch.top + trackHeight * 0.25;  // 轨道上边界向下25%
  const trackLowerBoundary = bestMatch.bottom - trackHeight * 0.25;  // 轨道下边界向上25%
  
  // 智能判断最终操作
  let finalResult: DragCalculationResult;
  
  if (relativeY <= trackUpperBoundary) {
    // 鼠标在轨道上方25%区域 - 插入到上方
    finalResult = {
      targetTrackIndex: bestMatch.originalIndex,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'above',
      snapZone: 'top'
    };
  } else if (relativeY >= trackLowerBoundary) {
    // 鼠标在轨道下方25%区域 - 插入到下方
    finalResult = {
      targetTrackIndex: bestMatch.originalIndex,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'below',
      snapZone: 'bottom'
    };
  } else {
    // 鼠标在轨道中央区域 - 替换到现有轨道
    finalResult = {
      targetTrackIndex: bestMatch.originalIndex >= 0 ? bestMatch.originalIndex : 0,
      isValidDrop: true,
      isNewTrack: bestMatch.originalIndex === -1,
      insertPosition: 'replace',
      snapZone: 'center'
    };
  }

  // 从素材库拖拽时，优先创建新轨道
  if (isDraggingFromLibrary && finalResult.insertPosition === 'replace') {
    // 根据拖拽位置决定插入方向
    const positionInTrack = (relativeY - bestMatch.top) / trackHeight;
    if (positionInTrack < 0.5) {
      finalResult.insertPosition = 'above';
      finalResult.isNewTrack = true;
    } else {
      finalResult.insertPosition = 'below'; 
      finalResult.isNewTrack = true;
      finalResult.targetTrackIndex = (finalResult.targetTrackIndex || 0) + 1;
    }
  }

  return finalResult;
}

/**
 * 根据鼠标位置精确计算目标轨道索引，支持轨道间插入和吸附，参考专业剪辑软件的拖拽行为
 */
export function calculateTargetTrackIndex(
  mouseY: number,
  trackType: TrackType,
  scrollAreaRect: DOMRect,
  scrollTop: number,
  trackHeight: number,
  displayTracks: any[],
  allTracks: any[],
  sourceTrackIndex?: number
): DragCalculationResult {
  const relativeY = mouseY - scrollAreaRect.top + scrollTop;
  
  // 参考阿里云智能媒体生产的轨道边界计算
  const trackBounds = displayTracks.map((track, index) => ({
    index,
    top: index * trackHeight,
    center: index * trackHeight + trackHeight / 2,
    bottom: (index + 1) * trackHeight,
    track
  }));

  // 动态阈值：根据轨道高度调整吸附区域，更精准的判断
  const insertThreshold = trackHeight * 0.25;  // 上下25%区域为插入区域
  const centerZone = trackHeight * 0.5;        // 中间50%为替换区域
  
  // 查找当前鼠标所在的轨道区域
  let currentTrackIndex = Math.floor(relativeY / trackHeight);
  currentTrackIndex = Math.max(0, Math.min(currentTrackIndex, displayTracks.length - 1));
  
  // 计算在当前轨道内的相对位置（0-1）
  const trackStartY = currentTrackIndex * trackHeight;
  const relativePositionInTrack = (relativeY - trackStartY) / trackHeight;
  
  // 根据相对位置和拖拽方向智能判断意图
  let insertPosition: 'above' | 'below' | 'replace' = 'replace';
  let targetTrackIndex = currentTrackIndex;
  let isNewTrack = false;
  
  // 处理拖拽到轨道区域外的情况（更严格的边界检查）
  if (relativeY < -insertThreshold) {
    // 拖拽到最上方，插入到第一个轨道之前
    return {
      targetTrackIndex: 0,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'above',
      snapZone: 'top'
    };
  }
  
  const totalTracksHeight = displayTracks.length * trackHeight;
  if (relativeY > totalTracksHeight + insertThreshold) {
    // 拖拽到最下方，添加新轨道
    return {
      targetTrackIndex: allTracks.length,
      isValidDrop: true,
      isNewTrack: true,
      insertPosition: 'below', 
      snapZone: 'bottom'
    };
  }
  
  // 智能判断拖拽意图（参考专业剪辑软件的行为）
  if (sourceTrackIndex !== undefined) {
    const isDraggingDown = sourceTrackIndex < currentTrackIndex;
    const isDraggingUp = sourceTrackIndex > currentTrackIndex;
    
    // 上边缘区域（0-25%）
    if (relativePositionInTrack < 0.25) {
      if (isDraggingDown) {
        // 从上往下拖到轨道上边缘：插入到当前轨道上方
        insertPosition = 'above';
        targetTrackIndex = currentTrackIndex;
        isNewTrack = true;
      } else if (isDraggingUp && relativePositionInTrack < 0.15) {
        // 从下往上拖到轨道上边缘：插入到当前轨道上方
        insertPosition = 'above';
        targetTrackIndex = currentTrackIndex;
        isNewTrack = true;
      } else {
        // 其他情况：替换到当前轨道
        insertPosition = 'replace';
        targetTrackIndex = currentTrackIndex;
      }
    }
    // 下边缘区域（75%-100%）
    else if (relativePositionInTrack > 0.75) {
      if (isDraggingUp) {
        // 从下往上拖到轨道下边缘：插入到当前轨道下方
        insertPosition = 'below';
        targetTrackIndex = currentTrackIndex + 1;
        isNewTrack = true;
      } else if (isDraggingDown && relativePositionInTrack > 0.85) {
        // 从上往下拖到轨道下边缘：插入到当前轨道下方
        insertPosition = 'below';
        targetTrackIndex = currentTrackIndex + 1;
        isNewTrack = true;
      } else {
        // 其他情况：替换到当前轨道
        insertPosition = 'replace';
        targetTrackIndex = currentTrackIndex;
      }
    }
    // 中心区域（25%-75%）
    else {
      // 中心区域优先考虑替换，除非拖拽距离很远
      const trackDistance = Math.abs(sourceTrackIndex - currentTrackIndex);
      if (trackDistance > 2) {
        // 长距离拖拽时，根据拖拽方向倾向于插入
        if (isDraggingDown && relativePositionInTrack > 0.6) {
          insertPosition = 'below';
          targetTrackIndex = currentTrackIndex + 1;
          isNewTrack = true;
        } else if (isDraggingUp && relativePositionInTrack < 0.4) {
          insertPosition = 'above';
          targetTrackIndex = currentTrackIndex;
          isNewTrack = true;
        } else {
          insertPosition = 'replace';
          targetTrackIndex = currentTrackIndex;
        }
      } else {
        // 短距离拖拽优先替换
        insertPosition = 'replace';
        targetTrackIndex = currentTrackIndex;
      }
    }
  } else {
    // 没有源轨道信息时的简单判断
    if (relativePositionInTrack < 0.2) {
      insertPosition = 'above';
      targetTrackIndex = currentTrackIndex;
      isNewTrack = true;
    } else if (relativePositionInTrack > 0.8) {
      insertPosition = 'below';
      targetTrackIndex = currentTrackIndex + 1;
      isNewTrack = true;
    } else {
      insertPosition = 'replace';
      targetTrackIndex = currentTrackIndex;
    }
  }
  
  // 处理目标轨道不存在的情况
  const targetTrack = displayTracks[currentTrackIndex];
  if (!targetTrack) {
    return { targetTrackIndex: null, isValidDrop: false, isNewTrack: false };
  }
  
  // 修正目标轨道索引
  if (insertPosition === 'replace') {
    targetTrackIndex = targetTrack.originalIndex >= 0 ? targetTrack.originalIndex : 0;
    isNewTrack = targetTrackIndex >= allTracks.length;
  }
  
  // 确定吸附区域
  let snapZone: 'top' | 'center' | 'bottom' = 'center';
  if (insertPosition === 'above') {
    snapZone = 'top';
  } else if (insertPosition === 'below') {
    snapZone = 'bottom';
  }

  return { 
    targetTrackIndex, 
    isValidDrop: true, 
    isNewTrack,
    insertPosition,
    snapZone
  };
}
