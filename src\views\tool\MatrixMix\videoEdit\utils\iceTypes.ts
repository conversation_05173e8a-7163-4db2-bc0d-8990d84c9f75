/**
 * 阿里云ICE WebSDK数据类型定义
 * 根据官方文档创建: https://help.aliyun.com/zh/ims/developer-reference/access-the-video-clip-web-sdk
 */

// 播放器宽高比枚举
export enum PlayerAspectRatio {
  w1h1 = '1:1',
  w2h1 = '2:1',
  w4h3 = '4:3',
  w3h4 = '3:4',
  w9h16 = '9:16',
  w16h9 = '16:9',
  w21h9 = '21:9',
}

// License配置
export interface LicenseConfig {
  rootDomain?: string; // license使用的根域名，例如abc.com
  licenseKey?: string; // 申请的licenseKey
}

// ASR配置
export interface AsrConfig {
  interval?: number; // 轮询时长，单位：毫秒
  defaultText?: string; // 默认的文案
  maxPlaceHolderLength?: number; // 默认的文案最大长度
  submitASRJob: (mediaId: string, startTime: string, duration: string) => Promise<ASRJobInfo>;
  getASRJobResult?: (jobId: string) => Promise<ASRJobInfo>;
}

export interface ASRJobInfo {
  jobId?: string;
  jobDone: boolean;
  jobError?: string;
  result?: ASRResult[];
}

export interface ASRResult {
  content: string; // 字幕的内容
  from: number; // 字幕的开始相对于识别素材的开始的时间偏移量
  to: number; // 字幕的结束相对于识别素材的开始的时间偏移量
}

// TTS配置
export interface TTSConfig {
  interval?: number; // 轮询时长，单位：毫秒
  submitAudioProduceJob: (text: string, voice: string, voiceConfig?: VoiceConfig) => Promise<TTSJobInfo>;
  getAudioJobResult?: (jobId: string) => Promise<TTSJobInfo>;
}

export interface VoiceConfig {
  volume: number; // 音量，取值0~100，默认值50
  speech_rate: number; // 语速，取值范围：-500～500，默认值：0
  pitch_rate: number; // 语调，取值范围：-500～500，默认值：0
  format?: string; // 输出文件格式，支持：PCM/WAV/MP3
  custom?: boolean;
}

export interface TTSJobInfo {
  jobId?: string;
  jobDone: boolean;
  jobError?: string;
  asr?: AudioASRResult[];
  result?: InputAudio | null;
}

export interface AudioASRResult {
  begin_time?: string;
  end_time?: string;
  text?: string;
  content?: string;
  from?: number;
  to?: number;
}

// 字幕配置
export interface SubtitleConfig {
  // 自定义纹理列表
  customTextures?: {
    list: () => Promise<
      Array<{
        key: string;
        url: string;
      }>
    >;
    // 添加自定义纹理
    onAddTexture: () => Promise<{
      key: string;
      url: string;
    }>;
    // 删除自定义纹理
    onDeleteTexture: (key: string) => Promise<void>;
  };
}

// 公共素材库配置
export interface PublicMaterialLibrary {
  getLists: () => Promise<any[]>;
}

export interface MaterialList {
  name?: string;
  key: string;
  tag?: string;
  mediaType: 'video' | 'audio' | 'image';
  styleType?: 'video' | 'audio' | 'image' | 'background';
  getItems: (
    pageIndex: number,
    pageSize: number,
  ) => Promise<{
    items: InputMedia[];
    end: boolean;
  }>;
}

// 媒体相关类型
export type MediaIdType = 'mediaId' | 'mediaURL';

export interface SpriteConfig {
  num: string;
  lines: string;
  cols: string;
  cellWidth?: string;
  cellHeight?: string;
}

export interface MediaMark {
  startTime: number;
  endTime: number;
  content: string;
}

export interface InputSource {
  sourceState?: 'ready' | 'loading' | 'fail';
}

export interface InputVideo extends InputSource {
  mediaId: string;
  mediaIdType?: MediaIdType;
  mediaType: 'video';
  video: {
    title: string;
    coverUrl?: string;
    duration: number;
    format?: string;
    src?: string;
    snapshots?: string[];
    sprites?: string[];
    spriteConfig?: SpriteConfig;
    width?: number;
    height?: number;
    rotate?: number;
    bitrate?: number;
    fps?: number;
    hasTranscodedAudio?: true;
    agentAudioSrc?: string;
    marks?: MediaMark[];
    codec?: string;
  };
}

export interface InputAudio extends InputSource {
  mediaId: string;
  mediaIdType?: MediaIdType;
  mediaType: 'audio';
  audio: {
    title: string;
    duration: number;
    coverUrl?: string;
    src?: string;
    marks?: MediaMark[];
    formatNames?: string[];
  };
}

export interface InputImage extends InputSource {
  mediaId: string;
  mediaIdType?: MediaIdType;
  mediaType: 'image';
  image: {
    title: string;
    coverUrl?: string;
    src?: string;
    width?: number;
    height?: number;
    rotate?: number;
  };
}

export type InputMedia = InputVideo | InputAudio | InputImage;

export interface TimelineMaterial {
  mediaIdType: MediaIdType;
  mediaId: string;
  mediaType: 'video' | 'audio' | 'image';
}

// 贴纸相关类型
export interface StickerCategory {
  id: string; // 分类的 id
  name: string; // 分类的名称
}

export interface Sticker {
  mediaId: string;
  src: string;
}

export interface StickerResponse {
  total: number;
  stickers: Sticker[];
}

// 生产推荐配置
export interface IProduceRecommend {
  width?: number;
  height?: number;
  bitrate?: number;
}

// 自定义字体
export interface CustomFontItem {
  key: string; // 字体唯一标识
  name?: string; // 展示的名字，没有用key
  url: string; // 字体地址
  // 用于前、后端字体渲染保持一致
  fontServerScale?: {
    // 普通字幕字体倍数
    common: number;
    // 花字字体倍数
    decorated: number;
  };
}

// 语音组
export enum VoiceType {
  Male = 'Male', // 男声
  Female = 'Female', // 女声
  Boy = 'Boy', // 男孩童声
  Girl = 'Girl', // 女孩童声
}

export interface Voice {
  voiceUrl?: string; // 示例音频地址
  demoMediaId?: string; // 示例音频的播放地址
  voiceType: VoiceType; // 类型
  voice: string; // 人声 key
  name: string; // "人名"
  desc: string; // 简介
  tag?: string; // 标签
  remark?: string; // 备注支持的语言等信息
  custom?: boolean; // 是否专属人声
}

export interface VoiceGroup {
  type: string; // 分类
  category: string; // 主分类
  voiceList?: Voice[];
  emptyContent?: {
    description: string;
    linkText: string;
    link: string;
  };
  getVoiceList?: (page: number, pageSize: number) => Promise<{ items: Voice[]; total: number }>;
  getVoice?: (voiceId: string) => Promise<Voice | null>;
  getDemo?: (mediaId: string) => Promise<{ src: string }>;
}

// 数字人配置
export interface AvatarConfig {
  enabled: boolean;
  // 其他数字人配置参数...
}

// 视频翻译配置
export interface VideoTranslation {
  enabled: boolean;
  languages: string[];
}

// Timeline类型
export interface Timeline {
  VideoTracks?: any[];
  AudioTracks?: any[];
  AspectRatio?: PlayerAspectRatio;
  [key: string]: any;
}

export interface MaterialMapsType {
  video?: string;
  audio?: string;
  image?: string;
  text?: string;
  liveStream?: string;
  editingProject?: string;
  [key: string]: string | undefined; // 允许其他媒体类型
}