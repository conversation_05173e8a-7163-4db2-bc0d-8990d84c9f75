<template>
  <div class="sound-container">
    <div class="sound-header">
      <div class="header-content">
        <div class="header-left">
          <div class="sound-logo">
            <div class="sound-waves">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          <h2>声音资源中心</h2>
        </div>
        <div class="header-right">
          <div class="equalizer-container">
            <div class="equalizer">
              <span></span><span></span><span></span>
              <span></span><span></span><span></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="content-section">
      <div class="search-section">
        <el-card shadow="hover" class="search-panel">
          <template #header>
            <div class="card-header">
              <i class="filter-icon"></i>
              <span>搜索筛选</span>
            </div>
          </template>
          <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px" @submit.prevent class="search-form">
            <el-form-item label="声音名称" prop="soundName" v-hasPermi="['platform:sound:listName']">
              <el-input v-model="queryParams.soundName" placeholder="请输入声音名称" clearable @keyup.enter="handleQuery" class="query-input">
                <template #prefix><el-icon><search /></el-icon></template>
              </el-input>
            </el-form-item>
            <el-form-item label="声音状态" prop="soundStatus" v-hasPermi="['platform:sound:listState']">
              <el-select v-model="queryParams.soundStatus" placeholder="请选择声音状态" class="query-select">
                <el-option v-for="dict in platform_sound_sound_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="数据范围" prop="soundFiltration" v-hasPermi="['platform:sound:listSoundFiltration']">
              <el-select v-model="queryParams.soundFiltration" placeholder="请选择数据范围" class="query-select">
                <el-option v-for="dict in platform_sound_sound_filtration" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="部门列表" prop="deptId" v-hasPermi="['platform:sound:listDeptId']">
              <el-tree-select 
                v-model="queryParams.deptId" :props="{value: 'deptId',label: 'deptName', children: 'children' }" 
                :data="deptOptions" check-strictly placeholder="请选择部门列表" class="query-select"/>
            </el-form-item>
            <el-form-item class="action-item">
              <div class="button-group">
                <el-button @click="handleQuery" class="action-button search-btn">
                  <div class="search-btn-content">
                    <div class="search-icon-container">
                      <i class="search-icon"></i>
                      <div class="search-ripple"></div>
                    </div>
                    <span>搜索</span>
                  </div>
                </el-button>
                <el-button @click="resetQuery" class="action-button reset-btn">
                  <div class="reset-btn-content">
                    <div class="reset-icon-container">
                      <i class="reset-icon"></i>
                      <div class="reset-particles">
                        <span></span><span></span><span></span>
                      </div>
                    </div>
                    <span>重置</span>
                  </div>
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <div class="action-toolbar">
        <div class="main-actions">
          <el-button @click="openAddSoundByAudio = true" class="fancy-btn upload-btn">
            <div class="btn-content">
              <div class="upload-icon-container">
                <i class="upload-icon"></i>
                <div class="upload-particles">
                  <span></span><span></span><span></span>
                </div>
              </div>
              <span>上传待训练声音</span>
              <div class="upload-pulse"></div>
            </div>
            <div class="btn-shine"></div>
          </el-button>
          <el-button @click="openAddSoundByModel = true" v-hasPermi="['platform:sound:addModel']" class="fancy-btn model-btn">
            <div class="btn-content">
              <div class="model-icon-container">
                <i class="model-icon"></i>
                <div class="model-particles">
                  <span></span><span></span><span></span>
                </div>
              </div>
              <span>上传已训练声音</span>
              <div class="model-pulse"></div>
            </div>
            <div class="btn-shine"></div>
          </el-button>
          <el-button
            :disabled="!selectedRows.length"
            @click="handleBatchDelete"
            v-hasPermi="['platform:sound:deleteModel']"
            class="fancy-btn batch-delete-btn">
            <div class="btn-content">
              <div class="delete-icon-container">
                <i class="delete-icon"></i>
                <div class="delete-particles">
                  <span></span><span></span><span></span>
                </div>
              </div>
              <span class="btn-text">批量删除</span>
              <div class="selection-badge" v-if="selectedRows.length > 0">
                <span class="badge-count">{{ selectedRows.length }}</span>
                <div class="badge-pulse"></div>
              </div>
            </div>
            <div class="btn-shine"></div>
          </el-button>
        </div>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </div>

      <el-card shadow="hover" class="table-panel">
        <template #header>
          <div class="card-header">
            <i class="sound-list-icon"></i>
            <span>声音资源列表</span>
          </div>
        </template>
        <div class="table-container">
          <el-table v-loading="loading" :data="soundList" @selection-change="handleSelectionChange" 
            class="sound-table" :header-cell-style="{background:'#f5f7fa', color: '#606266', fontWeight: '600'}"
            :row-class-name="tableRowClassName">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="声音名称" prop="soundName" min-width="140" show-overflow-tooltip>
              <template #default="scope">
                <div class="sound-name-cell">
                  <div class="sound-icon-wrapper" :class="getSoundIconClass(scope.row.soundId)">
                    <i :class="getSoundIconType(scope.row.soundId)"></i>
                    <div class="icon-glow"></div>
                  </div>
                  <span class="sound-name-text">{{ scope.row.soundName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="声音状态" align="center" prop="soundStatus" width="200">
              <template #default="scope">
                <div :class="['status-tag', 'status-' + scope.row.soundStatus]">
                  <div class="status-indicator"></div>
                  <dict-tag :options="platform_sound_sound_status" :value="scope.row.soundStatus" />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="所属部门" align="center" prop="deptName" min-width="120" v-hasPermi="['platform:sound:listDeptId']" show-overflow-tooltip />
            <el-table-column label="数据范围" align="center" prop="soundFiltration" width="100" v-hasPermi="['platform:sound:listSoundFiltration']">
              <template #default="scope">
                <dict-tag :options="platform_sound_sound_filtration" :value="scope.row.soundFiltration" />
              </template>
            </el-table-column>
            <el-table-column label="操作人" align="center" prop="createBy" v-if="!isCustomer"/>
            <el-table-column label="操作时间" align="center" prop="createTime" v-if="!isCustomer" width="200">
              <template #default="scope">
                <div class="time-info">
                  <div>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}-{h}:{i}:{s}') }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="280">
              <template #default="scope">
                <div class="table-actions">
                  <el-tooltip v-if="scope.row.soundStatus == 0 || scope.row.soundStatus==4" content="提交审核" placement="top" effect="light">
                    <el-button type="success" size="small" circle @click="handleTrain(scope.row)" class="action-btn success-action">
                      <el-icon><upload /></el-icon>
                    </el-button>
                  </el-tooltip>
                  
                  <el-tooltip v-if="scope.row.soundStatus == 1 && scope.row.soundTrain != null" content="正在训练中" placement="top" effect="light">
                    <el-button type="info" size="small" circle disabled class="action-btn training-action">
                      <div class="loading-wave"><span></span><span></span><span></span><span></span><span></span>
                      </div>
                    </el-button>
                  </el-tooltip>
                  
                  <el-tooltip v-if="!(isCustomer && scope.row.soundFiltration == 1)" content="修改" placement="top" effect="light">
                    <el-button type="primary" size="small" circle @click="handleUpdate(scope.row)" class="action-btn primary-action">
                      <el-icon><edit /></el-icon>
                    </el-button>
                  </el-tooltip>
                  
                  <el-tooltip v-if="!(isCustomer && scope.row.soundFiltration == 1)" content="删除" placement="top" effect="light">
                    <el-button type="danger" size="small" circle @click="handleDelete(scope.row)" class="action-btn danger-action">
                      <el-icon><delete /></el-icon>
                    </el-button>
                  </el-tooltip>
                  
                  <el-tooltip content="审核" placement="top" effect="light">
                    <el-button type="warning" size="small" circle :disabled="scope.row.soundStatus != 0" @click="scope.row.soundStatus == 0 ? handleAudit(scope.row) : null" v-hasPermi="['platform:sound:soundAudit']" class="action-btn warning-action">
                      <el-icon><check /></el-icon>
                    </el-button>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>

            <el-pagination v-show="total > 0" :total="total" v-model:current-page="queryParams.pageNum"
              v-model:page-size="queryParams.pageSize" @current-change="getList" @size-change="getList"
              :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
              background class="pagination-container" />
        </div>
      </el-card>
    </div>

    <!-- 审核对话框 -->
    <el-dialog :title="title" v-model="dialogVisible" width="450px" center custom-class="custom-dialog audit-dialog" :show-close="false" :close-on-click-modal="false">
      <div class="dialog-body">
        <div class="dialog-icon">
          <i class="large-audit-icon"></i>
          <div class="pulse-ring"></div>
        </div>
        <div class="dialog-message">
          确认通过该声音？它将被加入到队列中，并创建一个任务等待训练。
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button plain @click="confirmAudit(false)" class="dialog-btn reject-btn">驳回</el-button>
          <el-button type="primary" @click="confirmAudit(true)" class="dialog-btn approve-btn">通过</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改声音对话框 -->
    <el-dialog v-model="openUpdate" @close="resetForm" center title="修改声音" width="550px"   custom-class="update-dialog">
      <el-form :model="form" class="update-form" label-width="100px">
        <el-form-item label="声音名称" required>
          <el-input v-model="form.soundName" placeholder="请输入声音名称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="参考音频">
          <soundUpload :uploadFileUrl="'/platform/sound/uploadSoundref'"  @uploadSuccess="uploadRefAudio"
            v-model:modelValue="form.soundRef"  @removeFile="removeFile"
            :fileType="['mp3', 'wav', 'flac', 'm4a']" :limit="1" :fileSize="10" ref="soundUploadRef"/>
        </el-form-item>
        <el-form-item v-if="refAudioURL" >
          <audio :src="refAudioURL" controls style="width: 100%"></audio>
        </el-form-item>
        <el-form-item label="参考文本" required>
          <el-input type="textarea" v-model="form.soundRefText"  placeholder="请确保参考文本与参考音频内容完全一致，以免影响文案转音频的效果。"  rows="4" maxlength="500" show-word-limit />
        </el-form-item>
        <el-form-item label="部门列表" prop="deptId" v-hasPermi="['platform:sound:editDeptId']">
          <el-tree-select v-model="form.deptId" :props="{value: 'deptId',label: 'deptName',children: 'children'}" 
            :data="deptOptions" check-strictly  placeholder="请选择部门列表" style="width: 100%"/>
        </el-form-item>
        <el-form-item label="数据范围" prop="soundFiltration" v-hasPermi="['platform:sound:editSoundFiltration']">
          <el-select v-model="form.soundFiltration" placeholder="请选择数据范围" style="width: 100%">
            <el-option v-for="dict in platform_sound_sound_filtration" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="openUpdate = false">取消</el-button>
          <el-button type="primary" @click="submit">提交</el-button>
        </div>
      </template>
    </el-dialog>

    <addByAudio v-model="openAddSoundByAudio" @uploadByAudioSuccess="uploadByAudioSuccess" />
    <addByModel v-model="openAddSoundByModel" @uploadByModelSuccess="uploadByModelSuccess" />
  </div>
</template>

<script setup name="Sound">
import { downloadSoundRefBySoundId } from "@/api/platform/audio";
import { delSound, getSound, listSound, updateSound, soundAudit } from "@/api/platform/sound";
import { fetchDeptOptions } from '@/utils/deptUtils';
import { computed, ref, onMounted } from "vue";
import addByAudio from "./add-by-audio.vue";
import addByModel from "./add-by-model.vue";
import soundUpload from "./sound-upload.vue";
import useUserStore from '@/store/modules/user';
import { Delete, Edit, Upload, Check, Search, Refresh } from '@element-plus/icons-vue'; // 导入图标

const form = ref({
  soundId: null,
  soundName: null,
  soundRef: null,
  soundRefText: null,
  deptId: null
});
const refAudioFromLocal = ref(null);
const refAudioFromServer = ref(null);
const refAudioURL = computed(() => refAudioFromLocal.value ? URL.createObjectURL(refAudioFromLocal.value.raw) : refAudioFromServer.value);
const soundUploadRef = ref(null);
const fileValidationFailed = ref(false); // 新增：文件验证失败标志

const { proxy } = getCurrentInstance();
const { platform_sound_sound_status, platform_sound_sound_filtration } = proxy.useDict("platform_sound_sound_status", "platform_sound_sound_filtration");

const soundList = ref([]);
const openAddSoundByAudio = ref(false);
const openAddSoundByModel = ref(false);
const openUpdate = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const title = ref("");
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  soundName: null,
  soundStatus: null,
  soundStatus: null,
  soundFiltration: null
});
const userStore = useUserStore();
const isCustomer = computed(() => !userStore.roles.includes('admin') && !userStore.roles.includes('engineer'));
const deptOptions = ref([]); // 获取部门树桩列表
const dialogVisible = ref(false);
const selectedSoundId = ref(null);
let originalForm = {};

// 添加选中行数据
const selectedRows = ref([]);

/** 查询声音管理列表 */
function getList() { 
  loading.value = true;
  listSound(queryParams.value).then(response => {
    soundList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 重置修改表单 */
function resetForm() {
  form.value = {
    soundId: null,
    soundName: null,
    soundRef: null,
    soundRefText: null,
    deptId: null,
    soundFiltration: null
  };
  soundUploadRef.value.clearUploadList();
  refAudioFromLocal.value = null;
  refAudioFromServer.value = null;
  refAudioURL.value = null;
  fileValidationFailed.value = false; // 重置验证标志
}

/** 修改按钮操作 */
function handleUpdate(row) {
  // 检查状态是否为0（待审核）
  if (row.soundStatus == 1) {
    proxy.$modal.msgWarning("当前声音模型正在训练中，不能编辑！");
    return;
  }
  openUpdate.value = true;
  const _soundId = row.soundId || ids.value;
  getSound(_soundId).then(response => {
    form.value = response.data;
    originalForm = { ...form.value }; 
    openUpdate.value = true;
    title.value = "修改声音管理";
    downloadSoundRefBySoundId(_soundId).then(response => {
      refAudioFromServer.value = URL.createObjectURL(response);
    });
  });
}

/** 修改提交按钮操作 */
async function submit() {
  // 检查文件验证状态
  if (fileValidationFailed.value) {
    proxy.$modal.msgError("文件格式或大小不符合要求，请检查！");
    return;
  }
  try {
    const uploadedFiles = await soundUploadRef.value.executeUpload();
    if (uploadedFiles.length > 0) {
      form.value.soundRef = uploadedFiles[0].url; // 使用uploadList中保存的url
    }
    if (form.value.soundRefText !== originalForm.soundRefText || form.value.soundRef !== originalForm.soundRef) {
      form.value.soundStatus = 4;
    }
    await updateSound(form.value);
    getList();
    proxy.$modal.msgSuccess("修改成功");
    openUpdate.value = false;
    soundUploadRef.value.clearUploadList();
    form.value = {
      soundId: null,
      soundName: null,
      soundRef: null,
      soundRefText: null,
      deptId: null,
      soundFiltration: null
    };
  } catch (error) {
    console.log(error);
  }
}

/** 上传参考音频 */
function uploadRefAudio(refAudio) {
  form.value.soundRef = refAudio[0].url;
  // 更新本地音频预览
  const file = soundUploadRef.value.$refs.fileUpload.uploadFiles[0];
  if (file && file.raw) {
    refAudioFromLocal.value = file;
    refAudioFromServer.value = null;
  }
}

function uploadByAudioSuccess(res) {
  getList()
  openAddSoundByAudio.value = false
}

function uploadByModelSuccess(res) {
  getList()
  openAddSoundByModel.value = false
}

/** 删除上传列表中的音频 */
function removeFile(file) {
  form.value.soundRef = null;
  refAudioFromLocal.value = null;
  refAudioFromServer.value = null;
  fileValidationFailed.value = false; // 重置验证标志
}

/** 提交审核 */
function handleTrain(row) {
  updateSound({soundId: row.soundId, soundStatus: 0}).then(response => {
    getList();
    proxy.$modal.msgSuccess("等待管理员审核！");
  })
}

//审核弹窗
function handleAudit (row) {
  title.value = '用户 '+row.createBy+' 声音审核'
  selectedSoundId.value = row.soundId;
  dialogVisible.value = true; // 显示对话框
};

/** 审核声音模型 */
const confirmAudit = async (isApproved) => {
  try {
    await soundAudit(selectedSoundId.value, isApproved);
    const message = isApproved ? '审核通过' : '已驳回';
    proxy.$modal.msgSuccess(`${message}`);
    dialogVisible.value = false;
    getList(); // 刷新列表
  } catch (error) {
    proxy.$modal.msgError('操作失败，请重试');
  }
};

/** 删除按钮操作 */
function handleDelete(row) {
  const _soundIds = row.soundId || ids.value;
  proxy.$modal.confirm('是否确认删除声音管理编号为"' + _soundIds + '"的数据项？').then(function () {
    return delSound(_soundIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

// 处理选择变化
function handleSelectionChange(rows) {
  selectedRows.value = rows;
}

// 批量删除功能
function handleBatchDelete() {
  const soundIds = selectedRows.value.map(row => row.soundId).join(',');
  proxy.$modal.confirm('是否确认删除选中的' + selectedRows.value.length + '条声音数据？').then(() => {
    return delSound(soundIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
    selectedRows.value = []; // 清空选择
  }).catch(() => {});
}
// 声音图标类型数组
const soundIconTypes = [
  'sound-icon-music',
  'sound-icon-voice',
  'sound-icon-wave',
  'sound-icon-mic',
  'sound-icon-speaker',
  'sound-icon-headphone'
];

const soundIconColors = [
  'icon-color-blue',
  'icon-color-green',
  'icon-color-purple',
  'icon-color-orange',
  'icon-color-pink',
  'icon-color-cyan'
];

// 根据声音ID获取图标类型
function getSoundIconType(soundId) {
  const index = soundId % soundIconTypes.length;
  return soundIconTypes[index];
}

// 根据声音ID获取图标颜色类
function getSoundIconClass(soundId) {
  const index = soundId % soundIconColors.length;
  return soundIconColors[index];
}

// 获取部门树状信息列表
onMounted(async () => {
  deptOptions.value = await fetchDeptOptions();
});
getList();
</script>

<style lang="scss" scoped>
/* 整体容器和基本布局 */
.sound-container {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 20px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 顶部标题区域 */
.sound-header {
  background: linear-gradient(135deg, #6c7ae0 0%, #7b68ee 100%);
  border-radius: 12px;
  padding: 0;
  overflow: hidden;
  margin-bottom: 24px;
  box-shadow: 0 4px 16px rgba(108, 122, 224, 0.15);
  transition: all 0.3s ease;
}

.sound-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 122, 224, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left h2 {
  color: #ffffff;
  font-size: 22px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 声音波动动画 */
.sound-logo {
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  transition: all 0.3s ease;
}

.sound-logo:hover {
  transform: scale(1.05);
}

.sound-waves {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  width: 20px;
}

.sound-waves span {
  display: inline-block;
  width: 3px;
  margin: 0 1px;
  background: #ffffff;
  border-radius: 2px;
  animation: soundWave 1.4s ease-in-out infinite;
}

.sound-waves span:nth-child(1) { animation-delay: 0s; height: 8px; }
.sound-waves span:nth-child(2) { animation-delay: 0.35s; height: 16px; }
.sound-waves span:nth-child(3) { animation-delay: 0.7s; height: 10px; }

/* 均衡器动画 */
.equalizer-container {
  background: rgba(255, 255, 255, 0.15);
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.equalizer-container:hover {
  background: rgba(255, 255, 255, 0.2);
}

.equalizer {
  display: flex;
  align-items: flex-end;
  height: 20px;
  gap: 2px;
}

.equalizer span {
  width: 3px;
  background: #ffffff;
  border-radius: 1px;
  animation: equalize 1.6s ease-in-out infinite;
  opacity: 0.9;
}
.equalizer span:nth-child(1) { animation-delay: 0.1s; height: 8px; }
.equalizer span:nth-child(2) { animation-delay: 0.3s; height: 12px; }
.equalizer span:nth-child(3) { animation-delay: 0.5s; height: 16px; }
.equalizer span:nth-child(4) { animation-delay: 0.2s; height: 14px; }
.equalizer span:nth-child(5) { animation-delay: 0.4s; height: 10px; }
.equalizer span:nth-child(6) { animation-delay: 0.6s; height: 6px; }

/* 内容布局 */
.content-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.search-section {
  display: flex;
  width: 100%;
}

.search-panel {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #e9ecef;
  background: #ffffff;
  transition: all 0.3s ease;
}

.search-panel:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  transform: translateY(-1px);
}

.card-header {
  display: flex;
  align-items: center;
  height: 32px;
  font-weight: 600;
  color: #495057;
  font-size: 16px;
}

.filter-icon {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c7ae0'%3E%3Cpath d='M10,18h4v-2h-4V18z M3,6v2h18V6H3z M6,13h12v-2H6V13z'/%3E%3C/svg%3E");
  margin-right: 8px;
  background-size: contain;
  background-repeat: no-repeat;
}

.sound-list-icon {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c7ae0'%3E%3Cpath d='M4,10h18V8H4V10z M4,16h18v-2H4V16z M4,4v2h18V4H4z M2,20v-2h18v2H2z'/%3E%3C/svg%3E");
  margin-right: 8px;
  background-size: contain;
  background-repeat: no-repeat;
}

/* 表单样式 */
.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.el-form-item {
  margin-bottom: 0;
  margin-right: 0;
}

.query-input, .query-select {
  width: 200px;
  transition: all 0.3s ease;
  border-radius: 6px;
}

.query-input:hover, .query-select:hover {
  box-shadow: 0 2px 8px rgba(108, 122, 224, 0.1);
}

.action-item {
  margin-left: 0;
  margin-right: 0;
}

.button-group {
  display: flex;
  gap: 12px;
}

/* 搜索和重置按钮基础样式 */
.sound-container .el-button.action-button {
  min-width: 80px !important;
  padding: 0 16px !important;
  height: 36px !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
}

/* 搜索按钮样式 */
.sound-container .el-button.action-button.search-btn {
  background: linear-gradient(135deg, #007bff 0%, #6c7ae0 100%) !important;
  background-color: #007bff !important;
  border: 1px solid #007bff !important;
  color: white !important;
  box-shadow: 0 3px 10px rgba(0, 123, 255, 0.25) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.sound-container .el-button.action-button.search-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.35) !important;
  background: linear-gradient(135deg, #0056b3 0%, #5a67d8 100%) !important;
  background-color: #0056b3 !important;
  border-color: #0056b3 !important;
}

.sound-container .el-button.action-button.search-btn:focus {
  background: linear-gradient(135deg, #007bff 0%, #6c7ae0 100%) !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
  box-shadow: 0 3px 10px rgba(0, 123, 255, 0.25) !important;
}

.sound-container .el-button.action-button.search-btn:active {
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 10px rgba(0, 123, 255, 0.3) !important;
}

/* 重置按钮样式 */
.sound-container .el-button.action-button.reset-btn {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
  background-color: #6c757d !important;
  border: 1px solid #6c757d !important;
  color: white !important;
  box-shadow: 0 3px 10px rgba(108, 117, 125, 0.25) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.sound-container .el-button.action-button.reset-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 15px rgba(108, 117, 125, 0.35) !important;
  background: linear-gradient(135deg, #5a6268 0%, #343a40 100%) !important;
  background-color: #5a6268 !important;
  border-color: #5a6268 !important;
}

.sound-container .el-button.action-button.reset-btn:focus {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  box-shadow: 0 3px 10px rgba(108, 117, 125, 0.25) !important;
}

.sound-container .el-button.action-button.reset-btn:active {
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 10px rgba(108, 117, 125, 0.3) !important;
}

/* 按钮内容布局 */
.search-btn-content,
.reset-btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  position: relative;
  z-index: 2;
  pointer-events: none;
}

/* 确保按钮可以接收鼠标事件 */
.sound-container .el-button.action-button.search-btn,
.sound-container .el-button.action-button.reset-btn {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* 搜索图标容器 */
.search-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.search-icon {
  width: 14px;
  height: 14px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
  position: relative;
}

.search-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

/* 重置图标容器 */
.reset-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.reset-icon {
  width: 14px;
  height: 14px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
  position: relative;
}

.reset-particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.reset-particles span {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  opacity: 0;
  animation: resetParticles 2s infinite;
}

.reset-particles span:nth-child(1) {
  top: 0;
  left: 50%;
  animation-delay: 0s;
}

.reset-particles span:nth-child(2) {
  top: 50%;
  right: 0;
  animation-delay: 0.3s;
}

.reset-particles span:nth-child(3) {
  bottom: 0;
  left: 50%;
  animation-delay: 0.6s;
}

/* 悬停效果 */
.sound-container .el-button.action-button.search-btn:hover .search-icon {
  transform: scale(1.1) !important;
}

.sound-container .el-button.action-button.search-btn:hover .search-ripple {
  width: 30px !important;
  height: 30px !important;
}

.sound-container .el-button.action-button.search-btn:active .search-ripple {
  width: 40px !important;
  height: 40px !important;
  transition: width 0.1s, height 0.1s !important;
}

/* 强制确保搜索按钮动画效果 */
.search-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.35) !important;
}

.search-btn:hover .search-icon {
  transform: scale(1.1) !important;
}

.search-btn:hover .search-ripple {
  width: 30px !important;
  height: 30px !important;
}

/* 强制确保重置按钮动画效果 */
.reset-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 15px rgba(108, 117, 125, 0.35) !important;
}

.reset-btn:hover .reset-icon {
  transform: scale(1.1) rotate(180deg) !important;
}

.reset-btn:hover .reset-particles span {
  animation-play-state: running !important;
}

.sound-container .el-button.action-button.reset-btn:hover .reset-icon {
  transform: scale(1.1) rotate(180deg) !important;
}

.sound-container .el-button.action-button.reset-btn:hover .reset-particles span {
  animation-play-state: running !important;
}

/* 操作工具栏 */
.action-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.main-actions {
  display: flex;
  gap: 16px;
}

.fancy-btn {
  height: 44px;
  padding: 0 20px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  border: none;
  transition: all 0.3s ease;
  font-weight: 500;
}

.fancy-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  z-index: 1;
}

/* 上传待训练声音按钮 */
.sound-container .el-button.fancy-btn.upload-btn {
  background: linear-gradient(135deg, #6c7ae0 0%, #7b68ee 100%) !important;
  background-color: #6c7ae0 !important;
  border: 1px solid #6c7ae0 !important;
  box-shadow: 0 4px 16px rgba(108, 122, 224, 0.3) !important;
  color: white !important;
  position: relative;
  overflow: hidden;
}

.sound-container .el-button.fancy-btn.upload-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(108, 122, 224, 0.4) !important;
  background: linear-gradient(135deg, #5a67d8 0%, #6a4190 100%) !important;
  background-color: #5a67d8 !important;
  border-color: #5a67d8 !important;
}

/* 上传已训练声音按钮 */
.sound-container .el-button.fancy-btn.model-btn {
  background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%) !important;
  background-color: #20c997 !important;
  border: 1px solid #20c997 !important;
  box-shadow: 0 4px 16px rgba(32, 201, 151, 0.3) !important;
  color: white !important;
  position: relative;
  overflow: hidden;
}

.sound-container .el-button.fancy-btn.model-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(32, 201, 151, 0.4) !important;
  background: linear-gradient(135deg, #1ea085 0%, #138496 100%) !important;
  background-color: #1ea085 !important;
  border-color: #1ea085 !important;
}

/* 批量删除按钮样式 - 红色主题 */
.sound-container .el-button.fancy-btn.batch-delete-btn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  background-color: #dc3545 !important;
  border: 1px solid #dc3545 !important;
  box-shadow: 0 4px 16px rgba(220, 53, 69, 0.3) !important;
  color: white !important;
  min-width: 140px !important;
  position: relative;
  overflow: hidden;
}

.sound-container .el-button.fancy-btn.batch-delete-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4) !important;
  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%) !important;
  background-color: #c82333 !important;
  border-color: #c82333 !important;
}

.sound-container .el-button.fancy-btn.batch-delete-btn:focus {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
  box-shadow: 0 4px 16px rgba(220, 53, 69, 0.3) !important;
}

.sound-container .el-button.fancy-btn.batch-delete-btn:active {
  background: linear-gradient(135deg, #a71e2a 0%, #8b1a1a 100%) !important;
  background-color: #a71e2a !important;
  border-color: #a71e2a !important;
  transform: translateY(-1px) !important;
}

/* 删除图标容器 */
.delete-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.delete-icon {
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

/* 删除粒子效果 */
.delete-particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.delete-particles span {
  position: absolute;
  width: 3px;
  height: 3px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  opacity: 0;
  animation: deleteParticles 2s infinite;
}

.delete-particles span:nth-child(1) {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.delete-particles span:nth-child(2) {
  top: 20%;
  right: 20%;
  animation-delay: 0.3s;
}

.delete-particles span:nth-child(3) {
  bottom: 20%;
  left: 50%;
  animation-delay: 0.6s;
}

/* 选择数量徽章 */
.selection-badge {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 3px 8px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(220, 53, 69, 0.3);
  overflow: hidden;
}

.badge-count {
  font-size: 11px;
  font-weight: 700;
  color: #dc3545;
  z-index: 2;
  position: relative;
}

.badge-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(220, 53, 69, 0.1);
  border-radius: 12px;
  animation: badgeGlow 2s infinite;
}

/* 按钮光泽效果 */
.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
  z-index: 1;
}

.batch-delete-btn:hover .btn-shine {
  left: 100%;
}

.batch-delete-btn:hover .delete-icon {
  transform: scale(1.1) rotate(5deg);
}

.batch-delete-btn:hover .delete-particles span {
  animation-play-state: running;
}

/* 禁用状态 */
.sound-container .el-button.fancy-btn.batch-delete-btn:disabled,
.sound-container .el-button.fancy-btn.batch-delete-btn.is-disabled {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  background-color: #f8f9fa !important;
  border: 1px solid #dee2e6 !important;
  color: #6c757d !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08) !important;
  transform: none !important;
  cursor: not-allowed !important;
}

.sound-container .el-button.fancy-btn.batch-delete-btn:disabled .delete-icon,
.sound-container .el-button.fancy-btn.batch-delete-btn.is-disabled .delete-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%236c757d' viewBox='0 0 24 24'%3E%3Cpath d='M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z'/%3E%3C/svg%3E");
}

.sound-container .el-button.fancy-btn.batch-delete-btn:disabled .selection-badge,
.sound-container .el-button.fancy-btn.batch-delete-btn.is-disabled .selection-badge {
  display: none;
}

.sound-container .el-button.fancy-btn.batch-delete-btn:disabled .delete-particles span,
.sound-container .el-button.fancy-btn.batch-delete-btn.is-disabled .delete-particles span {
  animation-play-state: paused;
}

.sound-container .el-button.fancy-btn.batch-delete-btn:disabled:hover,
.sound-container .el-button.fancy-btn.batch-delete-btn.is-disabled:hover {
  transform: none !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08) !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  background-color: #f8f9fa !important;
}

/* 按钮内容布局 */
.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  z-index: 2;
  position: relative;
}

/* 上传图标容器 */
.upload-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.upload-icon {
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

/* 模型图标容器 */
.model-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.model-icon {
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M12 3v9.28c-.47-.17-.97-.28-1.5-.28C8.01 12 6 14.01 6 16.5S8.01 21 10.5 21c2.31 0 4.2-1.75 4.45-4H15V6h4V3h-7z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

/* 上传粒子效果 */
.upload-particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.upload-particles span {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  opacity: 0;
  animation: uploadParticles 2.5s infinite;
}

.upload-particles span:nth-child(1) {
  top: 10%;
  left: 30%;
  animation-delay: 0s;
}

.upload-particles span:nth-child(2) {
  top: 10%;
  right: 30%;
  animation-delay: 0.4s;
}

.upload-particles span:nth-child(3) {
  bottom: 10%;
  left: 50%;
  animation-delay: 0.8s;
}

/* 模型粒子效果 */
.model-particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.model-particles span {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  opacity: 0;
  animation: modelParticles 2.5s infinite;
}

.model-particles span:nth-child(1) {
  top: 20%;
  left: 20%;
  animation-delay: 0.2s;
}

.model-particles span:nth-child(2) {
  top: 20%;
  right: 20%;
  animation-delay: 0.6s;
}

.model-particles span:nth-child(3) {
  bottom: 20%;
  left: 50%;
  animation-delay: 1s;
}

/* 脉冲效果 */
.upload-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: uploadPulse 3s infinite;
}

.model-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: modelPulse 3s infinite;
}

/* 悬停效果 */
.upload-btn:hover .upload-icon {
  transform: scale(1.1) translateY(-2px);
}

.upload-btn:hover .upload-particles span {
  animation-play-state: running;
}

.model-btn:hover .model-icon {
  transform: scale(1.1) rotate(5deg);
}

.model-btn:hover .model-particles span {
  animation-play-state: running;
}

/* 表格样式 */
.table-panel {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  background: #ffffff;
  transition: all 0.3s ease;
}

.table-panel:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  transform: translateY(-1px);
}

.table-container {
  padding: 0;
  overflow: hidden;
  background: #ffffff;
}

.sound-table {
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
}

.sound-table :deep(.el-table__header-wrapper) {
  background: #f8f9fa;
}

.sound-table :deep(.el-table__header) {
  background: #f8f9fa;
}

.sound-table :deep(.el-table__body-wrapper) {
  background: #ffffff;
}

.sound-table .highlighted-row {
  background-color: #f8f9fa !important;
}

.sound-table :deep(.el-table__row:hover) {
  background-color: #f8f9fa !important;
  transition: background-color 0.2s ease !important;
}

.sound-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 0;
}

/* 声音图标容器 */
.sound-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.sound-icon-wrapper:hover {
  transform: scale(1.05);
}

/* 图标发光效果 */
.icon-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sound-icon-wrapper:hover .icon-glow {
  opacity: 1;
}

/* 不同颜色主题 */
.icon-color-blue {
  background: linear-gradient(135deg, #007bff 0%, #6c7ae0 100%);
}

.icon-color-green {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.icon-color-purple {
  background: linear-gradient(135deg, #6f42c1 0%, #8e44ad 100%);
}

.icon-color-orange {
  background: linear-gradient(135deg, #fd7e14 0%, #f39c12 100%);
}

.icon-color-pink {
  background: linear-gradient(135deg, #e83e8c 0%, #d63384 100%);
}

.icon-color-cyan {
  background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
}

/* 不同类型的声音图标 */
.sound-icon-music {
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M12 3v9.28c-.47-.17-.97-.28-1.5-.28C8.01 12 6 14.01 6 16.5S8.01 21 10.5 21c2.31 0 4.2-1.75 4.45-4H15V6h4V3h-7z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.sound-icon-voice {
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.sound-icon-wave {
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.sound-icon-mic {
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z'/%3E%3Cpath d='M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.sound-icon-speaker {
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M3 9v6h4l5 5V4L7 9H3zm7-.17v6.34L7.83 13H5v-2h2.83L10 8.83zM16.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77 0-4.28-2.99-7.86-7-8.77z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.sound-icon-headphone {
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 24 24'%3E%3Cpath d='M12 1c-4.97 0-9 4.03-9 9v7c0 1.66 1.34 3 3 3h3v-8H5v-2c0-3.87 3.13-7 7-7s7 3.13 7 7v2h-4v8h3c1.66 0 3-1.34 3-3v-7c0-4.97-4.03-9-9-9z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.sound-name-text {
  font-weight: 500;
  color: #495057;
  transition: color 0.3s ease;
}

.sound-name-cell:hover .sound-name-text {
  color: #6c7ae0;
}

/* 分页样式优化 */
.pagination-container {
  padding: 20px 0 !important;
  margin-top: 16px;
  justify-content: center !important;
  background: transparent !important;
}

.table-container :deep(.el-pagination) {
  padding: 20px 0;
  margin-top: 16px;
  justify-content: center;
  background: transparent;
}

.table-container :deep(.el-pagination .el-pager li.is-active) {
  background-color: #6c7ae0 !important;
  border-color: #6c7ae0 !important;
  color: white !important;
}

.table-container :deep(.el-pagination .btn-prev:hover),
.table-container :deep(.el-pagination .btn-next:hover),
.table-container :deep(.el-pagination .el-pager li:hover) {
  color: #6c7ae0 !important;
}

.table-container :deep(.el-pagination .btn-prev),
.table-container :deep(.el-pagination .btn-next) {
  background: white;
  border: 1px solid #dcdfe6;
}

.table-container :deep(.el-pagination .el-pager li) {
  background: white;
  border: 1px solid #dcdfe6;
  margin: 0 2px;
}

/* 状态标签样式 - 使用字典默认样式 */
.status-tag {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  gap: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: none;
}

/* 训练中状态特殊动画 */
.status-1 .status-indicator {
  animation: statusPulse 2s infinite;
}

/* 等待审核状态闪烁动画 */
.status-4 .status-indicator {
  animation: statusBlink 1.5s infinite;
}



/* 表格操作按钮 */
.table-actions {
  display: flex;
  justify-content: center;
  gap: 6px;
  padding: 5px 0;
}

.action-btn {
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  padding: 0 !important;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.action-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.action-btn:active {
  transform: translateY(1px) scale(0.98);
}

.action-btn .el-icon {
  font-size: 14px;
}

/* 提交审核按钮 - 绿色渐变 */
.success-action {
  background: linear-gradient(135deg, #28a745, #20c997) !important;
  border-color: transparent !important;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.25) !important;
}

.success-action:hover {
  background: linear-gradient(135deg, #218838, #1ea085) !important;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.35) !important;
  transform: translateY(-2px) scale(1.05) !important;
}

/* 修改按钮 - 蓝色渐变 */
.primary-action {
  background: linear-gradient(135deg, #007bff, #6c7ae0) !important;
  border-color: transparent !important;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.25) !important;
}

.primary-action:hover {
  background: linear-gradient(135deg, #0056b3, #5a67d8) !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.35) !important;
  transform: translateY(-2px) scale(1.05) !important;
}

/* 删除按钮 - 红色渐变 */
.danger-action {
  background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
  border-color: transparent !important;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.25) !important;
}

.danger-action:hover {
  background: linear-gradient(135deg, #c82333, #c0392b) !important;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.35) !important;
  transform: translateY(-2px) scale(1.05) !important;
}

/* 审核按钮 - 橙色渐变 */
.warning-action {
  background: linear-gradient(135deg, #fd7e14, #f39c12) !important;
  border-color: transparent !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(253, 126, 20, 0.25) !important;
}

.warning-action:hover {
  background: linear-gradient(135deg, #e8590c, #e67e22) !important;
  box-shadow: 0 4px 12px rgba(253, 126, 20, 0.35) !important;
  color: white !important;
  transform: translateY(-2px) scale(1.05) !important;
}

.warning-action:disabled {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
  color: #6c757d !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
  transform: none !important;
  cursor: not-allowed !important;
}

/* 训练中按钮 - 紫色渐变 */
.training-action {
  background: linear-gradient(135deg, #6f42c1, #8e44ad) !important;
  border-color: transparent !important;
  cursor: default !important;
  box-shadow: 0 2px 8px rgba(111, 66, 193, 0.25) !important;
  position: relative;
  overflow: hidden;
}

.training-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: trainingShine 2s infinite;
}

.training-action:hover {
  transform: none !important;
  box-shadow: 0 3px 10px rgba(111, 66, 193, 0.3) !important;
}

/* 加载波动画 */
.loading-wave {
  display: flex;
  align-items: flex-end;
  height: 18px;
  justify-content: center;
  width: 100%;
}

.loading-wave span {
  width: 2px;
  height: 2px;
  margin: 0 1px;
  background-color: white;
  display: inline-block;
  animation: wave 1.4s infinite ease-in-out;
  border-radius: 1px;
  opacity: 0.8;
}

.loading-wave span:nth-child(1) { animation-delay: -1.2s; }
.loading-wave span:nth-child(2) { animation-delay: -1.0s; }
.loading-wave span:nth-child(3) { animation-delay: -0.8s; }
.loading-wave span:nth-child(4) { animation-delay: -0.6s; }
.loading-wave span:nth-child(5) { animation-delay: -0.4s; }

/* 修改声音弹窗样式 */
.update-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.update-dialog :deep(.el-dialog__header) {
  padding: 16px 24px;
  margin-right: 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  color: #495057;
}

.update-dialog :deep(.el-dialog__body) {
  padding: 24px;
  background: #ffffff;
}

.update-dialog :deep(.el-form-item__label) {
  font-weight: 500;
  color: #495057;
}

.update-form {
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
}

/* 音频播放器样式 */
.audio-player-wrapper {
  width: 100%;
  border-radius: 8px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  position: relative;
}

.audio-visualizer {
  display: flex;
  align-items: flex-end;
  height: 30px;
  gap: 2px;
  margin-bottom: 12px;
  justify-content: center;
}

.visualizer-bar {
  width: 3px;
  height: 16px;
  background: #6c7ae0;
  opacity: 0.8;
  border-radius: 2px;
  animation: wave 1.4s ease-in-out infinite;
}

.visualizer-bar:nth-child(odd) { animation-delay: 0.2s; height: 20px; }
.visualizer-bar:nth-child(even) { animation-delay: 0.4s; height: 12px; }
.visualizer-bar:nth-child(3n) { animation-delay: 0.6s; height: 24px; }

.audio-player {
  width: 100%;
  height: 40px;
  outline: none;
  border-radius: 20px;
}

.audio-player::-webkit-media-controls-panel {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

/* 对话框底部按钮 */
.dialog-btn {
  min-width: 100px;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-btn {
  background: linear-gradient(135deg, #6c7ae0, #7b68ee);
  border-color: transparent;
  box-shadow: 0 3px 8px rgba(108, 122, 224, 0.2);
  color: white;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #5a67d8, #6a4190);
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(108, 122, 224, 0.3);
}

/* 动画关键帧 */
@keyframes soundWave {
  0%, 100% {
    transform: scaleY(1);
    opacity: 0.8;
  }
  50% {
    transform: scaleY(2);
    opacity: 1;
  }
}

@keyframes equalize {
  0%, 100% {
    transform: scaleY(1);
    opacity: 0.8;
  }
  50% {
    transform: scaleY(1.6);
    opacity: 1;
  }
}

@keyframes statusPulse {
  0% {
    box-shadow: 0 0 0 0px rgba(32, 201, 151, 0.4);
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    box-shadow: 0 0 0 6px rgba(32, 201, 151, 0);
    transform: scale(1);
  }
}

@keyframes statusBlink {
  0%, 50% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0.3;
  }
}

@keyframes wave {
  0%, 40%, 100% {
    transform: scaleY(1);
    opacity: 0.6;
  }
  20% {
    transform: scaleY(6);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes badgePulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes deleteParticles {
  0% {
    opacity: 0;
    transform: scale(0) translate(0, 0);
  }
  20% {
    opacity: 1;
    transform: scale(1) translate(0, 0);
  }
  80% {
    opacity: 1;
    transform: scale(1) translate(var(--random-x, 10px), var(--random-y, -10px));
  }
  100% {
    opacity: 0;
    transform: scale(0) translate(var(--random-x, 10px), var(--random-y, -10px));
  }
}

@keyframes badgeGlow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
}

@keyframes uploadParticles {
  0% {
    opacity: 0;
    transform: scale(0) translateY(0);
  }
  25% {
    opacity: 1;
    transform: scale(1) translateY(-5px);
  }
  75% {
    opacity: 1;
    transform: scale(1) translateY(-15px);
  }
  100% {
    opacity: 0;
    transform: scale(0) translateY(-20px);
  }
}

@keyframes modelParticles {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  25% {
    opacity: 1;
    transform: scale(1) rotate(90deg);
  }
  75% {
    opacity: 1;
    transform: scale(1) rotate(270deg);
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(360deg);
  }
}

@keyframes uploadPulse {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(3);
  }
}

@keyframes modelPulse {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(2.5);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(4);
  }
}

@keyframes trainingShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes resetParticles {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  25% {
    opacity: 1;
    transform: scale(1);
  }
  75% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0);
  }
}

/* 页面加载动画 */
.sound-container {
  animation: fadeInUp 0.6s ease-out;
}

.search-panel {
  animation: fadeInUp 0.8s ease-out 0.1s both;
}

.table-panel {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .sound-container {
    padding: 16px;
  }

  .header-content {
    padding: 18px 24px;
  }

  .query-input, .query-select {
    width: 200px;
  }

  .main-actions {
    gap: 12px;
  }

  .fancy-btn {
    height: 42px;
    padding: 0 16px;
  }
}

@media (max-width: 992px) {
  .header-left h2 {
    font-size: 20px;
  }

  .sound-logo {
    width: 40px;
    height: 40px;
    margin-right: 14px;
  }

  .equalizer-container {
    padding: 6px 12px;
  }

  .search-form {
    gap: 12px;
  }

  .query-input, .query-select {
    width: 160px;
  }

  .action-button {
    min-width: 70px;
    padding: 8px 12px;
  }

  .action-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .main-actions {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .sound-container {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
    padding: 16px;
  }

  .header-left {
    justify-content: center;
  }

  .header-left h2 {
    font-size: 18px;
  }

  .search-form {
    flex-direction: column;
    width: 100%;
    gap: 16px;
    align-items: stretch;
  }

  .query-input, .query-select {
    width: 100%;
  }

  .action-item {
    width: 100%;
    margin-left: 0;
  }

  .button-group {
    width: 100%;
    justify-content: center;
  }

  .action-button {
    flex: 1;
    min-width: auto;
    max-width: 120px;
  }

  .main-actions {
    flex-direction: column;
    gap: 12px;
  }

  .fancy-btn {
    width: 100%;
    justify-content: center;
  }

  .batch-delete-btn {
    width: 100% !important;
    min-width: auto !important;
    height: 42px !important;
  }

  .btn-content {
    gap: 6px;
  }

  .btn-text {
    font-size: 13px;
  }

  .table-actions {
    flex-wrap: wrap;
    gap: 4px;
  }

  .action-btn {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
  }

  .action-btn .el-icon {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .sound-container {
    padding: 8px;
  }

  .header-content {
    padding: 12px;
  }

  .sound-logo {
    width: 36px;
    height: 36px;
    margin-right: 12px;
  }

  .header-left h2 {
    font-size: 16px;
  }

  .equalizer-container {
    padding: 4px 10px;
  }

  .card-header {
    font-size: 14px;
  }

  .fancy-btn {
    height: 38px;
    font-size: 13px;
  }

  .batch-delete-btn {
    height: 38px !important;
    padding: 0 16px !important;
    border-radius: 8px !important;
    min-width: 120px !important;
  }

  .delete-icon-container {
    width: 18px;
    height: 18px;
  }

  .delete-icon {
    width: 14px;
    height: 14px;
  }

  .btn-text {
    font-size: 12px;
  }

  .selection-badge {
    padding: 2px 6px;
    min-width: 16px;
    height: 16px;
  }

  .badge-count {
    font-size: 10px;
  }

  .btn-content {
    gap: 6px;
  }

  .upload-icon, .model-icon {
    width: 16px;
    height: 16px;
  }

  .sound-name-cell {
    gap: 8px;
  }

  .sound-icon-wrapper {
    width: 28px;
    height: 28px;
  }

  .cell-sound-icon {
    width: 14px;
    height: 14px;
  }

  .sound-name-text {
    font-size: 14px;
  }

  .action-btn {
    width: 26px !important;
    height: 26px !important;
    min-width: 26px !important;
  }

  .action-btn .el-icon {
    font-size: 11px;
  }

  .table-container :deep(.el-pagination) {
    padding: 16px 0 !important;
  }

  .table-container :deep(.el-pagination .el-pager li) {
    margin: 0 1px;
    min-width: 28px;
    height: 28px;
    line-height: 28px;
  }
}
</style>