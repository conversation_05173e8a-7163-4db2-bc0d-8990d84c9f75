<script setup>
import { listScenecon } from "@/api/platform/scenecon";
import { useRoute, useRouter } from "vue-router";
import { computed, nextTick } from "vue";
import modal from "@/plugins/modal";
const route = useRoute()
const projectId = computed(() => route.params.projectId);
const router = useRouter();
const sceneconList = ref([])
const total = ref(0)
const sceneconTable = ref(null)
const props = defineProps({
    ids: {
        type: Number
    },
})
const queryParams = reactive({
    pageNum: 1,
    pageSize: 5,
    projectId: projectId.value
});
const idsSelect = ref({})
const emit = defineEmits(['select']);
function handleSelectionChange(select) {
    if (select.length > 1) {
        modal.msgError("最多只能选择一个场控")
        const lastSelectedRow = select.pop();
        sceneconTable.value.toggleRowSelection(lastSelectedRow, false);
    } else {
        idsSelect.value = {}
        for (let x of select) {
            idsSelect.value[x.sceneconId] = x.sceneconName;
        }
        emit('select', idsSelect.value)
    }

}
function getListScenecon() {
    listScenecon({ projectId: projectId.value }).then(res => {
        sceneconList.value = res.rows;
        total.value = res.total;
        handleIdsChange();
    });
}

function handleIdsChange() {
    sceneconList.value.forEach(row => {
        sceneconTable.value.toggleRowSelection(row, false);
    })
    if (props.ids) {
        sceneconList.value.forEach(row => {
            if (row.sceneconId === props.ids) {
                sceneconTable.value.toggleRowSelection(row, true);
            }
        });

    }
}

watch(() => props.ids, handleIdsChange, { immediate: true });
onMounted(() => {
    getListScenecon();
});
</script>
<template>
    <el-table :data="sceneconList" ref="sceneconTable" @selection-change="handleSelectionChange" max-height="200"
        :row-key="(row) => row.sceneconId">
        <el-table-column type="selection" width="80" align="center" reserve-selection />
        <el-table-column prop="sceneconName" min-width="100" label="场控" />
    </el-table>
</template>
<style lang="scss" scoped>
.scene {
    display: flex;
    justify-content: center;
}
</style>