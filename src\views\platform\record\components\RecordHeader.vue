<template>
    <div class="record-header">
        <el-dropdown trigger="click" @command="handleCreateCommand">
            <el-button type="primary" class="record-create-btn">新 建</el-button>
            <template #dropdown>
                <el-dropdown-menu class="record-create-dropdown">
                    <el-dropdown-item command="upload">
                        <i class="iconfont icon-upload" style="color:#1ec6e6;font-size:20px;margin-right:8px;"></i>
                        上传本地音视频文件
                    </el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
        
        <div class="record-header-actions">
            <!-- 非选择模式下显示的链接 -->
            <template v-if="!isSelectionMode">
                <el-dropdown trigger="click" @command="handleViewCommand">
                    <span class="record-header-link">
                        视图 <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu class="view-dropdown">
                            <el-dropdown-item command="grid" :class="{ 'is-active': currentView === 'grid' }">
                                <el-icon><Grid /></el-icon>
                                网格视图
                            </el-dropdown-item>
                            <el-dropdown-item command="list" :class="{ 'is-active': currentView === 'list' }">
                                <el-icon><List /></el-icon>
                                列表视图
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
                <el-dropdown trigger="click" @command="handleSortCommand">
                    <span class="record-header-link">
                        排序 <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu class="sort-dropdown">
                            <el-dropdown-item command="createTime_desc" :class="{ 'is-active': currentSort === 'createTime_desc' }">
                                <el-icon><Clock /></el-icon>
                                创建时间 (最新优先)
                            </el-dropdown-item>
                            <el-dropdown-item command="createTime_asc" :class="{ 'is-active': currentSort === 'createTime_asc' }">
                                <el-icon><Clock /></el-icon>
                                创建时间 (最早优先)
                            </el-dropdown-item>
                            <el-dropdown-item command="duration_desc" :class="{ 'is-active': currentSort === 'duration_desc' }">
                                <el-icon><Timer /></el-icon>
                                时长 (长到短)
                            </el-dropdown-item>
                            <el-dropdown-item command="duration_asc" :class="{ 'is-active': currentSort === 'duration_asc' }">
                                <el-icon><Timer /></el-icon>
                                时长 (短到长)
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
                <span class="record-header-link" @click="toggleAdvancedFilter">
                    {{ showAdvancedFilter ? '收起筛选' : '高级筛选' }}
                </span>
            </template>

            <!-- 选择模式下显示批量删除按钮（放在取消批量左边） -->
            <el-button
                v-if="isSelectionMode"
                size="small"
                :disabled="selectedItems.length === 0"
                @click="handleBatchDelete"
                class="batch-delete-btn"
            >
                <el-icon>
                    <Delete />
                </el-icon>
                批量删除 ({{ selectedItems.length }})
            </el-button>

            <span class="record-header-link" @click="toggleSelectionMode">
                {{ isSelectionMode ? '取消批量' : '批量' }}
            </span>
        </div>
    </div>
</template>

<script setup>
import { ArrowDown, Grid, List, Clock, Timer, Delete } from '@element-plus/icons-vue'

const props = defineProps({
    isSelectionMode: {
        type: Boolean,
        default: false
    },
    selectedItems: {
        type: Array,
        default: () => []
    },
    currentView: {
        type: String,
        default: 'grid'
    },
    currentSort: {
        type: String,
        default: 'createTime_desc'
    },
    showAdvancedFilter: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits([
    'create-command',
    'view-command', 
    'sort-command',
    'toggle-advanced-filter',
    'batch-delete',
    'toggle-selection-mode'
])

const handleCreateCommand = (command) => {
    console.log('RecordHeader handleCreateCommand called with:', command)
    emit('create-command', command)
}

const handleViewCommand = (command) => {
    emit('view-command', command)
}

const handleSortCommand = (command) => {
    emit('sort-command', command)
}

const toggleAdvancedFilter = () => {
    emit('toggle-advanced-filter')
}

const handleBatchDelete = () => {
    emit('batch-delete')
}

const toggleSelectionMode = () => {
    emit('toggle-selection-mode')
}
</script>

<style lang="scss" scoped>
.record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-left: 32px;

    .record-create-btn {
        width: 96px;
        height: 40px;
        font-size: 18px;
        border-radius: 12px;
        margin-right: 32px;
    }

    .record-header-actions {
        display: flex;
        gap: 16px;
        align-items: center;
        margin-right:20px;

        .record-header-link {
            color: #7a8599;
            font-size: 16px;
            cursor: pointer;
            transition: color 0.2s;

            &:hover {
                color: #409eff;
            }
        }

        .batch-delete-btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            background-color: #ffffff;
            color: #606266;
            border-color: #dcdfe6;

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                background-color: #f5f7fa;
            }

            &:not(:disabled):hover {
                transform: translateY(-1px);
                color: #409eff;
                border-color: #c6e2ff;
                background-color: #ecf5ff;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
            }

            .el-icon {
                color: #f56c6c;
                margin-right: 4px;
            }
        }
    }
}

:deep(.record-create-dropdown) {
    min-width: 220px;
    padding: 8px 0;
    border-radius: 16px;
    box-shadow: 0 4px 24px 0 rgba(31, 56, 88, 0.10);
}

:deep(.view-dropdown),
:deep(.sort-dropdown) {
    min-width: 200px;
    padding: 8px 0;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    .el-dropdown-menu__item {
        padding: 12px 16px;
        font-size: 14px;
        color: #606266;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 8px;

        &:hover {
            background: #f5f7fa;
            color: #409eff;
        }

        &.is-active {
            background: #ecf5ff;
            color: #409eff;
            font-weight: 500;

            .el-icon {
                color: #409eff;
            }
        }

        .el-icon {
            font-size: 16px;
            color: #909399;
            transition: color 0.2s ease;
        }
    }
}
</style>
