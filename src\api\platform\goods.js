import request from '@/utils/request'

// 查询产品管理列表
export function listGoods(query) {
  return request({
    url: '/platform/goods/list',
    method: 'get',
    params: query
  })
}

// 查询产品管理详细
export function getGoods(goodsId) {
  return request({
    url: '/platform/goods/' + goodsId,
    method: 'get'
  })
}

// 新增产品管理
export function addGoods(data) {
  return request({
    url: '/platform/goods',
    method: 'post',
    data: data
  })
}

// 修改产品管理
export function updateGoods(data) {
  return request({
    url: '/platform/goods',
    method: 'put',
    data: data
  })
}

// 删除产品管理
export function delGoods(goodsId) {
  return request({
    url: '/platform/goods/' + goodsId,
    method: 'delete'
  })
}

//获取产品信息
export function getAllGoods(data) {
  return request({
    url: '/platform/goods/goodsIdsAndName',
    method: 'get',
    params:{
      goodsId:data
    },
  })
}