/**
 * @file projectMaterialsLoader.ts
 * @description 工程素材加载器，专门处理云剪辑和模板工厂两种模式下的素材加载
 *              进一步简化 useVideoEditor.ts 中的长函数，提供统一的素材加载接口
 */

import type { EditSource } from './editSourceSelector';
import { createSelector } from './editSourceSelector';
import { ElMessage } from 'element-plus';

/**
 * 素材加载结果
 */
export interface MaterialsLoadResult {
  materials: any[];
  loadMethod: 'cloudEdit' | 'templateNew' | 'templateFallback';
  success: boolean;
  message?: string;
}


/**
 * 检查素材加载结果并显示用户友好的提示
 * @param result - 素材加载结果
 * @param showMessage - 是否显示消息提示
 */
export function handleMaterialsLoadResult(result: MaterialsLoadResult, showMessage: boolean = true): void {
  if (!showMessage) return;
  
  if (result.success) {
    const methodText = {
      cloudEdit: '云剪辑API',
      templateNew: '模板素材API（已废弃）',
      templateFallback: '模板标准方案'
    }[result.loadMethod];
    
    if (result.materials.length > 0) {
      ElMessage.success(`素材加载成功，共${result.materials.length}个素材 (${methodText})`);
    } else {
      ElMessage.info(`暂无素材 (${methodText})`);
    }
  } else {
    ElMessage.error(`素材加载失败: ${result.message}`);
  }
}

/**
 * 安全地处理素材加载结果
 * @param result - 素材加载结果
 * @param showUserMessage - 是否显示用户提示
 * @returns 处理后的结果
 */
export function handleMaterialsLoadResultSafe(
  result: MaterialsLoadResult, 
  showUserMessage: boolean = false
): { materials: any[], hasError: boolean } {
  if (result.success) {
    return { materials: result.materials, hasError: false };
  } else {
    if (showUserMessage) {
      console.error(`素材加载失败: ${result.message}`);
    }
    return { materials: [], hasError: true };
  }
}
