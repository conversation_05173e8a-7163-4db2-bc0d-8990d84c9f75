<template>
  <div class="qa-content">
    <div v-if="qaReview" class="qa-list">
      <div v-for="(qa, index) in formattedQaReview" :key="index" class="qa-item">
        <div class="qa-question">
          <el-tag size="small" type="info">{{ qa.sort }}</el-tag>
          <span class="question-text">{{ qa.question }}</span>
        </div>
        <div class="qa-answer">
          <div class="answer-icon">💡</div>
          <span class="answer-text">{{ qa.answer }}</span>
        </div>
      </div>
    </div>
    <div v-else class="custom-empty">
      <div class="empty-icon">❓</div>
      <div class="empty-text">暂无问答信息</div>
      <div class="empty-desc">问答内容正在生成中</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  qaReview: {
    type: String,
    default: ''
  }
})

// 格式化问答回顾
const formattedQaReview = computed(() => {
  if (!props.qaReview) return []
  try {
    const qaReview = JSON.parse(props.qaReview)
    return qaReview.map((qa, index) => ({
      question: qa.Question,
      answer: qa.Answer,
      sort: `问题${index + 1}`,
    }))
  } catch (error) {
    console.error('解析问答回顾失败:', error)
    return []
  }
})
</script>

<style lang="scss" scoped>
/* 问答回顾样式 */
.qa-content {
  padding: 0;
  position: relative;
}

.qa-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.qa-item {
  position: relative;
  padding: 24px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.04),
    0 1px 3px rgba(0, 0, 0, 0.02);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp 0.6s ease-out forwards;
}

.qa-item:nth-child(1) { animation-delay: 0.1s; }
.qa-item:nth-child(2) { animation-delay: 0.2s; }
.qa-item:nth-child(3) { animation-delay: 0.3s; }
.qa-item:nth-child(4) { animation-delay: 0.4s; }
.qa-item:nth-child(5) { animation-delay: 0.5s; }

.qa-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
  background-size: 200% 100%;
  animation: gradientShift 4s ease-in-out infinite;
}

.qa-item:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04);
  border-color: rgba(59, 130, 246, 0.2);
}

.qa-question {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.qa-question :deep(.el-tag) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  font-weight: 600;
  font-size: 11px;
  padding: 4px 10px;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.25);
  min-width: 28px;
  text-align: center;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.qa-question :deep(.el-tag):hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35);
}

.question-text {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  flex: 1;
  line-height: 1.6;
  position: relative;
  padding-left: 24px;
}

.question-text::before {
  content: '🤔';
  position: absolute;
  left: 0;
  top: 0;
  font-size: 16px;
  animation: bounce 2s ease-in-out infinite;
}

.qa-answer {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  position: relative;
  transition: all 0.2s ease;
}

.qa-answer:hover {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.qa-answer::before {
  content: '�';
  position: absolute;
  top: 18px;
  left: -12px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 50%;
  padding: 4px;
  font-size: 14px;
  box-shadow:
    0 3px 8px rgba(0, 0, 0, 0.12),
    0 1px 3px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.8);
  animation: pulse 3s ease-in-out infinite;
  transition: all 0.2s ease;
}

.qa-answer:hover::before {
  transform: scale(1.1);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 6px rgba(0, 0, 0, 0.1);
}

.answer-text {
  font-size: 14px;
  color: #334155;
  line-height: 1.7;
  font-weight: 400;
  text-align: justify;
  margin-left: 12px;
}

/* 动画定义 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-3px); }
  60% { transform: translateY(-2px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 自定义空状态样式 */
.custom-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 2px dashed rgba(148, 163, 184, 0.3);
  transition: all 0.3s ease;
  animation: fadeIn 0.8s ease-out;
}

.custom-empty:hover {
  border-color: rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  transform: translateY(-2px);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  animation: float 3s ease-in-out infinite;
}

.empty-text {
  font-size: 18px;
  font-weight: 600;
  color: #475569;
  margin-bottom: 8px;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.empty-desc {
  font-size: 14px;
  color: #64748b;
  opacity: 0.8;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 答案图标样式 */
.answer-icon {
  position: absolute;
  top: 18px;
  left: -12px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 50%;
  padding: 4px;
  font-size: 14px;
  box-shadow:
    0 3px 8px rgba(0, 0, 0, 0.12),
    0 1px 3px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.8);
  animation: pulse 3s ease-in-out infinite;
  transition: all 0.2s ease;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qa-answer:hover .answer-icon {
  transform: scale(1.1);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 6px rgba(0, 0, 0, 0.1);
}
</style>
