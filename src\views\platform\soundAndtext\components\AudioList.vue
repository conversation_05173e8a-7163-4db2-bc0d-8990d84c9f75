<template>
  <div class="div-container">
    <div class="audio-list-toolbar">
      <div class="audio-list-title">
        <el-icon>
          <Microphone />
        </el-icon>
        <span>音频列表</span>
      </div>
      <div class="audio-list-toolbar-right">
        <el-checkbox v-model="allSelected" @change="toggleSelectAll">全选</el-checkbox>
        <el-button type="danger" size="small" :disabled="selectedIds.length === 0"
          @click="handleDelete()" v-if="!isSelectionMode">批量删除</el-button>
        <el-button type="info" size="small" @click="handleReset" v-if="!isSelectionMode">重置</el-button>
        <el-button type="success" size="small" :disabled="selectedIds.length === 0"
          @click="confirmSelection" v-if="isSelectionMode">确认选择 ({{ selectedIds.length }})</el-button>
      </div>
    </div>
    <div class="audio-grid">
      <div v-for="item in audioList" :key="item.audioId" class="audio-card">
        <div class="card-header">
          <div class="title-section">
            <el-checkbox v-model="selectedIds" :label="item.audioId" class="audio-checkbox" />
            <el-tooltip :content="item.content" placement="top" effect="dark">
              <span class="title-text" @click="handleGoToSynthesis(item)">{{ truncateText(item.content, 12)}}</span>
            </el-tooltip>
            <el-button type="danger" size="small" @click.stop="handleDelete(item.audioId)">删除</el-button>
          </div>
        </div>
        <div class="player-section">
          <audioPlayer :audioSrc="item.audioId" style="width: 100%;" />
        </div>
      </div>
    </div>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getAudioList" class="pagination-container" />
    <SelectSynthesisVersion v-model:visible="showVersionDialog" :audioInfo="selectedAudio"
      @version-selected="onVersionSelected" />
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import audioPlayer from '@/views/platform/material/components/audio-player'
import { audioAndText, delAudio } from '@/api/platform/audio'
import Pagination from '@/components/Pagination/index.vue'
import SelectSynthesisVersion from './SelectSynthesisVersion.vue'
import { useRoute, useRouter } from 'vue-router'

const props = defineProps({
  versionKey: String,
  isSelectionMode: {
    type: Boolean,
    default: false
  },
  maxCount: {
    type: Number,
    default: 0
  },
  returnPath: {
    type: String,
    default: ''
  }
})

const audioList = ref([])
const total = ref(0)
const queryParams = ref({
  pageNum: 1,
  pageSize: 6 // 调整为每页显示6个，一行2个，共3行
})

const showVersionDialog = ref(false)
const selectedAudio = ref(null)
const selectedIds = ref([])
const allSelected = ref(false)

const router = useRouter()
const route = useRoute()

// 添加文本截断函数
const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 点击音频弹窗
const handleGoToSynthesis = (audio) => {
  const audioPath = audio.audioPath || audio.audioAddress || audio.path || audio.address || audio.filePath
  if (!audioPath) {
    ElMessage.error('音频路径无效，无法选择');
    return;
  }
  selectedAudio.value = { audioPath, audioName: audio.audioName, content: audio.content }
  if (props.versionKey) {
    // 有版本参数，直接跳转
    let versionRoute = ''
    if (props.versionKey === 'm') versionRoute = '/szbVideo/createMusetalk'
    else if (props.versionKey === 'v') versionRoute = '/szbVideo/synthesisCreate'
    else if (props.versionKey === 'h') versionRoute = '/szbVideo/createHeygem'
    if (versionRoute) {
      const query = {
        audioPath,
        audioName: audio.audioName,
        content: audio.content, // 传递content
        from: 'textAudio',
      }
      if (route.query.keepImageAddress) query.keepImageAddress = route.query.keepImageAddress
      if (route.query.imageAddress) query.imageAddress = route.query.imageAddress
      router.push({ path: versionRoute, query })
      return
    }
  }
  // 没有版本参数，弹窗
  showVersionDialog.value = true
}

// 版本选择弹窗回调，选择后直接跳转
function onVersionSelected(versionKey) {
  if (versionKey) {
    const audio = selectedAudio.value
    if (audio) {
      let versionRoute = ''
      if (versionKey === 'm') versionRoute = '/szbVideo/createMusetalk'
      else if (versionKey === 'v') versionRoute = '/szbVideo/synthesisCreate'
      else if (versionKey === 'h') versionRoute = '/szbVideo/createHeygem'
      if (versionRoute) {
        const query = {
          audioPath: audio.audioPath,
          audioName: audio.audioName,
          content: audio.content, // 传递content
          from: 'textAudio',
        }
        if (route.query.keepImageAddress) query.keepImageAddress = route.query.keepImageAddress
        if (route.query.imageAddress) query.imageAddress = route.query.imageAddress
        router.push({ path: versionRoute, query })
      }
    }
  }
}

const getAudioList = async () => {
  try {
    const response = await audioAndText(queryParams.value)
    audioList.value = (response.rows || []).sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
    total.value = response.total || 0
    selectedIds.value = []
    allSelected.value = false
  } catch (error) {
    console.error('获取音频列表失败:', error)
  }
}

// 单个或批量删除
function handleDelete(id) {
  let ids = []
  if (id) {
    ids = [id]
  } else {
    ids = selectedIds.value
  }
  if (!ids.length) return
  ElMessageBox.confirm(
    `是否确认删除选中的${ids.length}个音频？`,
    '删除',
    { type: 'warning', confirmButtonText: '删除', cancelButtonText: '取消' }
  ).then(async () => {
    await delAudio(ids)
    ElMessage.success('删除成功')
    getAudioList()
  }).catch(() => { })
}

// 重置操作，清空选中项、重置分页并刷新列表
function handleReset() {
  selectedIds.value = [];
  allSelected.value = false;
  queryParams.value.pageNum = 1;
  queryParams.value.pageSize = 6; // 重置为每页6个
  getAudioList();
}

function toggleSelectAll(val) {
  if (val) {
    selectedIds.value = audioList.value.map(item => item.audioId)
  } else {
    selectedIds.value = []
  }
}

// 确认选择方法
function confirmSelection() {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请至少选择一个声音')
    return
  }

  if (props.maxCount > 0 && selectedIds.value.length > props.maxCount) {
    ElMessage.warning(`最多只能选择${props.maxCount}个声音`)
    return
  }

  // 获取选中的声音数据
  const selectedVoices = audioList.value.filter(item => selectedIds.value.includes(item.audioId))

  // 跳转回对话合成界面，携带选择结果
  router.push({
    path: props.returnPath,
    query: {
      selectedData: encodeURIComponent(JSON.stringify(selectedVoices)),
      type: 'voices'
    }
  })
}

onMounted(() => {
  getAudioList()
})
</script>

<style lang="scss" scoped>
.div-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  @media (max-width: 899px) {
    padding: 16px;
  }

  @media (max-width: 480px) {
    padding: 12px;
  }
}

.audio-list-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
  flex-shrink: 0;

  @media (max-width: 899px) {
    margin-bottom: 14px;
    gap: 10px;
  }

  @media (max-width: 600px) {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}

.pagination-container {
  background-color: #ffffff;
  margin-top: 16px;
  flex-shrink: 0;

  @media (max-width: 899px) {
    margin-top: 14px;
  }
}

.audio-list-title {
  font-size: 18px;
  font-weight: 600;
  color: #3b3f5c;
  display: flex;
  align-items: center;
  gap: 8px;
  letter-spacing: 1px;

  @media (max-width: 899px) {
    font-size: 16px;
  }

  @media (max-width: 600px) {
    font-size: 15px;
    justify-content: center;
  }
}

.audio-list-toolbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;

  @media (max-width: 899px) {
    gap: 12px;
  }

  @media (max-width: 600px) {
    justify-content: center;
    gap: 10px;
  }
}

.audio-checkbox {
  margin-right: 8px;

  @media (max-width: 480px) {
    margin-right: 6px;
  }
}

.audio-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 一行显示2个音频 */
  margin: 0 auto;
  box-sizing: border-box;
  flex: 1;
  overflow-y: auto;
  padding: 8px 4px;
  position: relative;

  // 间距设置
  gap: 16px;

  // 小屏幕适配
  @media (max-width: 899px) {
    gap: 12px;
  }

  // 超小屏幕适配
  @media (max-width: 480px) {
    grid-template-columns: 1fr; /* 超小屏幕一行显示1个 */
    gap: 10px;
  }
}

.audio-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e8eaed;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;

  min-height: 150px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    border-color: #667eea;
  }

  .card-header {
    background: linear-gradient(to right, #f8fafc, white);
    border-bottom: 1px solid #e2e8f0;
    padding: 12px 16px 0 16px;
  }

  .title-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 8px 0;
  }

  .title-text {
    font-weight: 600;
    color: #409EFF;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    min-width: 0;
    border-radius: 4px;
    
    &:hover {
      color: #66b1ff;
      text-decoration: underline;
      background: rgba(64, 158, 255, 0.1);
    }
  }

  .player-section {
    background: linear-gradient(to bottom, white, #f8fafc);
    flex: 1;
    display: flex;
    align-items: center;
    padding: 16px;
  }
}

// 确保复选框在小屏幕上的显示
.audio-checkbox {
  @media (max-width: 480px) {
    :deep(.el-checkbox__label) {
      display: none;
    }
  }
}

// 工具栏按钮在小屏幕上的优化
.audio-list-toolbar-right {
  .el-button {
    @media (max-width: 480px) {
      font-size: 12px;
      padding: 6px 12px;
    }
  }

  .el-checkbox {
    @media (max-width: 480px) {
      font-size: 13px;
    }
  }
}
</style>  