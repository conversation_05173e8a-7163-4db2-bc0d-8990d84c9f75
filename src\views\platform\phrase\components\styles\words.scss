/* 右侧词汇列表区域 */
.right-words-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-left: 4px;
}

.words-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 8px;
    color: white;
}

.words-title {
    font-size: 16px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
}

.words-title::before {
    content: '📝';
    font-size: 18px;
}

.limit-warning {
    font-size: 12px;
    color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
    font-weight: 500;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.words-header .el-button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.words-header .el-button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.words-list-container {
    flex: 1;
    overflow-y: auto;
    border-radius: 12px;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
}

.words-list-container::-webkit-scrollbar {
    width: 6px;
}

.words-list-container::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 3px;
}

.words-list-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 3px;
}

.words-list-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

.empty-words {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    font-size: 14px;
    gap: 12px;
}

.empty-words::before {
    content: '📋';
    font-size: 48px;
    opacity: 0.5;
}

.words-list {
    padding: 16px;
}

.word-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.word-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.word-item:hover {
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.word-item:hover::before {
    transform: scaleX(1);
}

.word-item:last-child {
    margin-bottom: 0;
}

.word-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.word-text {
    font-size: 15px;
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 8px;
}

.word-text::before {
    content: '🏷️';
    font-size: 14px;
}

.word-actions {
    display: flex;
    gap: 8px;
}

.word-actions .el-button {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 6px;
}

.error-message {
    color: #e53e3e;
    font-size: 12px;
    margin-top: 6px;
    padding: 4px 8px;
    background: rgba(229, 62, 62, 0.1);
    border-radius: 4px;
    border-left: 3px solid #e53e3e;
}

/* 权重标签样式 */
.weight-tag {
    margin: 0;
    font-size: 12px;
    border-radius: 16px;
    padding: 4px 12px;
    white-space: nowrap;
    font-weight: 600;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.weight-tag.el-tag--success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.weight-tag.el-tag--danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
}

.weight-tag.el-tag--warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
}

.weight-tag:not(.el-tag--success):not(.el-tag--danger):not(.el-tag--warning) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}
