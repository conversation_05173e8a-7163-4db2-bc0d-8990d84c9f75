<script setup name="Anchor">
import { ref, onMounted, computed } from 'vue';
import { ElMessage } from "element-plus";
import { useRoute } from 'vue-router';
import { getProjectId, trainAnchor, generateReply, updateAnchor, deleteFieldAnchor } from '@/api/platform/anchor';

const route = useRoute();
const projectId = ref(route.params.projectId); // 项目编号
const loading = ref(false);
const replyLoading = ref(false); // 回复加载状态
const formRef = ref(null);
const form = ref({ questions: [''] }); 
const reply = ref(''); // 智能主播回复
const isTraining = ref(false); // 训练智能主播
const editMode = ref(false); // 取消编辑和编辑
const editableAnchor = ref({}); // 编辑时，用于存储原始数据 编辑主播信息
const anchor = ref({}); //展示智能主播数据
const anchorName = computed(() => anchor.value.anchorName);
const anchorRepository = computed(() => parseAnchorRepository(anchor.value.anchorRepository) || {});
const anchorId = ref(null); // 智能主播序号
const hasData = computed(() => Object.keys(anchorRepository.value).length > 0 || anchorName.value); // 检查知识库有没有数据
const newFields = ref([]); // 创建知识库字段
const showNewFields = ref(true); // 控制创建字段显示
const keys = ref([]); // 存储知识库键名
const loadingText = computed(() => (replyLoading.value ? "智能主播正在 回复中....." : isTraining.value ? "智能主播正在 训练中....." : ""));
const isLoading = computed(() => replyLoading.value || isTraining.value); //训练和智能展示状态
const editableFormRef = ref(null); // 智能主播信息编辑表单

/** 获取智能主播信息 */
const fetchAnchorDetails = async () => {
  loading.value = true;
  try {
    const response = await getProjectId(projectId.value);
    if (response.code !== 200) throw new Error('解析失败');
    const data = response.data;
    anchor.value = data || {};
    editableAnchor.value = { ...data, anchorRepository: parseAnchorRepository(data.anchorRepository) };  
    anchorId.value = data?.anchorId;
    keys.value = Object.keys(editableAnchor.value.anchorRepository);
  } catch (error) {
    console.error(`获取主播详情失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

/** 解析智能主播知识库数据 反 JSON 解析 */
const parseAnchorRepository = (data) => {
  if (!data) return {};
  try {
    return JSON.parse(data);
  } catch (error) {
    console.error('解析 JSON 失败:', error);
    return {}; // 解析失败，返回空对象
  }
};

/** 训练智能主播 */
const handleTrainAnchor = async () => {
  isTraining.value = true;
  try {
    const response = await trainAnchor(projectId.value);
    if (response.code === 200) {
      ElMessage.success("训练智能主播成功!");
      await fetchAnchorDetails();
    } else {
      ElMessage.error(`训练智能主播失败: ${response.msg}`);
    }
  } catch (error) {
    console.log(`训练智能主播失败: ${error.message}`);
  } finally {
    isTraining.value = false;
  }
};

/** 智能主播回复 */
const handleReplyAnchor = async () => {
  if (form.value.questions.length === 0 || form.value.questions.every(question => !question.trim())) {
    ElMessage.error('请至少输入一个问答！');
    return;
  }
  if (!hasData.value) {
    ElMessage.warning("知识库为空，暂时无法使用智能主播问答，请先训练智能主播！");
    return;
  }
  validateForm(async () => {
    replyLoading.value = true;
    reply.value = '';
    try {
      const res = await generateReply(projectId.value, { questions: form.value.questions }, (text) => {
        reply.value += text; });
      try {
        const x=JSON.parse(res);
        ElMessage.error("您算力点不足暂时无法使用此功能，请联系管理员进行充值！");
        reply.value = '';
      } catch (e) {} 
    } catch (error) {
      ElMessage.error(`智能主播回复失败: ${error.message}`);
    } finally {
      replyLoading.value = false;
    }
  }, formRef);
};

/**重置智能问答数据*/
const clearQuestions = () => {
  form.value.questions = [''];
  reply.value = '';
  structuredReply.value = [];
  replyLoading.value = false; // 重置回复加载状态
};

/**智能主播回复展示数据*/
const structuredReply = computed(() => {
  if (!reply.value) return [];
  try {
    const parsedReply = JSON.parse(reply.value);
    return parsedReply.map((answer, index) => ({
      question: form.value.questions[index],
      answer: answer
    }));
  } catch (error) {
    return [];
  }
});

const toggleEditMode = () => (editMode.value = !editMode.value); // 切换编辑模式
const addField = () => (showNewFields.value = true, newFields.value.push({ key: '', value: '' })); // 知识库新加字段
const addQuestion = () => form.value.questions.push(''); // 创建智能问答
const removeQuestion = (index) => form.value.questions.splice(index, 1); // 删除智能问答

/** 更新智能主播知识库 */
const handUpdateAchor = async () => {
  await validateForm(async () => {
    if (newFields.value.some(field => !field.key || !field.value)) {
      ElMessage.error('请您将新增字段都已填写完整！');
      return;
    }
    newFields.value.forEach(field => {
      if (field.key && field.value) {
        editableAnchor.value.anchorRepository[field.key] = field.value;
      }
    });
    const dataToSend = { ...editableAnchor.value, anchorRepository: JSON.stringify(editableAnchor.value.anchorRepository) };
    try {
      const response = await updateAnchor(dataToSend.anchorId, dataToSend);
      if (response.code === 200) {
        ElMessage.success("更新智能主播信息成功！");
        anchor.value = { ...dataToSend };
        toggleEditMode(); //切换编辑模式
        await fetchAnchorDetails(); //展示智能主播数据
        showNewFields.value = false;
        newFields.value = [];
      } else {
        ElMessage.error("更新智能主播信息失败！");
      }
    } catch (error) {
      ElMessage.error(`更新智能主播信息失败: ${error.message}`);
    }
  }, editableFormRef);
};

/**删除创建的字段 */
const removeField = async (keyOrIndex, isNewField = false) => {
  if (isNewField) {
    newFields.value.splice(keyOrIndex, 1);
  } else {
    try {
      delete editableAnchor.value.anchorRepository[keyOrIndex];
      await deleteFieldAnchor(anchorId.value, keyOrIndex);
      hasData.value = Object.keys(editableAnchor.value.anchorRepository).length > 0 || anchorName.value;
      ElMessage.success("删除成功！");
    } catch (error) {
      ElMessage.error(`删除失败: ${error.message}`);
      await fetchAnchorDetails();
    }
  }
};

/**更新键名 */
const updateKey = (oldKey, newKey, index) => {
  if (oldKey !== newKey) {
    const oldValue = editableAnchor.value.anchorRepository[oldKey];
    editableAnchor.value.anchorRepository[newKey] = oldValue;
    delete editableAnchor.value.anchorRepository[oldKey];
    keys.value = Object.keys(editableAnchor.value.anchorRepository);
  }
};

/**取消编辑 */
const cancelEdit = () => {
  editableAnchor.value = { ...anchor.value, anchorRepository: parseAnchorRepository(anchor.value.anchorRepository) };
  newFields.value = []; // 清空新字段
  showNewFields.value = false; // 隐藏新增字段部分
  toggleEditMode();
  fetchAnchorDetails();
};

/** 表单验证 */
const validateForm = async (callback, formRef) => {
  if (formRef.value) {
    if (await formRef.value.validate()) {
      await callback();
    } else {
      ElMessage.error('请先填写智能问答！');
    }
  } else {
    ElMessage.error('表单未初始化，请稍后再试！');
  }
};
onMounted(fetchAnchorDetails);
</script>

<template>
  <div class="anchor-workspace">
    <el-card class="control-panel">
      <template #header>
        <div class="action-header" v-loading="isLoading" :element-loading-text="loadingText">
          <div class="btn-group">
            <el-button class="custom-btn primary-btn" @click="handleTrainAnchor"><i class="el-icon-refresh-right rotate-animation"></i>训练智能主播</el-button>
            <el-button class="custom-btn success-btn" @click="handleReplyAnchor"><i class="el-icon-chat-dot-round"></i>智能问答回复</el-button>
            <el-button class="custom-btn warning-btn" @click="addQuestion"><i class="el-icon-plus"></i>新增智能问答</el-button>
            <el-button class="custom-btn danger-btn" @click="clearQuestions"><i class="el-icon-refresh"></i>重置对话信息</el-button>
          </div>
        </div>
      </template>
      <div class="qa-container">
        <el-form ref="formRef" :model="form" label-width="110px" @submit.prevent class="qa-form">
          <el-scrollbar ref="scrollbarRef" class="custom-scrollbar">
            <div class="scrollbar-wrapper">
              <transition-group name="list" tag="div" class="questions-list">
                <div v-for="(question, index) in form.questions" :key="index" class="question-item">
                  <el-form-item :label="`智能问答 ${index + 1}`" class="form-item-wrapper">
                    <div class="input-group">
                      <el-input v-model="form.questions[index]" placeholder="请输入您想咨询的问题"
                        @keydown.enter.prevent="addQuestion" />
                      <el-button class="delete-btn" type="danger" circle @click="removeQuestion(index)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </el-form-item>
                </div>
              </transition-group>
            </div>
          </el-scrollbar>
        </el-form>
       
        <div class="reply-section">
          <el-scrollbar >
          <transition name="fade" mode="out-in">
            <el-card v-if="structuredReply.length > 0" class="reply-card">
              <div class="reply-content">
                <div v-for="(item, index) in structuredReply" :key="index" class="reply-item animate__animated animate__fadeIn" :style="{ animationDelay: `${index * 0.2}s` }">
                  <div class="question-part">Q: {{ item.question }}</div>
                  <div class="answer-part">A: {{ item.answer }}</div>
                </div>
              </div>
            </el-card>
            <el-card v-else-if="reply" class="reply-card typing"><div class="reply-content"><div class="typing-text">{{ reply }}</div></div></el-card>
            <el-card v-else-if="replyLoading" class="reply-card loading"><div class="reply-content"><div class="loading-dots">智能主播正在思考中<span>...</span></div></div></el-card>
            <el-card v-else class="reply-card empty"><div class="reply-content"><div class="empty-state"><i class="el-icon-chat-line-round"></i><span>期待您的提问</span></div></div></el-card>
          </transition>
        </el-scrollbar>
        </div>
      </div>
    </el-card>

    <el-card class="anchor-info">
      <template #header>
        <div class="info-header">
          <div class="left-section"><i class="el-icon-user"></i><span class="anchor-name">{{ anchorName || '未命名主播' }}</span></div>
          <div class="right-section">
            <el-button v-if="editMode" class="custom-btn success-btn" @click="addField" :disabled="!hasData"><i class="el-icon-plus"></i>创建字段</el-button>
            <el-button class="custom-btn primary-btn" @click="toggleEditMode" :disabled="!hasData">
              <i :class="editMode ? 'el-icon-close' : 'el-icon-edit'"></i>{{ editMode ? '取消编辑' : '编辑信息' }}
            </el-button>
          </div>
        </div>
      </template>
      <div class="info-content">
        <div v-if="!isTraining">
          <div v-if="hasData">
            <el-form v-if="editMode" ref="editableFormRef" :model="editableAnchor" label-width="120px">
              <el-scrollbar style="max-height:352px; border: 1px solid #dcdfe6; padding-top:13px;padding-right:10px; overflow-y: auto">
                <el-form-item label="主播名称"><el-input v-model="editableAnchor.anchorName" placeholder="请输入主播名称" /></el-form-item>
                <el-form-item v-for="(value, key, index) in editableAnchor.anchorRepository" :key="index">
                  <el-input v-model="keys[index]" :placeholder="`请输入字段键名`" @change="updateKey(key, keys[index], index)" style="width: 15%" />
                  <el-input v-model="editableAnchor.anchorRepository[key]" :placeholder="`请输入${key}`" style="width: 80%; margin-left: 10px" />
                  <el-button class="delete-btn" icon="el-icon-delete" @click="removeField(key, false)" />
                </el-form-item>
                <el-form-item v-if="showNewFields" v-for="(field, index) in newFields" :key="`new-${index}`" :label="`新增字段 ${index + 1}`">
                  <el-input v-model="field.key" placeholder="请输入字段键名" style="width: 15%; margin-right: 10px" />
                  <el-input v-model="field.value" placeholder="请输入字段值" style="width: 80%" />
                  <el-button class="delete-btn" circle icon="el-icon-delete" @click="removeField(index, true)" />
                </el-form-item>
              </el-scrollbar>
              <br>
              <el-form-item>
                <el-button type="primary" @click="handUpdateAchor">保存</el-button>
                <el-button @click="cancelEdit">取消</el-button>
              </el-form-item>
            </el-form>
            <div v-else><div v-for="(value, key) in anchorRepository" :key="key" style="margin-bottom: 10px"><strong>{{ key }}:</strong> {{ value }}</div></div>
          </div>
          <div v-else>智能主播还未训练，暂时没有数据！</div>
        </div>
        <div v-else>智能主播正在训练中...</div>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
// 变量定义
$primary-gradient: linear-gradient(135deg, #667eea, #764ba2);
$success-gradient: linear-gradient(135deg, #2ecc71, #27ae60);
$warning-gradient: linear-gradient(135deg, #f1c40f, #f39c12);
$danger-gradient: linear-gradient(135deg, #e74c3c, #c0392b);
$delete-color: #ff4d4f;
$card-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
$border-radius: 12px;

.anchor-workspace {
  padding: 10px;
  background: #f0f2f5;

  // 通用卡片样式
  .el-card {
    margin-bottom: 10px;
    border-radius: $border-radius;
    box-shadow: $card-shadow;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }

  // 按钮样式
  .custom-btn {
    border: none;
    padding: 8px 15px;
    border-radius: 8px;
    color: white;
    transition: all 0.3s ease;
    margin: 0 5px;
    
    i {
      margin-right: 5px;
    }
    
    &.primary-btn { background-image: $primary-gradient; }
    &.success-btn { background-image: $success-gradient; }
    &.warning-btn { background-image: $warning-gradient; }
    &.danger-btn { background-image: $danger-gradient; }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  // 删除按钮统一样式
  .delete-btn {
    padding: 5px 8px;
    color: $delete-color;
    border: none;
    background: transparent;
    transition: all 0.3s;

    i {
      font-size: 16px;
    }
    
    &:hover {
      color: darken($delete-color, 10%);
      background: rgba($delete-color, 0.1);
    }
  }

  // 头部样式
  .info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;

    .left-section {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .right-section {
      display: flex;
      gap: 8px;
    }
  }

  // 保持其他动画效果
  .rotate-animation {
    display: inline-block;
    animation: rotate 2s linear infinite;
  }

  @keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  // 问答区域
  .qa-container {
    display: flex;
    height: 200px; // 改为固定高度
    gap: 20px;
    padding: 15px;
    margin-bottom: 20px; // 添加底部间距

    .qa-form {
      flex: 1;
      position: relative;
      min-height: 60px;
      max-width: 45%; // 调整宽度比例
    }

    .custom-scrollbar {
      height: 100%;
      border: 1px solid #dcdfe6;
      border-radius: 8px;
      padding: 12px;
      background: #fff;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      .scrollbar-wrapper {
        height: 100%;
      }
    }

    .questions-list {
      min-height: 44px;
    }

    .question-item {
      padding: 4px 0;
      
      .form-item-wrapper {
        margin-bottom: 4px;
        width: 100%;
        
        :deep(.el-form-item__content) {
          display: flex;
          width: calc(100% - 110px);
        }
      }

      .input-group {
        display: flex;
        align-items: center;
        width: 100%;
        gap: 8px;

        .el-input {
          flex: 1;
          height: 32px;
          
          :deep(.el-input__inner) {
            height: 32px;
            line-height: 32px;
          }
        }

        .delete-btn {
          flex-shrink: 0;
          width: 32px;
          height: 32px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .el-icon {
            font-size: 14px;
          }
        }
      }
    }

    // 渐变阴影效果
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 20px;
      background: linear-gradient(transparent, rgba(255,255,255,0.9));
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s;
    }

    &:hover::after {
      opacity: 1;
    }
  }

  // 回复区域
  .reply-section {
    flex: 1;
    overflow: hidden; // 防止内容溢出
    
    .reply-card {
      height: 100%;
      
      .reply-content {
        height: 133px; // 设置固定高度
        overflow-y: auto;
        padding: 15px;
        background: #fff;
        border-radius: 8px;
        
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.05);
        }
      }
      
      .reply-item {
        margin-bottom: 15px;
        padding: 12px;
        border-radius: 8px;
        background: #f8f9fa;
        border: 1px solid #ebeef5;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        
        .question-part {
          color: #409EFF;
          font-weight: 500;
          margin-bottom: 8px;
        }
        
        .answer-part {
          color: #333;
          line-height: 1.6;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      .empty-state, .loading-dots {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #909399;
        font-size: 16px;

        i {
          margin-right: 8px;
          font-size: 24px;
        }
      }
    }
  }

  // 添加淡入淡出过渡效果
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
</style>