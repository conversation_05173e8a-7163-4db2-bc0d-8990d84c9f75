/**
 * 轨道ID重排器
 * 专门处理轨道插入时的ID重新分配，保证ID不重复且不影响其他分组
 */

export interface TrackIdReorderResult {
  success: boolean;
  newTrackId: number;
  adjustedTracks: Array<{ oldId: number; newId: number }>;
  error?: string;
}

export interface InsertInfo {
  trackType: 'Video' | 'Audio' | 'Subtitle';
  insertPosition: 'top' | 'bottom' | 'above' | 'below';
  targetTrackIndex?: number; // 具体要插入的轨道索引（来自dragResult）
}

/**
 * 轨道ID重排器类
 */
export class TrackIdReorderer {
  
  /**
   * 为新轨道插入重新分配ID
   * @param timeline Timeline数据
   * @param insertInfo 插入信息
   * @returns 重排结果
   */
  static reorderTrackIds(timeline: any, insertInfo: InsertInfo): TrackIdReorderResult {
    try {
      const { trackType, insertPosition, targetTrackIndex } = insertInfo;
      
      // 1. 获取当前轨道类型的所有轨道
      const tracksKey = `${trackType}Tracks`;
      const tracks = timeline[tracksKey] || [];
      
      if (tracks.length === 0) {
        return {
          success: true,
          newTrackId: 1,
          adjustedTracks: []
        };
      }
      
      // 2. 按当前ID排序（降序，ID大的在前面）
      const sortedTracks = [...tracks].sort((a, b) => b.Id - a.Id);
      
      // 3. 根据插入位置计算新的ID分配
      let newTrackId: number;
      let adjustedTracks: Array<{ oldId: number; newId: number }> = [];
      
      if (insertPosition === 'top') {
        // 插入到最上面：新ID = 最大ID + 1（ID越大显示越上面）
        const maxId = Math.max(...tracks.map((t: any) => t.Id));
        newTrackId = maxId + 1;
        
      } else if (insertPosition === 'bottom') {
        // 插入到最下面：新ID = 最小ID - 1，如果小于1则重排
        const minId = Math.min(...tracks.map((t: any) => t.Id));
        newTrackId = minId - 1;
        
        if (newTrackId < 1) {
          // 如果计算出的ID小于1，给新轨道ID=1，其他轨道ID依次+1
          newTrackId = 1;
          adjustedTracks = tracks.map((track: any) => ({
            oldId: track.Id,
            newId: track.Id + 1
          }));
        }
        
      } else if ((insertPosition === 'above' || insertPosition === 'below') && targetTrackIndex !== undefined) {
        // 插入到指定轨道的上方或下方
        
        // 特殊情况处理：如果是插入到最后一个轨道下方，等同于插入到底部
        const sortedTracks = [...tracks].sort((a, b) => b.Id - a.Id);
        if (insertPosition === 'below' && targetTrackIndex >= sortedTracks.length - 1) {
          const minId = Math.min(...tracks.map((t: any) => t.Id));
          
          // 修复：在最下方插入应该是最小ID-1，如果小于1则需要重新分配
          if (minId <= 1) {
            // 为了在底部插入，所有现有轨道ID+1，新轨道取ID=1
            newTrackId = 1;
            adjustedTracks = tracks.map((track: any) => ({
              oldId: track.Id,
              newId: track.Id + 1
            }));
          } else {
            // 如果最小ID > 1，新轨道可以直接使用minId-1
            newTrackId = minId - 1;
          }
        } else {
          // 正常的上方/下方插入
          const result = this.handleSpecificInsertion(tracks, targetTrackIndex, insertPosition);
          newTrackId = result.newTrackId;
          adjustedTracks = result.adjustedTracks;
        }
        
      } else {
        throw new Error('无效的插入位置信息');
      }
      
      // 4. 应用ID调整到timeline
      this.applyIdAdjustments(timeline, trackType, adjustedTracks);
      
      return {
        success: true,
        newTrackId,
        adjustedTracks
      };
      
    } catch (error: any) {
      return {
        success: false,
        newTrackId: -1,
        adjustedTracks: [],
        error: error.message
      };
    }
  }
  
  /**
   * 处理具体位置插入 - 根据实际轨道位置分配ID
   * @param tracks 轨道数组
   * @param targetTrackIndex 目标轨道索引
   * @param insertPosition 插入位置（'above' 或 'below'）
   */
  private static handleSpecificInsertion(
    tracks: any[], 
    targetTrackIndex: number,
    insertPosition: 'above' | 'below'
  ): { newTrackId: number; adjustedTracks: Array<{ oldId: number; newId: number }> } {
    
    // 按ID降序排列轨道（ID大的显示在上面）
    const sortedTracks = [...tracks].sort((a, b) => b.Id - a.Id);
    
    // 确保索引有效
    if (targetTrackIndex < 0 || targetTrackIndex >= sortedTracks.length) {
      const maxId = Math.max(...tracks.map((t: any) => t.Id));
      return {
        newTrackId: maxId + 1,
        adjustedTracks: []
      };
    }
    
    const targetTrack = sortedTracks[targetTrackIndex];
    const adjustedTracks: Array<{ oldId: number; newId: number }> = [];
    
    if (insertPosition === 'above') {
      // 插入到目标轨道上方：新ID = 目标轨道ID + 1
      const newTrackId = targetTrack.Id + 1;
      
      // 检查ID冲突，如果有冲突的轨道，需要向上调整
      const conflictTracks = tracks.filter(t => t.Id >= newTrackId);
      conflictTracks.forEach(track => {
        adjustedTracks.push({
          oldId: track.Id,
          newId: track.Id + 1
        });
      });
      
      return { newTrackId, adjustedTracks };
    } else {
      // 插入到目标轨道下方：新ID = 目标轨道ID - 1
      const newTrackId = targetTrack.Id - 1;
      
      if (newTrackId < 1) {
        // 如果新ID小于1，重新分配所有ID
        return this.redistributeAllIds(tracks, targetTrackIndex, 'below');
      }
      
      return { newTrackId, adjustedTracks };
    }
  }
  
  /**
   * 重新分配所有轨道ID（当出现ID小于1的情况时）
   * @param tracks 轨道数组
   * @param targetTrackIndex 目标轨道索引
   * @param insertPosition 插入位置
   */
  private static redistributeAllIds(
    tracks: any[], 
    targetTrackIndex: number, 
    insertPosition: 'below'
  ): { newTrackId: number; adjustedTracks: Array<{ oldId: number; newId: number }> } {
    // 简单重分配：所有现有轨道ID+1，新轨道ID=1
    const adjustedTracks = tracks.map((track: any) => ({
      oldId: track.Id,
      newId: track.Id + 1
    }));
    
    return {
      newTrackId: 1,
      adjustedTracks
    };
  }
  private static handleBetweenInsertion(
    sortedTracks: any[], 
    targetIndex: number
  ): { newTrackId: number; adjustedTracks: Array<{ oldId: number; newId: number }> } {
    
    // 找到插入位置的上下轨道
    const upperTrack = sortedTracks[targetIndex];
    const lowerTrack = sortedTracks[targetIndex + 1];
    
    if (!upperTrack) {
      throw new Error('无法找到插入位置的上方轨道');
    }
    
    let newTrackId: number;
    let adjustedTracks: Array<{ oldId: number; newId: number }> = [];
    
    if (!lowerTrack) {
      // 如果没有下方轨道，说明是插入到最下面
      newTrackId = upperTrack.Id - 1;
      if (newTrackId < 1) {
        // 如果计算出的ID小于1，需要重排
        newTrackId = 1;
        adjustedTracks = sortedTracks.map((track, index) => ({
          oldId: track.Id,
          newId: index + 2 // 从2开始，给新轨道留出ID=1
        }));
      }
    } else {
      // 插入到两个轨道之间
      const upperTrackId = upperTrack.Id;
      const lowerTrackId = lowerTrack.Id;
      
      if (upperTrackId - lowerTrackId > 1) {
        // 如果有空间，直接插入中间ID
        newTrackId = Math.floor((upperTrackId + lowerTrackId) / 2);
        if (newTrackId === lowerTrackId || newTrackId === upperTrackId) {
          // 如果计算出的ID冲突，选择一个安全的ID
          newTrackId = lowerTrackId + 1;
        }
      } else {
        // 没有空间，需要重排下方的轨道
        newTrackId = lowerTrackId;
        
        // 从插入位置开始，所有下方轨道ID-1
        adjustedTracks = sortedTracks
          .slice(targetIndex + 1) // 获取下方所有轨道
          .map(track => ({
            oldId: track.Id,
            newId: track.Id - 1
          }));
      }
    }
    
    return { newTrackId, adjustedTracks };
  }
  
  /**
   * 应用ID调整到timeline
   * @param timeline Timeline数据
   * @param trackType 轨道类型
   * @param adjustments ID调整数组
   */
  private static applyIdAdjustments(
    timeline: any, 
    trackType: string, 
    adjustments: Array<{ oldId: number; newId: number }>
  ): void {
    if (adjustments.length === 0) return;
    
    const tracksKey = `${trackType}Tracks`;
    const tracks = timeline[tracksKey] || [];
    
    // 应用ID调整
    adjustments.forEach(({ oldId, newId }) => {
      const track = tracks.find((t: any) => t.Id === oldId);
      if (track) {
        track.Id = newId;
      }
    });
  }
  
  /**
   * 验证ID唯一性
   * @param timeline Timeline数据
   * @param trackType 轨道类型
   * @returns 是否唯一
   */
  static validateIdUniqueness(timeline: any, trackType: string): boolean {
    const tracksKey = `${trackType}Tracks`;
    const tracks = timeline[tracksKey] || [];
    
    const ids = tracks.map((t: any) => t.Id);
    const uniqueIds = new Set(ids);
    
    return ids.length === uniqueIds.size;
  }
  
  /**
   * 获取轨道ID统计信息
   * @param timeline Timeline数据
   * @param trackType 轨道类型
   */
  static getTrackIdStats(timeline: any, trackType: string): { min: number; max: number; count: number; ids: number[] } {
    const tracksKey = `${trackType}Tracks`;
    const tracks = timeline[tracksKey] || [];
    const ids = tracks.map((t: any) => t.Id).sort((a: number, b: number) => a - b);
    
    return {
      min: ids.length > 0 ? Math.min(...ids) : 0,
      max: ids.length > 0 ? Math.max(...ids) : 0,
      count: ids.length,
      ids
    };
  }
}

/**
 * 简化的API：为新轨道插入重新分配ID
 * @param timeline Timeline数据
 * @param trackType 轨道类型
 * @param insertPosition 插入位置
 * @param targetTrackIndex 目标轨道索引（用于具体位置插入）
 * @returns 重排结果
 */
export function reorderTrackIdsForInsertion(
  timeline: any,
  trackType: 'Video' | 'Audio' | 'Subtitle',
  insertPosition: 'top' | 'bottom' | 'above' | 'below',
  targetTrackIndex?: number
): TrackIdReorderResult {
  return TrackIdReorderer.reorderTrackIds(timeline, {
    trackType,
    insertPosition,
    targetTrackIndex
  });
}

// 导出默认实例
export const trackIdReorderer = TrackIdReorderer;
