<script setup>
import CategoryTree from './category-tree.vue';
import AudioPage from './pages/audio-page.vue';
import ArticlePage from './pages/article-page.vue';
import KeywordPage from './pages/keyword-page.vue';
import { useRoute, useRouter } from "vue-router";
const router = useRouter();
const route = useRoute();

// 获取分类树id
const categoryId = ref(undefined);
const card = ref('article')
const projectId = ref(0)
function handleClick(cardTitle) {

  const querys = {
    ...router.currentRoute.value.query,
    id: cardTitle
  };
  router.push({
    path: router.currentRoute.value.path,
    query: querys
  });
}
function getRouter() {
  projectId.value = +route.params.projectId;
  card.value = route.query.id || card.value;
}
getRouter()
</script>
<template>
  <el-row :gutter="20" style="height: 78.5vh;">
    <el-col :span="4" :xs="24" style="height: 100%">
      <div class="left-container">
        <div class="card-container classify-tree">
          <category-tree v-model="categoryId" />
        </div>
        <div class="card-container keyword-card">
          <keyword-page :categoryId="categoryId" />
        </div>
      </div>
    </el-col>
    <el-col :span="20" :xs="24">
      <el-tabs type="border-card" v-model="card" @tab-change="handleClick">
        <el-tab-pane label="文案" name="article" lazy>
          <div class="tabs-content">
            <article-page :categoryId="categoryId" v-if="card === 'article'" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="音频" name="audio" lazy>
          <div class="tabs-content">
            <audio-page :categoryId="categoryId" v-if="card === 'audio'" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-col>
  </el-row>
</template>
<style scoped lang="scss">
.left-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .card-container {
    box-shadow: var(--el-box-shadow-lighter);
    padding: 10px;
    border-radius: 15px;
    background-color: #ffffff;

    &.classify-tree {
      height: 50%;
    }

    &.keyword-card {
      flex: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;
      margin-bottom: -17px;

      :deep(.el-card__body) {
        flex: 1;
        overflow: hidden;
      }
    }
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.tabs-content {
  width: 100%;
  height: 72.2vh;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
