<!-- <template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>高级文件上传测试</span>
        </div>
      </template>
      
      <el-form :model="form" label-width="120px">
        <el-form-item label="选择文件" required>
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :limit="1"
            :file-list="fileList"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              拖拽文件到此处或 <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">支持任意格式文件，上传前会自动计算MD5用于秒传检测</div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="上传方式">
          <el-radio-group v-model="form.uploadMode">
            <el-radio label="chunk">常规分片上传</el-radio>
            <el-radio label="multiThread">多线程上传</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="分片大小">
          <el-select v-model="form.chunkSize" placeholder="请选择分片大小">
            <el-option label="5MB" :value="5 * 1024 * 1024" />
            <el-option label="10MB" :value="10 * 1024 * 1024" />
            <el-option label="30MB" :value="30 * 1024 * 1024" />
            <el-option label="50MB" :value="50 * 1024 * 1024" />
            <el-option label="80MB" :value="80 * 1024 * 1024" />
          </el-select>
        </el-form-item>

        <el-form-item :label="form.uploadMode === 'multiThread' ? '线程数' : '并发数'">
          <el-slider 
            v-model="threadOrConcurrencyCount" 
            :min="form.uploadMode === 'multiThread' ? 2 : 1" 
            :max="form.uploadMode === 'multiThread' ? 8 : 5" 
            show-input 
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitUpload" :loading="uploading" :disabled="!form.file">开始上传</el-button>
          <el-button @click="cancelUpload" v-if="uploading">取消上传</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>

      <div v-if="uploading || uploadResult" class="progress-container">
        <div v-if="fastUploadDetected" class="fast-upload-container">
          <el-alert
            type="success"
            :closable="false"
            show-icon>
            <template #title>
              <div class="fast-upload-title">文件秒传成功！</div>
            </template>
            <template #default>
              <div class="fast-upload-message">
                <p>系统检测到您上传的文件已存在，已自动完成秒传</p>
                <p v-if="md5Value">文件MD5值: {{ md5Value }}</p>
              </div>
            </template>
          </el-alert>
        </div>
        <div v-if="uploading && !fastUploadDetected">
          <div class="progress-info">
            <span>总体进度: {{ totalProgressPercent }}%</span>
            <span>已上传: {{ uploadedSize }}</span>
            <span>总大小: {{ totalSize }}</span>
            <span v-if="uploadSpeed">速度: {{ uploadSpeed }}</span>
          </div>
          <el-progress :percentage="totalProgressPercent" :status="progressStatus" :striped="true" striped-flow />
          

          <div class="chunk-progress-container" v-if="chunkProgresses.length > 0 && !isMerging">
            <div class="chunk-header">
              <h4>分片上传进度</h4>
              <span class="chunk-counter">{{ getUploadedChunksCount() }}/{{ chunkProgresses.length }}</span>
            </div>
            
            <div class="chunk-list">
              <div v-for="(chunk, index) in chunkProgresses" :key="index" class="chunk-item">
                <div class="chunk-info">
                  <span>分片 {{ index + 1 }}</span>
                  <span>{{ chunk.percent }}%</span>
                </div>
                <el-progress :percentage="chunk.percent" :status="chunk.status" :stroke-width="10" />
              </div>
            </div>
          </div>
          

          <div class="threads-progress-container" v-if="form.uploadMode === 'multiThread' && workerProgresses.length > 0">
            <div class="threads-header">
              <h4>多线程上传进度</h4>
            </div>
            
            <div class="threads-list">
              <div v-for="(worker, index) in workerProgresses" :key="index" class="thread-item">
                <div class="thread-info">
                  <span>线程 {{ index + 1 }}</span>
                  <span>{{ worker.percent }}%</span>
                </div>
                <el-progress :percentage="worker.percent" :status="worker.status" :stroke-width="10" />
              </div>
            </div>
          </div>
          

          <div class="merge-progress-container" v-if="isMerging">
            <div class="merge-header">
              <h4>文件合并进度</h4>
            </div>
            <el-progress :percentage="100" :status="mergeStatus" :indeterminate="true">
              <span>{{ mergeStatusText }}</span>
            </el-progress>
          </div>


          <div class="task-status" v-if="taskId">
            <p><strong>任务ID:</strong> {{ taskId }}</p>
            <p><strong>当前状态:</strong> {{ uploadState }}</p>
            <p v-if="md5Value"><strong>文件MD5:</strong> {{ md5Value }}</p>
          </div>
        </div>
      </div>


      <el-card v-if="uploadResult" class="result-card">
        <template #header>
          <div class="card-header">
            <span>上传结果</span>
            <el-button type="primary" link @click="expandResult = !expandResult">
              {{ expandResult ? '收起' : '展开' }}详情
            </el-button>
          </div>
        </template>
        

        <div class="result-info">
          <div class="result-item">
            <span class="label">任务ID：</span>
            <span class="value">{{ uploadResult.taskId || taskId }}</span>
          </div>
          <div class="result-item">
            <span class="label">文件名：</span>
            <span class="value">{{ fileName }}</span>
          </div>
          <div class="result-item">
            <span class="label">上传方式：</span>
            <span class="value">{{ fastUploadDetected ? '秒传' : getUploadType() }}</span>
          </div>
          <div class="result-item" v-if="!fastUploadDetected">
            <span class="label">分片数量：</span>
            <span class="value">{{ totalChunks }}</span>
          </div>
          <div class="result-item">
            <span class="label">上传耗时：</span>
            <span class="value">{{ elapsedTimeFormatted }}</span>
          </div>
          <div class="result-item" v-if="md5Value">
            <span class="label">文件MD5：</span>
            <span class="value">{{ md5Value }}</span>
            <el-button type="info" link @click="copyMd5" size="small">
              <el-icon><CopyDocument /></el-icon> 复制
            </el-button>
          </div>
          <div class="result-item success-path" v-if="uploadResult.filePath">
            <span class="label">文件链接：</span>
            <span class="value">{{ uploadResult.filePath }}</span>
            <el-button type="primary" link @click="copyPath" size="small">
              <el-icon><CopyDocument /></el-icon> 复制
            </el-button>
          </div>
        </div>
        

        <el-collapse-transition>
          <div v-show="expandResult">
            <div class="divider"></div>
            <div class="json-container">
              <pre class="result-pre">{{ JSON.stringify(uploadResult, null, 2) }}</pre>
            </div>
          </div>
        </el-collapse-transition>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onBeforeUnmount } from 'vue'
import { ElMessage, ElNotification, ElMessageBox } from 'element-plus'
import { UploadFilled, CopyDocument, Check, Key } from '@element-plus/icons-vue'
import { initUpload, uploadChunk, completeUpload, getProgress, checkFileMd5 } from "@/api/test/fileUpload";

// 状态变量
const uploadRef = ref(null);
const fileList = ref([]);
const uploading = ref(false);
const progressStatus = ref('');
const uploadResult = ref(null);
const expandResult = ref(false);
const taskId = ref('');
const uploadState = ref('准备中');
const totalChunks = ref(0);
const startTime = ref(0);
const elapsedTime = ref(0);
const chunkProgresses = ref([]);
const uploadedChunks = ref(0);
const totalProgressPercent = ref(0);
const fileName = ref('');
const md5Value = ref('');
const uploadSpeed = ref('');
const lastUploadedSize = ref(0);
const uploadSpeedTimer = ref(null);
const lastUploadedTime = ref(0);
const fastUploadDetected = ref(false);

// 多线程状态
const workerProgresses = ref([]);

// 合并状态
const isMerging = ref(false);
const mergeStatus = ref('');
const mergeStatusText = ref('正在合并文件...');

// 队列控制
const uploadQueue = ref([]);

// 表单数据
const form = reactive({
  chunkSize: 10 * 1024 * 1024, // 默认10MB
  concurrency: 3, // 默认并发数
  threadCount: 4, // 默认线程数
  uploadMode: 'chunk', // 默认上传模式：常规分片
  file: null
});

// 文件大小格式化
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / 1024 / 1024).toFixed(2) + ' MB';
  } else {
    return (size / 1024 / 1024 / 1024).toFixed(2) + ' GB';
  }
};

// 耗时格式化
const elapsedTimeFormatted = computed(() => {
  const seconds = Math.floor(elapsedTime.value / 1000);
  const milliseconds = elapsedTime.value % 1000;
  if (seconds > 60) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒${milliseconds}毫秒`;
  } else {
    return `${seconds}秒${milliseconds}毫秒`;
  }
});

// 计算属性
const threadOrConcurrencyCount = computed({
  get() {
    return form.uploadMode === 'multiThread' ? form.threadCount : form.concurrency;
  },
  set(value) {
    if (form.uploadMode === 'multiThread') {
      form.threadCount = value;
    } else {
      form.concurrency = value;
    }
  }
});

const totalSize = computed(() => {
  if (form.file) {
    return formatFileSize(form.file.size);
  }
  return '0 B';
});

const uploadedSize = computed(() => {
  if (form.file && totalProgressPercent.value) {
    const uploadedBytes = form.file.size * (totalProgressPercent.value / 100);
    return formatFileSize(uploadedBytes);
  }
  return '0 B';
});

// 创建文件分片
const createFileChunks = (file, chunkSize) => {
  const chunks = [];
  let start = 0;
  while (start < file.size) {
    const end = Math.min(start + chunkSize, file.size);
    chunks.push(file.slice(start, end));
    start = end;
  }
  return chunks;
};

// 计算已上传的分片数量
const getUploadedChunksCount = () => {
  return chunkProgresses.value.filter(chunk => chunk.status === 'success').length;
};

// 获取上传类型文本
const getUploadType = () => {
  const types = {
    'chunk': '常规分片上传',
    'multiThread': '多线程上传'
  };
  return types[form.uploadMode] || '未知';
};

// 检查是否可以秒传
const checkFastUpload = async (fileName) => {
  try {
    uploadState.value = '检查文件是否可以秒传...';
    
    // 不再传入MD5值，后端将处理MD5计算或检查
    const response = await checkFileMd5('', fileName);
    
    if (response.code === 200 && response.data && response.data.exists) {
      // 可以秒传
      fastUploadDetected.value = true;
      uploadState.value = '文件秒传成功';
      totalProgressPercent.value = 100;
      progressStatus.value = 'success';
      
      // 如果后端返回了文件的MD5值，更新前端显示
      if (response.data.md5) {
        md5Value.value = response.data.md5;
      }
      
      // 计算总耗时
      elapsedTime.value = Date.now() - startTime.value;
      
      // 设置结果
      uploadResult.value = {
        filePath: response.data.filePath,
        fileName: response.data.fileName,
        md5: response.data.md5,
        fastUpload: true
      };
      
      // 通知用户
      ElNotification({
        title: '上传成功',
        message: '文件秒传成功，无需重新上传',
        type: 'success',
        duration: 3000
      });
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.warn('秒传检查失败:', error);
    return false;
  }
};

// 处理常规分片上传
const handleNormalUpload = async () => {
  try {
    // 不再计算MD5，直接检查是否可以秒传
    if (await checkFastUpload(fileName.value)) {
      uploading.value = false;
      return true;
    }
    
    // 1. 将文件分片
    const chunks = createFileChunks(form.file, form.chunkSize);
    totalChunks.value = chunks.length;
    
    // 2. 初始化上传任务
    uploadState.value = '初始化上传任务...';
    const initResponse = await initUpload(
      fileName.value,
      form.file.size,
      chunks.length
      // 不再提供MD5值，由后端处理
    );
    
    // 处理后端返回的MD5（如果有）
    if (initResponse.data && initResponse.data.md5) {
      md5Value.value = initResponse.data.md5;
    }
    
    // 再次检查服务器返回的秒传状态
    if (initResponse.data && initResponse.data.fastUpload) {
      // 支持秒传
      fastUploadDetected.value = true;
      uploadState.value = '文件秒传成功';
      totalProgressPercent.value = 100;
      progressStatus.value = 'success';
      
      // 计算总耗时
      elapsedTime.value = Date.now() - startTime.value;
      
      // 返回结果
      uploadResult.value = initResponse.data;
      
      // 通知用户
      ElNotification({
        title: '上传成功',
        message: '文件秒传成功',
        type: 'success',
        duration: 3000
      });
      
      // 短暂延迟后结束上传状态
      setTimeout(() => {
        uploading.value = false;
      }, 1000);
      return true;
    }
    
    // 常规上传流程
    taskId.value = initResponse.data.taskId;
    uploadState.value = '文件分片中...';
    
    // 3. 初始化进度数组
    for (let i = 0; i < chunks.length; i++) {
      chunkProgresses.value.push({
        index: i,
        percent: 0,
        status: 'info'
      });
    }
    
    uploadState.value = '上传分片中...';
    
    // 4. 添加所有分片到上传队列
    uploadQueue.value = [];
    for (let i = 0; i < chunks.length; i++) {
      uploadQueue.value.push({
        taskId: taskId.value,
        chunkIndex: i,
        chunk: chunks[i]
      });
    }
    
    // 5. 开始处理队列
    if (form.uploadMode === 'chunk') {
      await processChunkQueue();
    } else {
      await processMultiThreadQueue();
    }
    
    return true;
  } catch (error) {
    console.error('上传处理失败:', error);
    progressStatus.value = 'exception';
    uploadState.value = '上传失败: ' + error.message;
    
    ElNotification({
      title: '上传失败',
      message: error.message || '初始化失败',
      type: 'error',
      duration: 0
    });
    
    uploading.value = false;
    return false;
  }
};

// 处理常规分片上传队列
const processChunkQueue = async () => {
  try {
    const concurrency = form.concurrency;
    const tasks = [];
    
    // 创建并发上传任务
    for (let i = 0; i < concurrency; i++) {
      tasks.push(processChunkWorker(i));
    }
    
    // 等待所有任务完成
    await Promise.all(tasks);
    
    // 验证所有分片是否上传成功
    if (getUploadedChunksCount() !== totalChunks.value) {
      throw new Error('部分分片上传失败，请重试');
    }
    
    // 完成上传
    await finishUpload();
    return true;
  } catch (error) {
    console.error('上传处理失败:', error);
    throw error;
  }
};

// 分片上传工作进程
const processChunkWorker = async (workerId) => {
  while (uploadQueue.value.length > 0 && uploading.value) {
    // 取出一个任务
    const task = uploadQueue.value.shift();
    if (!task) break;
    
    try {
      await uploadSingleChunk(task.taskId, task.chunkIndex, task.chunk);
    } catch (error) {
      // 出错时，如果还在上传状态，则重新放回队列
      if (uploading.value) {
        if (!task.retries) task.retries = 0;
        if (task.retries < 2) {
          task.retries++;
          // 放回队列头部优先处理
          uploadQueue.value.unshift(task);
          // 暂停一下再重试
          await new Promise(resolve => setTimeout(resolve, 1000));
        } else {
          console.error(`分片${task.chunkIndex}多次上传失败`);
          throw error;
        }
      }
    }
  }
};

// 处理多线程上传队列
const processMultiThreadQueue = async () => {
  try {
    // 分配任务给各线程
    const threadCount = form.threadCount;
    const chunks = [];
    for (let i = 0; i < threadCount; i++) {
      chunks.push([]);
      // 初始化线程进度数组
      workerProgresses.value.push({
        id: i,
        percent: 0,
        status: 'info'
      });
    }
    
    // 平均分配分片给各线程
    uploadQueue.value.forEach((task, index) => {
      const threadIndex = index % threadCount;
      chunks[threadIndex].push(task);
    });
    
    // 启动线程
    const threadTasks = chunks.map((tasks, threadIndex) => {
      return uploadThreadGroup(tasks, threadIndex);
    });
    
    // 等待所有线程完成
    await Promise.all(threadTasks);
    
    // 验证所有分片是否上传成功
    if (getUploadedChunksCount() !== totalChunks.value) {
      throw new Error('部分分片上传失败，请重试');
    }
    
    // 完成上传
    await finishUpload();
    return true;
  } catch (error) {
    console.error('多线程上传失败:', error);
    throw error;
  }
};

// 上传线程组
const uploadThreadGroup = async (tasks, threadIndex) => {
  try {
    for (let i = 0; i < tasks.length; i++) {
      if (!uploading.value) {
        throw new Error('上传已取消');
      }
      
      const task = tasks[i];
      try {
        await uploadSingleChunk(task.taskId, task.chunkIndex, task.chunk, threadIndex, i, tasks.length);
      } catch (error) {
        // 出错时重试
        if (uploading.value) {
          if (!task.retries) task.retries = 0;
          if (task.retries < 2) {
            task.retries++;
            // 重试当前任务
            i--;
            // 暂停后重试
            await new Promise(resolve => setTimeout(resolve, 1000));
          } else {
            console.error(`线程${threadIndex}上的分片${task.chunkIndex}多次上传失败`);
            throw error;
          }
        }
      }
    }
    
    // 线程完成
    workerProgresses.value[threadIndex].status = 'success';
    workerProgresses.value[threadIndex].percent = 100;
  } catch (error) {
    workerProgresses.value[threadIndex].status = 'exception';
    throw error;
  }
};

// 上传单个分片
const uploadSingleChunk = async (taskId, chunkIndex, chunk, threadIndex = null, taskIndex = null, totalTasks = null) => {
  try {
    // 设置状态为上传中
    chunkProgresses.value[chunkIndex].status = '';
    
    // 执行上传
    const response = await uploadChunk(taskId, chunkIndex, chunk, {
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          // 更新分片进度
          const percent = Math.round((progressEvent.loaded / progressEvent.total) * 100);
          chunkProgresses.value[chunkIndex].percent = percent;
          
          // 如果是多线程模式，还要更新线程进度
          if (threadIndex !== null && taskIndex !== null && totalTasks !== null) {
            const threadProgress = Math.round(((taskIndex * 100) + percent) / totalTasks);
            workerProgresses.value[threadIndex].percent = threadProgress;
          }
          
          // 更新总体进度
          updateTotalProgress();
        }
      }
    });
    
    // 检查服务器响应
    if (response.code !== 200) {
      throw new Error(response.msg || "服务器返回错误");
    }
    
    // 设置分片上传成功
    chunkProgresses.value[chunkIndex].status = 'success';
    chunkProgresses.value[chunkIndex].percent = 100;
    uploadedChunks.value++;
    
    return response;
  } catch (error) {
    // 设置分片上传失败
    chunkProgresses.value[chunkIndex].status = 'exception';
    throw error;
  } finally {
    // 更新总体进度
    updateTotalProgress();
  }
};

// 更新总体进度
const updateTotalProgress = () => {
  if (chunkProgresses.value.length === 0) return 0;
  
  // 计算总进度百分比
  const totalProgress = chunkProgresses.value.reduce((sum, chunk) => sum + chunk.percent, 0);
  totalProgressPercent.value = Math.round(totalProgress / chunkProgresses.value.length);
  
  // 更新状态
  if (totalProgressPercent.value === 100) {
    progressStatus.value = 'success';
  } else if (progressStatus.value === 'exception') {
    // 保持错误状态
  } else {
    progressStatus.value = '';
  }
  
  return totalProgressPercent.value;
};

// 完成上传
const finishUpload = async () => {
  try {
    // 确保所有分片都上传成功
    const allSuccess = chunkProgresses.value.every(chunk => chunk.status === 'success');
    if (!allSuccess) {
      throw new Error("部分分片上传失败，无法合并文件");
    }
    
    // 检查服务器端的分片状态
    uploadState.value = '检查服务器端分片状态...';
    const progressData = await getProgress(taskId.value);
    
    if (progressData.data && progressData.data.uploadedChunks !== totalChunks.value) {
      throw new Error(`服务器记录的分片数量不匹配: ${progressData.data.uploadedChunks}/${totalChunks.value}`);
    }
    
    // 开始合并文件
    uploadState.value = '正在合并文件...';
    isMerging.value = true;
    mergeStatus.value = '';
    
    // 调用合并接口
    const response = await completeUpload(taskId.value);
    
    // 计算总耗时
    elapsedTime.value = Date.now() - startTime.value;
    
    // 更新状态
    uploadState.value = '上传完成';
    uploadResult.value = response.data;
    uploadResult.value.md5 = md5Value.value;
    progressStatus.value = 'success';
    mergeStatus.value = 'success';
    mergeStatusText.value = '合并完成';
    
    // 通知用户
    ElNotification({
      title: '上传成功',
      message: '文件上传并合并完成',
      type: 'success',
      duration: 3000
    });
    
    return response;
  } catch (error) {
    console.error('完成上传失败:', error);
    progressStatus.value = 'exception';
    
    if (isMerging.value) {
      mergeStatus.value = 'exception';
      mergeStatusText.value = error.message || '合并失败';
    }
    
    uploadState.value = '上传失败: ' + error.message;
    
    ElNotification({
      title: '上传失败',
      message: error.message || '文件上传失败',
      type: 'error',
      duration: 0
    });
    
    throw error;
  } finally {
    uploading.value = false;
  }
};

// 提交上传表单
const submitUpload = async () => {
  if (!form.file) {
    ElMessage.warning('请选择文件');
    return;
  }

  try {
    // 重置上传状态
    startTime.value = Date.now();
    uploading.value = true;
    progressStatus.value = '';
    uploadResult.value = null;
    uploadState.value = '准备中...';
    uploadedChunks.value = 0;
    totalProgressPercent.value = 0;
    chunkProgresses.value = [];
    workerProgresses.value = [];
    uploadQueue.value = [];
    isMerging.value = false;
    fastUploadDetected.value = false;
    md5Value.value = ''; // 清空MD5值，将由后端提供
    
    // 开始计算上传速度
    startSpeedCalculation();
    
    // 设置文件名
    fileName.value = form.file.name;
    
    // 开始上传处理，不再需要前端计算MD5
    await handleNormalUpload();
  } catch (error) {
    console.error('上传失败:', error);
    progressStatus.value = 'exception';
    uploadState.value = '上传失败: ' + error.message;
    
    ElNotification({
      title: '上传失败',
      message: error.message || '上传失败',
      type: 'error',
      duration: 0
    });
    
    uploading.value = false;
  } finally {
    stopSpeedCalculation();
  }
};

// 处理文件选择变化
const handleFileChange = (file, fileList) => {
  if (file.status === 'ready') {
    // 设置当前选择的文件
    form.file = file.raw;
    // 更新文件列表，只保留最后选择的文件
    fileList.value = [file];
    // 重置相关状态
    fileName.value = file.name;
    uploadResult.value = null;
    md5Value.value = '';
    taskId.value = '';
    fastUploadDetected.value = false;
  }
};

// 复制MD5值
const copyMd5 = () => {
  if (md5Value.value) {
    navigator.clipboard.writeText(md5Value.value)
      .then(() => {
        ElMessage.success('MD5值已复制到剪贴板');
      })
      .catch(() => {
        ElMessage.error('复制失败，请手动复制');
      });
  }
};

// 取消上传
const cancelUpload = () => {
  ElMessageBox.confirm('确定要取消上传吗？已上传的分片将被丢弃。', '取消上传', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    uploading.value = false;
    progressStatus.value = 'exception';
    uploadState.value = '已取消';
    uploadQueue.value = []; // 清空队列
    ElMessage.info('已取消上传');
  }).catch(() => {});
};

// 重置表单
const resetForm = () => {
  form.file = null;
  fileList.value = [];
  fileName.value = '';
  uploadResult.value = null;
  taskId.value = '';
  totalChunks.value = 0;
  elapsedTime.value = 0;
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

// 复制上传路径到剪贴板
const copyPath = () => {
  const path = uploadResult.value?.filePath;
  if (path) {
    navigator.clipboard.writeText(path)
      .then(() => {
        ElMessage.success('路径已复制到剪贴板');
      })
      .catch(() => {
        ElMessage.error('复制失败，请手动复制');
      });
  }
};

// 开始计算上传速度
const startSpeedCalculation = () => {
  lastUploadedSize.value = 0;
  lastUploadedTime.value = Date.now();
  
  if (uploadSpeedTimer.value) {
    clearInterval(uploadSpeedTimer.value);
  }
  
  uploadSpeedTimer.value = setInterval(() => {
    const currentSize = form.file.size * (totalProgressPercent.value / 100);
    const currentTime = Date.now();
    const timeDiff = (currentTime - lastUploadedTime.value) / 1000; // 秒
    const sizeDiff = currentSize - lastUploadedSize.value; // 字节
    
    if (timeDiff > 0 && sizeDiff > 0) {
      const speedBps = sizeDiff / timeDiff;
      
      if (speedBps < 1024) {
        uploadSpeed.value = speedBps.toFixed(2) + ' B/s';
      } else if (speedBps < 1024 * 1024) {
        uploadSpeed.value = (speedBps / 1024).toFixed(2) + ' KB/s';
      } else {
        uploadSpeed.value = (speedBps / 1024 / 1024).toFixed(2) + ' MB/s';
      }
      
      lastUploadedSize.value = currentSize;
      lastUploadedTime.value = currentTime;
    }
  }, 1000);
};

// 停止计算上传速度
const stopSpeedCalculation = () => {
  if (uploadSpeedTimer.value) {
    clearInterval(uploadSpeedTimer.value);
    uploadSpeedTimer.value = null;
  }
  uploadSpeed.value = '';
};

// 组件销毁前清理
onBeforeUnmount(() => {
  stopSpeedCalculation();
  if (taskId.value && uploading.value) {
    uploading.value = false;
  }
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.box-card {
  width: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-container {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

/* 分片进度容器样式 */
.chunk-progress-container,
.threads-progress-container {
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
  padding-top: 15px;
}

.chunk-header,
.threads-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chunk-header h4,
.threads-header h4 {
  margin: 0;
}

.chunk-counter {
  background-color: #409eff;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.8em;
}

.chunk-list,
.threads-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.chunk-item,
.thread-item {
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.chunk-info,
.thread-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 0.9em;
}

/* 秒传提示样式 */
.fast-upload-container {
  margin-bottom: 16px;
}

.fast-upload-title {
  font-weight: 600;
  font-size: 15px;
}

.fast-upload-message {
  margin-top: 4px;
  color: #67c23a;
  line-height: 1.6;
}

.fast-upload-message p {
  margin: 4px 0;
}

/* 合并进度样式 */
.merge-progress-container {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.merge-header {
  margin-bottom: 10px;
}

.merge-header h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

/* 任务状态样式 */
.task-status {
  margin-top: 15px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.task-status p {
  margin: 5px 0;
}

/* 结果卡片样式 */
.result-card {
  margin-top: 20px;
}

.result-info {
  margin: 10px 0;
}

.result-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.success-path {
  background-color: #f0f9eb;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #67c23a;
}

.label {
  font-weight: bold;
  min-width: 80px;
  color: #606266;
}

.value {
  word-break: break-all;
  flex: 1;
  color: #303133;
}

.divider {
  height: 1px;
  background-color: #e6e6e6;
  margin: 15px 0;
}

.json-container {
  margin-top: 10px;
}

.result-pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 0.9em;
  max-height: 300px;
  overflow-y: auto;
}
</style> -->
<template> 
</template>
<script setup> 
</script>