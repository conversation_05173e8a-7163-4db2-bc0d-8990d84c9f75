<script setup>
defineProps({
  title: String,
  icon: String,
  fontSize: {
    type: Number,
    default: 90
  }
})
</script>

<template>
  <div class="body">
    <a class="card human-resources">
      <div class="overlay"></div>
      <div class="circle">
        <el-icon :size="fontSize">
          <component :is="icon" />
        </el-icon>
      </div>
      <p>{{ title }}</p>
    </a>
  </div>
</template>

<style lang="scss" scoped>
// 基础样式
.body {
  min-width: 220px;
}

// 卡片主题样式
.human-resources {
  // 卡片颜色变量
  --bg-color: #DCE9FF;
  --bg-color-light: #f1f7ff;
  --text-color-hover: #4C5656;
  --box-shadow-color: rgba(220, 233, 255, 0.48);
}

// 卡片基础样式
.card {
  $card-width: 220px;
  $card-height: 280px;

  width: $card-width;
  height: $card-height;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  position: relative;

  // 弹性布局
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  // 阴影与过渡效果
  box-shadow: 0 14px 26px rgba(0, 0, 0, 0.04);
  transition: all 0.4s ease-out;
  text-decoration: none;

  // 悬浮效果
  &:hover {
    transform: scale(1.1) translateY(-5px) translateZ(0);
    box-shadow:
      0 24px 36px rgba(0, 0, 0, 0.11),
      0 24px 46px var(--box-shadow-color);

    .overlay {
      transform: translate(-50%, -50%) scale(4);
    }

    .circle {
      border-color: var(--bg-color-light);
      background: var(--bg-color);

      &:after {
        background: var(--bg-color-light);
      }
    }

    p {
      color: var(--text-color-hover);
    }
  }

  // 激活状态
  &:active {
    transform: scale(1) translateZ(0);
    box-shadow:
      0 15px 24px rgba(0, 0, 0, 0.11),
      0 15px 24px var(--box-shadow-color);
  }

  // 卡片文字
  p {
    font-size: 17px;
    color: #4C5656;
    margin-top: 30px;
    z-index: 1000;
    transition: color 0.3s ease-out;
  }
}

// 圆形图标容器
.circle {
  $circle-size: 131px;
  $inner-circle-size: 118px;

  width: $circle-size;
  height: $circle-size;
  border-radius: 50%;
  background: #fff;
  border: 3px solid var(--bg-color);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease-out;

  &:after {
    content: "";
    width: $inner-circle-size;
    height: $inner-circle-size;
    display: block;
    position: absolute;
    background: var(--bg-color);
    border-radius: 50%;
    transition: opacity 0.3s ease-out;
  }

  svg {
    z-index: 10000;
    transform: translateZ(0);
  }
}

// 背景遮罩
.overlay {
  $overlay-size: 118px;

  width: $overlay-size;
  position: absolute;
  height: $overlay-size;
  border-radius: 50%;
  background: var(--bg-color);
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
  transition: transform 0.3s ease-out;
}
</style>
