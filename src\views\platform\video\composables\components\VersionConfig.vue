<template>
  <div v-if="version" class="config-section">
    <h4 class="section-title">{{ getVersionName(version) }} 配置</h4>
    
    <!-- M版配置 -->
    <div v-if="version === 'M'" class="config-panel">
      <div class="config-item">
        <label class="config-label">阈值设置 (bboxShiftValue)</label>
        <div class="slider-container">
          <el-slider
            :model-value="config.bboxShiftValue"
            @update:model-value="updateBboxShiftValue"
            :min="-7"
            :max="7"
            :step="1"
            show-stops
            show-input
            class="config-slider"
          />
        </div>
        <p class="config-tip">调整数字人面部区域的偏移量，范围：-7 到 +7</p>
      </div>
    </div>

    <!-- V版配置 -->
    <div v-if="version === 'V'" class="config-panel">
      <div class="config-item">
        <label class="config-label">选择模型</label>
        <el-select 
          :model-value="config.model"
          @update:model-value="updateModel"
          placeholder="请选择AI模型" 
          class="model-select"
          size="large"
        >
          <el-option
            v-for="model in availableModels"
            :key="model.modelCode"
            :label="model.modelName"
            :value="model.modelCode"
            :disabled="model.modelStatus !== 1"
          >
            <div class="model-option">
              <span class="model-name">{{ model.modelName }}</span>
              <span class="model-status" :class="{ available: model.modelStatus === 1 }">
                {{ model.modelStatus === 1 ? '可用' : '不可用' }}
              </span>
            </div>
          </el-option>
        </el-select>
        <p class="config-tip">选择用于视频合成的AI模型，不同模型效果可能有所差异</p>
      </div>
    </div>

    <!-- H版配置 -->
    <div v-if="version === 'H'" class="config-panel">
      <div class="config-info">
        <el-icon class="info-icon"><InfoFilled /></el-icon>
        <div class="info-content">
          <h5>H版 - 高清画质</h5>
          <p>H版使用默认的高清配置，无需额外设置。将为您提供最佳的画质效果。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { InfoFilled } from '@element-plus/icons-vue'

const props = defineProps({
  version: {
    type: String,
    default: ''
  },
  config: {
    type: Object,
    required: true
  },
  availableModels: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:config'])

const getVersionName = (version) => {
  const versionMap = {
    'M': 'M版 - 标准版',
    'V': 'V版 - 增强版',
    'H': 'H版 - 高清版'
  }
  return versionMap[version] || '未选择'
}

const updateBboxShiftValue = (value) => {
  emit('update:config', { ...props.config, bboxShiftValue: value })
}

const updateModel = (value) => {
  emit('update:config', { ...props.config, model: value })
}
</script>

<style scoped>
.config-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.config-panel {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.config-item {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.config-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.slider-container {
  padding: 0 12px;
}

.config-slider {
  margin-bottom: 12px;
}

.model-select {
  width: 100%;
}

.config-tip {
  font-size: 12px;
  color: #666;
  margin: 8px 0 0 0;
  line-height: 1.4;
}

.config-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(64, 158, 255, 0.1) 100%);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
}

.info-icon {
  font-size: 20px;
  color: #409eff;
  flex-shrink: 0;
  margin-top: 2px;
}

.info-content {
  flex: 1;

  h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0 0 6px 0;
  }

  p {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.4;
  }
}

.model-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.model-name {
  font-weight: 500;
}

.model-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  background: #f56c6c;
  color: white;

  &.available {
    background: #67c23a;
  }
}
</style>
