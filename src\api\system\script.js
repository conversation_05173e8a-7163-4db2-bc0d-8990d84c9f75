import request from '@/utils/request'

// 查询脚本列表
export function listScript(query) {
  return request({
    url: '/system/script/list',
    method: 'get',
    params: query
  })
}

// 查询脚本详细
export function getScript(scriptId) {
  return request({
    url: '/system/script/' + scriptId,
    method: 'get'
  })
}

// 新增脚本
export function addScript(data) {
  return request({
    url: '/system/script',
    method: 'post',
    data: data
  })
}

// 修改脚本
export function updateScript(data) {
  return request({
    url: '/system/script',
    method: 'put',
    data: data
  })
}

// 删除脚本
export function delScript(scriptId) {
  return request({
    url: '/system/script/' + scriptId,
    method: 'delete'
  })
}
