<template>
  <div class="export-task-manager">
    <!-- 查看导出任务按钮 -->
    <el-button 
      type="success" 
      icon="Download" 
      @click="openTaskDialog" 
      class="export-status-btn"
    >
      查看导出任务
    </el-button>

    <!-- 导出任务管理对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="导出任务管理"
      width="800px"
      :close-on-click-modal="false"
      class="export-task-dialog"
    >
      <div class="task-manager-content">
        <!-- 输入任务ID查看 -->
        <div class="input-section">
          <div class="section-title">
            <el-icon><Search /></el-icon>
            <span>查看任务状态</span>
          </div>
          <div class="input-group">
            <el-input
              v-model="inputJobId"
              placeholder="请输入导出任务ID，例如：03bd777ae68e4445803bd57e7a349482"
              clearable
              class="job-id-input"
              @keyup.enter="viewJobStatus"
            >
              <template #prepend>任务ID</template>
            </el-input>
            <el-button 
              type="primary" 
              @click="viewJobStatus"
              :disabled="!inputJobId"
              class="view-btn"
            >
              查看状态
            </el-button>
          </div>
        </div>

        <!-- 最近导出记录 -->
        <div class="recent-section" v-if="recentTasks.length > 0">
          <div class="section-title">
            <span>最近导出记录</span>
            <el-button 
              type="text" 
              size="small" 
              @click="clearRecentTasks"
              class="clear-btn"
            >
              清空记录
            </el-button>
          </div>
          <div class="recent-tasks">
            <div 
              v-for="task in recentTasks" 
              :key="task.jobId"
              class="task-item"
              @click="selectTask(task)"
            >
              <div class="task-info">
                <div class="task-header">
                  <span class="task-id">{{ task.jobId }}</span>
                </div>
                <div class="task-details">
                  <span class="project-title">{{ task.projectTitle }}</span>
                  <span class="export-time">{{ parseTime(task.exportTime) }}</span>
                </div>
              </div>
              <div class="task-actions">
                <el-button 
                  type="text" 
                  size="small" 
                  @click.stop="viewTaskDetail(task)"
                  class="detail-btn"
                >
                  查看详情
                </el-button>
                <el-button 
                  type="text" 
                  size="small" 
                  @click.stop="removeTask(task.jobId)"
                  class="remove-btn"
                >
                  移除
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <el-empty description="暂无导出记录">
            <el-button type="primary" @click="dialogVisible = false">
              去导出工程
            </el-button>
          </el-empty>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导出任务状态对话框 -->
    <ExportJobDialog v-model:visible="jobDialogVisible" :job-id="currentJobId"/>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Clock } from '@element-plus/icons-vue';
import ExportJobDialog from './ExportJobDialog.vue';
import { parseTime } from '@/utils/ruoyi';

// 导出任务接口
interface ExportTask {
  jobId: string;
  projectId: string;
  projectTitle: string;
  exportType: string;
  bucket: string;
  prefix?: string;
  status: 'pending' | 'processing' | 'success' | 'failed';
  exportTime: number;
  width?: number;
  height?: number;
}

// 组件状态
const dialogVisible = ref(false);
const jobDialogVisible = ref(false);
const inputJobId = ref('');
const currentJobId = ref('');

// 最近导出任务记录（使用 localStorage 持久化）
const recentTasks = ref<ExportTask[]>([]);

// 组件挂载时加载本地存储的任务记录
const loadRecentTasks = () => {
  try {
    const stored = localStorage.getItem('export_tasks_history');
    if (stored) {
      recentTasks.value = JSON.parse(stored);
    }
  } catch (error) {
    console.error('加载导出任务记录失败:', error);
    recentTasks.value = [];
  }
};

// 保存任务记录到本地存储
const saveRecentTasks = () => {
  try {
    localStorage.setItem('export_tasks_history', JSON.stringify(recentTasks.value));
  } catch (error) {
    console.error('保存导出任务记录失败:', error);
  }
};

// 添加新的导出任务记录
const addExportTask = (task: ExportTask) => {
  // 检查是否已存在
  const existingIndex = recentTasks.value.findIndex(t => t.jobId === task.jobId);
  if (existingIndex >= 0) {
    // 更新现有记录
    recentTasks.value[existingIndex] = task;
  } else {
    // 添加新记录，保持最多10条记录
    recentTasks.value.unshift(task);
    if (recentTasks.value.length > 10) {
      recentTasks.value = recentTasks.value.slice(0, 10);
    }
  }
  saveRecentTasks();
};

// 移除任务记录
const removeTask = async (jobId: string) => {
  try {
    await ElMessageBox.confirm('确定要移除这条导出记录吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    
    recentTasks.value = recentTasks.value.filter(task => task.jobId !== jobId);
    saveRecentTasks();
    ElMessage.success('记录已移除');
  } catch (error) {
    // 用户取消操作
  }
};

// 清空所有记录
const clearRecentTasks = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有导出记录吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    
    recentTasks.value = [];
    saveRecentTasks();
    ElMessage.success('记录已清空');
  } catch (error) {
    // 用户取消操作
  }
};

// 打开任务管理对话框
const openTaskDialog = () => {
  loadRecentTasks();
  dialogVisible.value = true;
};

// 查看任务状态
const viewJobStatus = () => {
  if (!inputJobId.value.trim()) {
    ElMessage.warning('请输入任务ID');
    return;
  }
  currentJobId.value = inputJobId.value.trim();
  jobDialogVisible.value = true;
};

// 选择任务
const selectTask = (task: ExportTask) => {
  inputJobId.value = task.jobId;
};

// 查看任务详情
const viewTaskDetail = (task: ExportTask) => {
  currentJobId.value = task.jobId;
  jobDialogVisible.value = true;
};
loadRecentTasks();

// 暴露方法给父组件
defineExpose({
  addExportTask,
  openTaskDialog
});
</script>

<style lang="scss" scoped>
.export-task-manager {
  .export-status-btn {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    border: none;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(103, 194, 58, 0.3);
    }
  }
}

.export-task-dialog {
  .task-manager-content {
    .input-section {
      margin-bottom: 24px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-weight: 600;
        color: #2c3e50;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .input-group {
        display: flex;
        gap: 12px;

        .job-id-input {
          flex: 1;
        }

        .view-btn {
          min-width: 100px;
        }
      }
    }

    .recent-section {
      .section-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        font-weight: 600;
        color: #2c3e50;

        > div {
          display: flex;
          align-items: center;

          .el-icon {
            margin-right: 8px;
            color: #67c23a;
          }
        }

        .clear-btn {
          color: #f56c6c;
          
          &:hover {
            color: #f78989;
          }
        }
      }

      .recent-tasks {
        max-height: 400px;
        overflow-y: auto;

        .task-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          margin-bottom: 12px;
          background: white;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #409eff;
            box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
          }

          .task-info {
            flex: 1;

            .task-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 8px;

              .task-id {
                font-family: 'Courier New', monospace;
                font-size: 12px;
                color: #409eff;
                background: #ecf5ff;
                padding: 2px 6px;
                border-radius: 4px;
              }
            }

            .task-details {
              display: flex;
              justify-content: space-between;
              margin-bottom: 4px;
              font-size: 14px;

              .project-title {
                font-weight: 500;
                color: #303133;
              }

              .export-time {
                color: #909399;
                font-size: 12px;
              }
            }
          }

          .task-actions {
            width: 120px;
            display: flex;
            padding-top: 20px;
            gap: 2px;

            .detail-btn {
              color: #409eff;
            }

            .remove-btn {
              color: #f56c6c;
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
    }
  }
}
</style>
