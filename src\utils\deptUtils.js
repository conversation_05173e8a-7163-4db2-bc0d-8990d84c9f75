// utils/deptUtils   封装获取部门树状列表
import { getCurrentInstance } from 'vue';
import { listDept, listDeptExcludeChild } from "@/api/system/dept";

export async function fetchDeptOptions(excludeDeptId = null) {
  const { proxy } = getCurrentInstance();
  let response;
  if (excludeDeptId !== null) {
    response = await listDeptExcludeChild(excludeDeptId);
  } else {
    response = await listDept();
  }
  return proxy.handleTree(response.data, "deptId");
}