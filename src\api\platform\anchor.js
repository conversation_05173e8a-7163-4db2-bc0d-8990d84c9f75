import request, { streamAction } from '@/utils/request'

// 查询智能主播列表
export function listAnchor(query) {
  return request({
    url: '/platform/anchor/list',
    method: 'get',
    params: query
  })
}

// 查询智能主播详细
export function getAnchor(anchorId) {
  return request({
    url: '/platform/anchor/' + anchorId,
    method: 'get'
  })
}

// 新增智能主播
export function addAnchor(data) {
  return request({
    url: '/platform/anchor',
    method: 'post',
    data: data
  })
}

// 修改智能主播
export function updateAnchor(anchorId, data) {
  return request({
    url: '/platform/anchor/' + anchorId,
    method: 'put',
    data: data
  })
}

// 删除智能主播
export function delAnchor(anchorId) {
  return request({
    url: '/platform/anchor/' + anchorId,
    method: 'delete'
  })
}

// 删除智能主播知识库中的特定字段
export function deleteFieldAnchor(anchorId, category) {
  return request({
    url: `/platform/anchor/${anchorId}/repository/${category}`,
    method: 'delete'
  });
}

// 根据项目ID查询智能主播
export function getProjectId(projectId) {
  return request({
    url: '/platform/anchor/selectPlatProjectId/' + projectId,
    method: 'get'
  })
}

// 训练智能主播
export function trainAnchor(projectId) {
  return request({
    url: '/platform/anchor/trainAnchor/' + projectId,
    method: 'post',
    timeout: 30000
  })
}

// 智能主播回复
export function generateReply(projectId, data, callback) {
  return streamAction({
    url: '/platform/anchor/reply/' + projectId,
    method: 'post',
    data: data,
    timeout: 30000,
    responseType: 'stream',
    headers: {
      'repeatSubmit': false
    },
  }, callback);
}
