import { tansParams } from "./ruoyi";

/**
 * 通配符比较
 * @param {*} str 待比较的字符串
 * @param {*} pattern 含有*或者?通配符的字符串
 * @returns
 */
export function wildcardCompare(str: string, pattern: string): boolean {
  const regex = pattern.replace(/[*?]/g, (match) => {
    if (match === "*") {
      return ".*";
    } else if (match === "?") {
      return ".";
    } else {
      return match;
    }
  });
  const regexPattern = new RegExp("^" + regex + "$");
  return regexPattern.test(str);
}

/**
 * 深度复制
 * @param obj 待复制的对象
 * @returns 复制的对象
 */
export function deepClone(obj: any) {
  if (obj == null || typeof obj !== "object") {
    return obj;
  }
  let result;
  if (Array.isArray(obj)) {
    result = [];
  } else {
    result = new Map();
  }
  for (let [key, value] of Object.entries(obj)) {
    // @ts-ignore
    result[key] = deepClone(value);
  }
  return result;
}

/**
 * 深度复制
 * @param obj 待复制的对象
 * @param result 要复制到的对象
 * @returns 复制的对象
 */
export function deepCloneTo<T>(obj: T, result: T) {
  if (obj == null || typeof obj !== "object") {
    return obj;
  }
  for (let [key, value] of Object.entries(obj)) {
    // @ts-ignore
    result[key] = deepClone(value);
  }
  return result;
}

/**
 * 获取uuid
 * @returns 生成的uuid字符串
 */
export function generateUUID(): string {
  let uuid = "";
  const chars = "0123456789abcdef";

  for (let i = 0; i < 32; i++) {
    if (i === 8 || i === 12 || i === 16 || i === 20) {
      uuid += "-";
    }
    uuid += chars[Math.floor(Math.random() * chars.length)];
  }

  return uuid;
}

/**
 * 获取code
 * @returns 生成的code字符串
 */
export async function getCode() {
  // #ifdef H5
  let appid = "wx98501e665b0f0596";
  let url = "http://localhost:3002/index.html";
  let code = "";

  // 截取url中的code方法
  function getUrlCode() {
    let url = location.search;
    console.log(url);
    let theRequest: any = new Object();
    if (url.indexOf("?") != -1) {
      let str = url.substr(1);
      let strs = str.split("&");
      for (let i = 0; i < strs.length; i++) {
        theRequest[strs[i].split("=")[0]] = strs[i].split("=")[1];
      }
    }
    return theRequest as { code: string };
  }

  code = getUrlCode().code; // 截取code
  if (code == undefined || code == "" || code == null) {
    // 如果没有code，则去请求
    console.log("h5");
    window.location.href =
      "https://open.weixin.qq.com/connect/oauth2/authorize?" +
      tansParams({
        appid: appid,
        redirect_uri: url,
        response_type: "code",
        scope: "snsapi_userinfo",
        state: "STATE",
      }) +
      "#wechat_redirect";
  } else {
    return code;
  }
  // #endif

  // #ifdef MP-WEIXIN
  // @ts-ignore
  const res = await wx.login();
  return res.code;
  // #endif
}

/**
 * 拖拽函数
 * @param onDraging 拖拽时触发的函数
 * @param beforeStop 拖拽结束前触发的函数
 * @returns 用于启动拖动的函数
 */
export function drag(onDraging: (arg: { x: number, y: number, dx: number, dy: number }) => void, beforeStop?: Function) {
  let initialX: null | number = null;
  let initialY: null | number = null;
  function doDrag(event: MouseEvent) {
    if (initialX === null || initialY === null) {
      initialX = event.clientX;
      initialY = event.clientY;
    }
    fun.event = event;
    onDraging({
      x: event.clientX,
      y: event.clientY,
      dx: event.clientX - initialX,
      dy: event.clientY - initialY,
    })
    initialX = event.clientX;
    initialY = event.clientY;
  }
  function stopDrag() {
    if (beforeStop) {
      beforeStop()
    }
    initialX = null;
    initialY = null;
    window.removeEventListener("mousemove", doDrag);
    window.removeEventListener("mouseup", stopDrag)
  }
  const fun = () => {
    window.addEventListener("mousemove", doDrag);
    window.addEventListener("mouseup", stopDrag);
  }
  fun.stopDrag = stopDrag
  fun.onDraging = onDraging
  fun.event = {} as any
  return fun
}

/**
 * 根据输入字符串生成带有调整过亮度和鲜艳度的颜色值
 * @param str 输入的字符串
 * @param brightness 亮度调整因子，默认为1（原始亮度）
 * @param saturation 鲜艳度调整因子，默认为1（原始鲜艳度）
 * @returns 调整后的颜色的十六进制字符串
 */
export function stringToColor(str: string, brightness: number = 1, saturation: number = 1): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
    hash = hash & hash; // Convert to 32bit integer
  }

  let r = (hash >> 16) & 0xFF;
  let g = (hash >> 8) & 0xFF;
  let b = hash & 0xFF;

  // 转换 RGB 到 HSL
  let [h, s, l] = rgbToHsl(r, g, b);

  // 调整饱和度和亮度
  s = Math.min(1, Math.max(0, s * saturation));
  l = Math.min(1, Math.max(0, l * brightness));

  // 确保 h 始终有值，即使在灰色的情况下
  if (isNaN(h)) {
    h = 0;
  }

  // 转换 HSL 回 RGB
  [r, g, b] = hslToRgb(h, s, l);

  let color = '#';
  color += (`00${r.toString(16)}`).slice(-2);
  color += (`00${g.toString(16)}`).slice(-2);
  color += (`00${b.toString(16)}`).slice(-2);

  return color;
}

/**
 * 将RGB颜色值转换为HSL颜色值
 * @param r 红色通道值 (0-255)
 * @param g 绿色通道值 (0-255)
 * @param b 蓝色通道值 (0-255)
 * @returns [h, s, l] 色相、饱和度、亮度值
 */
export function rgbToHsl(r: number, g: number, b: number): [number, number, number] {
  r /= 255;
  g /= 255;
  b /= 255;

  let max = Math.max(r, g, b), min = Math.min(r, g, b);
  let h = 0, s = 0, l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    let d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return [h, s, l];
}

/**
 * 将HSL颜色值转换为RGB颜色值
 * @param h 色相值 (0-1)
 * @param s 饱和度值 (0-1)
 * @param l 亮度值 (0-1)
 * @returns [r, g, b] 红、绿、蓝通道值
 */
export function hslToRgb(h: number, s: number, l: number): [number, number, number] {
  let r, g, b;

  if (s === 0) {
    r = g = b = l;
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;

    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }

  return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
}

function hue2rgb(p: number, q: number, t: number): number {
  if (t < 0) t += 1;
  if (t > 1) t -= 1;
  if (t < 1/6) return p + (q - p) * 6 * t;
  if (t < 1/2) return q;
  if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
  return p;
}

export function stringToHSLColor(str: string, options: { 
  saturation?: number, 
  lightness?: number, 
  alpha?: number,
  hueShift?: number 
} = {}): string {
  if (!str) {
    // 处理空字符串情况，返回一个默认颜色
    return 'hsla(210, 60%, 80%, 0.9)';
  }

  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    // 更新哈希值
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }

  // 将哈希值映射到0-360度的色相
  const hue = ((hash % 360) + 360) % 360; // 确保总是正数

  // 如果提供了色相偏移，则应用它
  const finalHue = options.hueShift ? 
    ((hue + options.hueShift) % 360) : hue;

  // 默认值和范围限制
  // 适中的饱和度（40%-80%）避免过于鲜艳的颜色
  const saturation = options.saturation !== undefined ? 
    Math.max(40, Math.min(80, options.saturation)) : 
    60 + ((hash % 20) - 10); // 默认饱和度在50%-70%之间浮动

  // 适中的亮度（60%-85%）确保背景不会太暗也不会太亮
  const lightness = options.lightness !== undefined ? 
    Math.max(60, Math.min(85, options.lightness)) : 
    70 + ((hash % 150) % 15); // 默认亮度在70%-85%之间浮动

  // 透明度默认0.9，使背景略微透明以便与页面更好融合
  const alpha = options.alpha !== undefined ? 
    Math.max(0.2, Math.min(1, options.alpha)) : 
    0.9;

  // 构建hsla颜色代码
  return `hsla(${finalHue}, ${saturation}%, ${lightness}%, ${alpha.toFixed(2)})`;
}