<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside"/>
    
    <!-- 顶部导航栏 -->
    <div class="header-container">
      <navbar ref="navbarRef" @setLayout="setLayout" />
    </div>

    <!-- 主体内容区 -->
    <div class="main-wrapper">
      <!-- 左侧导航 -->
      <div :class="{'opened': sidebar.opened}" class="left-section">
        <sidebar v-if="!sidebar.hide" class="sidebar-container" />
      </div>

      <!-- 中间内容区 -->
      <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }" class="main-container">
        <tags-view v-if="needTagsView" />
        <app-main />
        <settings ref="settingRef" />
      </div>

      <!-- 右侧应用区 -->
      <div class="right-section">
        <right-apps
          @openBrowser="openBrowser"
          @openLiveConsole="handleLiveConsoleClick"
          @openDyRobot="openDyRobot"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watchEffect, provide } from 'vue'
import { useWindowSize } from '@vueuse/core'
import Sidebar from './components/Sidebar/index.vue'
import { AppMain, Navbar, Settings, TagsView } from './components'
import RightApps from './components/RightApps/index.vue'
import useContext from '@/store/modules/context'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import modal from '@/plugins/modal'
import { fetchRecentLives, openLive } from '@/utils/liveService'

const userStore = useUserStore()
const recentCreatedLives = ref([])
const recentStartedLives = ref([])
const showModal = ref(false)

const settingsStore = useSettingsStore()
const theme = computed(() => settingsStore.theme);
const sideTheme = computed(() => settingsStore.sideTheme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const needTagsView = computed(() => settingsStore.tagsView);
const fixedHeader = computed(() => settingsStore.fixedHeader);

const navbarRef = ref(null)

// 提供 navbarRef 给子组件使用
provide('navbarRef', navbarRef)

// 浏览器涡轮
async function openBrowser() {
  await useContext().invoke('openChildBrowser');
}

// 直播控制台
async function handleLiveConsoleClick() {
  try {
    if (await checkAndShowLiveWindow()) return;
    const { recentCreatedLives: tempRecentCreatedLives, recentStartedLives: tempRecentStartedLives } = await fetchRecentLives(userStore.name);
    recentCreatedLives.value = tempRecentCreatedLives;
    recentStartedLives.value = tempRecentStartedLives;
    // 如果有正在直播的，则直接打开当前直播控制台，没有就显示弹窗让用户选择
    const isLiveStarted = recentStartedLives.value.some(live => live.status === 'started');
    if (isLiveStarted) {
      await openLive(recentStartedLives.value[0]);
    } else if (tempRecentCreatedLives.length || tempRecentStartedLives.length) {
      showModal.value = true;
    } else {
      modal.alertError("请先选择一场直播，请稍后再试！");
    }
  } catch (error) {
    console.error('未能获取直播信息', error);
  }
}

// 抖音机器人
async function openDyRobot() {
  await useContext().invoke("openDyWindow")
}

async function checkAndShowLiveWindow() {
  const liveWindowExists = await useContext().invoke('checkLiveWindow');
  if (liveWindowExists) {
    await useContext().invoke('openLiveWindow');
    return true;
  }
  return false;
}

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === 'mobile'
}))

const { width, height } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

watchEffect(() => {
  if (device.value === 'mobile' && sidebar.value.opened) {
    useAppStore().closeSideBar({ withoutAnimation: false })
  }
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice('mobile')
    useAppStore().closeSideBar({ withoutAnimation: true })
  } else {
    useAppStore().toggleDevice('desktop')
  }
})

function handleClickOutside() {
  useAppStore().closeSideBar({ withoutAnimation: false })
}

const settingRef = ref(null);
function setLayout() {
  settingRef.value.openSetting();
}
</script>

<style lang="scss" scoped>
@use "@/assets/styles/mixin.scss";
@use "@/assets/styles/variables.module.scss";

.app-wrapper {
  @include mixin.clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: rgba(0, 0, 0, 0.3);
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
  backdrop-filter: blur(4px);
}

.header-container {
  width: 100%;
  height: 64px;
  background: #ffffff;
  position: relative;
  z-index: 11;
}

.main-wrapper {
  flex: 1;
  width: 100%;
  display: flex;
  position: relative;
}

.left-section {
  position: relative;
  height: calc(100vh - 64px);
  transition: all 0.3s ease;
  background: #ffffff;
  z-index: 10;
  // width: $base-sidebar-width;

  &.collapse {
    width: 54px;
  }
}

.main-container {
  flex: 1;
  height: calc(100vh - 64px);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 16px;
  
  .app-main {
    border-radius: 8px;
    height: 100%;
    overflow: hidden;
  }
  
  &.hasTagsView {
    .app-main {
      height: calc(100% - 60px);
    }
  }

  .tags-view-container {
    margin-bottom: 16px;
  }
}

.right-section {
  width: 64px;
  height: calc(100vh - 64px);
  background: #ffffff;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
  gap: 8px;
  z-index: 10;
}

// 响应式处理
@media (max-width: 1200px) {
  .right-section {
    display: none;
  }
}

@media (max-width: 992px) {
  .app-wrapper {
    &.mobile {
      .left-section {
        position: fixed;
        top: 64px;
        left: 0;
        height: calc(100vh - 64px);
        transform: translateX(- variables.$base-sidebar-width);
        transition: transform 0.28s;
        z-index: 1001;
        
        &.opened {
          transform: translateX(0);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .app-wrapper {
    flex-direction: column;
  }
  
  .left-section {
    height: auto;
  }

  .main-container {
    margin: 8px;
  }
}
</style>