<template>
    <div class="app-container model-page">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px" class="search-form">
        <el-form-item label="模型编码" prop="modelCode">
          <el-input v-model="queryParams.modelCode" placeholder="请输入模型编码" clearable @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="模型名称" prop="modelName">
          <el-input v-model="queryParams.modelName" placeholder="请输入模型名称" clearable @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="模型状态" prop="modelStatus">
          <el-select v-model="queryParams.modelStatus" placeholder="请选择模型状态" clearable style="width: 170px">
            <el-option v-for="dict in model_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery" class="search-btn">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery" class="reset-btn">重置</el-button>
        </el-form-item>
      </el-form>
  
      <el-row :gutter="10" class="mb8 operation-row">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd" class="btn-operation">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" class="btn-operation">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport" class="btn-operation">导出</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
  
      <el-table 
        v-loading="loading" 
        :data="wymodelList" 
        @selection-change="handleSelectionChange"
        class="model-table"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        :row-class-name="tableRowClassName"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="模型编码" align="center" prop="modelCode" />
        <el-table-column label="模型名称" align="center" prop="modelName" />
        <el-table-column label="模型状态" align="center" prop="modelStatus" >
        <template #default="scope">
            <dict-tag :options="model_status" :value="scope.row.modelStatus" />
          </template>
        </el-table-column>
        <el-table-column label="价位" align="center" prop="modelVersion" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" class="btn-text">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" class="btn-text">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        class="pagination-container"
      />
  
      <!-- 添加或修改AI模型对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body custom-class="model-dialog">
        <el-form ref="wymodelRef" :model="form" :rules="rules" label-width="80px" class="dialog-form">
          <el-form-item label="模型编码" prop="modelCode">
            <el-input v-model="form.modelCode" placeholder="请输入模型编码" />
          </el-form-item>
          <el-form-item label="模型名称" prop="modelName">
            <el-input v-model="form.modelName" placeholder="请输入模型名称" />
          </el-form-item>
          <el-form-item label="模型状态" prop="modelStatus">
          <el-select v-model="form.modelStatus" placeholder="请选择模型状态" clearable >
            <el-option v-for="dict in model_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
          <el-form-item label="模型价位" prop="modelVersion">
            <el-input v-model="form.modelVersion" placeholder="请输入模型价位" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm" class="submit-btn">确 定</el-button>
            <el-button @click="cancel" class="cancel-btn">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup name="Wymodel">
  import { listWymodel, getWymodel, delWymodel, addWymodel, updateWymodel } from "@/api/platform/model";
  
  const { proxy } = getCurrentInstance();
  const { model_status } = proxy.useDict("model_status");
  
  const wymodelList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");
  
  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      modelCode: null,
      modelName: null,
      modelStatus: null,
    },
    rules: {
      modelCode: [
        { required: true, message: "模型编码不能为空", trigger: "blur" }
      ],
      modelName: [
        { required: true, message: "模型名称不能为空", trigger: "blur" }
      ],
      modelStatus: [
        { required: true, message: "模型状态不能为空", trigger: "change" }
      ],
      modelVersion: [
        { required: true, message: "模型价位不能为空", trigger: "blur" }
      ]
    }
  });
  
  const { queryParams, form, rules } = toRefs(data);
  
  /** 查询AI模型列表 */
  function getList() {
    loading.value = true;
    listWymodel(queryParams.value).then(response => {
      wymodelList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }
  
  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }
  
  // 表单重置
  function reset() {
    form.value = {
      modelId: null,
      modelCode: null,
      modelName: null,
      modelType: null,
      modelStatus: null,
      modelVersion: null,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      remark: null
    };
    proxy.resetForm("wymodelRef");
  }
  
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }
  
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }
  
  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.modelId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  
  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "创建模型";
  }
  
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _modelId = row.modelId || ids.value
    getWymodel(_modelId).then(response => {
      // 将response.data赋值给form.value之前，确保modelStatus是字符串类型
      let data = response.data;
      if (data.modelStatus !== null && data.modelStatus !== undefined) {
        data.modelStatus = String(data.modelStatus);
      }
      form.value = data;
      open.value = true;
      title.value = "编辑模型";
    });
  }
  
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["wymodelRef"].validate(valid => {
      if (valid) {
        if (form.value.modelId != null) {
          updateWymodel(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addWymodel(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }
  
  /** 删除按钮操作 */
  function handleDelete(row) {
    const _modelIds = row.modelId || ids.value;
    proxy.$modal.confirm('是否确认删除AI模型编号为"' + _modelIds + '"的数据项？').then(function() {
      return delWymodel(_modelIds);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }
  
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('wy/wymodel/export', {
      ...queryParams.value
    }, `wymodel_${new Date().getTime()}.xlsx`)
  }
  
  /** 表格行样式 */
  function tableRowClassName({ rowIndex }) {
    return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
  }
  
  getList();
  </script>
  
  <style scoped lang="scss">
.model-page { padding: 24px; background: #f9fafc; border-radius: 10px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08); min-height: calc(100vh - 120px); }
.search-form { margin-bottom: 24px; padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05); border-left: 4px solid #409eff; }
.operation-row { margin-bottom: 20px !important; }
.btn-operation { transition: all 0.3s ease; font-weight: 600; border-radius: 6px; padding: 8px 16px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); }
.btn-operation:hover { transform: translateY(-3px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12); }
.search-btn { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border: none; color: white; font-weight: 500; }
.search-btn:hover { background: linear-gradient(135deg, #5cb8ff 0%, #20f7ff 100%); }
.reset-btn { border: 1px solid #e0e5eb; background: #fff; color: #5a6978; }
.reset-btn:hover { color: #409eff; border-color: #b3d8ff; background-color: #ecf5ff; }
.model-table { margin-top: 16px; border-radius: 10px; overflow: hidden; box-shadow: 0 3px 15px rgba(0, 0, 0, 0.07); }
.model-table :deep(.el-table__header) { font-weight: 600; background: linear-gradient(to right, #f8f9fa, #eef2f7); }
.model-table :deep(.el-table__header) th { padding: 14px 0; }
.model-table :deep(.el-table__cell) { padding: 12px 0; }
.even-row { background-color: #ffffff; }
.odd-row { background-color: #f7fafd; }
.btn-text { font-size: 13px; transition: all 0.3s; margin: 0 4px; }
.btn-text:hover { transform: scale(1.1); text-shadow: 0 0 1px rgba(64, 158, 255, 0.4); }
.pagination-container { margin-top: 24px; text-align: right; padding: 10px 20px; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); }
.model-dialog { border-radius: 10px; overflow: hidden; }
.model-dialog :deep(.el-dialog__header) { background: linear-gradient(to right, #f0f5ff, #e6f7ff); border-bottom: 1px solid #e9ecef; padding: 18px 20px; }
.model-dialog :deep(.el-dialog__header) .el-dialog__title { font-weight: 600; color: #2c3e50; font-size: 18px; }
.model-dialog :deep(.el-dialog__body) { padding: 24px 30px; }
.model-dialog :deep(.el-dialog__footer) { border-top: 1px solid #e9ecef; padding: 16px 30px; }
.dialog-form :deep(.el-form-item__label) { font-weight: 500; color: #606266; }
.dialog-form :deep(.el-input__wrapper) { box-shadow: 0 0 0 1px #dcdfe6 inset; }
.dialog-form :deep(.el-input__wrapper):hover { box-shadow: 0 0 0 1px #c0c4cc inset; }
.dialog-form :deep(.el-input__wrapper).is-focus { box-shadow: 0 0 0 1px #409eff inset; }
.dialog-footer { text-align: right; }
.submit-btn { background: linear-gradient(135deg, #3f87f5 0%, #40b3ff 100%); border: none; transition: all 0.3s; padding: 10px 24px; font-weight: 500; }
.submit-btn:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3); }
.cancel-btn { transition: all 0.3s; border: 1px solid #dcdfe6; }
.cancel-btn:hover { border-color: #c6e2ff; color: #409eff; }
</style>
