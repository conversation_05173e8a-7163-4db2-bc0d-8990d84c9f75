import { 
  initChunkUpload, 
  uploadChunk, 
  completeChunkUpload,
  type ChunkUploadInitRequest,
  type FilePartETag,
  type CompleteUploadRequest
} from '../api/media'

// 分片上传配置
export interface ChunkUploadConfig {
  chunkSize: number // 分片大小，默认5MB
  maxConcurrent: number // 最大并发数，默认1（串行上传避免重复提交错误）
  retryTimes: number // 重试次数，默认3
}

// 上传进度回调
export interface UploadProgressCallback {
  (progress: {
    loaded: number
    total: number
    percentage: number
    currentChunk: number
    totalChunks: number
    speed: number // 上传速度 bytes/s
    remainingTime: number // 剩余时间 seconds
  }): void
}

// 分片上传器类
export class ChunkUploader {
  private config: ChunkUploadConfig
  private file: File
  private category: string
  private onProgress?: UploadProgressCallback
  private uploadId?: string
  private filePath?: string
  private chunks: Blob[] = []
  private uploadedChunks: Set<number> = new Set()
  private partETags: FilePartETag[] = []
  private startTime: number = 0
  private uploadedBytes: number = 0
  private isUploading: boolean = false
  private isCancelled: boolean = false

  constructor(
    file: File, 
    category: string = 'video',
    config: Partial<ChunkUploadConfig> = {},
    onProgress?: UploadProgressCallback
  ) {
    this.file = file
    this.category = category
    this.onProgress = onProgress
    this.config = {
      chunkSize: 5 * 1024 * 1024, // 5MB
      maxConcurrent: 1, // 默认串行上传，避免重复提交错误
      retryTimes: 3,
      ...config
    }
  }

  // 开始上传
  async upload(): Promise<any> {
    if (this.isUploading) {
      throw new Error('上传已在进行中')
    }

    this.isUploading = true
    this.isCancelled = false
    this.startTime = Date.now()
    this.uploadedBytes = 0

    try {
      // 1. 初始化上传
      await this.initUpload()
      
      // 2. 分割文件
      this.splitFile()
      
      // 3. 上传分片
      await this.uploadChunks()
      
      // 4. 完成上传
      const result = await this.completeUpload()
      
      return result
    } catch (error) {
      this.isUploading = false
      throw error
    } finally {
      this.isUploading = false
    }
  }

  // 取消上传
  cancel(): void {
    this.isCancelled = true
    this.isUploading = false
  }

  // 初始化上传
  private async initUpload(): Promise<void> {
    const initRequest: ChunkUploadInitRequest = {
      fileName: this.file.name,
      fileSize: this.file.size,
      category: this.category
    }

    console.log('初始化上传请求:', initRequest)
    const response = await initChunkUpload(initRequest)
    console.log('初始化上传响应:', response)

    if (response.code !== 200 && response.code !== '200') {
      throw new Error(`初始化上传失败: ${response.msg}`)
    }

    this.uploadId = response.data.uploadId
    this.filePath = response.data.filePath
  }

  // 分割文件
  private splitFile(): void {
    this.chunks = []
    const chunkSize = this.config.chunkSize
    let start = 0

    while (start < this.file.size) {
      const end = Math.min(start + chunkSize, this.file.size)
      const chunk = this.file.slice(start, end)
      this.chunks.push(chunk)
      start = end
    }
  }

  // 上传分片（串行上传避免重复提交错误）
  private async uploadChunks(): Promise<void> {
    const totalChunks = this.chunks.length

    // 串行上传每个分片
    for (let i = 0; i < totalChunks; i++) {
      if (this.isCancelled) {
        throw new Error('上传已取消')
      }

      const chunk = this.chunks[i]
      await this.uploadSingleChunk(chunk, i)

      // 添加小延迟，避免请求过于频繁
      if (i < totalChunks - 1) {
        await this.delay(100)
      }
    }

    if (this.uploadedChunks.size !== totalChunks) {
      throw new Error(`上传不完整: ${this.uploadedChunks.size}/${totalChunks}`)
    }
  }

  // 上传单个分片
  private async uploadSingleChunk(chunk: Blob, index: number): Promise<void> {
    if (this.isCancelled) return

    let retryCount = 0
    const maxRetries = this.config.retryTimes

    while (retryCount <= maxRetries) {
      try {
        const uploadRequest = {
          uploadId: this.uploadId!,
          filePath: this.filePath!,
          chunkIndex: index,
          chunk
        }

        console.log(`上传分片 ${index + 1}:`, {
          uploadId: uploadRequest.uploadId,
          filePath: uploadRequest.filePath,
          chunkIndex: uploadRequest.chunkIndex,
          chunkSize: chunk.size
        })

        const response = await uploadChunk(uploadRequest)
        console.log(`分片 ${index + 1} 上传响应:`, response)

        if (response.code !== 200 && response.code !== '200') {
          throw new Error(`分片上传失败: ${response.msg}`)
        }

        // 记录上传成功的分片
        this.uploadedChunks.add(index)
        this.partETags.push({
          partNumber: response.data.partNumber,
          ETag: response.data.etag, // 使用大写ETag匹配后端@JsonProperty("ETag")
          md5: response.data.md5
        })

        // 更新进度
        this.uploadedBytes += chunk.size
        this.updateProgress()

        return
      } catch (error) {
        console.error(`分片 ${index + 1} 上传失败 (重试 ${retryCount}/${maxRetries}):`, error)
        retryCount++
        if (retryCount > maxRetries) {
          throw new Error(`分片 ${index + 1} 上传失败: ${error}`)
        }

        // 等待一段时间后重试
        await this.delay(1000 * retryCount)
      }
    }
  }

  // 完成上传
  private async completeUpload(): Promise<any> {
    if (!this.uploadId || !this.filePath) {
      throw new Error('上传未初始化')
    }

    // 按分片号排序
    this.partETags.sort((a, b) => a.partNumber - b.partNumber)

    const completeRequest: CompleteUploadRequest = {
      uploadId: this.uploadId,
      filePath: this.filePath,
      fileSize: this.file.size,
      fileName: this.file.name,
      category: this.category,
      partETags: this.partETags
    }

    const response = await completeChunkUpload(completeRequest)
    if (response.code !== 200 && response.code !== '200') {
      throw new Error(`完成上传失败: ${response.msg}`)
    }

    return response.data
  }

  // 更新进度
  private updateProgress(): void {
    if (!this.onProgress) return

    const now = Date.now()
    const elapsed = (now - this.startTime) / 1000 // 秒
    const speed = elapsed > 0 ? this.uploadedBytes / elapsed : 0
    const remainingBytes = this.file.size - this.uploadedBytes
    const remainingTime = speed > 0 ? remainingBytes / speed : 0

    this.onProgress({
      loaded: this.uploadedBytes,
      total: this.file.size,
      percentage: (this.uploadedBytes / this.file.size) * 100,
      currentChunk: this.uploadedChunks.size,
      totalChunks: this.chunks.length,
      speed,
      remainingTime
    })
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}



// 工具函数：格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 工具函数：格式化上传速度
export function formatSpeed(bytesPerSecond: number): string {
  return formatFileSize(bytesPerSecond) + '/s'
}

// 工具函数：格式化剩余时间
export function formatRemainingTime(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.round(seconds % 60)
    return `${minutes}分${remainingSeconds}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}
