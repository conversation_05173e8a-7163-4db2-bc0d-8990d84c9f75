/**
 * 时间轴水平吸附配置预设
 * 提供不同场景下的吸附配置选项
 */

import type { TimeSnapConfig } from './timelineHorizontalSnapUtils';

/**
 * 默认配置 - 适合通用编辑
 */
export const DEFAULT_HORIZONTAL_SNAP: TimeSnapConfig = {
  threshold: 0.2,          // 0.2秒吸附范围
  enableClipSnap: true,    // 片段边界吸附
  enableRulerSnap: true,   // 标尺吸附 
  enablePlayheadSnap: true, // 播放头吸附
  rulerInterval: 1.0,      // 1秒间隔
  stabilityThreshold: 3    // 稳定性阈值
};

/**
 * 精确编辑配置 - 更严格的吸附
 */
export const PRECISION_HORIZONTAL_SNAP: TimeSnapConfig = {
  threshold: 0.1,          // 0.1秒精确吸附
  enableClipSnap: true,
  enableRulerSnap: true,
  enablePlayheadSnap: true,
  rulerInterval: 0.5,      // 0.5秒精细间隔
  stabilityThreshold: 5    // 更高稳定性要求
};

/**
 * 快速编辑配置 - 宽松的吸附
 */
export const QUICK_EDIT_HORIZONTAL_SNAP: TimeSnapConfig = {
  threshold: 0.5,          // 0.5秒宽松吸附
  enableClipSnap: true,
  enableRulerSnap: false,  // 关闭标尺吸附减少干扰
  enablePlayheadSnap: true,
  rulerInterval: 2.0,      // 2秒大间隔
  stabilityThreshold: 1    // 低稳定性要求快速响应
};

/**
 * 仅片段吸附配置 - 只吸附到其他片段
 */
export const CLIP_ONLY_HORIZONTAL_SNAP: TimeSnapConfig = {
  threshold: 0.3,
  enableClipSnap: true,    // 只启用片段吸附
  enableRulerSnap: false,  // 关闭标尺吸附
  enablePlayheadSnap: false, // 关闭播放头吸附
  rulerInterval: 1.0,
  stabilityThreshold: 2
};

/**
 * 音频同步配置 - 适合音频对齐
 */
export const AUDIO_SYNC_HORIZONTAL_SNAP: TimeSnapConfig = {
  threshold: 0.05,         // 极精确的0.05秒吸附
  enableClipSnap: true,
  enableRulerSnap: true,
  enablePlayheadSnap: true,
  rulerInterval: 0.1,      // 0.1秒音频级别精度
  stabilityThreshold: 8    // 高稳定性确保精确对齐
};

/**
 * 根据编辑模式获取推荐配置
 */
export function getRecommendedSnapConfig(mode: 'default' | 'precision' | 'quick' | 'clip-only' | 'audio-sync'): TimeSnapConfig {
  switch (mode) {
    case 'precision':
      return PRECISION_HORIZONTAL_SNAP;
    case 'quick':
      return QUICK_EDIT_HORIZONTAL_SNAP;
    case 'clip-only':
      return CLIP_ONLY_HORIZONTAL_SNAP;
    case 'audio-sync':
      return AUDIO_SYNC_HORIZONTAL_SNAP;
    default:
      return DEFAULT_HORIZONTAL_SNAP;
  }
}

/**
 * 检查配置是否有效
 */
export function validateSnapConfig(config: TimeSnapConfig): boolean {
  return (
    config.threshold > 0 &&
    config.rulerInterval > 0 &&
    config.stabilityThreshold >= 0 &&
    (config.enableClipSnap || config.enableRulerSnap || config.enablePlayheadSnap)
  );
}

/**
 * 创建自定义配置的辅助函数
 */
export function createCustomSnapConfig(overrides: Partial<TimeSnapConfig>): TimeSnapConfig {
  return {
    ...DEFAULT_HORIZONTAL_SNAP,
    ...overrides
  };
}
