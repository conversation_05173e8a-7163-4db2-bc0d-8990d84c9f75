<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="词表表名" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入词表表名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['tingwu:phrase:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['tingwu:phrase:remove']">批量删除</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="phraseList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="词表表名" align="center" prop="name" />
            <el-table-column label="词表描述" align="center" prop="description" />
            <el-table-column label="词表权重" align="center" prop="wordWeights" width="300">
                <template #default="scope">
                    <div v-if="scope.row.wordWeights" class="word-weights-display">
                        <div class="tags-container">
                            <el-tag v-for="(item, index) in getDisplayWordWeights(scope.row.wordWeights)" :key="index"
                                size="small" class="weight-tag" :type="getTagType(item.weight)">
                                {{ item.word }}:{{ item.weight }}
                            </el-tag>
                            <el-button
                                v-if="shouldShowMoreButton(scope.row.wordWeights)"
                                type="primary"
                                link
                                size="small"
                                @click="showWordWeightsDetail(scope.row)"
                                class="more-button">
                                +{{ getExtraCount(scope.row.wordWeights) }}更多
                            </el-button>
                        </div>
                    </div>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['tingwu:phrase:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['tingwu:phrase:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改词表信息对话框 -->
        <PhraseFormDialog
            v-model:visible="open"
            :title="title"
            :form-data="form"
            :rules="rules"
            @submit="handleFormSubmit"
            @cancel="cancel"
        />

        <!-- 词表权重详情弹窗 -->
        <WordWeightDetail
            v-model:visible="weightDetailOpen"
            :phrase-detail="currentPhraseDetail"
            :word-weights="currentWordWeights"
        />
    </div>
</template>

<script setup name="Phrase">
import { listPhrase, getPhraseByphraseId, delPhraseByPhraseIds, addPhrase, updatePhrase } from "@/api/tingwu/phrase"
import WordWeightDetail from './components/WordWeightDetail.vue'
import PhraseFormDialog from './components/PhraseFormDialog.vue'

const { proxy } = getCurrentInstance()

const phraseList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

// 词表权重详情弹窗相关
const weightDetailOpen = ref(false)
const currentPhraseDetail = ref({})
const currentWordWeights = ref([])

// 显示配置
const MAX_DISPLAY_TAGS = 3 // 最多显示的标签数量

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        description: null,
        wordWeights: null
    },
    rules: {
        name: [
            { required: true, message: "词表表名不能为空", trigger: "blur" }
        ],
    }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询词表信息列表 */
function getList() {
    loading.value = true
    listPhrase(queryParams.value).then(response => {
        phraseList.value = response.rows
        total.value = response.total
        loading.value = false
    })
}

// 取消按钮
function cancel() {
    open.value = false
    reset()
}

// 处理表单提交
function handleFormSubmit(formData) {
    if (formData.phraseId != null) {
        updatePhrase(formData).then(response => {
            proxy.$modal.msgSuccess("修改成功")
            open.value = false
            getList()
        })
    } else {
        addPhrase(formData).then(response => {
            proxy.$modal.msgSuccess("新增成功")
            open.value = false
            getList()
        })
    }
}

// 表单重置
function reset() {
    form.value = {
        keyId: null,
        name: null,
        description: null,
        wordWeights: JSON.stringify({}) // 设置为空的JSON对象字符串，避免后端空指针异常
    }
    proxy.resetForm("phraseRef")
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.phraseId)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
    reset()
    open.value = true
    title.value = "添加词表信息"
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset()
    const _phraseId = row.phraseId || ids.value
    getPhraseByphraseId(_phraseId).then(response => {
        const data = response.data[0]
        form.value = {
            phraseId: data.PhraseId,
            name: data.Name,
            description: data.Description,
            wordWeights: data.WordWeights
        }
        open.value = true
        title.value = "修改词表信息"
    })
}



/** 删除按钮操作 */
function handleDelete(row) {
    console.log("handleDelete", row)
    const _phraseIds = row.phraseId || ids.value
    proxy.$modal.confirm('是否确认删除所选词表的数据项？').then(function () {
        return delPhraseByPhraseIds(_phraseIds)
    }).then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
    }).catch(() => { })
}







/** 解析词汇权重为显示数组 */
function parseWordWeights(wordWeights) {
    try {
        // 如果为空或null，返回空数组
        if (!wordWeights) {
            return []
        }

        let weightsObj = wordWeights

        // 如果是字符串，才需要解析
        if (typeof weightsObj === 'string') {
            weightsObj = JSON.parse(weightsObj)
        }

        // 如果解析后为空或null，返回空数组
        if (!weightsObj || typeof weightsObj !== 'object') {
            return []
        }

        // 如果已经是对象，直接使用
        return Object.entries(weightsObj).map(([word, weight]) => ({
            word,
            weight
        }))
    } catch (e) {
        console.warn('解析词汇权重失败:', e)
        return []
    }
}

/** 获取用于表格显示的词汇权重（限制数量） */
function getDisplayWordWeights(wordWeights) {
    const allWeights = parseWordWeights(wordWeights)
    return allWeights.slice(0, MAX_DISPLAY_TAGS)
}

/** 判断是否需要显示"更多"按钮 */
function shouldShowMoreButton(wordWeights) {
    const allWeights = parseWordWeights(wordWeights)
    return allWeights.length > MAX_DISPLAY_TAGS
}

/** 获取超出显示数量的词汇个数 */
function getExtraCount(wordWeights) {
    const allWeights = parseWordWeights(wordWeights)
    return Math.max(0, allWeights.length - MAX_DISPLAY_TAGS)
}

/** 根据权重值获取标签类型 */
function getTagType(weight) {
    if (weight > 0) {
        return 'success' // 绿色 - 增大识别概率
    } else if (weight === -6) {
        return 'danger' // 红色 - 尽量不识别
    } else if (weight < 0) {
        return 'warning' // 橙色 - 负权重
    } else {
        return '' // 默认颜色 - 权重为0
    }
}

/** 显示词表权重详情 */
function showWordWeightsDetail(row) {
    currentPhraseDetail.value = {
        name: row.name,
        description: row.description
    }
    currentWordWeights.value = parseWordWeights(row.wordWeights)
    weightDetailOpen.value = true
}

getList()
</script>

<style scoped>
/* 词表权重显示样式 */
.word-weights-display {
    max-width: 100%;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
    justify-content: center;
}

.tags-container .weight-tag {
    margin: 0;
    font-size: 11px;
    border-radius: 12px;
    padding: 2px 8px;
    white-space: nowrap;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.more-button {
    font-size: 11px;
    padding: 2px 8px;
    margin: 0;
    height: auto;
    line-height: 1.2;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.more-button:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}
</style>
