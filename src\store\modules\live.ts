
import { defineStore } from 'pinia'
import { listLive, getLive, delLive, addLive, updateLive, getLiveOption, getLiveInfo } from "@/api/platform/live";
import { getKeywordFormat } from "@/api/platform/keyword";
type CategoryMap = {
  [key: string]: number[];
};
export interface LiveVo {
    /*创建者 */
    createBy: string

    /*创建时间 */
    createTime: Record<string, unknown>

    /*更新者 */
    updateBy: string

    /*更新时间 */
    updateTime: Record<string, unknown>

    /*备注 */
    remark: string

    /*请求参数 */
    params: Record<string, unknown>

    /*主键 */
    liveId: number

    /*项目Id */
    projectId: number

    /*直播类型 0实景无人播 1数字人直播 */
    liveType: string

    /* */
    scenecon: {
        /*创建者 */
        createBy: string

        /*创建时间 */
        createTime: Record<string, unknown>

        /*更新者 */
        updateBy: string

        /*更新时间 */
        updateTime: Record<string, unknown>

        /*备注 */
        remark: string

        /*请求参数 */
        params: Record<string, unknown>

        /*主键 */
        sceneconId: number

        /*项目Id */
        projectId: number

        /*场控名称 */
        sceneconName: string

        /*场控互动分类Ids */
        sceneconInteractionId: number[]

        /*场控问答分类Ids */
        sceneconQuestionsId: number[]
    }

    /*产品列表 */
    goods: {
        /*创建者 */
        createBy: string

        /*创建时间 */
        createTime: Record<string, unknown>

        /*更新者 */
        updateBy: string

        /*更新时间 */
        updateTime: Record<string, unknown>

        /*备注 */
        remark: string

        /*请求参数 */
        params: Record<string, unknown>

        /*主键 */
        goodsId: number

        /*项目Id */
        projectId: number

        /*产品名称 */
        goodsName: string

        /*产品互动分类Ids */
        goodsInteractionId: number[]

        /*产品问答分类Ids */
        goodsQuestionsId: number[]
    }[]

    /*直播名称 */
    liveName: string
}


const useLiveStore = defineStore(
  'live',
  {
    state: () => ({
      projectId: null,
      liveId: '',
      rommId: null,
      categoryMap: {} as CategoryMap,
      liveVo:{} as LiveVo,
      categoryQuestions: [] as Array<number>,
      status:{
        isCheck:false,
        isLive:0,
        isBarrage:0,
        isReply:0
      },
      liveing: {} ,
    }),
    actions: {
      async checkLive(){
        if(this.liveId!==''){
          return getLiveInfo(this.liveId).then(res=>{
            if(res.code == 200){
              this.liveVo = res.data
              this.categoryQuestions = (this.liveVo.goods?.map(good => good.goodsQuestionsId) || [])
              .flat()
              .concat(this.liveVo.scenecon?.sceneconQuestionsId || []);
              return getLiveOption(this.liveId).then(res=>{
                if(res.code == 200){
                  this.categoryMap = res.data
                  return true
                }else{
                  return false
                }
              })
            }else{
              return false      
            } 
          })
        }else{
          return false
        }
      }
    }  
  })

export default useLiveStore