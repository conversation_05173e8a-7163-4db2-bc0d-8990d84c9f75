<script setup>
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const isCollapse = ref(false);

// 监听窗口大小变化
const handleResize = () => {
    isCollapse.value = window.innerWidth < 768;
};

onMounted(() => {
    handleResize();
    window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
});

function menuClickHandle(title) {
    router.push({ path: `/project/${route.params.projectId}/${title}`, query: { projectTitle: route.query.projectTitle } })
}
</script>
<template>
    <div class="common-layout">
        <el-container>
            <el-container>
                <el-aside :width="isCollapse ? '64px' : '200px'" class="responsive-aside">
                    <el-menu 
                        :default-active="route.path.split('/').splice(-1, 1)[0]" 
                        :collapse="isCollapse"
                        style="height: 100%;">
                        <el-menu-item index="material" @click="menuClickHandle('material')">
                            <el-icon><Document /></el-icon>
                            <template #title>素材</template>
                        </el-menu-item>
                        <el-menu-item index="anchor" @click="menuClickHandle('anchor')">
                            <el-icon><User /></el-icon>
                            <template #title>智能主播</template>
                        </el-menu-item>
                        <el-menu-item index="live" @click="menuClickHandle('live')">
                            <el-icon><Setting /></el-icon>
                            <template #title>规则</template>
                        </el-menu-item>
                        <el-menu-item index="broadcast" @click="menuClickHandle('broadcast')">
                            <el-icon><VideoPlay /></el-icon>
                            <template #title>直播</template>
                        </el-menu-item>
                    </el-menu>
                </el-aside>
                <el-main>
                    <router-view />
                </el-main>
            </el-container>
        </el-container>
    </div>
</template>
<style lang="scss" scoped>
.responsive-aside {
    transition: width 0.3s;
    background: none;
    height: 300px;

    :deep(.el-menu) {
        border-radius: 5px;
        background: none;
        border: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;

        .el-menu-item {
            width: 90%;
            margin: 10px auto;
            height: 40px;
            border-radius: 15px;
            background-color: #ffffff;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease-in-out;

            &.is-active {
                box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.3);
            }

            &:hover {
                transform: translateY(-5px);
            }

            &:active {
                transform: translateY(5px);
            }
        }
    }
}

@media screen and (max-width: 768px) {
    .responsive-aside {
        :deep(.el-menu) {
            .el-menu-item {
                width: 40px;
                margin: 10px auto;
                padding: 0 !important;
                display: flex;
                justify-content: center;
            }
        }
    }
}
</style>