import { ref, reactive } from 'vue'
import type { Timeline } from '../types/videoEdit';

export interface DragState {
    isDragging: boolean;
    draggedClip: {
        type: 'video' | 'audio' | 'subtitle';
        trackIndex: number;
        clipIndex: number;
        element: HTMLElement;
        initialLeft: number;
        offsetX: number;
    } | null;
    dragOverTrack: {
        type: 'video' | 'audio' | 'subtitle' | null;
        index: number | null;
    };
    isShowingPreviewTrack: boolean;
}

export interface TrackLayout {
    videoTracksCount: number;
    audioTracksCount: number;
    subtitleTracksCount: number;
    trackHeight: number;
}

export interface DragPosition {
    x: number;
    y: number;
    trackIndex: number;
    trackType: 'video' | 'audio' | 'subtitle' | null;
    targetTrackIndex: number | null;
}

/**
 * 计算拖拽位置和目标轨道
 */
export function calculateDragPosition(
    clientX: number,
    clientY: number,
    containerRect: DOMRect,
    scrollLeft: number,
    scrollTop: number,
    offsetX: number,
    trackLayout: TrackLayout
): DragPosition {
    // 计算新的水平位置
    const newLeft = clientX - containerRect.left + scrollLeft - offsetX;
    const clampedLeft = Math.max(0, newLeft);
    
    // 计算垂直位置和轨道索引
    const relativeY = clientY - containerRect.top + scrollTop;
    const trackIndex = Math.floor(relativeY / trackLayout.trackHeight);
    
    // 确定目标轨道类型和索引
    let targetTrackType: 'video' | 'audio' | 'subtitle' | null = null;
    let targetTrackIndex: number | null = null;
    
    const { videoTracksCount, audioTracksCount } = trackLayout;
    
    if (trackIndex >= 0 && trackIndex < videoTracksCount) {
        targetTrackType = 'video';
        // 直接使用轨道在显示列表中的索引
        targetTrackIndex = trackIndex;
    } else if (trackIndex >= videoTracksCount && trackIndex < videoTracksCount + audioTracksCount) {
        targetTrackType = 'audio';
        targetTrackIndex = trackIndex - videoTracksCount;
    } else if (trackIndex >= videoTracksCount + audioTracksCount) {
        targetTrackType = 'subtitle';
        targetTrackIndex = trackIndex - videoTracksCount - audioTracksCount;
    }
    
    return {
        x: clampedLeft,
        y: relativeY,
        trackIndex,
        trackType: targetTrackType,
        targetTrackIndex
    };
}

/**
 * 判断是否为跨轨道拖拽
 */
export function isCrossTrackDrag(
    draggedType: string,
    draggedTrackIndex: number,
    targetType: string | null,
    targetTrackIndex: number | null
): boolean {
    return targetType === draggedType && 
           targetTrackIndex !== null && 
           targetTrackIndex !== draggedTrackIndex;
}

/**
 * 判断拖拽方向和轨道创建需求
 */
export function analyzeDragDirection(
    draggedTrackIndex: number,
    targetTrackIndex: number | null,
    existingTracksCount: number
): {
    direction: 'up' | 'down' | 'same';
    needsNewTrack: boolean;
    newTrackPosition: 'top' | 'bottom' | null;
} {
    if (targetTrackIndex === null) {
        return {
            direction: 'same',
            needsNewTrack: false,
            newTrackPosition: null
        };
    }
    
    // 判断拖拽方向
    let direction: 'up' | 'down' | 'same' = 'same';
    if (targetTrackIndex < draggedTrackIndex) {
        direction = 'up';
    } else if (targetTrackIndex > draggedTrackIndex) {
        direction = 'down';
    }
    
    // 判断是否需要创建新轨道
    // 根据用户反馈，向上拖拽应该创建新轨道，向下拖拽也可能创建新轨道
    // 如果目标轨道索引等于-1或超出现有轨道范围，需要创建新轨道
    const needsNewTrack = targetTrackIndex === -1 || 
                         targetTrackIndex >= existingTracksCount ||
                         (direction === 'up' && targetTrackIndex === 0); // 向上拖拽到第一个轨道也创建新轨道
    
    // 确定新轨道位置
    let newTrackPosition: 'top' | 'bottom' | null = null;
    if (needsNewTrack) {
        // 向上拖拽总是创建在顶部，向下拖拽创建在底部
        if (direction === 'up' || targetTrackIndex < draggedTrackIndex) {
            newTrackPosition = 'top';
        } else {
            newTrackPosition = 'bottom';
        }
    }
    
    return {
        direction,
        needsNewTrack,
        newTrackPosition
    };
}

/**
 * 更新拖拽状态
 */
export function updateDragState(
    dragState: DragState,
    position: DragPosition,
    draggedClip: NonNullable<DragState['draggedClip']>,
    trackCounts: { video: number; audio: number; subtitle: number }
): void {
    const isCrossDrag = isCrossTrackDrag(
        draggedClip.type,
        draggedClip.trackIndex,
        position.trackType,
        position.targetTrackIndex
    );
    
    if (isCrossDrag && position.targetTrackIndex !== null) {
        dragState.dragOverTrack.type = position.trackType;
        dragState.dragOverTrack.index = position.targetTrackIndex;
        
        // 分析拖拽方向和轨道创建需求
        const trackCount = position.trackType === 'video' ? trackCounts.video :
                          position.trackType === 'audio' ? trackCounts.audio :
                          trackCounts.subtitle;
        
        const analysis = analyzeDragDirection(
            draggedClip.trackIndex,
            position.targetTrackIndex,
            trackCount
        );
        
        dragState.isShowingPreviewTrack = analysis.needsNewTrack;
        
        console.log('🔄 跨轨道拖拽分析:', {
            direction: analysis.direction,
            needsNewTrack: analysis.needsNewTrack,
            newTrackPosition: analysis.newTrackPosition,
            from: draggedClip.trackIndex,
            to: position.targetTrackIndex
        });
    } else {
        dragState.dragOverTrack.type = null;
        dragState.dragOverTrack.index = null;
        dragState.isShowingPreviewTrack = false;
    }
}

/**
 * 应用简化的吸附逻辑
 */
export function applySnapping(
    newTime: number,
    timeline: Timeline | null,
    draggedClip: NonNullable<DragState['draggedClip']>,
    pixelsPerSecond: number,
    threshold: number
): number {
    if (!timeline) return newTime;
    
    // 这里可以实现简化的吸附逻辑
    // 暂时返回原始时间，后续可以根据需要扩展
    return newTime;
}

/**
 * 创建响应式拖拽状态管理器
 */
export function createDragManager() {
    const isDragging = ref(false);
    const draggedClip = ref<DragState['draggedClip']>(null);
    const dragOverTrack = reactive({
        type: null as 'video' | 'audio' | 'subtitle' | null,
        index: null as number | null
    });
    const isShowingPreviewTrack = ref(false);
    
    return {
        // 响应式状态
        isDragging,
        draggedClip,
        dragOverTrack,
        isShowingPreviewTrack,
        
        // 方法
        startDrag: (clip: NonNullable<DragState['draggedClip']>) => {
            isDragging.value = true;
            draggedClip.value = clip;
            console.log('🚀 拖拽管理器: 开始拖拽', clip);
        },
        
        updatePosition: (position: DragPosition, trackCounts: { video: number; audio: number; subtitle: number }) => {
            if (!draggedClip.value) return;
            
            const isCrossDrag = isCrossTrackDrag(
                draggedClip.value.type,
                draggedClip.value.trackIndex,
                position.trackType,
                position.targetTrackIndex
            );
            
            if (isCrossDrag && position.targetTrackIndex !== null) {
                // 避免重复的状态更新和日志输出
                const hasStateChanged = dragOverTrack.type !== position.trackType || 
                                       dragOverTrack.index !== position.targetTrackIndex;
                
                dragOverTrack.type = position.trackType;
                dragOverTrack.index = position.targetTrackIndex;
                
                // 分析拖拽方向和轨道创建需求
                const trackCount = position.trackType === 'video' ? trackCounts.video :
                                  position.trackType === 'audio' ? trackCounts.audio :
                                  trackCounts.subtitle;
                
                const analysis = analyzeDragDirection(
                    draggedClip.value.trackIndex,
                    position.targetTrackIndex,
                    trackCount
                );
                
                isShowingPreviewTrack.value = analysis.needsNewTrack;
                
                // 只在状态发生变化时输出日志
                if (hasStateChanged) {
                    console.log('🔄 跨轨道拖拽:', {
                        direction: analysis.direction,
                        needsNewTrack: analysis.needsNewTrack,
                        from: draggedClip.value.trackIndex,
                        to: position.targetTrackIndex
                    });
                }
            } else {
                dragOverTrack.type = null;
                dragOverTrack.index = null;
                isShowingPreviewTrack.value = false;
            }
        },
        
        endDrag: () => {
            isDragging.value = false;
            console.log('🏁 拖拽管理器: 结束拖拽');
        },
        
        reset: () => {
            isDragging.value = false;
            draggedClip.value = null;
            dragOverTrack.type = null;
            dragOverTrack.index = null;
            isShowingPreviewTrack.value = false;
            console.log('🔄 拖拽管理器: 重置状态');
        }
    };
}
