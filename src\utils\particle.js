// 配置参数
import { rgbToHsl, hslToRgb } from './geek'

const config = {
  particleRadius: 3,         // 基础粒子半径
  particleOpacity: 0.8,      // 基础不透明度
  particleCount: 15000,      // 粒子数量
  animateTime: 50,           // 动画时间
  opacityStep: 0.03,         // 透明度增加速度
  repulsionRadius: 200,      // 排斥半径
  repulsionIntensity: 2,     // 排斥强度
  minDistance: 5,            // 最小距离
  returnSpeed: 0.1,          // 返回速度
  initialSpeed: 0.3,         // 初始移动速度
  minSpeed: 0.05,           // 最小移动速度
  designWidth: 1000,         // 设计宽度
  designHeight: 1000,        // 设计高度
  breathePeriod: 2000,       // 呼吸周期(毫秒)
  breatheScale: 0.2,         // 呼吸缩放幅度
  waveHeight: 20,           // 波浪高度
  waveSpeed: 0.002,         // 波浪速度
  colorShiftSpeed: 0.001    // 颜色变化速度
}

// 粒子类
class Particle {
  constructor(totalX, totalY, time, color) {
    // 计算初始位置
    const angle = Math.random() * Math.PI * 2
    const distance = Math.random() * 200  // 初始分布范围
    
    // 在目标位置周围随机分布
    this.x = totalX + Math.cos(angle) * distance
    this.y = totalY + Math.sin(angle) * distance
    
    // 目标位置
    this.totalX = totalX
    this.totalY = totalY
    
    // 移动相关
    this.time = time
    this.mx = 0
    this.my = 0
    this.vx = 0
    this.vy = 0
    this.speed = config.initialSpeed
    
    // 样式相关
    this.baseRadius = config.particleRadius * (0.8 + Math.random() * 0.4)  // 随机基础大小
    this.r = this.baseRadius
    this.color = [...color]
    this.baseColor = [...color]
    this.opacity = 0
    
    // 动画相关
    this.breatheOffset = Math.random() * Math.PI * 2  // 随机呼吸偏移
    this.waveOffset = Math.random() * Math.PI * 2     // 随机波浪偏移
    this.colorOffset = Math.random() * Math.PI * 2    // 随机颜色偏移
    this.startTime = Date.now()                       // 记录开始时间
  }
  
  // 更新粒子状态
  update(mouseX, mouseY) {
    const now = Date.now()
    const timeDiff = now - this.startTime
    
    // 呼吸效果
    const breathe = Math.sin(timeDiff * (Math.PI * 2 / config.breathePeriod) + this.breatheOffset)
    this.r = this.baseRadius * (1 + breathe * config.breatheScale)
    
    // 波浪效果
    const wave = Math.sin(this.x * 0.01 + timeDiff * config.waveSpeed + this.waveOffset)
    const waveY = wave * config.waveHeight
    
    // 颜色渐变效果
    const colorShift = Math.sin(timeDiff * config.colorShiftSpeed + this.colorOffset)
    this.color = this.baseColor.map((c, i) => {
      const shift = i === 0 ? colorShift * 20 : i === 1 ? colorShift * 15 : colorShift * 10
      return Math.max(0, Math.min(255, c + shift))
    })
    
    // 计算与目标位置的距离
    this.mx = this.totalX - this.x
    this.my = (this.totalY + waveY) - this.y  // 加入波浪效果
    
    // 计算当前距离
    const distance = Math.sqrt(this.mx * this.mx + this.my * this.my)
    
    // 根据距离动态调整速度
    if(distance > 100) {
      this.speed = config.initialSpeed
    } else {
      this.speed = config.minSpeed + (config.initialSpeed - config.minSpeed) * (distance / 100)
    }
    
    // 基础速度
    this.vx = this.mx * this.speed
    this.vy = this.my * this.speed
    
    // 如果有鼠标位置,计算排斥力
    if(mouseX !== undefined && mouseY !== undefined) {
      const dx = mouseX - this.x
      const dy = mouseY - this.y
      const distance = Math.sqrt(dx * dx + dy * dy)
      
      if(distance < config.repulsionRadius) {
        // 计算排斥力度
        const force = (1 - distance / config.repulsionRadius) * config.repulsionIntensity
        
        // 确保最小距离
        if(distance < config.minDistance) {
          this.x += (Math.random() - 0.5) * 2
          this.y += (Math.random() - 0.5) * 2
          return
        }
        
        // 计算排斥方向
        const repX = (dx / distance) * force * -1
        const repY = (dy / distance) * force * -1
        
        // 叠加到速度
        this.vx += repX
        this.vy += repY
        
        // 粒子大小变化
        this.r = this.baseRadius * (1 + force * 0.5 + breathe * config.breatheScale)
      }
    }
    
    // 更新位置
    this.x += this.vx
    this.y += this.vy
    
    // 更新透明度 (加入呼吸效果)
    const targetOpacity = config.particleOpacity * (0.8 + breathe * 0.2)
    if(this.opacity < targetOpacity) {
      this.opacity += config.opacityStep
    } else if(this.opacity > targetOpacity) {
      this.opacity -= config.opacityStep
    }
  }
  
  // 切换目标位置
  change(totalX, totalY, color) {
    this.totalX = totalX
    this.totalY = totalY
    this.baseColor = [...color]
    this.color = [...color]
    this.time = config.animateTime
  }
}

// 图片类
class LogoImg {
  constructor(imgSrc) {
    this.particleData = []
  }
  
  loadImage(imgSrc) {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        this.parseImage(img)
        resolve()
      }
      img.onerror = reject
      img.src = imgSrc
    })
  }
  
  parseImage(img) {
    // 创建临时canvas
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    // 设置canvas大小为图片大小
    canvas.width = img.width
    canvas.height = img.height
    
    // 绘制图片并调整颜色
    ctx.drawImage(img, 0, 0)
    
    try {
      // 获取像素数据
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data
      
      // 计算缩放比例
      const scale = Math.min(
        config.designWidth / canvas.width,
        config.designHeight / canvas.height
      ) * 0.8 // 留出边距
      
      // 计算居中偏移
      const offsetX = (config.designWidth - canvas.width * scale) / 2
      const offsetY = (config.designHeight - canvas.height * scale) / 2
      
      // 采样步长(控制粒子数量)
      const step = Math.max(1, Math.floor(Math.sqrt(canvas.width * canvas.height / config.particleCount)))
      
      // 筛选像素点
      for(let y = 0; y < canvas.height; y += step) {
        for(let x = 0; x < canvas.width; x += step) {
          const index = (y * canvas.width + x) * 4
          const alpha = data[index + 3]
          
          // 只选取不透明的像素
          if(alpha > 100) {
            // 转换坐标到设计坐标系中心
            const scaledX = x * scale + offsetX - config.designWidth / 2
            const scaledY = y * scale + offsetY - config.designHeight / 2
            
            // 增强颜色饱和度和对比度
            const r = Math.max(0, Math.min(255, data[index] * 0.9))      // 调整亮度
            const g = Math.max(0, Math.min(255, data[index + 1] * 0.9))
            const b = Math.max(0, Math.min(255, data[index + 2] * 0.9))
            
            // 计算颜色的HSL值
            const [h, s, l] = rgbToHsl(r, g, b)
            // 增加饱和度和亮度
            const [newR, newG, newB] = hslToRgb(h, Math.min(1, s * 1.2), Math.min(1, l * 0.9))
            
            const particle = new Particle(
              scaledX,
              scaledY,
              config.animateTime,
              [newR, newG, newB, alpha]
            )
            this.particleData.push(particle)
          }
        }
      }
    } catch(error) {
      console.error('Error parsing image:', error)
    }
  }
}

// 画布类
class ParticleCanvas {
  constructor(canvas) {
    this.canvas = canvas
    this.ctx = canvas.getContext('2d')
    this.width = canvas.width
    this.height = canvas.height
    this.particles = []
    this.mouseX = 0
    this.mouseY = 0
    this.scale = Math.min(this.width / config.designWidth, this.height / config.designHeight)
    
    // 绑定鼠标事件
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this))
    
    // 开始动画循环
    this.animate()
  }
  
  // 坐标转换 - 设计坐标到实际坐标
  transformCoord(x, y) {
    const realX = (x + config.designWidth / 2) * this.scale + (this.width - config.designWidth * this.scale) / 2
    const realY = (y + config.designHeight / 2) * this.scale + (this.height - config.designHeight * this.scale) / 2
    return [realX, realY]
  }
  
  // 坐标转换 - 实际坐标到设计坐标
  reverseTransformCoord(realX, realY) {
    const x = (realX - (this.width - config.designWidth * this.scale) / 2) / this.scale - config.designWidth / 2
    const y = (realY - (this.height - config.designHeight * this.scale) / 2) / this.scale - config.designHeight / 2
    return [x, y]
  }
  
  // 鼠标移动事件
  onMouseMove(e) {
    const rect = this.canvas.getBoundingClientRect()
    const realX = e.clientX - rect.left
    const realY = e.clientY - rect.top
    ;[this.mouseX, this.mouseY] = this.reverseTransformCoord(realX, realY)
  }
  
  // 动画循环
  animate() {
    // 清空画布
    this.ctx.clearRect(0, 0, this.width, this.height)
    
    // 更新并绘制所有粒子
    this.particles.forEach(particle => {
      particle.update(this.mouseX, this.mouseY)
      
      // 转换坐标后绘制
      const [realX, realY] = this.transformCoord(particle.x, particle.y)
      this.ctx.beginPath()
      this.ctx.fillStyle = `rgba(${particle.color[0]}, ${particle.color[1]}, ${particle.color[2]}, ${particle.opacity})`
      this.ctx.arc(realX, realY, particle.r * this.scale, 0, Math.PI * 2)
      this.ctx.fill()
      this.ctx.closePath()
    })
    
    requestAnimationFrame(this.animate.bind(this))
  }
  
  // 更换图片
  changeImg(logoImg) {
    if(this.particles.length) {
      // 已有粒子,需要处理映射关系
      const newParticles = logoImg.particleData
      const newLen = newParticles.length
      const oldLen = this.particles.length
      
      // 计算粒子分布
      const particleGroups = this.groupParticles(newParticles)
      
      // 更新已存在的粒子
      let currentIndex = 0
      for(const group of particleGroups) {
        for(const particle of group) {
          if(this.particles[currentIndex]) {
            this.particles[currentIndex].change(particle.totalX, particle.totalY, particle.color)
          } else {
            this.particles[currentIndex] = new Particle(particle.totalX, particle.totalY, config.animateTime, particle.color)
          }
          currentIndex++
        }
      }
      
      // 处理多余的粒子
      if(currentIndex < oldLen) {
        this.particles = this.particles.slice(0, currentIndex)
      }
    } else {
      // 首次加载,按组分配粒子
      const particleGroups = this.groupParticles(logoImg.particleData)
      this.particles = []
      
      for(const group of particleGroups) {
        for(const particle of group) {
          this.particles.push(
            new Particle(particle.totalX, particle.totalY, config.animateTime, particle.color)
          )
        }
      }
    }
  }
  
  // 将粒子分组以优化加载
  groupParticles(particles) {
    // 计算图像中心
    let centerX = 0
    let centerY = 0
    for(const p of particles) {
      centerX += p.totalX
      centerY += p.totalY
    }
    centerX /= particles.length
    centerY /= particles.length
    
    // 按照到中心的距离分组
    const groups = []
    const sortedParticles = [...particles].sort((a, b) => {
      const distA = Math.sqrt(Math.pow(a.totalX - centerX, 2) + Math.pow(a.totalY - centerY, 2))
      const distB = Math.sqrt(Math.pow(b.totalX - centerX, 2) + Math.pow(b.totalY - centerY, 2))
      return distA - distB
    })
    
    // 将粒子分成多个组,每组大约100个粒子
    const groupSize = 100
    for(let i = 0; i < sortedParticles.length; i += groupSize) {
      groups.push(sortedParticles.slice(i, i + groupSize))
    }
    
    return groups
  }
  
  // 更新画布大小
  resize(width, height) {
    this.width = width
    this.height = height
    this.scale = Math.min(this.width / config.designWidth, this.height / config.designHeight)
  }
}

export { Particle, LogoImg, ParticleCanvas, config } 