<template>
  <div class="app-container home">
    <div>
      <VipCard />
    </div>
    <div class="card-group">
      <div>
        <Cardbutton title="浏览器涡轮" icon="ChromeFilled" fontSize="90" style="margin: 20px;" @click="openBrowser()" />
      </div>
      <div>
        <Cardbutton title="直播控制台" icon="monitor" fontSize="70" style="margin: 20px;"
          @click="handleLiveConsoleClick()" />
      </div>
      <div>
        <Cardbutton title="算力点监控" icon="pieChart" fontSize="70" style="margin: 20px;" @click="handleConsumption()" />
      </div>
    </div>
    <el-dialog v-model="showModal" width="55%" :before-close="handleClose"
      style="margin: 0 auto; padding: 0px 15px; top: 20%; height: 310px;">
      <div class="live-tabs">
        <div class="recent-created">
          <h3 style="margin: 0px; padding-bottom: 8px;">最近创建</h3>
          <el-table :data="recentCreatedLives" style="width: 100%" @row-click="selectLive">
            <el-table-column prop="projectTitle" label="所属项目" width="120" align="center" />
            <el-table-column prop="liveName" label="直播名称" width="130" align="center" />
            <el-table-column prop="updateTime" label="最近创建" width="165" align="center" />
          </el-table>
        </div>
        <div class="recent-started">
          <h3 style="margin: 0px; padding-bottom: 8px;">最近开播</h3>
          <el-table :data="recentStartedLives" style="width: 100%" @row-click="selectLive">
            <el-table-column prop="projectTitle" label="所属项目" width="120" align="center" />
            <el-table-column prop="liveName" label="直播名称" width="130" align="center" />
            <el-table-column prop="updateTime" label="开播时间" width="165" align="center" />
          </el-table>
        </div>
      </div>
    </el-dialog>
    <el-dialog v-model="consumptionModal" :title="title" width="60%" :before-close="handleClose">
      <Statistics :user-name="userStore.name" style="width: 130%; height: 330px; " />
    </el-dialog>
  </div>
</template>

<script setup name="Index">
import { ref } from 'vue';
import Cardbutton from '@/views/components/cardbutton.vue';
import Statistics from './platform/consumption/statistics.vue';
import useUserStore from '@/store/modules/user';
import modal from '@/plugins/modal';
import useContext from '@/store/modules/context';
import { fetchRecentLives, openLive } from '@/utils/liveService';
import VipCard from '@/views/components/vip-card.vue';

const showModal = ref(false);
const recentCreatedLives = ref([]);
const recentStartedLives = ref([]);
const userStore = useUserStore();
const consumptionModal = ref(false);
const title = ref("");

async function handleLiveConsoleClick() {
  try {
    if (await checkAndShowLiveWindow()) return;
    const { recentCreatedLives: tempRecentCreatedLives, recentStartedLives: tempRecentStartedLives } = await fetchRecentLives(userStore.name);
    recentCreatedLives.value = tempRecentCreatedLives;
    recentStartedLives.value = tempRecentStartedLives;
    // 如果有正在直播的，则直接打开当前直播控制台，没有就显示弹窗让用户选择
    const isLiveStarted = recentStartedLives.value.some(live => live.status === 'started');
    if (isLiveStarted) {
      await openLive(recentStartedLives.value[0]);
    } else if (tempRecentCreatedLives.length || tempRecentStartedLives.length) {
      showModal.value = true;
    } else {
      modal.alertError("请先选择一场直播，请稍后再试！");
    }
  } catch (error) {
    console.error('未能获取直播信息', error);
  }
}

function selectLive(live) {
  if (live && live.liveId) {
    console.log('Selected live:', live);
    openLive({ liveId: live.liveId, projectId: live.projectId });
  }
  showModal.value = false;
}

// 浏览器涡轮
async function openBrowser() {
  await useContext().invoke('openChildBrowser');
}

//算力点监控
function handleConsumption() {
  consumptionModal.value = true;
  title.value = userStore.name + ' 算力点变化趋势';
}

function handleClose() {
  showModal.value = false;
  consumptionModal.value = false;
}

async function checkAndShowLiveWindow() {
  const liveWindowExists = await useContext().invoke('checkLiveWindow');
  if (liveWindowExists) {
    await useContext().invoke('openChildLive');
    return true;
  }
  return false;
}
</script>

<style scoped lang="scss">
.home {
  position: relative;
  height: 80vh;
  width: 100%;

  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans",
  "Helvetica Neue",
  Helvetica,
  Arial,
  sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}

.card-group {
  display: flex;
  width: 95%;
  height: 360px;
  background-color: #edeef088;
  margin: 0 auto;
  justify-content: space-evenly;
  border-radius: 20px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  padding: 20px 240px;

  &:has(> div:hover)>div:not(:hover) {
    filter: blur(10px);
    transform: scale(0.9, 0.9);
  }

  >div {
    transition: all 0.4s ease;
  }
}


//最近直播样式
.live-tabs {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.recent-created,
.recent-started {
  width: 50%;
  border: 1px solid #ddd;
  padding: 10px;
  margin: 0px;
  height: 277px;
  border-radius: 5px;
}

.el-table {
  width: 100%;
  border-collapse: collapse;
}
</style>