<script setup>
import { useRoute } from 'vue-router'
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

import { ElMessage } from 'element-plus'

import { getTingwuTrans, updateTingwuTrans } from '@/api/tingwu/record'
import { getUrlByAudioPath } from '@/api/platform/audio'

import AudioPlayer from './components/AudioPlayer.vue'
import KeywordsSection from './components/KeywordsSection.vue'
import ConversationList from './components/ConversationList.vue'
import RightPanel from './components/RightPanel.vue'
import ExportDialog from './components/ExportDialog.vue'
import SearchBox from './components/SearchBox.vue'
import NotesOverview from './components/NotesOverview.vue'

const route = useRoute()
const recordId = route.params.vaId
const record = ref(null)
const audioUrl = ref("")

// 笔记面板状态
const isNotesPanelOpen = ref(false)
const audioPlayerRef = ref(null) // AudioPlayer组件引用
const currentPlayingIndex = ref(-1) // 当前播放的对话索引
const currentPlayingTime = ref(0) // 当前播放时间（毫秒）

// 搜索相关状态 (由 SearchBox 组件管理)
const searchBoxRef = ref(null) // SearchBox 组件引用
const notesOverviewRef = ref(null) // NotesOverview 组件引用
const searchKeyword = ref('') // 搜索关键词
const searchResults = ref([]) // 搜索结果
const currentSearchIndex = ref(-1) // 当前搜索结果索引

// 高亮状态
const highlightedIndex = ref(-1) // 当前高亮的对话索引
const highlightTimer = ref(null) // 高亮定时器

// 格式化对话内容 - 处理新的数据格式
const formattedConversations = computed(() => {
    if (!record.value || !record.value.paragraphs) return []
    try {
        const paragraphsData = JSON.parse(record.value.paragraphs)
        console.log('paragraphsData>>>>', paragraphsData)
        return paragraphsData
    } catch (error) {
        console.error('解析对话内容失败:', error)
        return []
    }
})

// 格式化时间显示（毫秒转换为 MM:SS 格式）
const formatTimestamp = (milliseconds) => {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 点击对话item跳转到对应音频位置
const handleConversationClick = (conversation) => {
    if (audioPlayerRef.value && conversation.startTime) {
        // 跳转到对话开始时间
        audioPlayerRef.value.seekToTime(conversation.startTime)

        // 提供视觉反馈
        console.log(`跳转到音频位置: ${formatTimestamp(conversation.startTime)}`)
    }
}

// 根据当前播放时间更新高亮的对话
const updateCurrentPlayingConversation = (currentTimeMs) => {
    const conversations = formattedConversations.value
    let foundIndex = -1

    for (let i = 0; i < conversations.length; i++) {
        const conversation = conversations[i]
        if (currentTimeMs >= conversation.startTime && currentTimeMs <= conversation.endTime) {
            foundIndex = i
            break
        }
    }

    const previousIndex = currentPlayingIndex.value
    currentPlayingIndex.value = foundIndex

    // 如果当前播放的对话改变了，自动滚动到该对话
    if (foundIndex !== -1 && foundIndex !== previousIndex) {
        scrollToConversation(foundIndex)
    }
}

// 滚动到指定的对话
const scrollToConversation = (index) => {
    nextTick(() => {
        const conversationElement = document.querySelector(`.conversation-item:nth-child(${index + 1})`)
        if (conversationElement) {
            conversationElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            })
        }
    })
}

// 监听音频播放时间变化
const handleAudioTimeUpdate = (currentTimeSeconds) => {
    const currentTimeMs = currentTimeSeconds * 1000
    currentPlayingTime.value = currentTimeMs // 更新当前播放时间
    updateCurrentPlayingConversation(currentTimeMs)
}

// 搜索相关方法
const handleSearchResultChange = (searchData) => {
    searchResults.value = searchData.results
    currentSearchIndex.value = searchData.currentIndex
    searchKeyword.value = searchData.keyword
}

const handleScrollToConversation = (conversationIndex) => {
    // 清除之前的高亮定时器
    if (highlightTimer.value) {
        clearTimeout(highlightTimer.value)
    }

    // 设置高亮状态
    highlightedIndex.value = conversationIndex

    // 滚动到对应位置
    scrollToConversation(conversationIndex)

    // 3秒后清除高亮状态
    highlightTimer.value = setTimeout(() => {
        highlightedIndex.value = -1
    }, 3000)
}

const handleSearchKeywordChange = (keyword) => {
    searchKeyword.value = keyword
}

// 智能分配新文本到词汇详情
const distributeTextToWordDetails = (newText, wordDetails) => {
    if (!wordDetails || !Array.isArray(wordDetails) || wordDetails.length === 0) {
        return wordDetails
    }

    console.log('文本分配调试信息:')
    console.log('原始词汇:', wordDetails.map(w => w.text))
    console.log('新文本:', newText)
    console.log('原始词汇数量:', wordDetails.length)

    // 计算原始文本的总长度（包含标点符号）
    const originalText = wordDetails.map(word => word.text).join('')
    const originalLength = originalText.length
    const newLength = newText.length

    console.log('原始文本长度:', originalLength, '新文本长度:', newLength)

    // 如果新文本为空，保持原有结构但清空内容
    if (!newText.trim()) {
        return wordDetails.map(word => ({ ...word, text: '' }))
    }

    // 使用简单的按比例分配策略，保持用户编辑后的完整文本
    let currentIndex = 0
    const updatedWordDetails = wordDetails.map((word, index) => {
        const originalWordLength = word.text.length

        // 计算这个词汇应该分配的新文本长度
        let newWordLength
        if (index === wordDetails.length - 1) {
            // 最后一个词汇，分配剩余的所有字符
            newWordLength = newText.length - currentIndex
        } else {
            // 按原始长度比例分配
            const ratio = originalWordLength / originalLength
            newWordLength = Math.max(1, Math.round(newText.length * ratio))
        }

        // 确保不超出边界
        newWordLength = Math.min(newWordLength, newText.length - currentIndex)

        if (newWordLength > 0 && currentIndex < newText.length) {
            // 直接使用新文本的片段，保持用户编辑后的完整内容（包括标点符号）
            const newWordText = newText.substring(currentIndex, currentIndex + newWordLength)
            currentIndex += newWordLength

            console.log(`词汇 ${index}: "${word.text}" -> "${newWordText}" (长度: ${originalWordLength} -> ${newWordLength})`)

            return {
                ...word,
                text: newWordText
            }
        }

        // 如果没有更多字符可分配，清空该词汇
        console.log(`词汇 ${index}: "${word.text}" -> "" (无更多字符可分配)`)
        return { ...word, text: '' }
    })

    return updatedWordDetails
}



// 处理文本更新
const handleTextUpdate = async (updateData) => {
    try {
        // 解析当前的 paragraphs 数据
        const paragraphsData = JSON.parse(record.value.paragraphs)

        // 验证数据结构
        if (!Array.isArray(paragraphsData)) {
            throw new Error('paragraphs 数据格式不正确，应该是数组')
        }

        // 验证索引有效性
        if (updateData.index < 0 || updateData.index >= paragraphsData.length) {
            throw new Error(`索引 ${updateData.index} 超出范围，数组长度为 ${paragraphsData.length}`)
        }

        // 更新指定索引的文本内容
        const targetItem = paragraphsData[updateData.index]
        if (!targetItem) {
            throw new Error(`无法找到索引 ${updateData.index} 对应的数据`)
        }

        if (!targetItem.hasOwnProperty('text')) {
            throw new Error(`索引 ${updateData.index} 的数据缺少 text 字段`)
        }

        // 更新段落文本
        targetItem.text = updateData.newText

        // 同步更新 wordDetails 中的词汇文本，保持 id 不变
        if (targetItem.wordDetails && Array.isArray(targetItem.wordDetails)) {
            console.log('原始 wordDetails:', targetItem.wordDetails)
            console.log('新文本:', updateData.newText)

            targetItem.wordDetails = distributeTextToWordDetails(updateData.newText, targetItem.wordDetails)

            console.log('更新后 wordDetails:', targetItem.wordDetails)
        }

        // 更新 record 中的 paragraphs 字段
        const updatedParagraphs = JSON.stringify(paragraphsData)
        record.value.paragraphs = updatedParagraphs

        // 保存到数据库
        await saveTextToDatabase(updatedParagraphs)

        // 如果有回调函数，调用成功回调
        if (updateData.resolve) {
            updateData.resolve()
        }
    } catch (error) {
        console.error('更新文本失败:', error)
        console.error('错误详情:', {
            message: error.message,
            stack: error.stack,
            updateData: updateData
        })

        // 如果有回调函数，调用失败回调
        if (updateData.reject) {
            updateData.reject(error)
        } else {
            ElMessage.error('保存失败，请重试')
        }
    }
}

// 保存文本更改到数据库
const saveTextToDatabase = async (updatedParagraphs) => {
    try {
        const updateData = {
            vaId: recordId,
            paragraphs: updatedParagraphs
        }

        const response = await updateTingwuTrans(updateData)

        if (response.code === 200) {
            console.log('数据库更新成功')
        } else {
            throw new Error(response.msg || '保存失败')
        }
    } catch (error) {
        console.error('保存到数据库失败:', error)
        throw error
    }
}

// 处理关键词更新
const handleKeywordsUpdate = async (updateData) => {
    try {
        const { recordId: id, tags, originalTags } = updateData

        console.log('更新关键词:', { id, tags, originalTags })

        // 准备更新数据
        const requestData = {
            vaId: id,
            tags: tags
        }

        // 调用API更新数据库
        const response = await updateTingwuTrans(requestData)

        if (response.code === 200) {
            // 更新本地数据
            record.value.tags = tags
            console.log('关键词更新成功')
        } else {
            throw new Error(response.msg || '保存关键词失败')
        }
    } catch (error) {
        console.error('保存关键词失败:', error)
        ElMessage.error('保存关键词失败，请重试')
        throw error
    }
}

// 处理关键词点击事件
const handleKeywordClick = (keyword) => {
    // 通过 SearchBox 组件设置搜索关键词并执行搜索
    if (searchBoxRef.value) {
        searchBoxRef.value.setSearchKeyword(keyword)
    }
}

// 处理笔记更新




// 处理笔记更新（委托给NotesOverview组件）
const handleNotesUpdate = async (updateData) => {
    if (notesOverviewRef.value) {
        await notesOverviewRef.value.handleNotesUpdate(updateData)
    }
}

// 处理record更新
const handleRecordUpdate = (updatedRecord) => {
    record.value = updatedRecord
}

// 处理笔记面板状态变化
const handleNotesPanelStateChange = (isOpen) => {
    isNotesPanelOpen.value = isOpen
}

onMounted(async () => {
    //根据id查询具体的数据
    const res = await getTingwuTrans(recordId)
    record.value = res.data
    console.log('res.data>>>>', res.data)
    const path = res.data.vaPath
    const url = await getUrlByAudioPath(path)
    console.log('url>>>>', url)
    audioUrl.value = url.data
    console.log('音频URL:', audioUrl.value)

})

// 清理定时器
onUnmounted(() => {
    if (highlightTimer.value) {
        clearTimeout(highlightTimer.value)
    }
})
</script>
<template>
    <div class="record-detail">
        <div class="record-container" v-if="record">
            <!-- 头部标题 -->
            <div class="page-header">
                <div class="header-left">
                    <h1 class="page-title">{{ record.vaName }}</h1>
                </div>
                <div class="header-right">

                    <!-- 搜索功能 -->
                    <SearchBox ref="searchBoxRef" :conversations="formattedConversations"
                        @search-result-change="handleSearchResultChange"
                        @scroll-to-conversation="handleScrollToConversation"
                        @search-keyword-change="handleSearchKeywordChange" />


                    <!-- 笔记概览功能 -->
                    <NotesOverview ref="notesOverviewRef" :conversations="formattedConversations" :record-id="recordId"
                        :record="record" @scroll-to-conversation="handleScrollToConversation"
                        @record-updated="handleRecordUpdate" @panel-state-change="handleNotesPanelStateChange" />

                    <!-- 导出功能 -->
                    <ExportDialog :record="record" :conversations="formattedConversations" />


                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 左侧原文区域 -->
                <div class="left-panel">
                    <!-- 关键词标签 - 固定在顶部 -->
                    <div class="keywords-container">
                        <KeywordsSection :tags="record.tags" :record-id="recordId" @update:tags="handleKeywordsUpdate"
                            @keyword-click="handleKeywordClick" />
                    </div>

                    <!-- 对话列表 - 可滚动区域 -->
                    <ConversationList :paragraphs="record.paragraphs" :current-playing-index="currentPlayingIndex"
                        :current-playing-time="currentPlayingTime" :search-keyword="searchKeyword"
                        :search-results="searchResults" :current-search-index="currentSearchIndex"
                        :highlighted-index="highlightedIndex" @conversation-click="handleConversationClick"
                        @text-updated="handleTextUpdate" @notes-updated="handleNotesUpdate" />
                </div>

                <!-- 右侧总结区域 -->
                <transition name="right-panel-fade">
                    <RightPanel v-if="!isNotesPanelOpen" :record="record" />
                </transition>
            </div>

            <AudioPlayer ref="audioPlayerRef" :url="audioUrl" :conversations="formattedConversations"
                @timeUpdate="handleAudioTimeUpdate" />
        </div>

        <div v-else class="empty-wrap">
            <el-empty description="暂无详情" />
        </div>
    </div>
</template>

<style scoped>
.record-detail {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 0;
}

.record-container {
    max-width: 100%;
    margin: 0;
    border-radius: 16px;
    /* 添加整体圆角 */
    overflow: hidden;
    /* 确保子元素不会超出圆角 */
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    /* 添加整体阴影 */
}

.page-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 20px 32px;
    border-bottom: 1px solid #e2e8f0;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 16px;
    /* 添加顶部圆角 */
    margin: 0 16px;
    /* 添加底部间距 */
}

.header-left {
    flex: 1;
}

.page-title {
    font-size: 22px;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    text-align: left;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-right {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 16px;
}



.main-content {
    display: flex;
    background: transparent;
    /* 改为透明，让子元素的背景显示 */
    height: calc(100vh);
    /* 固定高度而不是最小高度 */
    gap: 2px;
    border-radius: 16px 16px 0 0;
    overflow: hidden;
    padding: 0;
    /* 移除内边距 */
}

.left-panel {
    flex: 0 0 60%;
    max-width: 60%;
    border-right: 2px solid #f1f5f9;
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    /* 调整背景色，让白色区域更突出 */
    overflow-y: auto;
    /* 允许整个左侧面板滚动 */
    overflow-x: hidden;
    padding-bottom: 140px;
    /* 为音频播放器预留空间 */
    scroll-behavior: smooth;
    /* 平滑滚动 */

    /* 隐藏滚动条但保留滚动功能 */
    &::-webkit-scrollbar {
        width: 0px;
        background: transparent;
    }

    /* 兼容Firefox */
    scrollbar-width: none;

    /* 兼容IE */
    -ms-overflow-style: none;
}

.keywords-container {
    padding: 32px 32px 24px 32px;
    margin: 16px 16px 0 16px;
    /* 添加左右边距 */
    border-bottom: none;
    /* 移除底部边框，让区域看起来连接 */
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(12px);
    position: relative;
    border-radius: 16px;
    /* 添加顶部圆角 */
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    /* 增强阴影效果 */
    border: 1px solid rgba(241, 245, 249, 0.8);
    /* 添加边框 */
    border-bottom: none;
    /* 底部无边框，与下方区域连接 */
}

.empty-wrap {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 60px 0;
    max-width: 800px;
    margin: 0 auto;
}

/* 右侧面板动画效果 */
.right-panel-fade-enter-active,
.right-panel-fade-leave-active {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.right-panel-fade-enter-from {
    opacity: 0;
    transform: translateX(30px) scale(0.95);
}

.right-panel-fade-leave-to {
    opacity: 0;
    transform: translateX(30px) scale(0.95);
}

.right-panel-fade-enter-to,
.right-panel-fade-leave-from {
    opacity: 1;
    transform: translateX(0) scale(1);
}
</style>