<template>
  <div class="media-library">
    <!-- 顶部标签栏 -->
    <div class="header-tabs">
      <div class="tab-item" :class="{ active: activeTab === 'video' }" @click="switchTab('video')">
        <el-icon>
          <VideoPlay />
        </el-icon>
        <span>视频库(多媒体)</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'audio' }" @click="switchTab('audio')">
        <el-icon>
          <Microphone />
        </el-icon>
        <span>音频库</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'image' }" @click="switchTab('image')">
        <el-icon>
          <Picture />
        </el-icon>
        <span>图片素材库</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'font' }" @click="switchTab('font')">
        <el-icon>
          <EditPen />
        </el-icon>
        <span>字体素材库</span>
      </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar">
      <div class="left-actions">
        <el-button type="primary" @click="showUploader = true">
          <el-icon><Plus /></el-icon>
          上传媒资
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>

      <div class="right-filters">
        <!-- 状态筛选 - 直接使用API文档中的状态值 -->
        <el-select v-model="status" placeholder="状态" style="width: 120px" @change="handleFilterChange">
          <el-option label="全部" value="" />
          <el-option label="正常" value="3" />
          <el-option label="转码中" value="13" />
          <el-option label="转码失败" value="14" />
          <el-option label="上传中" value="10" />
          <el-option label="已上传" value="12" />
          <el-option label="上传失败" value="18" />
          <el-option label="审核中" value="15" />
          <el-option label="审核不通过" value="16" />
        </el-select>

        <!-- 日期筛选 -->
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
          @change="handleFilterChange"
        />

        <!-- 排序 -->
        <el-select v-model="sortBy" placeholder="排序" style="width: 120px" @change="handleFilterChange">
          <el-option label="创建时间升序" value="utcCreate:Asc" />
          <el-option label="创建时间降序" value="utcCreate:Desc" />
          <el-option label="标题升序" value="title:Asc" />
          <el-option label="标题降序" value="title:Desc" />
        </el-select>

        <!-- 重置按钮 -->
        <el-button @click="handleResetFilters">
          <el-icon><RefreshLeft /></el-icon>
          重置筛选
        </el-button>

        <!-- 搜索框 -->
        <el-input
          v-model="searchKeyword"
          placeholder="搜索素材..."
          style="width: 200px"
          @input="handleSearchInput"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 内容区 -->
    <div class="content-area">
      <component 
        :is="currentComponent" 
        :loading="loading"
        :media-list="materialList"
      />
    </div>
    
    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination 
        v-model:current-page="currentPage" 
        v-model:page-size="pageSize" 
        :page-sizes="[10, 20, 40, 80]"
        :total="total" 
        layout="total, sizes, prev, pager, next, jumper" 
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 上传组件 -->
    <MediaUploader
      v-model:visible="showUploader"
      :media-type="activeTab"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay, Microphone, Picture, EditPen, Plus, Refresh, Search, RefreshLeft } from '@element-plus/icons-vue'
import VideoLibrary from './components/VideoLibrary.vue'
import AudioLibrary from './components/AudioLibrary.vue'
import ImageLibrary from './components/ImageLibrary.vue'
import FontLibrary from './components/FontLibrary.vue'
import MediaUploader from './components/add/MediaUploader.vue'
import request from '@/utils/request'
import type { Material } from '../types/media'

// 媒体类型定义
type MediaType = 'video' | 'audio' | 'image' | 'font'
interface Props {
  visible: boolean
  mediaType: 'video' | 'audio' | 'image' | 'text'
  mode: 'project' | 'template'
  editorId: string
}

// 当前活跃的标签页
const activeTab = ref<MediaType>('video')

// 响应式数据
const props = defineProps<Props>()
const loading = ref(false)
const materialList = ref<Material[]>([])
const rawMaterialsMap = ref<Map<string, any>>(new Map())
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const sortBy = ref('utcCreate:Desc')
const showUploader = ref(false)

// 筛选条件
const status = ref('')
const dateRange = ref('')

// 动态组件映射
const componentMap = {
  video: VideoLibrary,
  audio: AudioLibrary,
  image: ImageLibrary,
  font: FontLibrary
}

// 当前组件
const currentComponent = computed(() => componentMap[activeTab.value as keyof typeof componentMap])

/**
 * 搜索媒资信息API
 */
async function SearchMedia(params: any): Promise<any> {
  try {
    const response = await request({
      url: '/open/openApiPost',
      method: 'post',
      data: {
        Action: 'SearchMedia',
        Version: '2020-11-09',
        ...params
      }
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// 切换标签页
const switchTab = (tab: MediaType) => {
  activeTab.value = tab
  currentPage.value = 1
  
  // 清空所有筛选条件
  searchKeyword.value = ''
  status.value = ''
  dateRange.value = ''
  sortBy.value = 'utcCreate:Desc'
  
  // 立即加载对应类型的素材
  loadMaterials()
}

// 重置数据
const resetData = () => {
  rawMaterialsMap.value.clear()
  materialList.value = []
  currentPage.value = 1
  searchKeyword.value = ''
  status.value = ''
  dateRange.value = ''
  total.value = 0
  sortBy.value = 'utcCreate:Desc'
}
// 加载素材列表
const loadMaterials = async () => {
  loading.value = true
  materialList.value = [] // 立即清空旧数据，避免显示上次查询结果
  try {
    const matchConditions: string[] = []

    // 基础媒体类型条件 - 根据当前激活的标签页筛选
    const mediaTypeMap: Record<MediaType, string> = {
      video: "video",
      audio: "audio", 
      image: "image",
      font: "text"
    }
    
    const currentMediaType = mediaTypeMap[activeTab.value]
    if (currentMediaType) {
      matchConditions.push(`mediaType == '${currentMediaType}'`)
    }

    // 状态筛选 - 只有当status不为空时才添加条件
    if (status.value && status.value !== '') {
      matchConditions.push(`status == '${status.value}'`)
    }

    // 搜索关键词过滤 - 使用正确的模糊匹配语法
    if (searchKeyword.value && searchKeyword.value.trim()) {
      matchConditions.push(`title = '${searchKeyword.value.trim()}'`)
    }

    // 日期范围过滤 - 使用正确的时间戳和范围查询语法
    if (dateRange.value && Array.isArray(dateRange.value) && dateRange.value.length === 2) {
      const [startDate, endDate] = dateRange.value
      const startTime = new Date(startDate).getTime()
      const endTime = new Date(endDate).getTime()
      matchConditions.push(`utcCreate >= ${startTime} and utcCreate <= ${endTime}`)
    }

    const params: any = {
      PageSize: pageSize.value,
      PageNo: currentPage.value,
      SortBy: sortBy.value
    }

    // 如果有筛选条件，直接拼接Match参数，不进行URL编码
    if (matchConditions.length > 0) {
      const matchString = matchConditions.join(' and ')
      params.Match = matchString
    }

    const response = await SearchMedia(params)

    if (response?.MediaInfoList) {
      const { MediaInfoList = [], Total = 0 } = response

      // 存储原始API数据映射
      rawMaterialsMap.value.clear()
      MediaInfoList.forEach((item: any) => {
        rawMaterialsMap.value.set(item.MediaId, item)
      })
      // 转换为显示数据
      const materials: Material[] = MediaInfoList.map((item: any) => ({
        mediaId: item.MediaId,
        title: item.MediaBasicInfo?.Title || item.FileInfoList?.[0]?.FileBasicInfo?.FileName || '未知素材',
        coverUrl: item.MediaBasicInfo?.CoverURL,
        duration: item.FileInfoList?.[0]?.FileBasicInfo?.Duration ? parseFloat(String(item.FileInfoList[0].FileBasicInfo.Duration)) : undefined,
        size: item.FileInfoList?.[0]?.FileBasicInfo?.FileSize ? parseInt(String(item.FileInfoList[0].FileBasicInfo.FileSize)) : undefined,
        createTime: item.MediaBasicInfo?.CreateTime || item.FileInfoList?.[0]?.FileBasicInfo?.CreateTime || '',
        mediaType: (item.MediaBasicInfo?.MediaType || activeTab.value || 'video') as 'video' | 'audio' | 'image' | 'text',
        FileUrl: item.FileInfoList?.[0].FileBasicInfo.FileUrl,
        Status: item.MediaBasicInfo.Status,
        Source: item.MediaBasicInfo.Source,
        BusinessType: item.MediaBasicInfo.BusinessType,
        Height: item.FileInfoList?.[0].FileBasicInfo?.Height,
        Width: item.FileInfoList?.[0].FileBasicInfo?.Width,
      }))

      materialList.value = materials
      total.value = Total
    } else {
      materialList.value = []
      total.value = 0
    }
  } catch (error) {
    ElMessage.error('加载素材失败')
    materialList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理刷新
const handleRefresh = () => {
  loadMaterials()
}

// 处理筛选条件变化
const handleFilterChange = () => {
  currentPage.value = 1
  loadMaterials()
}

// 处理搜索输入
const handleSearchInput = debounce(() => {
  currentPage.value = 1
  loadMaterials()
}, 500)

// 处理分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadMaterials()
}

// 处理每页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadMaterials()
}

// 处理上传成功
const handleUploadSuccess = (result: any) => {
  console.log('上传成功:', result)
  showUploader.value = false
  // 刷新当前页面数据
  loadMaterials()
}

// 处理上传错误
const handleUploadError = (error: any) => {
  console.error('上传失败:', error)
  // 不关闭上传对话框，让用户可以重试
}

// 重置筛选条件（保留当前标签页类型）
const handleResetFilters = () => {
  // 重置所有筛选条件，但保留当前激活的标签页
  searchKeyword.value = ''
  status.value = ''
  dateRange.value = ''
  sortBy.value = 'utcCreate:Desc'
  currentPage.value = 1
  
  // 重新加载数据
  loadMaterials()
}

// 防抖函数
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: number
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 监听 props 变化
watch(() => props.mediaType, (newType) => {
  if (newType) {
    resetData()
    loadMaterials()
  }
})

// 监听 props.visible 变化
watch(() => props.visible, (visible) => {
  if (visible) {
    resetData()
    loadMaterials()
  }
})

// 初始化
loadMaterials()
</script>

<style lang="scss" scoped>
.media-library {
  display: flex;
  flex-direction: column;
  height: 80vh;
  background-color: #f5f7fa;

}

.pagination-wrapper {
  display: flex;
  justify-content: center;
}

.filter-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e6e6e6;
  gap: 16px;

  .left-actions {
    display: flex;
    gap: 8px;
  }

  .right-filters {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
  }
}

.header-tabs {
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .tab-item {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
    position: relative;

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      font-weight: 400;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: #ffffff;
    }

    &.active {
      background-color: rgba(255, 255, 255, 0.15);
      color: #ffffff;
      border-bottom-color: #ffffff;
      font-weight: 500;

      span {
        font-weight: 500;
      }
    }

    &:not(:last-child) {
      border-right: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}

.content-area {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-tabs {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;

    .tab-item {
      flex-shrink: 0;
      padding: 12px 20px;
      font-size: 13px;

      .el-icon {
        margin-right: 6px;
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .header-tabs {
    .tab-item {
      padding: 10px 15px;
      font-size: 12px;

      span {
        display: none;
      }

      .el-icon {
        margin-right: 0;
        font-size: 18px;
      }
    }
  }
}
</style>
