/**
 * 时间轴吸附功能快速测试
 * 验证垂直和水平吸附功能是否正常工作
 */

import { calculateNewTimePosition } from './timelineDragUtils';
import { DEFAULT_TIME_SNAP_CONFIG } from './timelineHorizontalSnapUtils';
import type { DragState } from './timelineDragUtils';

/**
 * 模拟测试数据
 */
const mockTimeline = {
  VideoTracks: [
    {
      VideoTrackClips: [
        { TimelineIn: 0, TimelineOut: 5, Duration: 5 },
        { TimelineIn: 10, TimelineOut: 15, Duration: 5 }
      ]
    }
  ],
  AudioTracks: [
    {
      AudioTrackClips: [
        { TimelineIn: 2, TimelineOut: 8, Duration: 6 }
      ]
    }
  ],
  SubtitleTracks: []
};

const mockDragState: DragState = {
  isDragging: true,
  element: null,
  clip: { start: 6, duration: 3, name: 'Test Clip' },
  type: 'video',
  sourceTrackIndex: 0,
  clipIndex: 0,
  offsetX: 0,
  offsetY: 0,
  startX: 100,
  startY: 50,
  initialClipCenterY: 100,
  clipRect: undefined
};

/**
 * 测试水平吸附功能
 */
export function testHorizontalSnapping() {
  console.log('🧪 开始测试水平吸附功能...');
  
  // 模拟鼠标事件 - 移动到接近片段边界的位置
  const mockEvent = {
    clientX: 150, // 假设移动了50像素，对应1秒
    clientY: 100
  } as MouseEvent;
  
  const result = calculateNewTimePosition(
    mockEvent,
    mockDragState,
    50, // 50像素每秒
    mockTimeline,
    true, // 启用水平吸附
    DEFAULT_TIME_SNAP_CONFIG
  );
  
  console.log('📊 水平吸附测试结果:', {
    原始计算时间: 6 + (50 / 50), // 6 + 1 = 7秒
    最终吸附时间: result.time,
    是否发生吸附: result.snapResult?.isSnapped,
    吸附到的点: result.snapResult?.snapPoint
  });
  
  return result;
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log('🚀 开始运行时间轴吸附功能测试...');
  
  try {
    const horizontalTest = testHorizontalSnapping();
    
    console.log('✅ 所有测试完成!');
    console.log('📝 测试总结:');
    console.log('- 水平吸附功能:', horizontalTest.snapResult?.isSnapped ? '✅ 正常' : '⚠️  未吸附');
    console.log('- 导入模块:', '✅ 正常');
    console.log('- 配置加载:', '✅ 正常');
    
    return true;
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return false;
  }
}

// 手动运行测试函数
// 在浏览器控制台中调用 runAllTests() 来测试功能
