worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       80;
        server_name  localhost;
        charset utf-8;

        # 前端根应用路径部署
        location / {
            root   /home/<USER>/projects/ruoyi-ui;
            try_files $uri $uri/ /index.html;
            index  index.html index.htm;
        }

        # 前端子应用路径部署
        # 需要与env环境变量中的VITE_BASE_ROUTER路由基础路径保持一致
        # location /admin {
        #     alias   /home/<USER>/projects/ruoyi-ui;
        #     try_files $uri $uri/ /admin/index.html;
        #     index  index.html index.htm;
        # }
        
        # 后端接口代理
        location /prod-api/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://localhost:8080/;
        }

        # 后端websocket代理
        location /websocket/ {
            proxy_http_version 1.1; 
            proxy_set_header Upgrade $http_upgrade; 
            proxy_set_header Connection "upgrade"; 
            proxy_set_header Host $host; 
            proxy_read_timeout 30000s; 
            proxy_pass http://localhost:8080/websocket; 
        }

        # 后端在线接口文档的请求路径
        location /v3/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://localhost:8080/v3/;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}