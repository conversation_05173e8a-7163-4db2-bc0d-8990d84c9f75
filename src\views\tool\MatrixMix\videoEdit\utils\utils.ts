import axios from 'axios'

// 定义媒资基础信息接口
interface MediaBasicInfo {
    MediaId: string;
    MediaType: 'video' | 'audio' | 'image';
    Title?: string;
    CoverURL?: string;
    SpriteImages?: string;
}

// 定义文件基础信息接口
interface FileBasicInfo {
    FileName: string;
    FileUrl: string;
    Duration?: string | number;
    Width?: string | number;
    Height?: string | number;
    Bitrate?: string | number;
    FileType?: string;
}

// 定义文件信息接口
interface FileInfo {
    FileBasicInfo: FileBasicInfo;
}

// 定义媒资信息接口
interface MediaInfo {
    MediaBasicInfo: MediaBasicInfo;
    FileInfoList: FileInfo[];
    MediaId?: string;
}

// 定义雪碧图配置接口
interface SpriteConfig {
    num?: number;
    lines?: number;
    cols?: number;
    cellWidth?: number;
    cellHeight?: number;
}

// 定义视频素材接口
interface VideoMaterial {
    title: string;
    duration: number;
    width: number;
    height: number;
    bitrate: number;
    coverUrl?: string;
    spriteConfig?: SpriteConfig;
    sprites?: string[];
}

// 定义音频素材接口
interface AudioMaterial {
    title: string;
    duration: number;
    coverURL?: string;
}

// 定义图片素材接口
interface ImageMaterial {
    title: string;
    coverUrl?: string;
    width: number;
    height: number;
}

// 定义转换后的素材结果接口
interface TransformedMaterial {
    mediaId: string;
    mediaType: 'video' | 'audio' | 'image';
    mediaOrigin?: 'public' | 'private'; // 添加媒资来源标识
    video?: VideoMaterial;
    audio?: AudioMaterial;
    image?: ImageMaterial;
}

/**
 * 将服务端的素材信息转换成 WebSDK 需要的格式
 */
export function transMediaList(data: MediaInfo | MediaInfo[] | null | undefined, isPublic?: boolean): TransformedMaterial[] {
    if (!data) return []

    if (Array.isArray(data)) {
        return data.map((item: MediaInfo): TransformedMaterial => {
            const basicInfo = item.MediaBasicInfo
            const fileBasicInfo = item.FileInfoList?.[0]?.FileBasicInfo
            
            if (!basicInfo || !fileBasicInfo) {
                console.warn('媒资信息不完整:', item);
                return {
                    mediaId: basicInfo?.MediaId || 'unknown',
                    mediaType: 'image' as const
                } as TransformedMaterial;
            }
            
            const mediaId = basicInfo.MediaId
            const mediaType = basicInfo.MediaType.toLowerCase() as 'video' | 'audio' | 'image'
            
            const result: TransformedMaterial = {
                mediaId,
                mediaType,
                mediaOrigin: isPublic ? 'public' : 'private' // 设置媒资来源
            }

            if (mediaType === 'video') {
                result.video = {
                    title: fileBasicInfo.FileName || basicInfo.Title || mediaId,
                    duration: Number(fileBasicInfo.Duration) || 0,
                    // 源视频的宽高、码率等数据，用于推荐合成数据，不传入或是0时无推荐数据
                    width: Number(fileBasicInfo.Width) || 0,
                    height: Number(fileBasicInfo.Height) || 0,
                    bitrate: Number(fileBasicInfo.Bitrate) || 0,
                    coverUrl: basicInfo.CoverURL || ''
                }
                
                const spriteImages = basicInfo.SpriteImages
                if (spriteImages) {
                    try {
                        const spriteArr = typeof spriteImages === 'string' ? JSON.parse(spriteImages) : spriteImages
                        if (Array.isArray(spriteArr) && spriteArr.length > 0) {
                            const sprite = spriteArr[0]
                            const config = typeof sprite.Config === 'string' ? JSON.parse(sprite.Config) : sprite.Config
                            result.video.spriteConfig = {
                                num: config?.Num,
                                lines: config?.SpriteSnapshotConfig?.Lines,
                                cols: config?.SpriteSnapshotConfig?.Columns,
                                cellWidth: config?.SpriteSnapshotConfig?.CellWidth,
                                cellHeight: config?.SpriteSnapshotConfig?.CellHeight
                            }
                            result.video.sprites = sprite.SnapshotUrlList || []
                        }
                    } catch (e) {
                        console.log('解析雪碧图失败:', e)
                    }
                }
            } else if (mediaType === 'audio') {
                result.audio = {
                    title: fileBasicInfo.FileName || basicInfo.Title || mediaId,
                    duration: Number(fileBasicInfo.Duration) || 0,
                    coverURL: basicInfo.CoverURL || '' // 您可以给音频文件一个默认的封面图
                }
            } else if (mediaType === 'image') {
                result.image = {
                    title: fileBasicInfo.FileName || basicInfo.Title || mediaId,
                    coverUrl: basicInfo.CoverURL || fileBasicInfo.FileUrl || '',
                    // 图片的宽高等数据，用于推荐合成数据，不传入或是0时无推荐数据
                    width: Number(fileBasicInfo.Width) || 0,
                    height: Number(fileBasicInfo.Height) || 0
                }
            }

            return result
        }).filter((item): item is TransformedMaterial => item !== null && item.mediaId !== 'unknown')
    } else {
        return transMediaList([data])
    }
}


export function formatTime(s: number): string {
    const minutes = Math.floor(s / 60)
    const seconds = s - minutes * 60
    return `${minutes < 10 ? '0' : ''}${minutes}:${seconds < 10 ? '0' : ''}${Math.floor(seconds)}`
}


// 轮询
export async function poll<T>(
    fn: () => Promise<T>, 
    fnCondition: (result: T) => boolean, 
    ms: number, 
    timeout: number = 1000 * 60 * 30
): Promise<{ timeout: boolean; result: T }> {
    const startTime = Date.now();
    await new Promise((resolve) => {
        setTimeout(resolve, ms);
    });
    let result = await fn();
    while (fnCondition(result)) {
        const currentTime = Date.now();
        if (currentTime - startTime > timeout - ms) {
            return {
                timeout: true,
                result,
            };
        }
        // eslint-disable-next-line no-await-in-loop
        await new Promise((resolve) => {
            setTimeout(resolve, ms);
        });
        // eslint-disable-next-line no-await-in-loop
        result = await fn();
    }
    return {
        timeout: false,
        result,
    };
}


export function pageData<T>(items: T[], pageSize: number) {
    const total = items.length;
    const pageCount = Math.ceil(total / pageSize);
    return {
        total,
        pageSize,
        pageCount,
        getData: (page: number): T[] => {
            if (page > pageCount) {
                page = pageCount;
            }
            if (page < 1) {
                page = 1;
            }
            const pageNo = page - 1;
            const startIndex = pageNo * pageSize;
            let endIndex = startIndex + pageSize;
            if (endIndex > total) {
                endIndex = total;
            }
            const data = items.slice(startIndex, endIndex);
            return data;
        },
    };
}

function camelCaseFromPascalCase(str: string): string {
    return str[0].toLowerCase() + str.slice(1);
}

export function objectKeyPascalCaseToCamelCase(obj: any): any {
    if (typeof obj !== 'object' || obj === null) return obj;
    if (Array.isArray(obj)) return obj.map(objectKeyPascalCaseToCamelCase);
    const res: Record<string, any> = {};
    for (const key of Object.keys(obj)) {
        const value = objectKeyPascalCaseToCamelCase(obj[key]);
        res[camelCaseFromPascalCase(key)] = value;
    }
    return res;
}