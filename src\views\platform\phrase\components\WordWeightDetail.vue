<template>
    <el-dialog 
        title="词表权重详情" 
        v-model="dialogVisible" 
        width="600px" 
        append-to-body
        @close="handleClose"
    >
        <div class="weight-detail-container">
            <div class="weight-detail-header">
                <span class="phrase-name">{{ phraseDetail.name }}</span>
                <span class="word-count">共 {{ wordWeights.length }} 个词汇</span>
            </div>
            <div class="weight-detail-content">
                <div class="weight-grid">
                    <div v-for="(item, index) in wordWeights" :key="index" class="weight-item">
                        <div class="word-info">
                            <span class="word-text">{{ item.word }}</span>
                            <el-tag :type="getTagType(item.weight)" size="small" class="weight-value">
                                权重: {{ item.weight }}
                            </el-tag>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">关 闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup name="WordWeightDetail">
// 定义组件属性
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    phraseDetail: {
        type: Object,
        default: () => ({})
    },
    wordWeights: {
        type: Array,
        default: () => []
    }
})

// 定义事件
const emit = defineEmits(['update:visible'])

// 计算属性：控制弹窗显示状态
const dialogVisible = computed({
    get() {
        return props.visible
    },
    set(value) {
        emit('update:visible', value)
    }
})

// 关闭弹窗
function handleClose() {
    dialogVisible.value = false
}

// 根据权重值获取标签类型
function getTagType(weight) {
    if (weight > 0) {
        return 'success' // 绿色 - 增大识别概率
    } else if (weight === -6) {
        return 'danger' // 红色 - 尽量不识别
    } else if (weight < 0) {
        return 'warning' // 橙色 - 负权重
    } else {
        return '' // 默认颜色 - 权重为0
    }
}
</script>

<style scoped>
/* 词表权重详情弹窗样式 */
.weight-detail-container {
    padding: 10px 0;
}

.weight-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
}

.phrase-name {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
}

.word-count {
    font-size: 14px;
    color: #909399;
}

.weight-detail-content {
    max-height: 400px;
    overflow-y: auto;
}

.weight-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
}

.weight-item {
    padding: 12px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    background-color: #fafafa;
    transition: all 0.3s ease;
}

.weight-item:hover {
    border-color: #409eff;
    background-color: #f0f9ff;
}

.word-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.word-text {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
}

.weight-value {
    font-size: 12px;
}

.weight-description {
    font-size: 12px;
    color: #606266;
    line-height: 1.4;
}
</style>
