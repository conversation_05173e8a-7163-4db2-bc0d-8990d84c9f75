<template>
  <!-- 任务列表面板 -->
  <div class="task-history" :class="{ 'task-history--open': isOpen }">
    <div class="task-history__header">
      <h3>转换任务</h3>
      <el-button link @click="close">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    
    <div class="task-history__content" ref="contentRef" @scroll="handleScroll">
      <div class="task-history__list">
        <TransitionGroup 
          name="task-item"
          tag="div"
          @before-enter="onBeforeEnter"
          @enter="onEnter"
          @leave="onLeave"
        >
          <div 
            v-for="task in tasks" 
            :key="task.taskId"
            class="task-item"
            :class="[
              `task-item--${task.taskStatus}`,
              { 'task-item--new': task.isNew },
              { 'task-item--selected': selectedTask?.taskId === task.taskId }
            ]"
            @click="handleTaskClick(task)"
          >
            <div class="task-item__icon">
              <el-icon v-if="task.taskStatus == 2"><Check /></el-icon>
              <el-icon v-else-if="task.taskStatus == 3"><Close /></el-icon>
              <el-icon v-else-if="task.taskStatus == 1" class="is-loading"><Loading /></el-icon>
              <el-icon v-else><Timer /></el-icon>
            </div>
            
            <div class="task-item__content">
              <div class="task-item__title">
                {{ getTaskTitle(task) }}
              </div>
              <div class="task-item__subtitle">
                {{ handleSound(task.modelName) }}
              </div>
              <div class="task-item__time">
                {{ parseTime(task.createTime) }}
              </div>
            </div>

            <div class="task-item__status">
              <el-tag 
                :type="task.taskStatus == 2 ? 'success' : task.taskStatus == 3 ? 'danger' : task.taskStatus == 1 ? 'warning' : 'info'"
                size="small"
              >
                {{ platform_task_task_status.find(item => item.value === task.taskStatus + '')?.label || '未知状态' }}
              </el-tag>
            </div>
          </div>
        </TransitionGroup>
      </div>
      
      <div v-if="loading" class="loading-more">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      
      <div v-if="!loading && !hasMore" class="no-more">
        没有更多数据了
      </div>
    </div>
  </div>

  <!-- 文案详情面板 -->
  <div class="task-detail" :class="{ 'task-detail--open': selectedTask }">
    <div class="task-detail__header">
      <h3>任务详情</h3>
      <el-button link @click="closeDetail">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    
    <div class="task-detail__content">
      <div v-if="selectedTask" class="task-detail__info">
        <div class="detail-item">
          <span class="label">创建时间：</span>
          <span class="value">{{ parseTime(selectedTask.createTime) }}</span>
        </div>
        <div class="detail-item">
          <span class="label">任务状态：</span>
          <el-tag 
            :type="selectedTask.taskStatus == 2 ? 'success' : selectedTask.taskStatus == 3 ? 'danger' : selectedTask.taskStatus == 1 ? 'warning' : 'info'"
            size="small"
          >
            {{ platform_task_task_status.find(item => item.value === selectedTask.taskStatus + '')?.label || '未知状态' }}
          </el-tag>
        </div>
        <div class="detail-item">
          <span class="label">声音模型：</span>
          <span class="value">{{ handleSound(selectedTask.modelName) }}</span>
        </div>
      </div>
      
      <div class="task-detail__articles">
        <h4>包含文案</h4>
        <div v-loading="articlesLoading" class="articles-list">
          <div v-for="(content, id) in articleContents" :key="id" class="article-item">
            <div class="article-content">{{ content }}</div>
            <div class="article-id">#{{ id }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'
import { listTask } from '@/api/platform/task'
import { getMutipleArticle } from '@/api/platform/article'
import useUserStore from '@/store/modules/user'
import { listSound } from '@/api/platform/sound'
import { useDict } from '@/utils/dict'
import { Timer, Loading, Close } from '@element-plus/icons-vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:isOpen', 'update:unreadCount'])
const { platform_task_task_status } = useDict('platform_task_task_status')

const userStore = useUserStore()
const tasks = ref([])
const sounds = ref([])
const contentRef = ref(null)

// 分页相关
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)
const hasMore = ref(true)

const selectedTask = ref(null)
const articleContents = ref({})
const articlesLoading = ref(false)

// 添加定时器引用
const timer = ref(null)

// 添加已读任务ID存储
const readTaskIds = ref(new Set())

// 获取声音列表
const getSounds = async () => {
  try {
    const res = await listSound({ soundStatus: '2' })
    if (res.total > 0) {
      sounds.value = res.rows.map(item => ({
        label: item.soundName,
        value: item.soundId
      }))
    }
  } catch (error) {
    console.error(error)
  }
}

// 处理声音显示
const handleSound = (id) => {
  const sound = sounds.value.find(s => s.value == id)
  return sound ? sound.label : '未知声音'
}

// 获取任务标题
const getTaskTitle = (task) => {
  try {
    // 如果有 messageTitle，优先使用
    if (task.messageTitle) {
      return task.messageTitle
    }

    // 解析 taskContent 获取文本内容
    if (task.taskContent) {
      const content = JSON.parse(task.taskContent)
      if (content.configs && content.configs.tts && content.configs.tts.text) {
        const textArray = content.configs.tts.text
        if (Array.isArray(textArray) && textArray.length > 0) {
          // 获取第一个文本对象的值
          const firstText = textArray[0]
          const textValue = Object.values(firstText)[0]
          if (textValue && textValue.length > 0) {
            // 限制显示长度，避免标题过长
            return textValue.length > 30 ? textValue.substring(0, 30) + '...' : textValue
          }
        }
      }
    }

    // 如果都没有，使用模型名称 + 创建时间
    const modelInfo = task.modelName ? `模型${task.modelName}` : ''
    const timeInfo = task.createTime ? task.createTime.split(' ')[1].substring(0, 5) : ''
    return `${modelInfo} ${timeInfo}`.trim() || '文案转音频'
  } catch (error) {
    console.error('解析任务标题失败:', error)
    return '文案转音频'
  }
}

// 获取任务列表
const getTasks = async (isLoadMore = false) => {
  if (loading.value || (!isLoadMore && !hasMore.value)) return
  
  loading.value = true
  try {
    const res = await listTask({ 
      createBy: userStore.name,
      type: 1,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    })
    
    total.value = res.total
    hasMore.value = pageNum.value * pageSize.value < total.value
    
    if (isLoadMore) {
      tasks.value.push(...res.rows.map(task => ({
        ...task,
        isNew: false
      })))
    } else {
      tasks.value = res.rows.map(task => ({
        ...task,
        isNew: false
      }))
    }
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 滚动加载
const handleScroll = async (e) => {
  const { scrollHeight, scrollTop, clientHeight } = e.target
  // 距离底部 20px 时加载更多
  if (scrollHeight - scrollTop - clientHeight <= 20 && !loading.value && hasMore.value) {
    pageNum.value++
    await getTasks(true)
  }
}

// 关闭详情面板
const closeDetail = () => {
  selectedTask.value = null
  articleContents.value = {}
}

// 关闭面板
const close = () => {
  emit('update:isOpen', false)
  // 同时关闭详情面板
  closeDetail()
}

// 动画处理
const onBeforeEnter = (el) => {
  el.style.opacity = 0
  el.style.transform = 'translateX(30px)'
}

const onEnter = (el, done) => {
  const delay = el.dataset.index * 150
  setTimeout(() => {
    el.style.transition = 'all 0.3s ease'
    el.style.opacity = 1
    el.style.transform = 'translateX(0)'
    done()
  }, delay)
}

const onLeave = (el, done) => {
  el.style.opacity = 0
  el.style.transform = 'translateX(30px)'
  setTimeout(done, 300)
}

// 添加新任务时的动画
const addNewTask = (task) => {
  // 检查是否已存在相同的任务
  const existingTask = tasks.value.find(t => t.taskId === task.taskId)
  if (existingTask) {
    // 如果任务已存在，只更新状态
    Object.assign(existingTask, task)
    return
  }
  
  // 添加新任务
  task.isNew = true
  tasks.value.unshift(task)
  setTimeout(() => {
    task.isNew = false
  }, 3000)
}

// 处理任务点击
const handleTaskClick = async (task) => {
  if (selectedTask.value?.taskId === task.taskId) {
    selectedTask.value = null
    articleContents.value = {}
    return
  }
  
  selectedTask.value = task
  articlesLoading.value = true
  
  // 如果是已完成的任务，标记为已读
  if (task.taskStatus === 2) {
    readTaskIds.value.add(task.taskId)
    // 存储到localStorage
    localStorage.setItem('readTaskIds', JSON.stringify([...readTaskIds.value]))
    // 通知父组件更新未读数量
    emit('update:unreadCount', getUnreadCount())
  }
  
  try {
    // 解析任务内容获取文案ID
    const taskContent = JSON.parse(task.taskContent)
    const articleIds = []
    if (taskContent.configs?.tts?.text) {
      taskContent.configs.tts.text.forEach(item => {
        Object.keys(item).forEach(id => {
          articleIds.push(id)
        })
      })
    }
    
    if (articleIds.length > 0) {
      const res = await getMutipleArticle(articleIds)
      articleContents.value = res.data
    }
  } catch (error) {
    console.error('获取文案内容失败:', error)
  } finally {
    articlesLoading.value = false
  }
}

// 计算未读已完成任务数量
const getUnreadCount = () => {
  // 只计算状态为已完成(2)且未在 readTaskIds 中的任务
  return tasks.value.filter(task => 
    task.taskStatus === 2 && !readTaskIds.value.has(task.taskId)
  ).length
}

// 初始化
onMounted(async () => {
  const savedIds = localStorage.getItem('readTaskIds')
  if (savedIds) {
    readTaskIds.value = new Set(JSON.parse(savedIds))
  }
  await getSounds()
  await getTasks()
})

// 更新任务状态
const updateTasksStatus = async () => {
  try {
    const res = await listTask({ 
      createBy: userStore.name,
      type: 1,
      pageNum: 1,
      pageSize: tasks.value.length || 10
    })
    
    if (res.rows?.length) {
      // 更新任务状态时,检查是否有新完成的任务
      const prevCompletedTasks = tasks.value.filter(t => t.taskStatus === 2).map(t => t.taskId)
      
      tasks.value = tasks.value.map(task => {
        const updatedTask = res.rows.find(t => t.taskId === task.taskId)
        return updatedTask ? { ...task, ...updatedTask } : task
      })
      
      // 获取更新后的已完成任务
      const newCompletedTasks = tasks.value.filter(t => t.taskStatus === 2).map(t => t.taskId)
      
      // 不管是否有新完成的任务,都重新计算未读数
      // 因为任务状态可能发生变化(比如从处理中变为已完成)
      emit('update:unreadCount', getUnreadCount())
    }
  } catch (error) {
    console.error('更新任务状态失败:', error)
  }
}

// 启动定时器
const startTimer = () => {
  if (timer.value) return
  timer.value = setInterval(updateTasksStatus, 1500)
}

// 停止定时器
const stopTimer = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

// 监听面板打开状态
watch(() => props.isOpen, (newVal) => {
  if (newVal) {
    getTasks()
    startTimer()
  } else {
    stopTimer()
    selectedTask.value = null
    articleContents.value = {}
  }
})

// 添加点击外部关闭功能
const handleClickOutside = (e) => {
  // 如果面板没有打开，不需要处理点击事件
  if (!props.isOpen) return
  
  const taskHistory = document.querySelector('.task-history')
  const taskDetail = document.querySelector('.task-detail')
  const taskHistoryBtn = document.querySelector('.task-history-btn')
  
  // 排除任务列表按钮、任务列表面板和详情面板的点击
  if (!taskHistory?.contains(e.target) && 
      !taskDetail?.contains(e.target) && 
      !taskHistoryBtn?.contains(e.target)) {
    close()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
  stopTimer()
})

defineExpose({
  addNewTask,
  getUnreadCount
})
</script>

<style lang="scss" scoped>
.task-history {
  position: fixed;
  top: 60px;
  right: -400px;
  width: 380px;
  height: calc(100vh - 80px);
  background: #fff;
  border-radius: 16px;
  box-shadow: 
    0 6px 16px -8px rgba(0, 0, 0, 0.08),
    0 9px 28px 0 rgba(0, 0, 0, 0.05),
    0 12px 48px 16px rgba(0, 0, 0, 0.03);
  z-index: 2000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  opacity: 0;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  
  &--open {
    right: 20px;
    opacity: 1;
    transform: translateX(0);
  }
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      letter-spacing: 0.5px;
    }
  }
  
  &__content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: #F8F9FA;
    display: flex;
    flex-direction: column;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #dcdfe6;
      border-radius: 3px;
      
      &:hover {
        background: #c0c4cc;
      }
    }
  }
  
  &__list {
    flex: 1;
    padding-bottom: 16px;
  }
}

.task-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 
      0 4px 12px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.06);
    border-color: #e4e7ed;
  }
  
  &--new {
    animation: task-new 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  }
  
  &--selected {
    border-color: #409eff;
    box-shadow: 0 0 0 1px #409eff;
    
    &:hover {
      border-color: #409eff;
    }
  }
  
  &__icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    
    .el-icon {
      font-size: 18px;
      transition: transform 0.3s ease;
      
      &.is-loading {
        animation: loading-rotate 2s linear infinite;
      }
    }
  }
  
  &__content {
    flex: 1;
    min-width: 0;
  }
  
  &__title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 6px;
    line-height: 1.4;
  }
  
  &__subtitle {
    font-size: 13px;
    color: #606266;
    margin-bottom: 6px;
    line-height: 1.4;
  }
  
  &__time {
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
  }
  
  &__status {
    margin-left: 12px;
    
    :deep(.el-tag) {
      border-radius: 4px;
      font-weight: 500;
      padding: 4px 8px;
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
    }
    
    :deep(.el-tag--success) {
      background-color: #67c23a;
      color: #fff;
    }
    
    :deep(.el-tag--warning) {
      background-color: #e6a23c;
      color: #fff;
    }
    
    :deep(.el-tag--danger) {
      background-color: #f56c6c;
      color: #fff;
    }
    
    :deep(.el-tag--info) {
      background-color: #909399;
      color: #fff;
    }
  }
  
  // 任务状态样式
  &--0 {
    .task-item__icon {
      color: #909399;
      background: #fff;
      border-color: #dcdfe6;
    }
  }
  
  &--1 {
    .task-item__icon {
      color: #e6a23c;
      background: #fff;
      border-color: #f5dab1;
      animation: pulse 1.5s ease-in-out infinite;
    }
  }
  
  &--2 {
    .task-item__icon {
      color: #67c23a;
      background: #fff;
      border-color: #b3e19d;
      animation: success-bounce 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    }
  }
  
  &--3 {
    .task-item__icon {
      color: #f56c6c;
      background: #fff;
      border-color: #fab6b6;
      animation: error-shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97);
    }
  }
}

.loading-more,
.no-more {
  text-align: center;
  padding: 12px 0;
  color: #909399;
  font-size: 13px;
  background: #F8F9FA;
  margin: 0 -16px;
  padding: 12px 16px;
  
  .el-icon {
    margin-right: 6px;
    font-size: 14px;
  }
}

@keyframes loading-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes task-new {
  0% {
    opacity: 0;
    transform: translateX(-30px) scale(0.9);
  }
  50% {
    transform: translateX(5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes success-bounce {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes error-shake {
  0%, 100% {
    transform: translateX(0);
  }
  20%, 60% {
    transform: translateX(-4px);
  }
  40%, 80% {
    transform: translateX(4px);
  }
}

.task-item-enter-active,
.task-item-leave-active {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.task-item-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.task-item-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.task-detail {
  position: fixed;
  top: 60px;
  right: 420px;
  width: 380px;
  height: calc(100vh - 80px);
  background: #fff;
  border-radius: 16px;
  box-shadow: 
    0 6px 16px -8px rgba(0, 0, 0, 0.08),
    0 9px 28px 0 rgba(0, 0, 0, 0.05),
    0 12px 48px 16px rgba(0, 0, 0, 0.03);
  z-index: 1999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  transform-origin: right center;
  opacity: 0;
  display: flex;
  flex-direction: column;
  pointer-events: none;
  transform: translateX(100%);
  
  &--open {
    transform: translateX(0);
    opacity: 1;
    pointer-events: auto;
  }
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      letter-spacing: 0.5px;
    }
  }
  
  &__content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: #F8F9FA;
    display: flex;
    flex-direction: column;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #dcdfe6;
      border-radius: 3px;
      
      &:hover {
        background: #c0c4cc;
      }
    }
  }
  
  &__info {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    
    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        color: #606266;
        font-size: 13px;
        margin-right: 8px;
      }
      
      .value {
        color: #303133;
        font-size: 13px;
      }
    }
  }
  
  &__articles {
    h4 {
      margin: 0 0 12px;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }
    
    .articles-list {
      min-height: 100px;
    }
    
    .article-item {
      background: #fff;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
      border: 1px solid #f0f0f0;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .article-content {
        font-size: 14px;
        color: #303133;
        line-height: 1.6;
        margin-bottom: 8px;
        white-space: pre-wrap;
        word-break: break-all;
      }
      
      .article-id {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}
</style>