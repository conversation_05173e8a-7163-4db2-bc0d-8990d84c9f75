import request from '@/utils/request'

// 查询词表信息列表
export function listPhrase(query) {
  return request({
    url: '/tingwu/phrase/list',
    method: 'get',
    params: query
  })
}

// 查询词表信息详细
export function getPhraseByphraseId(phraseId) {
  return request({
    url: '/tingwu/phrase/' + phraseId,
    method: 'get'
  })
}

// 新增词表信息
export function addPhrase(data) {
  return request({
    url: '/tingwu/phrase',
    method: 'post',
    data: data
  })
}

// 修改词表信息
export function updatePhrase(data) {
  return request({
    url: '/tingwu/phrase',
    method: 'put',
    data: data
  })
}

// 删除词表信息
export function delPhraseByPhraseIds(phraseIds) {
  return request({
    url: '/tingwu/phrase/' + phraseIds,
    method: 'delete'
  })
}
