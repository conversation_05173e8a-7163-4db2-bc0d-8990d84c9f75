<template>
  <div class="upload-container">
    <!-- 上传组件区域 -->
    <div v-if="showUploader" class="uploader-wrapper">
      <div class="upload-content">
        <div class="upload-icon">
          <el-icon :size="48"><video-play /></el-icon>
        </div>
        <div class="upload-text">
          <div class="upload-title">上传视频素材</div>
          <el-button type="primary" class="upload-button" @click="handleClickUpload">点击上传</el-button>
        </div>
        <div class="upload-tip">
          <el-icon><info-filled /></el-icon>
          <span><strong>只能上传视频文件</strong>，仅支持MP4、MOV、AVI格式，大小不超过500MB</span>
        </div>
      </div>
    </div>
    
    <!-- 文件信息显示区域 -->
    <div v-if="!showUploader" class="file-preview">
      <div class="file-preview-inner">
        <div class="file-preview-icon">
          <el-icon :size="40"><video-play /></el-icon>
        </div>
        
        <div class="file-info">
          <div class="file-name-wrapper">
            <div class="file-name-container">
              <span class="file-name">{{ displayFileName }}</span>
              <span v-if="getFileSize" class="file-size">({{ getFileSize }})</span>
            </div>
            <el-tag :type="isNewFile ? 'success' : 'info'" size="small">
              {{ isNewFile ? '待上传' : '原文件' }}
            </el-tag>
          </div>
          <div class="file-actions">
            <el-button type="primary" link @click="handleRemove">
              <el-icon><edit /></el-icon>更换文件
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 上传进度条 -->
      <div v-if="isUploading" class="upload-progress">
        <el-progress :percentage="uploadProgress" :stroke-width="8" :status="uploadProgress >= 100 ? 'success' : ''">
          <template #default>
            <span>{{ uploadProgress < 100 ? '正在上传: ' + uploadProgress + '%' : '上传完成' }}</span>
          </template>
        </el-progress>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onBeforeUnmount } from 'vue';
import { ElMessage } from 'element-plus';

// 只允许视频格式
const acceptFileTypes = ".mp4,.mov,.avi";

const props = defineProps({
  modelValue: {
    type: [File, String, null],
    default: null
  },
  existingVideoUrl: {
    type: String,
    default: ''
  },
  // 添加是否显示分片信息的属性
  showChunkInfo: {
    type: Boolean,
    default: false
  },
  // 添加分片大小属性
  chunkSize: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['update:modelValue']);
const fileList = ref([]);
const showUploader = ref(true);
const isUploading = ref(false);
const uploadProgress = ref(0);
let controller = null;

// 计算是否为新上传的文件
const isNewFile = computed(() => fileList.value.length > 0);

// 计算当前显示的文件名
const displayFileName = computed(() => {
  if (fileList.value.length > 0) return fileList.value[0].name;
  return props.existingVideoUrl ? props.existingVideoUrl.split('/').pop() : '';
});

// 获取文件大小显示
const getFileSize = computed(() => {
  if (fileList.value.length > 0 && fileList.value[0].raw) {
    return formatFileSize(fileList.value[0].raw.size);
  }
  return null;
});

// 格式化文件大小
function formatFileSize(size) {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / 1024 / 1024).toFixed(2) + ' MB';
  } else {
    return (size / 1024 / 1024 / 1024).toFixed(2) + ' GB';
  }
}

// 设置上传进度
function setUploadProgress(progress) {
  uploadProgress.value = progress;
  isUploading.value = true;
  
  if (progress >= 100) {
    setTimeout(() => isUploading.value = false, 1000);
  }
}

// 重置上传状态
function resetUpload() {
  uploadProgress.value = 0;
  isUploading.value = false;
  if (controller) {
    controller.abort();
    controller = null;
  }
}

// 处理选择的文件
function handleSelectedFile(file) {
  // 检查文件大小
  if (file.size / 1024 / 1024 >= 500) {
    ElMessage.error('文件大小不能超过 500MB!');
    return;
  }
  
  fileList.value = [{name: file.name, raw: file}];
  showUploader.value = false;
  emit('update:modelValue', file);
}

// 点击上传按钮处理
function handleClickUpload() {
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = acceptFileTypes;
  fileInput.style.display = 'none';
  
  fileInput.addEventListener('change', (e) => {
    const file = e.target.files[0];
    if (file) handleSelectedFile(file);
    document.body.removeChild(fileInput);
  });
  
  document.body.appendChild(fileInput);
  fileInput.click();
}

// 移除文件
function handleRemove() {
  resetUpload();
  fileList.value = [];
  showUploader.value = true;
  emit('update:modelValue', null);
}

// 重置组件状态
function resetComponent() {
  resetUpload();
  fileList.value = [];
  showUploader.value = true;
  emit('update:modelValue', null);
}

// 获取AbortController用于控制上传
function getAbortController() {
  if (controller) controller.abort();
  controller = new AbortController();
  return controller;
}

// 暴露方法给父组件
defineExpose({
  setUploadProgress,
  getAbortController,
  resetComponent,
  resetUpload
});

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  if (newVal instanceof File) {
    fileList.value = [{ name: newVal.name, raw: newVal }];
    showUploader.value = false;
  } else if (typeof newVal === 'string' && newVal) {
    fileList.value = [];
    showUploader.value = false;
  } else {
    fileList.value = [];
    showUploader.value = true;
  }
}, { immediate: true });

// 监听 existingVideoUrl 变化
watch(() => props.existingVideoUrl, (newVal) => {
  if (newVal && !props.modelValue) showUploader.value = false;
}, { immediate: true });

// 组件销毁时清理
onBeforeUnmount(() => {
  if (controller) {
    controller.abort();
    controller = null;
  }
});
</script>

<style lang="scss" scoped>
.upload-container {
  width: 100%;
  border-radius: 8px;
}

// 上传组件区域
.uploader-wrapper {
  width: 100%;
  border-radius: 8px;
  border: 2px dashed #dcdfe6;
  background: #f8f9fa;
  padding: 30px 20px;
  text-align: center;
  
  .upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    
    .upload-icon {
      color: #909399;
      margin-bottom: 20px;
    }
    
    .upload-text {
      text-align: center;
      color: #606266;
      margin-bottom: 20px;
      
      .upload-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 16px;
      }
      
      .upload-button {
        padding: 12px 30px;
        background: linear-gradient(135deg, #409EFF, #53a8ff);
        border: none;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;
        font-size: 15px;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
        }
      }
    }
    
    .upload-tip {
      margin-top: 20px;
      color: #909399;
      font-size: 13px;
      display: flex;
      align-items: center;
      gap: 6px;
      background: #f5f7fa;
      padding: 8px 16px;
      border-radius: 20px;
      
      .el-icon {
        color: #e6a23c;
      }
    }
  }
}

// 文件预览区域
.file-preview {
  width: 100%;
  border-radius: 8px;
  background-color: #ffffff;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.04);
  padding: 16px;
  
  .file-preview-inner {
    display: flex;
    align-items: center;
    
    .file-preview-icon {
      width: 80px;
      height: 80px;
      border-radius: 6px;
      margin-right: 16px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #36d1dc, #5b86e5);
      color: #fff;
    }
    
    .file-info {
      flex: 1;
      min-width: 0;
      
      .file-name-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        .file-name-container {
          min-width: 0;
          
          .file-name {
            font-size: 15px;
            color: #303133;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
          }
          
          .file-size {
            color: #909399;
            font-size: 13px;
            margin-left: 6px;
          }
        }
      }
      
      .file-meta {
        margin-bottom: 10px;
      }
    }
  }
}

// 上传进度条
.upload-progress {
  margin-top: 16px;
  
  :deep(.el-progress__text) {
    font-size: 13px;
  }
  
  :deep(.el-progress-bar__outer) {
    border-radius: 8px;
    background-color: #f0f2f5;
  }
  
  :deep(.el-progress-bar__inner) {
    border-radius: 8px;
  }
}
</style>