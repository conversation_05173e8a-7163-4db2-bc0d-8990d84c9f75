{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "compounds": [
    {
      "name": "Compound",
      "configurations": ["dev","Launch Edge"]
    }
  ],
  "configurations": [
    {
      "name": "Launch Edge",
      "request": "launch",
      "type": "msedge",
      "url": "http://localhost:82",
      "webRoot": "${workspaceFolder}"
    },
    {
      "name": "Chrome启动",
      "request": "launch",
      "type": "chrome",
      "url": "http://localhost:82",
      "runtimeArgs": ["--do-not-de-elevate"]
    },
    {
      "name": "dev",
      "request": "launch",
      "runtimeArgs": ["run", "dev"],
      "runtimeExecutable": "npm",
      "skipFiles": ["<node_internals>/**"],
      "type": "node"
    },
    {
      "name": "prod",
      "request": "launch",
      "runtimeArgs": ["run", "prod"],
      "runtimeExecutable": "npm",
      "skipFiles": ["<node_internals>/**"],
      "type": "node"
    }
  ]
}
