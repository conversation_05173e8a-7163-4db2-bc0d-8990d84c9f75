import request from '@/utils/request'

// 查询关键词管理列表
export function listKeyword(query) {
  return request({
    url: '/platform/keyword/list',
    method: 'get',
    params: query
  })
}

// 查询关键词管理详细
export function getKeyword(keywordId) {
  return request({
    url: '/platform/keyword/' + keywordId,
    method: 'get'
  })
}

// 新增关键词管理
export function addKeyword(data) {
  return request({
    url: '/platform/keyword',
    method: 'post',
    data: data
  })
}

// 修改关键词管理
export function updateKeyword(data) {
  return request({
    url: '/platform/keyword',
    method: 'put',
    data: data
  })
}

// 删除关键词管理
export function delKeyword(keywordId) {
  return request({
    url: '/platform/keyword/' + keywordId,
    method: 'delete'
  })
}

// 匹配关键词管理
export function getKeywordFormat(data) {
  return request({
    url: '/platform/keyword/listByFormat',
    method: 'post',
    data:data
  })
}