<script lang="ts" setup>
import modal from '@/plugins/modal'
import { UploadFile, UploadFiles, UploadProgressEvent, UploadUserFile, UploadRawFile, UploadRequestOptions } from 'element-plus';
import { computed, reactive, ref, watch } from 'vue';
interface IFile {
    audioId: number
    audioName: string
    audioType: string
    audioSize: string
    audioStatus: string
    audioProgress: number
    audioAid: number | null
}
const emit = defineEmits({
    uploadAudios: (x: UploadRequestOptions, y: AbortSignal, z: (data: number) => void) => undefined,
    deleteUploadFile: (x: any, y: () => void) => undefined,
    closeAudio: () => undefined,
});
const upload = ref()
const audioFiles = ref<Array<UploadFile>>([])
const audioViews = ref<Array<IFile>>([])
const audioTotalSize = computed(() => audioViews.value.reduce((pre, cur) => pre + parseFloat(cur.audioSize), 0))
const abortControllers = reactive<{ [key: number]: AbortController }>({});
const audioSuccessCount = computed(() => audioViews.value.filter(item => item.audioStatus === 'success').length)
function handleProgress(event: UploadProgressEvent, file: (UploadFile), files: UploadFiles) {
    const index = audioViews.value.findIndex(item => item.audioId === file.uid);
    if (index !== -1) {
        let fileraw = file.raw ? file.raw as unknown as (File & { percent: number }) : { percent: 0 }
        audioViews.value[index].audioProgress = fileraw.percent;
        audioViews.value[index].audioStatus = fileraw.percent < 100 ? 'uploading' : 'success';
    }
}
function handleChange(file: UploadFile, files: UploadFiles) {
    audioViews.value = files.map(f => {
        let file = f.raw ? f.raw as unknown as (File & { percent: number }) : { percent: 0 }
        return {
            audioId: f.uid,
            audioName: f.name.split('.').slice(0, -1).join('.'),
            audioType: f.name.slice(f.name.lastIndexOf('.') + 1),
            audioSize: (f.size! / (1024 * 1024)).toFixed(2),
            audioStatus: file.percent === 100 ? 'success' : f.status,
            audioProgress: file.percent,
            audioAid: null
        }
    });
}
function uploadAudios(x: UploadRequestOptions) {
    const controller = new AbortController();
    const signal = controller.signal;
    abortControllers[x.file.uid] = controller
    emit('uploadAudios', x, signal, (data: number) => {
        audioViews.value.filter(item => item.audioId === x.file.uid).forEach(item => item.audioAid = data);
    })
}
function handleExceed(files: File[], uploadFiles: UploadUserFile[]) {
    const totalFiles = files.length + uploadFiles.length;
    if (totalFiles > 5) {
        modal.msgWarning(`上传音频数量超出限制，最多上传5个音频文件。当前已选择 ${files.length} 个新文件，已有 ${uploadFiles.length} 个文件`)
    }
}
function submitAudio() {
    const limitType = ['mp3', 'wav', 'flac', 'm4a'];
    const limitSize = 25;
    try {
        if (audioFiles.value.length == 0) {
            throw new Error('请先选择音频文件!');
        }
        audioFiles.value.forEach(f => {
            const fileType = f.name.slice(f.name.lastIndexOf('.') + 1).toLowerCase();
            const fileSizeMB = (f.size! / (1024 * 1024)).toFixed(2);

            if (!limitType.includes(fileType)) {
                throw new Error(`${f.name.split('.').slice(0, -1).join('.')}文件类型不正确，请上传${limitType.join('、')}格式音频文件`);
            }

            if (Number(fileSizeMB) > limitSize) {
                throw new Error(`${f.name.split('.').slice(0, -1).join('.')}文件大小不能超过${limitSize}MB`);
            }
        })
        upload.value.submit()
    } catch (error) {
        modal.msgError(error.message);
        return;
    }
}

function cancelUpload(uid: number) {
    let item = abortControllers[uid]
    item.abort()
    audioFiles.value = audioFiles.value.filter(item => item.uid !== uid);
    audioViews.value = audioViews.value.filter(item => item.audioId !== uid);

}

function deleteUploadFile(x: number, y: any) {
    if (y.audioStatus == 'ready') {
        audioFiles.value = audioFiles.value.filter(item => item.uid !== y.audioId);
        audioViews.value.splice(x, 1)
    } else {
        emit("deleteUploadFile", y, () => {
            audioFiles.value = audioFiles.value.filter(item => item.uid !== y.audioId);
            audioViews.value.splice(x, 1)
        });
    }
}
function closeAudio() {
    audioFiles.value = []
    audioViews.value = []
    emit("closeAudio")
}
</script>
<template>
    <el-dialog @update:modelValue="closeAudio">
        <div class="audioUpload">
            <el-upload ref="upload" :auto-upload="false" multiple :limit="5" :show-file-list="false"
                v-model:file-list="audioFiles" accept="audio/*" :http-request="uploadAudios" :on-change="handleChange"
                :on-progress="handleProgress" :on-exceed="handleExceed">
                <template #trigger>
                    <el-button type="primary">选择文件</el-button>
                </template>
            </el-upload>
            <el-button type="success" @click="submitAudio">上传文件</el-button>
        </div>
        <hr>
        <div class="audioView">
            <div class="audioDetail">
                <el-tag>文件数量：{{ audioViews.length }}个</el-tag>
                <el-tag type="success">上传成功：{{ audioSuccessCount }}</el-tag>
                <el-tag type="info">总大小：{{ audioTotalSize.toFixed(2) }}MB</el-tag>
            </div>
            <el-table :data="audioViews">
                <el-table-column prop="audioName" label="文件名" center width="80"></el-table-column>
                <el-table-column prop="audioType" label="类型" center width="80"></el-table-column>
                <el-table-column prop="audioSize" label="大小" center width="80">
                    <template #default="scope">
                        {{ scope.row.audioSize + 'MB' }}
                    </template>
                </el-table-column>
                <el-table-column prop="audioStatus" label="状态" width="120">
                    <template #default="scope">
                        <el-tag v-if="scope.row.audioStatus == 'ready'">待上传</el-tag>
                        <el-tag type="warning" v-else-if="scope.row.audioStatus === 'uploading'">上传中</el-tag>
                        <el-tag type="danger" v-else-if="scope.row.audioStatus === 'fail'">上传失败</el-tag>
                        <el-tag type="success" v-else-if="scope.row.audioStatus === 'success'">上传成功</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="进度" width="120">
                    <template #default="scope">
                        <el-progress :text-inside="true" :stroke-width="15" :percentage="scope.row.audioProgress" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="120">
                    <template #default="scope">
                        <el-button type="warning" link v-if="scope.row.audioStatus == 'uploading'"
                            :disabled="!(scope.row.audioStatus === 'uploading')"
                            @click="cancelUpload(scope.row.audioId)">取消上传</el-button>
                        <el-button type="danger" link :disabled="scope.row.audioStatus === 'uploading'"
                            @click="deleteUploadFile(scope.$index, scope.row)">删除音频</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </el-dialog>
</template>
<style lang="scss" scoped>
.audioUpload {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}
</style>