/**
 * @file canvasManagerUtils.ts
 * @description Canvas 画布管理相关的工具函数集合
 *              提供画布尺寸计算、响应式调整、渲染引擎管理等功能
 *              从 TimelinePlayer.vue 中抽取，提供可复用的画布管理能力
 */

/**
 * @interface CanvasSizeConfig
 * @description 画布尺寸配置
 */
export interface CanvasSizeConfig {
  width: number;
  height: number;
  aspectRatio?: number;
}

/**
 * @interface CanvasManagerOptions
 * @description 画布管理器配置选项
 */
export interface CanvasManagerOptions {
  defaultWidth?: number;
  defaultHeight?: number;
  aspectRatio?: number;
  autoResize?: boolean;
}

/**
 * 计算画布尺寸
 * @param container - 容器元素
 * @param options - 配置选项
 * @returns 计算得出的尺寸配置
 */
export function calculateCanvasSize(
  container: HTMLElement | null,
  options: CanvasManagerOptions = {}
): CanvasSizeConfig {
  const {
    defaultWidth = 1280,
    defaultHeight = 720,
    aspectRatio
  } = options;

  if (!container) {
    return { width: defaultWidth, height: defaultHeight };
  }

  const rect = container.getBoundingClientRect();
  let width = rect.width || defaultWidth;
  let height = rect.height || defaultHeight;

  // 如果指定了宽高比，根据宽度计算高度
  if (aspectRatio && aspectRatio > 0) {
    height = width / aspectRatio;
  }

  return {
    width: Math.floor(width),
    height: Math.floor(height),
    aspectRatio: width / height
  };
}

/**
 * 创建响应式画布尺寸管理器
 * @param canvasRef - 画布元素引用
 * @param options - 配置选项
 * @returns 画布管理器实例
 */
export function createCanvasResizeManager(
  canvasRef: { value: HTMLCanvasElement | null },
  options: CanvasManagerOptions = {}
) {
  const {
    defaultWidth = 1280,
    defaultHeight = 720,
    autoResize = true
  } = options;

  let currentSize: CanvasSizeConfig = { width: defaultWidth, height: defaultHeight };
  let resizeCallback: ((size: CanvasSizeConfig) => void) | null = null;
  let resizeObserver: ResizeObserver | null = null;

  /**
   * 更新画布尺寸
   */
  function updateCanvasSize(): CanvasSizeConfig {
    const container = canvasRef.value?.parentElement || null;
    const newSize = calculateCanvasSize(container, options);
    
    if (newSize.width !== currentSize.width || newSize.height !== currentSize.height) {
      currentSize = newSize;
      
      // 更新画布实际尺寸
      if (canvasRef.value) {
        canvasRef.value.width = newSize.width;
        canvasRef.value.height = newSize.height;
      }
      
      // 触发回调
      resizeCallback?.(newSize);
      
      console.log('📐 画布尺寸已更新:', newSize);
    }
    
    return newSize;
  }

  /**
   * 设置尺寸变化回调
   */
  function setResizeCallback(callback: (size: CanvasSizeConfig) => void) {
    resizeCallback = callback;
  }

  /**
   * 启动自动尺寸调整
   */
  function startAutoResize() {
    if (!autoResize) return;

    // 使用 ResizeObserver 监听容器尺寸变化
    if (canvasRef.value?.parentElement) {
      resizeObserver = new ResizeObserver(() => {
        updateCanvasSize();
      });
      resizeObserver.observe(canvasRef.value.parentElement);
    }

    // 同时监听窗口尺寸变化作为备用
    window.addEventListener('resize', updateCanvasSize);
  }

  /**
   * 停止自动尺寸调整
   */
  function stopAutoResize() {
    if (resizeObserver) {
      resizeObserver.disconnect();
      resizeObserver = null;
    }
    window.removeEventListener('resize', updateCanvasSize);
  }

  /**
   * 手动设置画布尺寸
   */
  function setCanvasSize(width: number, height: number) {
    currentSize = { width, height, aspectRatio: width / height };
    
    if (canvasRef.value) {
      canvasRef.value.width = width;
      canvasRef.value.height = height;
    }
    
    resizeCallback?.(currentSize);
  }

  /**
   * 获取当前尺寸
   */
  function getCurrentSize(): CanvasSizeConfig {
    return { ...currentSize };
  }

  /**
   * 销毁管理器
   */
  function destroy() {
    stopAutoResize();
    resizeCallback = null;
  }

  return {
    updateCanvasSize,
    setResizeCallback,
    startAutoResize,
    stopAutoResize,
    setCanvasSize,
    getCurrentSize,
    destroy
  };
}

/**
 * Canvas 渲染引擎状态管理器
 * 用于管理渲染引擎的生命周期和状态
 */
export class CanvasEngineManager {
  private engine: any = null;
  private isInitialized = false;
  private loadingState = {
    isLoading: false,
    status: '',
    error: ''
  };
  private callbacks: {
    onLoadingChange?: (isLoading: boolean, status?: string) => void;
    onError?: (error: string) => void;
    onInitialized?: (engine: any) => void;
  } = {};

  /**
   * 设置回调函数
   */
  setCallbacks(callbacks: {
    onLoadingChange?: (isLoading: boolean, status?: string) => void;
    onError?: (error: string) => void;
    onInitialized?: (engine: any) => void;
  }) {
    this.callbacks = callbacks;
  }

  /**
   * 更新加载状态
   */
  private updateLoadingState(isLoading: boolean, status: string = '', error: string = '') {
    this.loadingState = { isLoading, status, error };
    this.callbacks.onLoadingChange?.(isLoading, status);
    
    if (error) {
      this.callbacks.onError?.(error);
    }
  }

  /**
   * 初始化渲染引擎
   */
  async initializeEngine(
    EngineClass: any,
    canvasElement: HTMLCanvasElement,
    config?: any
  ): Promise<any> {
    try {
      this.updateLoadingState(true, '初始化渲染引擎...');
      
      // 清理旧引擎
      this.cleanup();
      
      // 创建新引擎
      this.engine = new EngineClass(canvasElement, config);
      
      this.updateLoadingState(true, '渲染引擎初始化完成');
      this.isInitialized = true;
      
      this.callbacks.onInitialized?.(this.engine);
      
      // 成功初始化后，设置完成状态
      this.updateLoadingState(false, '初始化完成');
      
      return this.engine;
    } catch (error) {
      const errorMessage = `渲染引擎初始化失败: ${error instanceof Error ? error.message : '未知错误'}`;
      this.updateLoadingState(false, '', errorMessage);
      throw error;
    }
  }

  /**
   * 获取引擎实例
   */
  getEngine(): any {
    return this.engine;
  }

  /**
   * 检查是否已初始化
   */
  isEngineInitialized(): boolean {
    return this.isInitialized && this.engine !== null;
  }

  /**
   * 获取加载状态
   */
  getLoadingState() {
    return { ...this.loadingState };
  }

  /**
   * 清理引擎
   */
  cleanup() {
    if (this.engine && typeof this.engine.destroy === 'function') {
      this.engine.destroy();
    }
    this.engine = null;
    this.isInitialized = false;
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.cleanup();
    this.callbacks = {};
  }
}

/**
 * 创建 Canvas 调试信息管理器
 * @param canvasRef - 画布引用
 * @param isDev - 是否为开发环境
 * @returns 调试信息管理器
 */
export function createCanvasDebugManager(
  canvasRef: { value: HTMLCanvasElement | null },
  isDev: boolean = false
) {
  const debugInfo = {
    canvasSize: { width: 0, height: 0 },
    engineStatus: '未初始化',
    timelineStatus: '未加载',
    clipCounts: { video: 0, audio: 0, subtitle: 0 },
    playbackInfo: { currentTime: 0, maxDuration: 0 }
  };

  /**
   * 更新画布尺寸信息
   */
  function updateCanvasSize(width: number, height: number) {
    debugInfo.canvasSize = { width, height };
  }

  /**
   * 更新引擎状态
   */
  function updateEngineStatus(status: string) {
    debugInfo.engineStatus = status;
  }

  /**
   * 更新时间线状态
   */
  function updateTimelineStatus(status: string) {
    debugInfo.timelineStatus = status;
  }

  /**
   * 更新片段数量
   */
  function updateClipCounts(video: number, audio: number, subtitle: number = 0) {
    debugInfo.clipCounts = { video, audio, subtitle };
  }

  /**
   * 更新播放信息
   */
  function updatePlaybackInfo(currentTime: number, maxDuration: number) {
    debugInfo.playbackInfo = { currentTime, maxDuration };
  }

  /**
   * 获取调试信息
   */
  function getDebugInfo() {
    return { ...debugInfo };
  }

  /**
   * 是否显示调试信息
   */
  function shouldShowDebug(): boolean {
    return isDev;
  }

  return {
    updateCanvasSize,
    updateEngineStatus,
    updateTimelineStatus,
    updateClipCounts,
    updatePlaybackInfo,
    getDebugInfo,
    shouldShowDebug
  };
}
