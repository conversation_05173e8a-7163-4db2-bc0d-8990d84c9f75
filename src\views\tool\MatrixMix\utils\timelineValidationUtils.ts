/**
 * Timeline数据校验工具类
 * 专门处理轨道ID的校验，防止ID重复
 */

// 校验结果接口
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  duplicateIds?: string[];
}

// 轨道数据接口
export interface TrackData {
  Id: string;
  Type: 'Video' | 'Audio' | 'Subtitle';
  [key: string]: any; // 允许其他属性如 VideoTrackClips, AudioTrackClips 等
}

/**
 * 简单的ID记录管理器
 * 功能：存入、读取、初始化
 */
export class TrackIdManager {
  private usedIds: Set<string> = new Set();
  private maxIdByType: { Video: number; Audio: number; Subtitle: number } = {
    Video: -1,
    Audio: -1,
    Subtitle: -1
  };

  /**
   * 初始化 - 从timeline中读取所有现有ID
   * @param timeline Timeline数据
   */
  init(timeline: any) {
    this.usedIds.clear();
    this.maxIdByType = { Video: -1, Audio: -1, Subtitle: -1 };

    if (!timeline) return;

    // 读取视频轨道ID
    if (timeline.VideoTracks) {
      timeline.VideoTracks.forEach((track: any) => {
        if (track.Id) {
          this.addId(track.Id);
          const numericId = parseInt(track.Id);
          if (!isNaN(numericId)) {
            const trackType = track.Type || 'Video';
            this.maxIdByType[trackType as keyof typeof this.maxIdByType] = Math.max(
              this.maxIdByType[trackType as keyof typeof this.maxIdByType],
              numericId
            );
          }
        }
      });
    }

    // 读取音频轨道ID
    if (timeline.AudioTracks) {
      timeline.AudioTracks.forEach((track: any) => {
        if (track.Id) {
          this.addId(track.Id);
          const numericId = parseInt(track.Id);
          if (!isNaN(numericId)) {
            this.maxIdByType.Audio = Math.max(this.maxIdByType.Audio, numericId);
          }
        }
      });
    }

    // 读取字幕轨道ID  
    if (timeline.SubtitleTracks) {
      timeline.SubtitleTracks.forEach((track: any) => {
        if (track.Id) {
          this.addId(track.Id);
          const numericId = parseInt(track.Id);
          if (!isNaN(numericId)) {
            this.maxIdByType.Subtitle = Math.max(this.maxIdByType.Subtitle, numericId);
          }
        }
      });
    }

    console.log('🆔 ID管理器初始化完成:', {
      总ID数: this.usedIds.size,
      各类型最大ID: this.maxIdByType,
      所有ID: Array.from(this.usedIds).sort()
    });
  }

  /**
   * 存入 - 记录ID
   * @param id 要记录的ID
   */
  addId(id: string) {
    this.usedIds.add(id);
  }

  /**
   * 读取 - 检查ID是否已被使用
   * @param id 要检查的ID
   * @returns 是否已被使用
   */
  hasId(id: string): boolean {
    return this.usedIds.has(id);
  }

  /**
   * 生成新的唯一ID（简化版本）
   * @param trackType 轨道类型
   * @param insertPosition 插入位置
   * @returns 新的唯一ID
   */
  generateNewId(trackType: 'Video' | 'Audio' | 'Subtitle', insertPosition: 'top' | 'bottom' = 'bottom'): string {
    if (insertPosition === 'top') {
      // 🎯 顶部插入：直接返回 ID=0
      // 不需要复杂的验证，现有轨道会通过重排机制处理
      console.log('🔝 顶部插入，直接分配ID=0');
      return '0';
    } else {
      // 🔽 底部插入：使用最大ID+1
      let nextId = this.maxIdByType[trackType] + 1;
      console.log(`🔽 底部插入，分配ID=${nextId} (基于最大ID=${this.maxIdByType[trackType]})`);
      
      // 更新最大ID记录
      this.maxIdByType[trackType] = nextId;
      return nextId.toString();
    }
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      totalIds: this.usedIds.size,
      maxIdByType: { ...this.maxIdByType },
      allIds: Array.from(this.usedIds).sort((a, b) => parseInt(a) - parseInt(b))
    };
  }
}

// 创建全局实例
const globalTrackIdManager = new TrackIdManager();

/**
 * Timeline数据校验工具类
 */
export class TimelineValidator {
  
  /**
   * 校验整个timeline数据的完整性
   * @param timeline Timeline数据
   * @returns 校验结果
   */
  static validateTimeline(timeline: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      duplicateIds: []
    };

    if (!timeline) {
      result.isValid = false;
      result.errors.push('Timeline数据不能为空');
      return result;
    }

    // 校验各类型轨道
    const trackTypes = ['VideoTracks', 'AudioTracks', 'SubtitleTracks'];
    const allTrackIds = new Set<string>();

    for (const trackType of trackTypes) {
      if (timeline[trackType]) {
        const trackValidation = this.validateTracks(timeline[trackType], trackType);
        
        // 合并错误和警告
        result.errors.push(...trackValidation.errors);
        result.warnings.push(...trackValidation.warnings);
        
        // 检查轨道ID重复
        timeline[trackType].forEach((track: TrackData) => {
          if (allTrackIds.has(track.Id)) {
            result.duplicateIds!.push(`轨道ID重复: ${track.Id}`);
            result.isValid = false;
          } else {
            allTrackIds.add(track.Id);
          }
        });
      }
    }

    if (result.errors.length > 0 || result.duplicateIds!.length > 0) {
      result.isValid = false;
    }

    return result;
  }

  /**
   * 校验特定类型的轨道数组
   * @param tracks 轨道数组
   * @param trackType 轨道类型
   * @returns 校验结果
   */
  static validateTracks(tracks: TrackData[], trackType: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    if (!Array.isArray(tracks)) {
      result.isValid = false;
      result.errors.push(`${trackType}必须是数组类型`);
      return result;
    }

    tracks.forEach((track, index) => {
      const trackValidation = this.validateSingleTrack(track, trackType, index);
      result.errors.push(...trackValidation.errors);
      result.warnings.push(...trackValidation.warnings);
      
      if (!trackValidation.isValid) {
        result.isValid = false;
      }
    });

    return result;
  }

  /**
   * 校验单个轨道数据
   * @param track 轨道数据
   * @param trackType 轨道类型
   * @param index 轨道索引
   * @returns 校验结果
   */
  static validateSingleTrack(track: TrackData, trackType: string, index: number): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // 检查必要属性
    if (!track.Id) {
      result.isValid = false;
      result.errors.push(`${trackType}[${index}]: 缺少轨道ID`);
    }

    if (!track.Type) {
      result.isValid = false;
      result.errors.push(`${trackType}[${index}]: 缺少轨道类型`);
    } else {
      // 检查轨道类型是否匹配
      const expectedType = trackType.replace('Tracks', '');
      if (track.Type !== expectedType) {
        result.warnings.push(`${trackType}[${index}]: 轨道类型不匹配，期望${expectedType}，实际${track.Type}`);
      }
    }

    return result;
  }

  /**
   * 检查轨道ID是否重复
   * @param timeline Timeline数据
   * @param newTrackId 新轨道ID
   * @param trackType 轨道类型
   * @returns 是否重复
   */
  static isTrackIdDuplicate(timeline: any, newTrackId: string, trackType?: string): boolean {
    if (!timeline || !newTrackId) return false;

    const trackTypes = trackType ? [`${trackType}Tracks`] : ['VideoTracks', 'AudioTracks', 'SubtitleTracks'];
    
    for (const type of trackTypes) {
      if (timeline[type] && Array.isArray(timeline[type])) {
        const exists = timeline[type].some((track: TrackData) => track.Id === newTrackId);
        if (exists) return true;
      }
    }

    return false;
  }

  /**
   * 生成唯一的轨道ID
   * @param timeline Timeline数据
   * @param trackType 轨道类型
   * @param insertPosition 插入位置
   * @returns 唯一的轨道ID
   */
  static generateUniqueTrackId(
    timeline: any, 
    trackType: string, 
    insertPosition: 'top' | 'bottom' = 'bottom'
  ): string {
    // 初始化ID管理器
    globalTrackIdManager.init(timeline);
    
    // 生成新ID，现在支持插入位置
    const newId = globalTrackIdManager.generateNewId(trackType as 'Video' | 'Audio' | 'Subtitle', insertPosition);
    
    console.log('🆔 生成新轨道ID:', newId, '类型:', trackType, '位置:', insertPosition);
    return newId;
  }
}

// 导出单例函数
export function validateTimeline(timeline: any): ValidationResult {
  return TimelineValidator.validateTimeline(timeline);
}

export function generateUniqueTrackId(
  timeline: any, 
  trackType: 'Video' | 'Audio' | 'Subtitle', 
  insertPosition: 'top' | 'bottom' = 'bottom'
): string {
  return TimelineValidator.generateUniqueTrackId(timeline, trackType, insertPosition);
}

// 导出ID管理器，供外部直接使用
export { globalTrackIdManager };
