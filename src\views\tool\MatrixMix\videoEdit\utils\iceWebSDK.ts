/**
 * 阿里云ICE WebSDK封装 - 统一版本
 * 同时支持云剪辑项目和模板工厂模板编辑
 */
import { iceNPMManager, EditorMode } from './iceNPMManager';
import { ElMessage } from 'element-plus';
import { transMediaList } from './utils';
import logo from '@/assets/logo/logo.png';
import {
  CustomFontItem,
  VoiceGroup,
  AvatarConfig,
  VideoTranslation,
  Timeline,
  TimelineMaterial,
  ASRResult,
  VoiceConfig,
  StickerCategory,
  StickerResponse,
  PublicMaterialLibrary,
  MaterialMapsType,
} from './iceTypes';
import { get, lowerFirst } from "lodash";
import cache from '@/plugins/cache';

export interface UnifiedWebSDKConfig {
  container: HTMLElement;
  id: string; // 项目ID或模板ID
  mode: EditorMode; // 'project' | 'template'
}

export interface AliyunVideoEditor {
  init: (config: any) => void;
  destroy: (keepState?: boolean) => boolean;
  version: string;
  setCurrentTime: (currentTime: number) => void;
  getCurrentTime: () => number;
  getDuration: () => number;
}

/**
 * ICE WebSDK管理器 - 统一版本
 */
class UnifiedICEWebSDK {
  private editorInstance: AliyunVideoEditor | null = null;
  private currentId: string | null = null;
  private currentMode: EditorMode | null = null;
  private isInitializing = false;
  private callbackCache = new Map<string, any>();
  private activeRequests = new Map<string, Promise<any>>();
  private materialSelectorCallback: (() => Promise<any[]>) | null = null;

  // 缓存动态字体列表
  private dynamicFontList: (string | CustomFontItem)[] = [];

  /**
   * 设置素材选择器回调
   */
  setMaterialSelectorCallback(callback: () => Promise<any[]>) {
    this.materialSelectorCallback = callback;
  }

  /**
   * 初始化WebSDK - 统一处理云剪辑和模板工厂
   */
  async init(config: UnifiedWebSDKConfig): Promise<boolean> {
    if (this.isInitializing) {
      return false;
    }


    if (this.editorInstance) {
      this.destroy();
    }

    this.isInitializing = true;

    try {
      // 容器安全检查
      if (!config.container || !config.container.isConnected) {
        throw new Error('容器元素无效或未连接到DOM');
      }

      // 标记容器为非Vue管理
      (config.container as any).__VUE_SKIP__ = true;

      // 清空容器，防止冲突
      config.container.innerHTML = '';

      // 初始化NPM管理器
      await iceNPMManager.initialize();

      // 检查WebSDK是否加载
      if (!(window as any).AliyunVideoEditor) {
        throw new Error('AliyunVideoEditor WebSDK未加载，请检查脚本引入');
      }

      this.editorInstance = (window as any).AliyunVideoEditor;
      this.currentId = config.id;
      this.currentMode = config.mode;

      // 创建配置对象
      const websdkConfig = this.createWebSDKConfig(config);

      // 在安全的异步上下文中初始化WebSDK
      await new Promise<void>((resolve, reject) => {
        try {
          const editor = this.editorInstance;
          if (!editor) {
            reject(new Error('WebSDK实例获取失败'));
            return;
          }

          requestAnimationFrame(() => {
            try {
              editor.init(websdkConfig);

              setTimeout(() => {
                this.optimizeWebSDKContainer(config.container);
                resolve();
              }, 100);

            } catch (error) {
              reject(error);
            }
          });

        } catch (error) {
          reject(error);
        }
      });

      return true;

    } catch (error: any) {
      console.error('WebSDK初始化失败:', error);
      return false;
    } finally {
      this.isInitializing = false;
    }
  }

  /**
   * 创建WebSDK配置
   */
  private createWebSDKConfig(config: UnifiedWebSDKConfig): any {
    const baseConfig = {
      container: config.container,
      locale: 'zh-CN',
      mode: config.mode,

      // 使用预加载的完整字体列表
      customFontList: [
        'alibaba-sans',     // 阿里巴巴普惠体
        'fangsong',         // 仿宋字体
        'kaiti',            // 楷体
        'SimSun',           // 宋体
        'siyuan-heiti',     // 思源黑体
        'siyuan-songti',    // 思源宋体
        'wqy-zenhei-mono',  // 文泉驿等宽正黑
        'wqy-zenhei-sharp', // 文泉驿点阵正黑
        'wqy-microhei',     // 文泉驿微米黑
        'zcool-gaoduanhei', // 站酷高端黑体
        'zcool-kuaile',     // 站酷快乐体
        'zcool-wenyiti',    // 站酷文艺体
      ],

      // 动态水印配置
      getPreviewWaterMarks: this.getDynamicWatermarks.bind(this),


      // 基础配置
      //   defaultAspectRatio: { width: 16, height: 9 } as PlayerAspectRatio,
      defaultSubtitleText: '阿里云剪辑',
      useDynamicSrc: true,
      dynamicSrcQps: 10,

      // 功能开关配置
      disableAutoJobModal: true,
      disableGreenMatting: false,
      disableRealMatting: false,
      disableDenoise: false,
      audioWaveRenderDisabled: false,
      disableAutoAspectRatio: false,
      hasTranscodedAudio: false,

      //   // License配置
      //   licenseConfig: {
      //     licenseKey: process.env.VUE_APP_ICE_LICENSE_KEY || ''
      //   } as LicenseConfig,



      // 获取Timeline中的素材媒资信息
      getTimelineMaterials: this.createTimelineMaterialsGetter(),



      // AI功能配置
      submitASRJob: this.createASRJobSubmitter(),
      submitAudioProduceJob: this.createAudioProduceJobSubmitter(),

      // ASR和TTS配置
      asrConfig: {
        enabled: false,
        submitASRJob: this.createASRJobSubmitter()
      },

      ttsConfig: {
        enabled: false,
        submitAudioProduceJob: this.createAudioProduceJobSubmitter()
      },
      // 动态获取媒资播放地址
      getDynamicSrc: this.createDynamicSrcGetter(),
      // 修正公共素材库配置
      publicMaterials: {
        getLists: async () => {
          const mediaTypes = [
            {
              bType: "bgm",
              mediaType: "audio",
              name: "背景音乐",
            },
            {
              bType: "bgi",
              mediaType: "image",
              styleType: "background",
              name: "背景图片",
            },
            {
              bType: "sticker",
              mediaType: "image",
              name: "贴纸",
            }
          ];

          // 改为串行处理，防止API限流
          const result = [];

          for (const item of mediaTypes) {
            try {
              const res = await iceNPMManager.listAllPublicMediaTags(
                item.bType,
                this.editorInstance?.version || ''
              );
              const tagList = res.MediaTagList || [];

              const itemTags = tagList.map((tag: any) => {
                const tagName = tag.MediaTagNameChinese || tag.MediaTagNameEnglish;
                return {
                  name: item.name,
                  key: item.bType,
                  mediaType: item.mediaType,
                  styleType: item.styleType,
                  tag: tagName,
                  getItems: async (pageNo: number, pageSize: number) => {
                    try {
                      const itemRes = await iceNPMManager.listPublicMediaBasicInfos({
                        BusinessType: item.bType,
                        MediaTagId: tag.MediaTagId,
                        PageNo: pageNo,
                        PageSize: pageSize,
                        IncludeFileBasicInfo: true,
                      });
                      const total = get(itemRes, "TotalCount");
                      const items = get(itemRes, "MediaInfos", []);
                      const transItems = transMediaList(items, true); // 标识为公共素材
                      return {
                        items: transItems,
                        end: pageNo * pageSize >= total,
                      };
                    } catch (error) {
                      return {
                        items: [],
                        end: true,
                      };
                    }
                  },
                };
              });
              result.push(...itemTags);
              // 添加延迟，防止API限流
              if (mediaTypes.indexOf(item) < mediaTypes.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 400));
              }
            } catch (error) {
              continue;
            }
          }
          return result;
        }
      } as unknown as PublicMaterialLibrary,

      // 字幕配置
      subtitleConfig: {},

      // 贴纸相关配置
      getStickerCategories: this.createStickerCategoriesGetter(),
      getStickers: this.createStickersGetter(),

      // 自定义配置
      customTexts: {
        importButton: '导入素材',
        updateButton: config.mode === 'template' ? '保存模板' : '保存项目',
        produceButton: '生成视频',
        backButton: '返回',
        logoUrl: logo,
      },

      // customFontList: [
      //   'SimSun',
      //   'kaiti',
      //   'alibaba-sans',
      //   'zcool-kuaile',
      //   'wqy-microhei',
      //   // {
      //   //   key: 'alibaba-font',
      //   //   name: '阿里巴巴普惠体',
      //   //   url: 'https://example.com/fonts/alibaba.woff2'
      //   // } as CustomFontItem
      // ],

      customVoiceGroups: [
        {
          type: 'standard',
          category: '标准语音',
          voiceList: []
        }
      ] as VoiceGroup[],

      // // 水印配置
      // getPreviewWaterMarks: async () => {
      //   return [
      //     // {
      //     //   url: '/watermark.png',
      //     //   width: 0.2,
      //     //   height: 0.1,
      //     //   x: 0.8,
      //     //   y: 0.9,
      //     //   opacity: 0.5
      //     // }
      //   ];
      // },

      // 数字人配置
      avatarConfig: {
        enabled: false
      } as AvatarConfig,

      // 视频翻译配置
      videoTranslation: {
        enabled: false,
        languages: ['en', 'ja', 'ko']
      } as VideoTranslation,

      // 导出轨道/视频片段拆分
      exportVideoClipsSplit: this.createVideoClipsSplitExporter(),
      // 导出片段（从标记点导出）
      exportFromMediaMarks: this.createMediaMarksExporter(),
      // 合并导出
      exportVideoClipsMerge: this.createVideoClipsMergeExporter(),
      // 音频优化配置
      getAudioByMediaId: this.createAudioGetter()
    };

    // 根据模式添加特定配置
    if (config.mode === 'template') {
      return {
        ...baseConfig,
        getEditingProjectMaterials: this.createTemplateProjectMaterialsGetter(config.id),
        searchMedia: this.createTemplateSearchMedia(config.id),
        deleteEditingProjectMaterials: this.createTemplateDeleteMaterials(config.id),
        getEditingProject: this.createTemplateGetProject(config.id),
        updateTemplate: this.createTemplateUpdater(config.id),
        // 模板模式不需要这两个回调
        updateEditingProject: undefined,
        produceEditingProjectVideo: undefined
      };
    } else {
      return {
        ...baseConfig,
        getEditingProjectMaterials: this.createProjectMaterialsGetter(config.id),
        // 导入媒资
        searchMedia: this.createProjectSearchMedia(config.id),
        deleteEditingProjectMaterials: this.createProjectDeleteMaterials(config.id),
        getEditingProject: this.createProjectGetProject(config.id),
        updateEditingProject: this.createProjectUpdater(config.id),
        produceEditingProjectVideo: this.createVideoProducer(config.id),
        // 项目模式不需要模板更新回调
        updateTemplate: undefined
      };
    }
  }

  /**
   * 创建动态资源获取器 - 按照官方文档实现
   */
  private createDynamicSrcGetter() {
    return async (mediaId: string, mediaType: string, mediaOrigin?: 'private' | 'public' | 'mediaURL', inputUrl?: string) => {
      const requestKey = `getDynamicSrc_${mediaId}`;
      return this.executeWithDeduplication(requestKey, async () => {
        try {
          // 构建请求参数
          const params: any = {
            MediaId: mediaId,
            OutputType: "cdn", // 关键：使用CDN输出类型
          };

          // 从媒资库动态获取字体地址的例子，使用 InputURL 查询
          if (mediaType === "font") {
            params.InputURL = inputUrl;
            delete params.MediaId;
          }
          if (mediaOrigin === "mediaURL") {
            params.InputURL = mediaId;
            delete params.MediaId;
          }

          // 自动识别公共素材 - 根据 mediaId 格式判断
          let actualMediaOrigin = mediaOrigin;
          if (!actualMediaOrigin) {
            // 公共素材通常包含特定前缀，如 "public" 或者特殊格式
            if (mediaId && (mediaId.indexOf("public") >= 0 || mediaId.includes("ice-pub"))) {
              actualMediaOrigin = "public";
            } else {
              actualMediaOrigin = "private";
            }
          }

          // 根据来源选择API并调用 - 按照官方文档实现
          let response;
          if (actualMediaOrigin === "public") {
            // 对于公共素材，使用官方文档中的 GetPublicMediaInfo API
            response = await iceNPMManager.getPublicMediaInfo(params);
          } else if (actualMediaOrigin === "mediaURL") {
            // 对于外链素材，使用InputURL参数
            response = await iceNPMManager.getMediaInfoByUrl(params);
          } else {
            // 对于私有素材，使用现有的 getMediaInfo 方法
            response = await iceNPMManager.getMediaInfo(mediaId);
          }

          // 解析响应数据 - 按照官方文档格式
          const fileInfoList = get(response, "MediaInfo.FileInfoList", []);
          let mediaUrl: string = '';
          let maskUrl: string = '';
          let codec: string = '';

          // 按照官方逻辑优先选择 source_file 类型的文件
          let sourceFile = fileInfoList.find((item: any) => {
            return item?.FileBasicInfo?.FileType === "source_file";
          });

          // 如果没有找到 source_file，使用第一个文件
          if (!sourceFile) {
            sourceFile = fileInfoList[0];
          }

          // 查找遮罩文件（用于视频） - 按照官方逻辑
          const maskFile = fileInfoList.find((item: any) => {
            return (
              item?.FileBasicInfo &&
              item?.FileBasicInfo?.FileUrl &&
              item?.FileBasicInfo?.FileUrl.indexOf("_mask") > 0
            );
          });

          if (maskFile) {
            maskUrl = get(maskFile, "FileBasicInfo.FileUrl", '');
          }

          if (sourceFile) {
            mediaUrl = get(sourceFile, "FileBasicInfo.FileUrl", '');
            codec = get(sourceFile, "VideoStreamInfoList[0].CodecName", '');
          }

          // 根据官方文档返回格式
          if (mediaUrl) {
            return {
              url: mediaUrl,
              codec,
              maskUrl,
            };
          }

          return '';
        } catch (error) {
          console.error('获取动态媒资URL失败:', error);

          // 外链地址兜底逻辑 - 按照官方文档
          if (mediaOrigin === "mediaURL") {
            return mediaId;
          }

          return '';
        }
      });
    };
  }

  /**
   * 创建Timeline素材获取器
   */
  private createTimelineMaterialsGetter() {
    return async (params: TimelineMaterial[]) => {
      try {
        if (!params || params.length === 0) return [];

        const mediaIds = params.map(p => p.mediaId);
        const materialsData = await this.getMediaDetailsByIds(mediaIds);
        return transMediaList(materialsData);
      } catch (error) {
        console.error('获取Timeline素材失败:', error);
        return [];
      }
    };
  }

  /**
   * 创建模板项目素材获取器
   */
  private createTemplateProjectMaterialsGetter(templateId: string) {
    return this.createCachedCallback('getEditingProjectMaterials', async () => {
      try {
        const template = await iceNPMManager.getTemplate(templateId, 1);
        if (template.RelatedMediaids) {
          const mediaIdsMap = JSON.parse(template.RelatedMediaids);
          const mediaIds = Object.values(mediaIdsMap).reduce((acc: string[], cur: any) =>
            acc.concat(Array.isArray(cur) ? cur : [cur]), []
          );
          if (mediaIds.length > 0) {
            const materialsData = await this.getMediaDetailsByIds(mediaIds);
            return transMediaList(materialsData);
          }
        }
        return [];
      } catch (error) {
        console.error('获取模板素材失败:', error);
        return [];
      }
    });
  }

  /**
   * 创建云剪辑项目素材获取器
   */
  private createProjectMaterialsGetter(projectId: string) {
    return this.createCachedCallback('getEditingProjectMaterials', async () => {
      try {
        const mediaInfos = await iceNPMManager.getEditingProjectMaterials(projectId);
        return transMediaList(mediaInfos);
      } catch (error) {
        console.error('获取项目素材失败:', error);
        return [];
      }
    });
  }

  /**
   * 模板搜索媒体函数 - 按照官方文档实现
   */
  private createTemplateSearchMedia(templateId: string) {
    return this.createCachedCallback('searchMedia', async () => {
      try {
        // 使用回调函数而不是全局方法 - 不传入mediaType参数
        if (this.materialSelectorCallback) {
          // 调用素材选择器，获取所有选中的素材
          const selectedMaterials = await this.materialSelectorCallback();
          const convertedMaterials = transMediaList(selectedMaterials);
          if (convertedMaterials && convertedMaterials.length > 0) {
            // 按照官方文档的addTemplateMaterials逻辑处理
            const template = await iceNPMManager.getTemplate(templateId, 1);

            // 解析现有的RelatedMediaids - 按照官方parseRelatedMap函数
            let mediaIdsMap: Record<string, string[]> = {};
            try {
              if (template?.RelatedMediaids) {
                mediaIdsMap = JSON.parse(template.RelatedMediaids);
              }
            } catch (ex) {
              // 解析失败时使用空对象
              mediaIdsMap = {};
            }

            // 按照官方文档逻辑遍历素材并分类 - 安全解构
            convertedMaterials.forEach((material) => {
              const { mediaType, mediaId } = material
              if (!mediaIdsMap[mediaType]) {
                mediaIdsMap[mediaType] = [];
              }

              // 避免重复添加
              if (!mediaIdsMap[mediaType].includes(mediaId)) {
                mediaIdsMap[mediaType].push(mediaId);
              }
            });

            console.log('🔍 更新后的 mediaIdsMap:', mediaIdsMap)

            // 更新模板绑定素材 - 按照官方文档UpdateTemplate参数
            const newRelatedMediaids = JSON.stringify(mediaIdsMap);
            await iceNPMManager.updateTemplate(templateId, {
              relatedMediaids: newRelatedMediaids
            });

            console.log('🔍 模板更新成功，返回素材:', selectedMaterials)
            console.log('🔍 转换后的素材数据:', convertedMaterials)
            return convertedMaterials;
          }
        } else {
          console.error('❌ 素材选择器未初始化');
          ElMessage.error('素材选择器未初始化');
        }

        return [];
      } catch (error) {
        console.error('❌ 搜索模板素材失败:', error);
        return [];
      }
    });
  }

  /**
   * 云剪辑搜索媒体函数
   */
  private createProjectSearchMedia(projectId: string) {
    return this.createCachedCallback('searchMedia', async (mediaType: 'video' | 'audio' | 'image' | 'text') => {
      try {
        // 使用回调函数而不是全局方法
        if (this.materialSelectorCallback) {
          const selectedMaterials = await this.materialSelectorCallback();

          if (selectedMaterials && selectedMaterials.length > 0) {
            const convertedMaterials = transMediaList(selectedMaterials);
            const valueObj: MaterialMapsType = {};
            convertedMaterials.reduce((acc, curr) => {
              if (!acc[curr.mediaType]) {
                acc[curr.mediaType] = curr.mediaId;
              } else {
                acc[curr.mediaType] = `${acc[curr.mediaType]},${curr.mediaId}`;
              }
              return acc;
            }, valueObj);
            await iceNPMManager.addEditingProjectMaterials(projectId, JSON.stringify(valueObj));
            return convertedMaterials;
          }
        } else {
          ElMessage.error('素材选择器未初始化');
        }
      } catch (error) {
        return [];
      }
    });
  }

  /**
   * 模板删除素材函数
   */
  private createTemplateDeleteMaterials(templateId: string) {
    return async (mediaId: string, mediaType: string) => {
      try {
        const template = await iceNPMManager.getTemplate(templateId, 1);

        if (template.RelatedMediaids) {
          const mediaIdsMap: Record<string, string[]> = JSON.parse(template.RelatedMediaids);

          if (mediaIdsMap[mediaType] && mediaIdsMap[mediaType].includes(mediaId)) {
            // 使用 splice 方法删除指定素材ID - 与官方逻辑一致
            mediaIdsMap[mediaType].splice(
              mediaIdsMap[mediaType].indexOf(mediaId),
              1
            );
            const newRelatedMediaids = JSON.stringify(mediaIdsMap);

            await iceNPMManager.updateTemplate(templateId, {
              relatedMediaids: newRelatedMediaids
            });

            return true;
          }
        }

        return false;
      } catch (error) {
        console.error('删除模板素材失败:', error);
        return false;
      }
    };
  }

  /**
   * 创建云剪辑删除素材函数
   */
  private createProjectDeleteMaterials(projectId: string) {
    return async (mediaId: string, mediaType: string) => {
      const requestKey = `deleteEditingProjectMaterials_${projectId}_${mediaId}`;
      return this.executeWithDeduplication(requestKey, async () => {
        try {
          const success = await iceNPMManager.deleteEditingProjectMaterials(
            projectId,
            mediaType,
            mediaId
          );
          if (!success) {
            throw new Error('删除项目素材失败');
          }
        } catch (error) {
          console.error('删除项目素材失败:', error);
          throw error;
        }
      });
    };
  }

  /**
   * 获取模板项目函数
   */
  private createTemplateGetProject(templateId: string) {
    return async () => {
      try {
        const template = await iceNPMManager.getTemplate(templateId);
        const timeline = template.Config ? JSON.parse(template.Config) : undefined;

        return {
          timeline,
          projectId: templateId
        };
      } catch (error) {
        console.error('获取模板失败:', error);
        return { timeline: undefined };
      }
    };
  }

  /**
   * 获取云剪辑工程函数
   */
  private createProjectGetProject(projectId: string) {
    return () => {
      const requestKey = `getEditingProject_${projectId}`;
      return this.executeWithDeduplication(requestKey, async () => {
        try {
          const result = await iceNPMManager.initializeFromProject(projectId);
          if (result && result.data) {
            const projectData = result.data;
            return {
              projectId: projectId,
              timeline: projectData.Timeline || undefined,
              modifiedTime: projectData.ModifiedTime || new Date().toISOString(),
              title: projectData.Title || '未命名项目'
            };
          }

          return {
            projectId: projectId,
            timeline: undefined,
            modifiedTime: new Date().toISOString(),
            title: '未命名项目'
          };
        } catch (error) {
          console.error('获取项目失败:', error);
          return {
            projectId: projectId,
            timeline: undefined,
            modifiedTime: new Date().toISOString(),
            title: '未命名项目'
          };
        }
      });
    };
  }

  /**
   * 模板保存函数
   */
  private createTemplateUpdater(templateId: string) {
    return async (data: { coverUrl: string; duration: number; timeline: Timeline; isAuto: boolean }) => {
      try {
        await iceNPMManager.updateTemplate(templateId, {
          coverUrl: data.coverUrl,
          duration: data.duration,
          timeline: data.timeline,
          isAuto: data.isAuto
        });

        if (!data.isAuto) {
          ElMessage.success({
            message: '模板保存成功！',
            duration: 3000,
            showClose: true
          });
        }

        return { projectId: templateId };
      } catch (error) {
        if (!data.isAuto) {
          ElMessage.error({
            message: `模板保存失败: ${error instanceof Error ? error.message : '未知错误'}`,
            duration: 5000,
            showClose: true
          });
        }
        throw error;
      }
    };
  }

  /**
   * 创建云剪辑更新函数
   */
  private createProjectUpdater(projectId: string) {
    return (data: { coverUrl: string; duration: number; timeline: any; isAuto: boolean }) => {
      const requestKey = `updateEditingProject_${projectId}_${Date.now()}`;
      return this.executeWithDeduplication(requestKey, async () => {
        try {
          const success = await iceNPMManager.updateProject({
            projectId: projectId,
            title: undefined,
            timeline: data.timeline
          });

          if (success) {
            if (!data.isAuto) {
              ElMessage.success('项目保存成功');
            }
            return { projectId };
          } else {
            throw new Error('更新项目失败');
          }
        } catch (error) {
          console.error('更新编辑工程失败:', error);
          throw error;
        }
      });
    };
  }

  /**
   * 创建视频生成函数
   */
  private createVideoProducer(projectId: string) {
    return async (data: any) => {
      try {
        console.log('🎬 开始生成视频，项目ID:', projectId);
        console.log('🎬 WebSDK传入的数据:', data);

        // 获取推荐的分辨率和码率，如果没有则使用默认值
        const recommend = data.recommend || {};
        const width = recommend.width || 1920;
        const height = recommend.height || 1080;
        const bitrate = recommend.bitrate || 2000;
        
        // 生成文件名：使用项目ID和时间戳
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileName = `project_${projectId}_${timestamp}.mp4`;

        // 显示开始提交的消息
        ElMessage.info('正在提交视频合成任务...');

        let outputMediaConfig: any;
        let outputMediaTarget = 'oss-object';

        try {
          // 尝试获取存储配置列表
          const storageListResponse = await iceNPMManager.getStorageList();
          const storageList = storageListResponse?.StorageInfoList || [];
          
          if (storageList.length > 0) {
            // 优先使用第一个可用的存储配置
            const storage = storageList[0];
            const storageLocation = storage.StorageLocation;
            
            if (storage.StorageType === 'vod_oss_bucket') {
              // VOD存储配置
              outputMediaTarget = 'vod-media';
              outputMediaConfig = {
                StorageLocation: storageLocation,
                FileName: fileName,
                Bitrate: bitrate,
                Width: width,
                Height: height
              };
            } else {
              // OSS存储配置
              outputMediaTarget = 'oss-object';
              outputMediaConfig = {
                MediaURL: `https://${storageLocation}/video-edit/${fileName}`,
                Bitrate: bitrate,
                Width: width,
                Height: height
              };
            }
            
            console.log('🗂️ 使用动态存储配置:', { storageLocation, storageType: storage.StorageType });
          } else {
            throw new Error('无可用存储配置');
          }
        } catch (storageError) {
          console.warn('⚠️ 获取存储配置失败，使用默认配置:', storageError);
          
          // 使用默认OSS配置作为备选方案
          outputMediaTarget = 'oss-object';
          outputMediaConfig = {
            MediaURL: `https://example-bucket.oss-cn-shanghai.aliyuncs.com/video-edit/${fileName}`,
            Bitrate: bitrate,
            Width: width,
            Height: height
          };
        }

        // 提交媒体生产作业的参数
        const submitParams = {
          ProjectId: projectId,
          OutputMediaTarget: outputMediaTarget,
          OutputMediaConfig: JSON.stringify(outputMediaConfig),
          Source: 'WebSDK',
          UserData: JSON.stringify({
            projectId: projectId,
            createdTime: new Date().toISOString(),
            resolution: `${width}x${height}`,
            bitrate: bitrate,
            outputTarget: outputMediaTarget
          })
        };
        // 调用API提交视频合成作业
        const response = await iceNPMManager.submitMediaProducingJob(submitParams);
        
        console.log('✅ 视频合成任务提交成功:', response);

        // 显示成功消息
        ElMessage.success({
          message: `视频合成任务提交成功！`,
          duration: 5000,
          showClose: true
        });

        // 返回作业信息
        return {
          success: true,
          jobId: response.JobId,
          mediaId: response.MediaId,
          vodMediaId: response.VodMediaId,
          projectId: response.ProjectId
        };

      } catch (error) {
        console.error('❌ 生成视频失败:', error);
        
        // 显示详细的错误信息
        let errorMessage = '视频生成失败';
        if (error instanceof Error) {
          errorMessage = `视频生成失败: ${error.message}`;
        }
        
        ElMessage.error({
          message: errorMessage,
          duration: 5000,
          showClose: true
        });
        
        return Promise.reject(error);
      }
    };
  }

  /**
   * 创建ASR任务提交器
   */
  private createASRJobSubmitter() {
    return async (mediaId: string, startTime: string, duration: string): Promise<ASRResult[]> => {
      try {
        const response = await iceNPMManager.submitASRJob(mediaId, startTime, duration);
        return response || [];
      } catch (error) {
        console.error('智能字幕识别失败:', error);
        return [];
      }
    };
  }

  /**
   * 创建音频生成任务提交器
   */
  private createAudioProduceJobSubmitter() {
    return async (text: string, voice: string, voiceConfig?: VoiceConfig) => {
      try {
        const response = await iceNPMManager.submitAudioProduceJob(text, voice, voiceConfig);
        return response;
      } catch (error) {
        console.error('文字转语音失败:', error);
        throw error;
      }
    };
  }

  /**
   * 创建贴纸分类获取器
   */
  private createStickerCategoriesGetter() {
    return async (): Promise<StickerCategory[]> => {
      try {
        const mediaTagList = await iceNPMManager.listAllPublicMediaTags(
          'sticker',
          this.editorInstance?.version || ''
        );

        return mediaTagList.MediaTagList.map((item: any) => ({
          id: item.MediaTagId,
          name: item.MediaTagNameChinese
        }));
      } catch (error) {
        console.error('获取贴纸分类失败:', error);
        return [];
      }
    };
  }

  /**
   * 创建贴纸获取器
   */
  private createStickersGetter() {
    return async (config: { categoryId?: string; page: number; size: number }): Promise<StickerResponse> => {
      try {
        const result = await iceNPMManager.listPublicMediaBasicInfos({
          PageNo: config.page,
          PageSize: config.size,
          IncludeFileBasicInfo: true,
          MediaTagId: config.categoryId
        });

        const stickers = (result.MediaInfos || []).map((item: any) => ({
          mediaId: item.MediaId,
          src: item.FileInfoList[0]?.FileBasicInfo?.FileUrl
        }));

        return {
          total: result.TotalCount || 0,
          stickers
        };
      } catch (error) {
        console.error('获取贴纸列表失败:', error);
        return { total: 0, stickers: [] };
      }
    };
  }

  /**
   * 创建视频片段拆分导出器
   */
  private createVideoClipsSplitExporter() {
    return async (data: Array<any>) => {
      try {
        // TODO 导出视频片段
        console.log('导出视频片段:', data);
        ElMessage.info('视频片段导出功能待实现');
      } catch (error) {
        throw error;
      }
    };
  }

  /**
   * 创建标记片段导出器
   */
  private createMediaMarksExporter() {
    return async (data: Array<any>) => {
      try {
        console.log('从标记导出:', data);
        ElMessage.info('标记片段导出功能待实现');
      } catch (error) {
        throw error;
      }
    };
  }

  /**
   * 创建视频片段合并导出器
   */
  private createVideoClipsMergeExporter() {
    return async (data: any) => {
      try {
        console.log('合并导出视频:', data);
        ElMessage.info('视频合并导出功能待实现');
      } catch (error) {
        throw error;
      }
    };
  }

  /**
   * 创建音频获取器
   */
  private createAudioGetter() {
    return async (mediaId: string): Promise<string> => {
      try {
        // 获取视频的代理音频地址
        const mediaInfo = await iceNPMManager.getMediaInfo(mediaId);
        // 这里应该返回代理音频地址
        return '';
      } catch (error) {
        return '';
      }
    };
  }

  /**
   * 创建带缓存的回调函数，防止重复调用
   */
  private createCachedCallback<T>(key: string, callback: (...args: any[]) => Promise<T>): (...args: any[]) => Promise<T> {
    const cacheKey = `cached_${key}`;
    const cachedValue = this.callbackCache.get(cacheKey);

    if (cachedValue) {
      return cachedValue;
    }

    const newCallback = async (...args: any[]) => {
      try {
        const result = await callback(...args);
        return result;
      } catch (error) {
        console.error(`执行回调 ${key} 失败:`, error);
        throw error;
      }
    };

    this.callbackCache.set(cacheKey, newCallback);
    return newCallback;
  }

  /**
   * 请求去重执行器
   */
  private executeWithDeduplication<T>(key: string, executor: () => Promise<T>): Promise<T> {
    // 如果相同的请求正在执行，返回相同的Promise
    if (this.activeRequests.has(key)) {
      console.log(`🔄 请求去重: ${key}`);
      return this.activeRequests.get(key) as Promise<T>;
    }

    // 执行新请求
    const promise = executor().finally(() => {
      // 请求完成后清理
      this.activeRequests.delete(key);
    });

    this.activeRequests.set(key, promise);
    return promise;
  }

  /**
   * 优化WebSDK容器，减少事件警告
   */
  private optimizeWebSDKContainer(container: HTMLElement): void {
    try {
      // 查找WebSDK创建的所有子元素
      const allElements = container.querySelectorAll('*');

      allElements.forEach((element: Element) => {
        const htmlElement = element as HTMLElement;

        // 优化事件监听器 - 添加passive事件监听器覆盖
        htmlElement.style.touchAction = 'auto';
        htmlElement.setAttribute('data-passive-events', 'true');

        // 特别处理滚轮事件
        if (htmlElement.addEventListener) {
          // 移除可能存在的非passive wheel事件监听器
          const originalAddEventListener = htmlElement.addEventListener;
          htmlElement.addEventListener = function (type: string, listener: any, options?: any) {
            if (type === 'wheel' || type === 'mousewheel' || type === 'DOMMouseScroll') {
              // 强制设置为passive
              if (typeof options === 'boolean') {
                options = { passive: true, capture: options };
              } else if (typeof options === 'object') {
                options = { ...options, passive: true };
              } else {
                options = { passive: true };
              }
            }
            return originalAddEventListener.call(this, type, listener, options);
          };
        }

        // 如果是画布或视频元素，特别优化
        if (htmlElement.tagName === 'CANVAS' || htmlElement.tagName === 'VIDEO') {
          htmlElement.style.touchAction = 'none';
        }
      });

      // 全局优化滚轮事件
      this.overrideGlobalWheelEvents(container);

    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 全局优化滚轮事件，防止passive警告
   */
  private overrideGlobalWheelEvents(container: HTMLElement): void {
    try {
      // 在WebSDK容器上添加passive wheel事件监听器
      const wheelHandler = (e: Event) => {
        // 不阻止默认行为，让WebSDK正常处理
      };

      container.addEventListener('wheel', wheelHandler, { passive: true });
      (container as any).addEventListener('mousewheel', wheelHandler, { passive: true });
      (container as any).addEventListener('DOMMouseScroll', wheelHandler, { passive: true });

    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 批量获取媒资详细信息
   */
  private async getMediaDetailsByIds(mediaIds: string[]): Promise<any[]> {
    try {
      // 调用批量获取媒资信息API，传入正确的对象参数
      const response = await iceNPMManager.batchGetMediaInfos({ mediaIds });

      // 正确解析响应数据结构
      let mediaInfos = [];
      if (response?.data?.MediaInfos) {
        mediaInfos = response.data.MediaInfos;
      } else if ((response as any)?.MediaInfos) {
        mediaInfos = (response as any).MediaInfos;
      } else if (Array.isArray(response)) {
        mediaInfos = response;
      } else {
        return [];
      }

      return Array.isArray(mediaInfos) ? mediaInfos.filter(Boolean) : [];
    } catch (error) {
      return [];
    }
  }
  /**
 * 动态获取水印列表
 */
  private async getDynamicWatermarks(): Promise<any[]> {
    try {
      // 调用媒资库API获取水印图片
      const response = await iceNPMManager.listPublicMediaBasicInfos({
        BusinessType: 'watermark', // 假设水印的业务类型是 watermark
        PageNo: 1,
        PageSize: 50,
        IncludeFileBasicInfo: true,
      });

      const watermarks = (response?.MediaInfos || []).map((watermarkInfo: any) => {
        const basicInfo = watermarkInfo.MediaBasicInfo || watermarkInfo;
        const fileInfo = watermarkInfo.FileInfoList?.[0]?.FileBasicInfo;

        return {
          url: fileInfo?.FileUrl,
          width: 0.2, // 默认宽度比例
          height: 0.1, // 默认高度比例
          x: 0.8, // 默认X位置
          y: 0.9, // 默认Y位置
          opacity: 0.8, // 默认透明度
          name: basicInfo.Title || '水印'
        };
      }).filter((item: any) => item.url); // 过滤掉没有URL的水印

      return watermarks;
    } catch (error) {
      console.error('获取动态水印列表失败:', error);
      return [];
    }
  }
  //   /**
  //  * 将前端选择器返回的素材转换为WebSDK需要的格式
  //  */
  //   private formatMaterialsForWebSDK(selectedMaterials: any[], mediaType: string): any[] {
  //     try {
  //       if (!selectedMaterials || !Array.isArray(selectedMaterials)) {
  //         return [];
  //       }

  //       return selectedMaterials.map((material) => {
  //         if (!material || !material.mediaId) {
  //           console.warn('素材缺少必要的 mediaId:', material);
  //           return null;
  //         }

  //         // 按照 WebSDK 官方文档格式创建结果对象
  //         const result: any = {
  //           mediaId: material.mediaId,
  //           mediaType: mediaType
  //         };

  //         if (mediaType === 'video') {
  //           result.video = {
  //             title: material.title || material.name || material.fileName || material.mediaId || '未知视频',
  //             duration: Number(material.duration) || 0,
  //             width: Number(material.width) || 0,
  //             height: Number(material.height) || 0,
  //             bitrate: Number(material.bitrate) || 0,
  //             coverUrl: material.coverUrl || material.coverURL || ''
  //           };

  //           // 如果有雪碧图信息，添加雪碧图配置
  //           if (material.spriteImages || material.sprites) {
  //             try {
  //               const spriteData = material.spriteImages || material.sprites;
  //               if (spriteData && Array.isArray(spriteData) && spriteData.length > 0) {
  //                 const sprite = spriteData[0];
  //                 if (sprite.Config) {
  //                   const config = typeof sprite.Config === 'string' ? JSON.parse(sprite.Config) : sprite.Config;
  //                   result.video.spriteConfig = {
  //                     num: config?.Num,
  //                     lines: config?.SpriteSnapshotConfig?.Lines,
  //                     cols: config?.SpriteSnapshotConfig?.Columns,
  //                     cellWidth: config?.SpriteSnapshotConfig?.CellWidth,
  //                     cellHeight: config?.SpriteSnapshotConfig?.CellHeight
  //                   };
  //                   result.video.sprites = sprite.SnapshotUrlList || [];
  //                 }
  //               }
  //             } catch (e) {
  //               // 静默处理雪碧图解析错误
  //             }
  //           }
  //         } else if (mediaType === 'audio') {
  //           result.audio = {
  //             title: material.title || material.name || material.fileName || material.mediaId || '未知音频',
  //             duration: Number(material.duration) || 0,
  //             coverURL: material.coverUrl || material.coverURL || ''
  //           };
  //         } else if (mediaType === 'image') {
  //           result.image = {
  //             title: material.title || material.name || material.fileName || material.mediaId || '未知图片',
  //             coverUrl: material.coverUrl || material.coverURL || '',
  //             width: Number(material.width) || 0,
  //             height: Number(material.height) || 0
  //           };
  //         }
  //         else if (mediaType === 'text') {
  //           result.font = {
  //             title: material.title || material.name || material.fileName || material.mediaId || '自定义字体',
  //             fontFamily: material.fontFamily || material.name || material.title || material.fileName || '自定义字体',
  //           };
  //         }

  //         return result;
  //       }).filter(Boolean); // 过滤掉 null 值
  //     } catch (error) {
  //       console.error('格式化素材数据失败:', error);
  //       return [];
  //     }
  //   }

  /**
   * 销毁WebSDK
   */
  destroy(): void {
    try {
      this.isInitializing = false;
      this.callbackCache.clear();
      this.activeRequests.clear();
      this.materialSelectorCallback = null; // 清理回调

      if (this.editorInstance) {
        this.editorInstance.destroy(false);
        this.editorInstance = null;
      }

      iceNPMManager.destroy();
      this.currentId = null;
      this.currentMode = null;
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 获取当前编辑器实例
   */
  getInstance(): AliyunVideoEditor | null {
    return this.editorInstance;
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.editorInstance !== null;
  }

  /**
   * 获取当前模式
   */
  getCurrentMode(): EditorMode | null {
    return this.currentMode;
  }
}

// 导出单例
export const iceWebSDK = new UnifiedICEWebSDK();
export default iceWebSDK;