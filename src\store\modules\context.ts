import modal from "@/plugins/modal";
import { defineStore } from "pinia";

type Electron = {
    ipcRenderer: {
        invoke: (title: string, data?: any) => Promise<any>;
        send: (title: string, data: any) => void;
        on: (event: string, callback: (event: any, data: any) => void) => void;
        once: (event: string, callback: (event: any, data: any) => void) => void;
    }
};

const useContext = defineStore(
    'context',
    {
        state: () => {
            let _electron: Electron | undefined = undefined
            if ('electron' in window) {
                _electron = window.electron as Electron
            }
            return {
                electron: _electron
            }
        },
        actions: {
            invoke(title: string, data?: any) {
                if (this.electron === undefined) {
                    modal.alertError('请下载客户端')
                    return Promise.reject()
                } else {
                    if (arguments.length === 1) {
                        return this.electron.ipcRenderer.invoke(title);
                    } else {
                        return this.electron.ipcRenderer.invoke(title, JSON.parse(JSON.stringify(data)));
                    }
                }
            },
            send(title: string, data: any) {
                if (this.electron === undefined) {
                    modal.alertError('请下载客户端')
                    return Promise.reject()
                } else {
                    if (!data) { 
                        return this.electron.ipcRenderer.send(title,data);
                    } else {
                        return this.electron.ipcRenderer.send(title, JSON.parse(JSON.stringify(data)));
                    }
                }
            },
            on(title: string, callback: (event: any, ata: any) => void) {
                if (this.electron === undefined) {
                    //modal.alertError('请下载客户端')
                    console.error('请下载客户端')
                } else {
                    this.electron.ipcRenderer.on(title, callback)
                }
            },
            once(title: string, callback: (event: any, ata: any) => void) {
                if (this.electron === undefined) {
                    console.error('请下载客户端')
                } else {
                    this.electron.ipcRenderer.once(title, callback)
                }
            }
        }
    }
)
export default useContext;