<template>
  <canvas ref="canvas" class="particle-canvas"></canvas>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { LogoImg, ParticleCanvas } from '@/utils/particle'
import logoImg from '@/assets/logo/logo.png'

const canvas = ref(null)
let particleCanvas = null

onMounted(async () => {
  // 设置canvas大小
  const updateCanvasSize = () => {
    const container = canvas.value.parentElement
    canvas.value.width = container.clientWidth
    canvas.value.height = container.clientHeight
  }
  
  updateCanvasSize()
  
  // 创建粒子系统
  particleCanvas = new ParticleCanvas(canvas.value)
  
  try {
    // 创建Logo图片并加载
    const logo = new LogoImg()
    await logo.loadImage(logoImg)
    
    // 加载图片到粒子系统
    particleCanvas.changeImg(logo)
  } catch(error) {
    console.error('加载图片错误:', error)
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', onResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', onResize)
})

// 窗口大小改变
const onResize = () => {
  if(canvas.value && particleCanvas) {
    const container = canvas.value.parentElement
    canvas.value.width = container.clientWidth
    canvas.value.height = container.clientHeight
    particleCanvas.resize(canvas.value.width, canvas.value.height)
  }
}
</script>

<style scoped>
.particle-canvas {
  display: block;
  width: 100%;
  height: 100%;
  background: transparent;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  pointer-events: auto;
}
</style> 