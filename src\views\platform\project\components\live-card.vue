<script setup>
import useLiveStore from '@/store/modules/live'
import { listLive, getLive, delLive, addLive, updateLive, getLiveOption, getLiveInfo } from "@/api/platform/live";
import { getProject } from "@/api/platform/project";
import modal from '@/plugins/modal';
import { useRoute, useRouter } from 'vue-router';
import useSoftWareStore from "@/store/modules/software";
import { onMounted, onUnmounted } from 'vue';
const route = useRoute();
const store = useLiveStore()
const liveList = ref([])
const loading = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 100,
  projectId: '',
})
// 获取直播列表
function getList() {
  // 如果没有选中项目,不加载列表
  if (!store.projectId) return

  loading.value = true;
  queryParams.value.projectId = parseInt(store.projectId)
  listLive(queryParams.value).then(response => {
    liveList.value = response.rows;
    loading.value = false;
  })
}

watch(() => store.projectId, (newId) => {
  if (newId !== null) getList()
})

onMounted(() => {
  if (store.projectId) {
    getList()
  }
})

onUnmounted(() => {
  liveList.value = []
  selectLiveId.value = null
})

const selectLiveId = ref(null)

function chooseLive(item) {
  if (store.status.isLive == 1) {
    modal.alertWarning("请您先关闭当前直播！")
    return
  }
  if (store.status.isBarrage == 1 || store.status.isBarrage == 2) {
    modal.alertWarning("请您先关闭获取弹幕程序！")
    return
  }
  if (store.status.isReply == 1 || store.status.isReply == 2) {
    modal.alertWarning("请您先关闭回复弹幕程序！")
    return
  }
  /*   if (selectLiveId.value !== item.liveId) { */
  store.liveing = {}
  store.status.isCheck = false
  store.status.isBarrage = 0
  store.status.isLive = 0
  store.status.isReply = 0
  selectLiveId.value = null;
  store.liveId = item.liveId
  selectLiveId.value = item.liveId;
  store.liveing = item
  // }
}
const allDisabled = ref(false)
const isClear = computed(() => (store.status.isLive == 1) || (store.status.isBarrage == 1 || store.status.isBarrage == 2) || (store.status.isReply == 1 || store.status.isReply == 2));
function handleLiveing() {
  if (!(JSON.stringify(store.liveing) === '{}')) {
    if (route.params.projectId == store.liveing.projectId) {
      allDisabled.value = false
      selectLiveId.value = store.liveing.liveId
    } else {
      allDisabled.value = true
      getProject(store.liveing.projectId).then(res => {
        if (res.code === 200) {
          modal.alertError(`请先关闭项目"${res.data.projectTitle}"中直播名称为"${store.liveing.liveName}"的直播。`);
        }
      })

    }
  }
}


watch(isClear, (newVal) => {
  if (!newVal) {
    store.liveing = {}
    selectLiveId.value = null;
  }
}, { immediate: true });
handleLiveing()

async function openLive(row) {
  await updateLive(row)
  await useSoftWareStore().openLiveWindow(row.liveId)
}

if (useSoftWareStore().isSoftWare) {
  useSoftWareStore().initLiveDb()
}
</script>
<template>
  <div class="live-card">
    <!-- 直播内容区 -->
    <div class="live-content" v-loading="loading">
      <el-empty v-if="liveList.length === 0" description="暂无直播" />

      <template v-else>
        <div class="live-list">
          <div v-for="item in liveList" :key="item.liveId" class="live-item" :class="{
            'is-selected': selectLiveId === item.liveId,
            'is-disabled': allDisabled
          }" @click="chooseLive(item)">
            <div class="live-info">
              <div class="live-icon">
                <el-icon>
                  <VideoCamera />
                </el-icon>
              </div>
              <div class="live-name">{{ item.liveName }}</div>
            </div>

            <div class="live-actions">
              <el-tooltip content="开始直播" placement="top" :disabled="allDisabled">
                <el-button type="success" :disabled="allDisabled" circle size="small" @click.stop="openLive(item)">
                  <el-icon>
                    <VideoPlay />
                  </el-icon>
                </el-button>
              </el-tooltip>
            </div>

            <div class="hover-indicator"></div>
            <div class="selected-indicator"></div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.live-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);

  .live-content {
    flex: 1;
    overflow-y: auto;
    padding: 4px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color-lighter);
      border-radius: 4px;
    }

    &:hover::-webkit-scrollbar-thumb {
      background: var(--el-border-color);
    }

    .live-list {
      display: flex;
      flex-direction: column;
    }

    .live-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 12px;
      cursor: pointer;
      border-radius: 8px;
      margin: 2px 0;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;

      .hover-indicator {
        position: absolute;
        inset: 0;
        background: var(--el-color-primary-light-9);
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      .selected-indicator {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%) scaleY(0);
        width: 3px;
        height: 70%;
        background: var(--el-color-primary);
        border-radius: 2px;
        transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      }

      &:hover {
        .hover-indicator {
          opacity: 1;
        }

        .live-info {
          .live-icon {
            background: var(--el-color-primary-light-8);
            transform: scale(1.1);

            .el-icon {
              color: var(--el-color-primary);
            }
          }
        }

        .live-actions {
          opacity: 1;
          transform: translateX(0);
        }
      }

      &.is-selected {
        .selected-indicator {
          transform: translateY(-50%) scaleY(1);
        }

        .live-info {
          .live-icon {
            background: var(--el-color-primary);

            .el-icon {
              color: white;
            }
          }

          .live-name {
            color: var(--el-color-primary);
            font-weight: 600;
          }
        }
      }

      &.is-disabled {
        cursor: not-allowed;
        opacity: 0.6;

        &:hover {
          .hover-indicator {
            opacity: 0;
          }

          .live-info .live-icon {
            transform: none;
          }
        }
      }

      .live-info {
        position: relative;
        z-index: 1;
        flex: 1;
        min-width: 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .live-icon {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          background: var(--el-fill-color-light);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

          .el-icon {
            font-size: 16px;
            color: var(--el-text-color-secondary);
            transition: color 0.2s ease;
          }
        }

        .live-name {
          font-size: 14px;
          color: var(--el-text-color-primary);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          transition: all 0.2s ease;
        }
      }

      .live-actions {
        position: relative;
        z-index: 1;
        opacity: 0;
        transform: translateX(8px);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

        .el-button {
          --el-button-size: 32px;
          --el-button-bg-color: var(--el-color-success);
          --el-button-border-color: transparent;
          --el-button-hover-bg-color: var(--el-color-success);
          --el-button-hover-border-color: transparent;
          --el-button-hover-text-color: white;
          --el-button-active-bg-color: var(--el-color-success-dark-2);
          box-shadow: 0 2px 8px rgba(var(--el-color-success-rgb), 0.35);

          .el-icon {
            font-size: 16px;
            color: white;
            transition: transform 0.2s ease;
          }

          &:not(:disabled) {
            &:hover {
              transform: scale(1.08);
              box-shadow: 0 4px 12px rgba(var(--el-color-success-rgb), 0.45);

              .el-icon {
                transform: scale(1.1);
              }
            }

            &:active {
              transform: scale(0.95);
              box-shadow: 0 2px 4px rgba(var(--el-color-success-rgb), 0.25);
            }
          }

          &:disabled {
            --el-button-bg-color: var(--el-color-success-light-5);
            --el-button-border-color: transparent;
            opacity: 0.5;
            box-shadow: none;
          }
        }
      }
    }
  }
}

:deep(.el-empty) {
  padding: 32px 0;
  opacity: 0.8;

  .el-empty__image {
    width: 64px;
    height: 64px;
  }

  .el-empty__description {
    margin-top: 8px;
    font-size: 13px;
    color: var(--el-text-color-secondary);
  }
}
</style>