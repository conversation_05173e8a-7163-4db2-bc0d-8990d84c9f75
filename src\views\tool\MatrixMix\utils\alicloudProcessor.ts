/**
 * 阿里云多轨道处理器
 * 用于处理视频编辑中的轨道创建、片段移动等操作
 */

import { reorderTrackIdsForInsertion } from './trackIdReorderer';

export interface TrackOperationResult {
  success: boolean
  timeline?: any
  error?: string
  newTrackIndex?: number
}

/**
 * 阿里云轨道处理器
 */
export class AlicloudProcessor {
  
  /**
   * 生成新轨道并移动片段
   * @param timeline 原始timeline数据
   * @param trackType 轨道类型
   * @param insertPosition 插入位置
   * @param clipToMove 要移动的片段数据
   * @param newStartTime 新的开始时间
   */
  generateNewTrackAndMoveClip(
    timeline: any,
    trackType: 'Video' | 'Audio' | 'Subtitle',
    insertPosition: 'top' | 'bottom' = 'bottom',
    clipToMove: any,
    newStartTime: number
  ): TrackOperationResult {
    try {
      if (!timeline) {
        return { success: false, error: 'Timeline数据不存在' }
      }

      // 深拷贝timeline
      const newTimeline = JSON.parse(JSON.stringify(timeline))
      
      // 1. 先从源轨道移除片段
      const removeResult = this.removeClipFromSourceTrack(newTimeline, clipToMove, trackType)
      if (!removeResult.success) {
        return removeResult
      }
      
      // 2. 🚀 使用智能ID重排系统生成新轨道ID
      console.log('🎯 使用智能ID重排系统:', { trackType, insertPosition });
      const reorderResult = reorderTrackIdsForInsertion(newTimeline, trackType, insertPosition);
      
      if (!reorderResult.success) {
        console.error('❌ ID重排失败:', reorderResult.error);
        return { success: false, error: `ID重排失败: ${reorderResult.error}` };
      }
      
      console.log('✅ ID重排成功:', {
        newTrackId: reorderResult.newTrackId,
        adjustedTracks: reorderResult.adjustedTracks
      });
      
      // 3. 创建新轨道对象
      const newTrack = this.createNewTrackObject(reorderResult.newTrackId, trackType)
      
      // 4. 将片段添加到新轨道
      const updatedClip = { ...clipToMove }
      updatedClip.TimelineIn = newStartTime
      updatedClip.TimelineOut = newStartTime + updatedClip.Duration
      updatedClip.TrackId = reorderResult.newTrackId
      
      // 添加片段到对应轨道类型
      if (trackType === 'Video') {
        newTrack.VideoTrackClips = [updatedClip]
      } else if (trackType === 'Audio') {
        newTrack.AudioTrackClips = [updatedClip]
      } else if (trackType === 'Subtitle') {
        newTrack.VideoTrackClips = [updatedClip] // 字幕轨道也使用VideoTrackClips
      }
      
      // 5. 插入新轨道到timeline（简化版）
      const insertResult = this.insertTrackToTimeline(newTimeline, newTrack, trackType)
      
      if (insertResult.success) {
        // 6. 计算新轨道在显示列表中的索引
        const newTrackIndex = this.calculateTrackDisplayIndex(newTimeline, reorderResult.newTrackId, trackType)
        
        return {
          success: true,
          timeline: newTimeline,
          newTrackIndex: newTrackIndex
        }
      } else {
        return insertResult
      }

    } catch (error: any) {
      console.error('❌ 轨道生成失败:', error);
      return {
        success: false,
        error: error.message || '轨道生成和片段移动失败'
      }
    }
  }

  /**
   * 从源轨道移除片段
   */
  private removeClipFromSourceTrack(timeline: any, clipToMove: any, trackType: string): TrackOperationResult {
    try {
      let found = false
      
      if (trackType === 'Video' || trackType === 'Subtitle') {
        if (timeline.VideoTracks) {
          timeline.VideoTracks.forEach((track: any) => {
            if (track.VideoTrackClips) {
              const clipIndex = track.VideoTrackClips.findIndex((c: any) => c.Id === clipToMove.Id)
              if (clipIndex !== -1) {
                track.VideoTrackClips.splice(clipIndex, 1)
                found = true
              }
            }
          })
        }
      } else if (trackType === 'Audio') {
        if (timeline.AudioTracks) {
          timeline.AudioTracks.forEach((track: any) => {
            if (track.AudioTrackClips) {
              const clipIndex = track.AudioTrackClips.findIndex((c: any) => c.Id === clipToMove.Id)
              if (clipIndex !== -1) {
                track.AudioTrackClips.splice(clipIndex, 1)
                found = true
              }
            }
          })
        }
      }
      
      if (!found) {
        return { success: false, error: `未找到要移除的片段 ${clipToMove.Id}` }
      }
      
      return { success: true }
      
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  /**
   * 计算轨道在显示列表中的索引
   */
  private calculateTrackDisplayIndex(timeline: any, trackId: number, trackType: string): number {
    if (trackType === 'Video') {
      const videoTracks = timeline.VideoTracks?.filter((track: any) => track.Type !== 'Subtitle') || []
      // 按ID降序排序后查找索引
      videoTracks.sort((a: any, b: any) => b.Id - a.Id)
      return videoTracks.findIndex((track: any) => track.Id === trackId)
    } else if (trackType === 'Audio') {
      const audioTracks = timeline.AudioTracks || []
      audioTracks.sort((a: any, b: any) => b.Id - a.Id)
      return audioTracks.findIndex((track: any) => track.Id === trackId)
    } else if (trackType === 'Subtitle') {
      const subtitleTracks = timeline.VideoTracks?.filter((track: any) => track.Type === 'Subtitle') || []
      subtitleTracks.sort((a: any, b: any) => b.Id - a.Id)
      return subtitleTracks.findIndex((track: any) => track.Id === trackId)
    }
    return -1
  }

  /**
   * 创建新轨道对象
   */
  private createNewTrackObject(trackId: number, trackType: string): any {
    const baseTrack = {
      Id: trackId,
      Type: trackType,
      Visible: true,
      Disabled: false,
      Count: 0
    }
    
    if (trackType === 'Video') {
      return {
        ...baseTrack,
        VideoTrackClips: []
      }
    } else if (trackType === 'Subtitle') {
      return {
        ...baseTrack,
        VideoTrackClips: []
      }
    } else if (trackType === 'Audio') {
      return {
        ...baseTrack,
        AudioTrackClips: []
      }
    }
    
    return baseTrack
  }

  /**
   * 插入轨道到Timeline（简化版 - ID重排已处理顺序）
   */
  private insertTrackToTimeline(
    timeline: any,
    newTrack: any,
    trackType: string
  ): TrackOperationResult {
    try {
      if (trackType === 'Video' || trackType === 'Subtitle') {
        if (!timeline.VideoTracks) {
          timeline.VideoTracks = []
        }
        timeline.VideoTracks.push(newTrack)
        // 按ID排序确保显示顺序正确（ID大的在上面）
        timeline.VideoTracks.sort((a: any, b: any) => b.Id - a.Id)
        
      } else if (trackType === 'Audio') {
        if (!timeline.AudioTracks) {
          timeline.AudioTracks = []
        }
        timeline.AudioTracks.push(newTrack)
        // 按ID排序确保显示顺序正确（ID大的在上面）
        timeline.AudioTracks.sort((a: any, b: any) => b.Id - a.Id)
      }
      
      return { success: true }
      
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }
}

/**
 * 创建阿里云处理器实例
 */
export function createAlicloudProcessor(): AlicloudProcessor {
  return new AlicloudProcessor()
}
