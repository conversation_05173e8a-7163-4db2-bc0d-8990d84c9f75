<template>
  <div>
    <el-dialog title="上传进度" v-model="uploadVisible" width="600px" :close-on-click-modal="false" :close-on-press-escape="false" :show-close>
      <div class="upload-progress">
        <div class="progress-info" v-if="!isFastUpload">
          <span>总体进度: {{ totalProgress }}%</span>
          <span>已上传: {{ uploadedSize }}</span>
          <span>总大小: {{ totalSize }}</span>
        </div>
        <el-progress :percentage="totalProgress" :status="totalProgressStatus" v-if="!isFastUpload"></el-progress>
        <div class="fast-upload-status" v-if="isFastUpload">
          <el-alert type="success" :title="`文件秒传成功: ${props.fileName}`" description="系统检测到相同内容的文件，已自动完成上传" show-icon :closable="false">
            <template #icon><el-icon class="fast-icon"><Lightning /></el-icon></template>
          </el-alert>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, defineEmits, defineProps, onUnmounted } from 'vue';
import { initImageChunk, uploadImageChunk, completeImageChunk, startEditImage, finishEditImage } from '@/api/platform/image';
import { Lightning } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  fileObj: { type: [File, null], default: null },
  fileName: { type: String, default: '' },
  imageId: { type: [Number, null], default: null }
});
const emit = defineEmits(['upload-success', 'upload-error', 'upload-progress']);
const uploadVisible = ref(false);
const totalProgress = ref(0);
const totalProgressStatus = computed(() => totalProgress.value === 100 ? 'success' : '');
const isFastUpload = ref(false);
const fastUploadData = ref(null);
const formatFileSize = (size) => {
  if (size < 1024) return size + ' B';
  if (size < 1024 * 1024) return (size / 1024).toFixed(2) + ' KB';
  if (size < 1024 * 1024 * 1024) return (size / 1024 / 1024).toFixed(2) + ' MB';
  return (size / 1024 / 1024 / 1024).toFixed(2) + ' GB';
}

const totalSize = computed(() => props.fileObj ? formatFileSize(props.fileObj.size) : '0 B');
const uploadedSize = computed(() => props.fileObj && totalProgress.value ? formatFileSize(props.fileObj.size * (totalProgress.value / 100)) : '0 B');

// 重置状态
const resetStatus = () => {
  totalProgress.value = 0;
  isFastUpload.value = false;
  fastUploadData.value = null;
};

// 计算文件分片
const createFileChunks = (file) => {
  const chunks = [];
  const DEFAULT_CHUNK_SIZE = 10 * 1024 * 1024; // 10MB
  let start = 0;
  while (start < file.size) {
    chunks.push(file.slice(start, start + DEFAULT_CHUNK_SIZE));
    start += DEFAULT_CHUNK_SIZE;
  }
  return chunks;
};

// 上传文件
const startChunkUpload = async () => {
  if (!props.fileObj || !props.fileName) {
    ElMessage.error('文件或文件名不能为空');
    return;
  }
  uploadVisible.value = true;
  resetStatus();
  try {
    const chunks = createFileChunks(props.fileObj);
    const totalChunks = chunks.length;
    let uploadedChunks = 0;
    // 初始化上传
    const { data: initResult } = await initImageChunk(props.fileName, props.fileObj.name, props.fileObj.size);
    if (!initResult.uploadId || !initResult.filePath) {
      throw new Error('初始化上传失败');
    }
    const { uploadId, filePath } = initResult;
    const partETags = [];
    // 上传第一个分片，检查是否触发秒传
    const firstChunk = chunks[0];
    const file = new File([firstChunk], `${props.fileName}_0`);
    const { data: firstChunkResponse } = await uploadImageChunk(uploadId, filePath, 0, file);
    // 检查是否秒传成功
    if (firstChunkResponse.fastUpload) {
      isFastUpload.value = true;
      fastUploadData.value = firstChunkResponse;
      totalProgress.value = 100;
      emit('upload-progress', 100);
      setTimeout(() => {
        uploadVisible.value = false;
        emit('upload-success', { success: true, fastUpload: true, data: fastUploadData.value });
        resetStatus(); }, 1500);
      return;
    }
    // 继续上传剩余分片
    partETags.push({ partNumber: 1, etag: firstChunkResponse.etag });
    uploadedChunks++;
    totalProgress.value = Math.round((uploadedChunks / totalChunks) * 100);
    emit('upload-progress', totalProgress.value);
    // 上传剩余分片
    for (let i = 1; i < chunks.length; i++) {
      const file = new File([chunks[i]], `${props.fileName}_${i}`);
      const { data: response } = await uploadImageChunk(uploadId, filePath, i, file);
      partETags.push({ partNumber: i + 1, etag: response.etag });
      uploadedChunks++;
      totalProgress.value = Math.round((uploadedChunks / totalChunks) * 100);
      emit('upload-progress', totalProgress.value);
    }
    // 完成上传
    const formattedPartETags = partETags.map(item => ({partNumber: item.partNumber, ETag: item.etag }));
    const { data: completeResult } = await completeImageChunk(props.fileName, uploadId, filePath,formattedPartETags);
    setTimeout(() => {
      uploadVisible.value = false;
      emit('upload-success', { success: true, fastUpload: false, data: completeResult });
      resetStatus(); }, 1000);
  } catch (error) {
    ElMessage.error('上传失败: ' + (error.message || '未知错误'));
    uploadVisible.value = false;
    emit('upload-error', error);
    resetStatus();
  }
};

// 编辑上传
const startEditUpload = async (imageId, imageName) => {
  if (!props.fileObj) {
    const result = await finishEditImage(imageId, null, null, null, imageName);
    emit('upload-success', { success: true, data: result });
    return;
  }
  uploadVisible.value = true;
  resetStatus();
  try {
    const chunks = createFileChunks(props.fileObj);
    const totalChunks = chunks.length;
    let uploadedChunks = 0;
    // 初始化编辑上传
    const { data: initResult } = await startEditImage(imageId, props.fileName, props.fileObj.size, imageName);
    if (!initResult.uploadId || !initResult.filePath) {
      throw new Error('初始化编辑上传失败');
    }
    const { uploadId, filePath } = initResult;
    const partETags = [];
    // 上传第一个分片，检查是否触发秒传
    const firstChunk = chunks[0];
    const file = new File([firstChunk], `${props.fileName}_0`);
    const { data: firstChunkResponse } = await uploadImageChunk(uploadId, filePath, 0, file);
    // 检查是否秒传成功
    if (firstChunkResponse.fastUpload) {
      isFastUpload.value = true;
      fastUploadData.value = firstChunkResponse;
      totalProgress.value = 100;
      emit('upload-progress', 100);
      setTimeout(() => {
        uploadVisible.value = false;
        emit('upload-success', { success: true, fastUpload: true, data: fastUploadData.value });
        resetStatus(); }, 1500);
      return;
    }
    // 继续上传剩余分片
    partETags.push({ partNumber: 1, etag: firstChunkResponse.etag });
    uploadedChunks++;
    totalProgress.value = Math.round((uploadedChunks / totalChunks) * 100);
    emit('upload-progress', totalProgress.value);
    // 上传剩余分片
    for (let i = 1; i < chunks.length; i++) {
      const file = new File([chunks[i]], `${props.fileName}_${i}`);
      const { data: response } = await uploadImageChunk(uploadId, filePath, i, file);
      partETags.push({ partNumber: i + 1, etag: response.etag });
      uploadedChunks++;
      totalProgress.value = Math.round((uploadedChunks / totalChunks) * 100);
      emit('upload-progress', totalProgress.value);
    }
    // 完成编辑上传
    const formattedPartETags = partETags.map(item => ({partNumber: item.partNumber, ETag: item.etag }));
    const { data: completeResult }=await finishEditImage(imageId, uploadId, filePath, formattedPartETags, imageName);
    setTimeout(() => {
      uploadVisible.value = false;
      emit('upload-success', { success: true, fastUpload: false, data: completeResult });
      resetStatus(); }, 1000);
  } catch (error) {
    ElMessage.error('上传失败: ' + (error.message || '未知错误'));
    uploadVisible.value = false;
    emit('upload-error', error);
    resetStatus();
  }
};
onUnmounted(resetStatus);
defineExpose({ startChunkUpload, startEditUpload });
</script>  

<style scoped lang="scss">
.upload-progress {
  padding: 10px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.fast-upload-status {
  margin-top: 20px;
  
  .fast-icon {
    color: #67c23a;
    font-size: 18px;
  }
}

:deep(.el-progress-bar__inner) {
  transition: width 0.8s cubic-bezier(0.23, 1, 0.32, 1);
  
  .el-progress.is-success & {
    background-color: #67c23a;
  }
  
  .el-progress.is-exception & {
    background-color: #f56c6c;
  }
}
</style>    