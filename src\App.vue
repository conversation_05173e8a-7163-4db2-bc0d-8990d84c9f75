<template>
  <div class="app-wrapper">
    <!-- 自定义窗口栏 -->
    <div v-if="useSoftWareStore().isSoftWare" class="top-section">
      <div class="window-bar">
        <div class="drag-area"></div>
        <div class="window-controls">
          <div class="control-button minimize" @click="minimizeIndex">
            <el-icon><Minus /></el-icon>
          </div>
          <div class="control-button maximize" @click="maximizeIndex">
            <el-icon>
              <component :is="isMaximized ? 'Crop' : 'FullScreen'" />
            </el-icon>
          </div>
          <div class="control-button close" @click="closeIndex">
            <el-icon><Close /></el-icon>
          </div>
        </div>
      </div>
    </div>
    
    <div v-loading="!settingsStore.inited" class="main-content" :class="{ 'is-electron': useSoftWareStore().isSoftWare }">
      <router-view />
    </div>
  </div>
</template>

<script setup>
import useSettingsStore from '@/store/modules/settings'
import { handleThemeStyle } from '@/utils/theme'
import { Minus, FullScreen, Close, Crop } from '@element-plus/icons-vue'
import { ref, computed } from 'vue'
import useSoftWareStore from "@/store/modules/software";
import modal from './plugins/modal';
useSoftWareStore().watchDebug()
useSoftWareStore().watchClearIndexLiveStore()
const settingsStore = useSettingsStore()
const isMaximized = ref(false)
async function minimizeIndex() {
  await useSoftWareStore().minimizeIndexWindow()
}
async function maximizeIndex() {
  await useSoftWareStore().maximizeIndexWindow(isMaximized.value)
  isMaximized.value = !isMaximized.value
}
async function closeIndex() {
  const res = await useSoftWareStore().isExistLiveWindow()
  if (res) {
    modal.confirm('您还有正在直播的窗口，是否确认关闭？').then(() => {
      useSoftWareStore().closeLiveWindow()
      useSoftWareStore().closeIndexWindow()
    })
  }else {
    await useSoftWareStore().closeIndexWindow()
  }
}
onMounted(() => {
  nextTick(() => {
    settingsStore.initSetting(() => {
      // 初始化主题样式
      handleThemeStyle(settingsStore.theme)
    })
  })
})
</script>

<style lang="scss" scoped>
.app-wrapper {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: #fff;
  overflow: hidden;
}

.top-section {
  position: relative;
  z-index: 2001;
}

.window-bar {
  height: 28px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  -webkit-app-region: drag;
  user-select: none;
}

.main-content {
  height: 100%;
  width: 100%;
  position: relative;
  
  &.is-electron {
    height: calc(100% - 28px);
    margin-top: -10px;
  }
  
  :deep(.navbar) {
    height: 48px;
    margin-top: 10px;
  }
}

.drag-area {
  flex: 1;
  height: 100%;
}

.window-controls {
  display: flex;
  align-items: center;
  gap: 4px;
  -webkit-app-region: no-drag;
}

.control-button {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  
  .el-icon {
    font-size: 16px;
    color: #909399;
    transition: all 0.2s ease;
  }

  &:hover {
    .el-icon {
      color: #606266;
    }
  }

  &.close {
    &:hover {
      .el-icon {
        color: #f56c6c;
      }
    }
  }
}
</style>
