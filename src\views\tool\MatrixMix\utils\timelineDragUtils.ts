/**
 * 时间轴拖拽功能工具类
 * 负责处理片段拖拽、时间调整等交互功能
 */

import type { ProcessedClip } from './timelineTrackUtils';
import { 
  createTimelineHorizontalSnapManager, 
  type TimeSnapResult, 
  type TimeSnapConfig 
} from './timelineHorizontalSnapUtils';

export interface DragState {
  isDragging: boolean;
  element: HTMLElement | null;
  clip: ProcessedClip | null;
  type: 'video' | 'audio' | 'subtitle' | null;
  sourceTrackIndex: number;
  clipIndex: number;
  offsetX: number;
  offsetY: number;
  startX: number;
  startY: number;
  initialClipCenterY: number;
  clipRect?: DOMRect;  // 添加片段矩形信息
}

export interface TimeResizeState {
  isResizing: boolean;
  element: HTMLElement | null;
  clip: ProcessedClip | null;
  type: 'video' | 'audio' | 'subtitle' | null;
  trackIndex: number;
  clipIndex: number;
  resizeType: 'start' | 'end' | null;
  startX: number;
  originalStartTime: number;
  originalDuration: number;
}

/**
 * 创建初始拖拽状态
 */
export function createInitialDragState(): DragState {
  return {
    isDragging: false,
    element: null,
    clip: null,
    type: null,
    sourceTrackIndex: -1,
    clipIndex: -1,
    offsetX: 0,
    offsetY: 0,
    startX: 0,
    startY: 0,
    initialClipCenterY: 0
  };
}

/**
 * 创建初始时间调整状态
 */
export function createInitialTimeResizeState(): TimeResizeState {
  return {
    isResizing: false,
    element: null,
    clip: null,
    type: null,
    trackIndex: -1,
    clipIndex: -1,
    resizeType: null,
    startX: 0,
    originalStartTime: 0,
    originalDuration: 0
  };
}

/**
 * 开始片段拖拽
 */
export function initializeClipDrag(
  event: MouseEvent,
  clip: ProcessedClip,
  type: 'video' | 'audio' | 'subtitle',
  trackIndex: number,
  clipIndex: number,
  mainScrollAreaRef: HTMLElement | null
): DragState {
  const element = event.currentTarget as HTMLElement;
  const rect = element.getBoundingClientRect();
  
  // 计算拖拽开始时片段中心线的Y坐标
  let initialClipCenterY = 0;
  if (mainScrollAreaRef) {
    const scrollAreaRect = mainScrollAreaRef.getBoundingClientRect();
    const scrollTop = mainScrollAreaRef.scrollTop;
    const clipTop = rect.top - scrollAreaRect.top + scrollTop;
    initialClipCenterY = clipTop + rect.height / 2;
  }
  
  // 设置拖拽样式
  element.style.cursor = 'grabbing';
  
  return {
    isDragging: true,
    element,
    clip,
    type,
    sourceTrackIndex: trackIndex,
    clipIndex,
    offsetX: event.clientX - rect.left,
    offsetY: event.clientY - rect.top,
    startX: event.clientX,
    startY: event.clientY,
    initialClipCenterY
  };
}

/**
 * 开始时间调整
 */
export function initializeTimeResize(
  event: MouseEvent,
  clip: ProcessedClip,
  type: 'video' | 'audio' | 'subtitle',
  trackIndex: number,
  clipIndex: number,
  resizeType: 'start' | 'end'
): TimeResizeState {
  const element = event.currentTarget as HTMLElement;
  
  return {
    isResizing: true,
    element,
    clip,
    type,
    trackIndex,
    clipIndex,
    resizeType,
    startX: event.clientX,
    originalStartTime: clip.start,
    originalDuration: clip.duration
  };
}

/**
 * 计算新的时间位置（增强版 - 支持水平吸附）
 */
export function calculateNewTimePosition(
  event: MouseEvent,
  dragState: DragState,
  PIXELS_PER_SECOND: number = 50,
  timeline?: any,
  enableHorizontalSnap: boolean = true,
  snapConfig?: TimeSnapConfig
): { time: number; snapResult?: TimeSnapResult } {
  if (!dragState.clip) return { time: 0 };
  
  // 计算鼠标X方向的移动距离
  const mouseMovedX = event.clientX - dragState.startX;
  
  // 基于原始片段开始时间 + 移动距离来计算新时间
  const originalStartTime = dragState.clip.start;
  const timeMovement = mouseMovedX / PIXELS_PER_SECOND;
  const rawNewTime = Math.max(0, originalStartTime + timeMovement);
  
  // 如果启用水平吸附且提供了timeline数据
  if (enableHorizontalSnap && timeline) {
    const horizontalSnapManager = createTimelineHorizontalSnapManager(snapConfig);
    
    // 排除当前拖拽的片段
    const excludeClip = {
      trackType: dragState.type || 'video',
      trackIndex: dragState.sourceTrackIndex,
      clipIndex: dragState.clipIndex
    };
    
    // 计算吸附结果
    const snapResult = horizontalSnapManager.calculateTimeSnap(
      timeline,
      rawNewTime,
      excludeClip,
      dragState.clip.duration  // 🔧 修复：传递片段长度，启用尾部吸附
    );
    
    return {
      time: snapResult.snappedTime,
      snapResult
    };
  } else {
    // 不启用水平吸附时，使用简单的0.1秒精度吸附
    const snappedTime = Math.round(rawNewTime * 10) / 10;
    return { time: snappedTime };
  }
}

/**
 * 计算新的时间位置（简化版 - 兼容现有代码）
 */
export function calculateNewTimePositionSimple(
  event: MouseEvent,
  dragState: DragState,
  PIXELS_PER_SECOND: number = 50
): number {
  const result = calculateNewTimePosition(event, dragState, PIXELS_PER_SECOND, undefined, false);
  return result.time;
}

/**
 * 计算时间调整的新尺寸
 */
export function calculateTimeResizeParams(
  event: MouseEvent,
  resizeState: TimeResizeState,
  PIXELS_PER_SECOND: number = 50
): { newWidth: number; newLeft: number; newTime: number } {
  // 计算鼠标移动距离
  const mouseMovedX = event.clientX - resizeState.startX;
  const timeMovement = mouseMovedX / PIXELS_PER_SECOND;
  
  let newWidth: number;
  let newLeft: number;
  let newTime: number;
  
  if (resizeState.resizeType === 'start') {
    // 左边界拉伸：调整开始时间，改变宽度，左边位置也改变
    const newStartTime = Math.max(0, resizeState.originalStartTime + timeMovement);
    const maxStartTime = resizeState.originalStartTime + resizeState.originalDuration - 0.1; // 最少保留0.1秒
    const clampedStartTime = Math.min(newStartTime, maxStartTime);
    
    newWidth = (resizeState.originalStartTime + resizeState.originalDuration - clampedStartTime) * PIXELS_PER_SECOND;
    newLeft = clampedStartTime * PIXELS_PER_SECOND;
    newTime = clampedStartTime;
  } else {
    // 右边界拉伸：调整持续时间，改变宽度，左边位置不变
    const newDuration = Math.max(0.1, resizeState.originalDuration + timeMovement); // 最少保留0.1秒
    
    newWidth = newDuration * PIXELS_PER_SECOND;
    newLeft = resizeState.originalStartTime * PIXELS_PER_SECOND;
    newTime = resizeState.originalStartTime + newDuration;
  }
  
  return { newWidth, newLeft, newTime };
}
