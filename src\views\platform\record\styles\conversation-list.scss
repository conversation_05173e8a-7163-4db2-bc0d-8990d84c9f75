// 通用变量定义
$primary-color: #3b82f6;
$primary-dark: #1d4ed8;
$primary-light: #f0f9ff;
$primary-lighter: #e0f2fe;
$border-color: #e2e8f0;
$text-color: #334155;
$text-muted: #64748b;
$text-light: #9ca3af;
$shadow-light: rgba(0, 0, 0, 0.08);
$shadow-medium: rgba(0, 0, 0, 0.12);
$shadow-heavy: rgba(0, 0, 0, 0.15);
$transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$transition-fast: all 0.2s ease;

// 通用混合器
@mixin gradient-bg($color1, $color2) {
  background: linear-gradient(135deg, $color1 0%, $color2 100%);
}

@mixin shadow-hover($shadow) {
  box-shadow: $shadow;
  transform: translateY(-2px);
}

@mixin button-base {
  padding: 6px 8px;
  min-width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: $transition-fast;
}

@mixin pulse-animation($name, $shadow1, $shadow2) {
  animation: #{$name} 2s ease-in-out infinite;

  @keyframes #{$name} {
    0%,
    100% {
      box-shadow: $shadow1;
    }
    50% {
      box-shadow: $shadow2;
    }
  }
}

// 说话人颜色配置
$speaker-colors: (
  1: (
    #1890ff,
    #096dd9,
  ),
  2: (
    #52c41a,
    #389e0d,
  ),
  3: (
    #fa8c16,
    #d46b08,
  ),
  4: (
    #eb2f96,
    #c41d7f,
  ),
  5: (
    #722ed1,
    #531dab,
  ),
  6: (
    #13c2c2,
    #08979c,
  ),
  7: (
    #f5222d,
    #cf1322,
  ),
  8: (
    #faad14,
    #d48806,
  ),
);

/* 原文对话区域样式 */
.original-section {
  margin: 0 16px 16px 16px; /* 添加左右和底部边距 */
  padding: 0 32px 24px 32px; /* 顶部无padding，与关键词区域连接 */
  border-top: none;
  background: rgba(255, 255, 255, 0.98); /* 添加白色背景 */
  border-radius: 16px; /* 添加底部圆角 */
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); /* 增强阴影效果 */
  -webkit-backdrop-filter: blur(12px); /* Safari 兼容性 */
  backdrop-filter: blur(12px);
  border: 1px solid rgba(241, 245, 249, 0.8); /* 添加边框 */
  border-top: none; /* 顶部无边框，与上方区域连接 */

  /* 添加顶部分隔线 */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 32px;
    right: 32px;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(241, 245, 249, 0.6) 20%,
      rgba(241, 245, 249, 0.6) 80%,
      transparent 100%
    );
  }

  position: relative;
}

.section-header {
  margin: 24px 0 24px 0; /* 添加顶部边距 */
  padding-top: 8px; /* 添加顶部内边距 */
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  position: relative;
  padding-left: 16px;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    @include gradient-bg($primary-color, $primary-dark);
    border-radius: 2px;
  }
}

/* 移除内部滚动，由父容器处理 */

.conversation-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  /* 移除底部padding，因为现在由父容器处理 */
}

.conversation-item {
  @include gradient-bg(#ffffff, #fafbfc);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px $shadow-light;
  border-left: 4px solid $border-color;
  border: 1px solid rgba($border-color, 0.5);
  transition: $transition-smooth;
  position: relative;

  &:hover {
    @include shadow-hover(0 8px 32px $shadow-medium);
    border-color: rgba($primary-color, 0.3);
  }

  &.clickable {
    cursor: pointer;
    -webkit-user-select: none;

    &:hover {
      @include shadow-hover(0 12px 40px $shadow-heavy);
      border-color: rgba($primary-color, 0.5);
    }

    &:active {
      transform: translateY(-1px);
      box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
    }
  }

  &.currently-playing {
    @include gradient-bg($primary-light, $primary-lighter);
    border-left-color: $primary-color !important;
    border-color: rgba($primary-color, 0.4);
    @include pulse-animation(
      currentlyPlayingPulse,
      0 8px 32px rgba($primary-color, 0.2) 0 0 0 2px rgba($primary-color, 0.1),
      0 12px 40px rgba($primary-color, 0.3) 0 0 0 3px rgba($primary-color, 0.2)
    );

    &::before {
      content: "🎵";
      position: absolute;
      top: 16px;
      right: 16px;
      font-size: 16px;
      animation: musicNote 1.5s ease-in-out infinite;
    }
  }
}

@keyframes musicNote {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes speakerPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  }
}

@keyframes speakerHalo {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.4;
  }
}

/* 不同说话人的边框颜色和编号样式 */
@each $id, $colors in $speaker-colors {
  $primary: nth($colors, 1);
  $dark: nth($colors, 2);

  .conversation-item.speaker-#{$id} {
    border-left-color: $primary;

    .speaker-id-circle {
      background-color: $primary !important;
      box-shadow: 0 3px 8px rgba($primary, 0.4);

      &::before {
        background: $primary;
      }
    }
  }
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;

  .conversation-item:hover &,
  .conversation-item.editing & {
    opacity: 1;
  }
}

.notes-btn {
  @include button-base;
  color: #909399;
  transition: $transition-fast;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(
      circle,
      rgba(103, 194, 58, 0.3) 0%,
      transparent 70%
    );
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
  }

  &:hover {
    color: #67c23a;
    background: rgba(103, 194, 58, 0.1);
    transform: translateY(-1px);

    &::before {
      width: 40px;
      height: 40px;
    }
  }

  &.has-notes {
    color: #67c23a;
    background: rgba(103, 194, 58, 0.1);
    animation: notesButtonPulse 2s ease-in-out infinite;

    &:hover {
      color: #5daf34;
      background: rgba(103, 194, 58, 0.2);
      animation-play-state: paused;
    }

    &::after {
      content: "";
      position: absolute;
      top: 2px;
      right: 2px;
      width: 6px;
      height: 6px;
      background: #16a34a;
      border-radius: 50%;
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
    }
  }
}

@keyframes notesButtonPulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(103, 194, 58, 0.1);
  }
}

.edit-btn {
  @include button-base;
  color: #909399;

  &:hover {
    color: #409eff;
    background: rgba(64, 158, 255, 0.1);
  }
}

.edit-actions {
  display: flex;
  gap: 4px;
}

.save-btn,
.cancel-btn {
  @include button-base;
}

.save-btn {
  background: #67c23a;
  border-color: #67c23a;
  color: white;

  &:hover {
    background: #5daf34;
    border-color: #5daf34;
  }
}

.cancel-btn {
  background: #f4f4f5;
  border-color: #dcdfe6;
  color: #909399;

  &:hover {
    background: #ecf5ff;
    border-color: #b3d8ff;
  }
}

.timestamp {
  font-size: 11px;
  color: $text-muted;
  @include gradient-bg(#f1f5f9, $border-color);
  padding: 4px 12px;
  border-radius: 8px;
  font-family: "Monaco", "Menlo", monospace;
  font-weight: 500;
  border: 1px solid rgba($border-color, 0.8);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 6px;
}

.playing-indicator {
  color: $primary-color;
  font-size: 10px;
  animation: playingBlink 1.5s ease-in-out infinite;
}

@keyframes playingBlink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.speaker-id-circle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  color: white;
  font-size: 16px;
  line-height: 1;
  font-weight: 800;
  border-radius: 50%;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.9);
  flex-shrink: 0;
  transition: $transition-smooth;
  animation: speakerPulse 3s ease-in-out infinite;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: inherit;
    opacity: 0.3;
    z-index: -1;
    animation: speakerHalo 3s ease-in-out infinite;
  }

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation-play-state: paused;
  }
}



.conversation-text {
  color: $text-color;
  line-height: 1.8;
  margin: 0;
  font-size: 15px;
  text-align: justify;
  font-weight: 400;
  letter-spacing: 0.01em;
}

/* 编辑相关样式 */
.conversation-item.editing {
  border-color: $primary-color;
  @include gradient-bg($primary-light, $primary-lighter);
  box-shadow: 0 8px 32px rgba($primary-color, 0.2);
}

/* 笔记展开状态样式 */
.conversation-item.notes-expanded {
  border-color: rgba(34, 197, 94, 0.4);
  @include gradient-bg(#f8fffe, #f0fdf4);
  box-shadow: 0 8px 32px rgba(34, 197, 94, 0.2);

  &:hover {
    cursor: default;
    transform: none;
    box-shadow: 0 8px 32px rgba(34, 197, 94, 0.2);
  }

  &.clickable {
    cursor: default;

    &:hover {
      transform: none;
      box-shadow: 0 8px 32px rgba(34, 197, 94, 0.2);
    }
  }
}

/* 高亮状态样式 - 简洁版本 */
.conversation-item.highlighted {
  border-color: rgba(6, 182, 212, 0.6) !important;
  background: rgba(6, 182, 212, 0.05) !important;
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
  position: relative;

  &::before {
    content: "📍";
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    z-index: 2;
    animation: pinFloat 2s ease-in-out infinite;
    filter: drop-shadow(0 2px 4px rgba(6, 182, 212, 0.3));
  }

  // 悬浮时保持简洁
  &:hover {
    background: rgba(6, 182, 212, 0.08) !important;
    box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.3);
  }
}

/* 📍图标浮动动画 */
@keyframes pinFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg) scale(1);
  }
  25% {
    transform: translateY(-2px) rotate(-3deg) scale(1.05);
  }
  50% {
    transform: translateY(-4px) rotate(0deg) scale(1.1);
  }
  75% {
    transform: translateY(-2px) rotate(3deg) scale(1.05);
  }
}

.edit-container {
  margin-top: 8px;
}

.edit-textarea {
  margin-bottom: 8px;

  :deep(.el-textarea__inner) {
    border: 2px solid $primary-color;
    border-radius: 8px;
    font-size: 15px;
    line-height: 1.6;
    resize: vertical;
    min-height: 80px;

    &:focus {
      border-color: $primary-dark;
      box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
    }
  }
}

.edit-tips {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
}

.tip-text {
  font-size: 12px;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

/* 无数据状态 */
.no-data {
  text-align: center;
  padding: 40px 20px;
  color: $text-light;

  p {
    margin: 0;
    font-size: 14px;
  }
}

/* 搜索相关样式 */
.conversation-item.search-result {
  border: 2px solid rgba($primary-color, 0.3);
  @include gradient-bg($primary-light, $primary-lighter);
}

.conversation-item.current-search-result {
  border: 2px solid $primary-color;
  @include gradient-bg(#dbeafe, #bfdbfe);
  @include pulse-animation(
    searchResultPulse,
    0 8px 32px rgba($primary-color, 0.3) 0 0 0 3px rgba($primary-color, 0.1),
    0 12px 40px rgba($primary-color, 0.4) 0 0 0 4px rgba($primary-color, 0.2)
  );
}

.conversation-text :deep(.search-highlight) {
  @include gradient-bg(#fef08a, #fde047);
  color: #92400e;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  animation: highlightGlow 1.5s ease-in-out infinite alternate;
}

@keyframes highlightGlow {
  from {
    @include gradient-bg(#fef08a, #fde047);
  }
  to {
    @include gradient-bg(#fbbf24, #f59e0b);
  }
}

/* 词汇高亮样式 */
.conversation-text :deep(.word) {
  transition: $transition-fast;
  border-radius: 3px;
  padding: 1px 2px;
  display: inline-block;
  position: relative;
}

.conversation-text :deep(.clickable-word) {
  cursor: pointer;
  transition: $transition-fast;
  border-bottom: 1px dotted transparent;
  position: relative;

  .conversation-item.currently-playing & {
    border-bottom: 1px dotted rgba($primary-color, 0.4);

    &:hover {
      border-bottom: 1px dotted $primary-color;
    }
  }

  &:hover {
    background: rgba($primary-color, 0.15);
    color: $primary-color;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba($primary-color, 0.2);
    border-bottom: 1px dotted $primary-color;
  }

  &:active {
    background: rgba($primary-color, 0.25);
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba($primary-color, 0.3);
  }
}

.conversation-text :deep(.word:hover) {
  background: rgba($primary-color, 0.15);
  color: $primary-color;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba($primary-color, 0.2);
  border-bottom: 1px dotted $primary-color;
}
/** 当前词高亮样式 */
.conversation-text :deep(.current-word) {
  @include gradient-bg($primary-color, $primary-dark);
  color: white;
  padding: 2px 2px;
  border-radius: 6px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba($primary-color, 0.4),
    0 0 0 2px rgba($primary-color, 0.2);
  animation: currentWordGlow 2s ease-in-out infinite;
  transition: $transition-fast;
  transform: translateY(-1px) scale(1.02);
  z-index: 1;
  position: relative;

}

@keyframes currentWordGlow {
  0%,
  100% {
    box-shadow: 0 2px 8px rgba($primary-color, 0.4),
      0 0 0 2px rgba($primary-color, 0.2);
  }
  50% {
    box-shadow: 0 4px 16px rgba($primary-color, 0.6),
      0 0 0 3px rgba($primary-color, 0.3);
  }
}

@keyframes currentWordHalo {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.4;
  }
}

/* 笔记相关样式 */
.notes-container {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fffe 0%, #f0fdf4 100%);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.1);
  transition: $transition-smooth;
  animation: notesSlideIn 0.3s ease-out;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #16a34a 0%, #22c55e 50%, #16a34a 100%);
    background-size: 200% 100%;
    animation: notesGradientMove 3s ease-in-out infinite;
  }

  &:hover {
    border-color: rgba(34, 197, 94, 0.3);
    box-shadow: 0 4px 16px rgba(34, 197, 94, 0.15);
    transform: translateY(-1px);
  }
}

@keyframes notesSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 500px;
  }
}

@keyframes notesGradientMove {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.notes-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #16a34a;

  .el-icon {
    color: #16a34a;
  }
}

.close-notes-btn {
  @include button-base;
  color: #6b7280;
  padding: 4px;
  min-width: 24px;
  height: 24px;

  &:hover {
    color: #374151;
    background: rgba(107, 114, 128, 0.1);
  }
}

.notes-display {
  .notes-content {
    background: rgba(255, 255, 255, 0.9);
    padding: 16px;
    border-radius: 10px;
    border: 1px solid rgba(34, 197, 94, 0.15);
    font-size: 14px;
    line-height: 1.7;
    color: #374151;
    margin-bottom: 16px;
    white-space: pre-wrap;
    word-break: break-word;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.08);
    transition: $transition-smooth;
    position: relative;

    &::before {
      //   content: '📝';
      position: absolute;
      top: -8px;
      left: 12px;
      background: linear-gradient(135deg, #f8fffe 0%, #f0fdf4 100%);
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      border: 1px solid rgba(34, 197, 94, 0.2);
    }

    &:hover {
      border-color: rgba(34, 197, 94, 0.25);
      box-shadow: 0 4px 16px rgba(34, 197, 94, 0.12);
      transform: translateY(-1px);
    }
  }

  .notes-empty {
    color: #9ca3af;
    font-style: italic;
    text-align: center;
    padding: 32px 20px;
    font-size: 13px;
    background: rgba(249, 250, 251, 0.5);
    border-radius: 8px;
    border: 2px dashed rgba(156, 163, 175, 0.3);
    margin-bottom: 16px;
    transition: $transition-smooth;

    &:hover {
      border-color: rgba(34, 197, 94, 0.3);
      color: #6b7280;
    }
  }

  .notes-actions {
    display: flex;
    justify-content: flex-end;
  }
}

.edit-notes-btn {
  background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
  border-color: #16a34a;
  color: white;
  font-size: 12px;
  padding: 8px 16px;
  height: 32px;
  border-radius: 8px;
  font-weight: 500;
  transition: $transition-smooth;
  box-shadow: 0 2px 8px rgba(22, 163, 74, 0.3);

  &:hover {
    background: linear-gradient(135deg, #15803d 0%, #16a34a 100%);
    border-color: #15803d;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(22, 163, 74, 0.4);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(22, 163, 74, 0.3);
  }
}

.notes-edit {
  .notes-textarea {
    margin-bottom: 12px;

    :deep(.el-textarea__inner) {
      border: 2px solid rgba(34, 197, 94, 0.3);
      border-radius: 8px;
      font-size: 14px;
      line-height: 1.6;
      resize: vertical;
      min-height: 80px;
      background: rgba(255, 255, 255, 0.9);

      &:focus {
        border-color: #16a34a;
        box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
      }
    }
  }

  .notes-edit-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
  }

  .save-notes-btn {
    background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
    border-color: #16a34a;
    color: white;
    font-size: 12px;
    padding: 8px 16px;
    height: 32px;
    border-radius: 6px;
    font-weight: 500;
    transition: $transition-smooth;
    box-shadow: 0 2px 6px rgba(22, 163, 74, 0.25);

    &:hover {
      background: linear-gradient(135deg, #15803d 0%, #16a34a 100%);
      border-color: #15803d;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(22, 163, 74, 0.35);
    }
  }

  .cancel-notes-btn {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-color: #e2e8f0;
    color: #64748b;
    font-size: 12px;
    padding: 8px 16px;
    height: 32px;
    border-radius: 6px;
    font-weight: 500;
    transition: $transition-smooth;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      border-color: #cbd5e1;
      color: #475569;
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }
  }

  .notes-tips {
    display: flex;
    justify-content: flex-end;

    .tip-text {
      font-size: 11px;
      color: #6b7280;
      background: rgba(107, 114, 128, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
    }
  }
}
