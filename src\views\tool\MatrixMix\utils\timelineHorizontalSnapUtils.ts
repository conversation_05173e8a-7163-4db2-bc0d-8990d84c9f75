/**
 * 时间轴水平吸附工具类
 * 负责处理片段在时间轴上的水平位置吸附逻辑
 */

// 水平吸附点类型定义
export interface TimeSnapPoint {
  time: number;                                    // 时间点（秒）
  type: 'clip-start' | 'clip-end' | 'ruler' | 'playhead';  // 吸附点类�?
  source: {
    trackType: 'video' | 'audio' | 'subtitle';    // 来源轨道类型
    trackIndex: number;                            // 来源轨道索引
    clipIndex?: number;                            // 来源片段索引（如果是片段边界�?
  };
}

// 水平吸附结果类型
export interface TimeSnapResult {
  snappedTime: number;                             // 吸附后的时间
  isSnapped: boolean;                              // 是否已吸�?
  snapPoint: TimeSnapPoint | null;                // 吸附点信�?
  originalTime: number;                            // 原始计算的时�?
  originalEndTime?: number;                        // 原始计算的结束时�?
  pixelsPerSecond?: number;                        // 像素每秒比例
}

// 水平吸附配置
export interface TimeSnapConfig {
  threshold: number;                               // 吸附距离阈值（秒）
  enableClipSnap: boolean;                         // 启用片段边界吸附
  enableRulerSnap: boolean;                        // 启用时间轴刻度吸�?
  enablePlayheadSnap: boolean;                     // 启用播放头吸�?
  rulerInterval: number;                           // 刻度间隔（秒�?
  stabilityThreshold: number;                      // 稳定性阈�?
}

// 默认水平吸附配置
export const DEFAULT_TIME_SNAP_CONFIG: TimeSnapConfig = {
  threshold: 1.0,          // 🔧 修正�?秒的吸附范围（在50px/s下是50px，合理范围）
  enableClipSnap: true,    // 重点：片段边界吸�?
  enableRulerSnap: false,  // 关闭刻度线吸附，排除干扰
  enablePlayheadSnap: true, // 重点：播放头吸附
  rulerInterval: 1.0,      // 1秒刻�?
  stabilityThreshold: 3
};

/**
 * 水平吸附状态管理器
 */
export class TimeSnapStateManager {
  private currentSnapPoint: TimeSnapPoint | null = null;
  private stabilityCounter = 0;
  private config: TimeSnapConfig;

  constructor(config: TimeSnapConfig = DEFAULT_TIME_SNAP_CONFIG) {
    this.config = config;
  }

  /**
   * 重置吸附状�?
   */
  reset(): void {
    this.currentSnapPoint = null;
    this.stabilityCounter = 0;
  }

  /**
   * 处理吸附稳定性逻辑
   */
  processStability(nearestPoint: TimeSnapPoint): TimeSnapPoint | null {
    const isSameSnapPoint = this.currentSnapPoint && 
      Math.abs(this.currentSnapPoint.time - nearestPoint.time) < 0.01 && 
      this.currentSnapPoint.type === nearestPoint.type;
    
    if (isSameSnapPoint) {
      this.stabilityCounter = 0;
      return nearestPoint;
    } else {
      this.stabilityCounter++;
      
      if (this.currentSnapPoint && this.stabilityCounter < this.config.stabilityThreshold) {
        return this.currentSnapPoint;
      } else {
        this.currentSnapPoint = nearestPoint;
        this.stabilityCounter = 0;
        return nearestPoint;
      }
    }
  }

  /**
   * 获取当前吸附�?
   */
  getCurrentSnapPoint(): TimeSnapPoint | null {
    return this.currentSnapPoint;
  }
}

/**
 * 水平吸附计算�?
 */
export class TimeSnapCalculator {
  private config: TimeSnapConfig;

  constructor(config: TimeSnapConfig = DEFAULT_TIME_SNAP_CONFIG) {
    this.config = config;
  }

  /**
   * 查找最近的时间吸附点（考虑片段头尾两个判定点）
   */
  findNearestTimeSnapPoint(points: TimeSnapPoint[], targetTime: number, clipDuration?: number): { 
    snapPoint: TimeSnapPoint | null; 
    draggedClipEdge: 'start' | 'end' | null;
  } {
    let nearestPoint: TimeSnapPoint | null = null;
    let draggedClipEdge: 'start' | 'end' | null = null;
    let minDistance = Infinity;
    
    // 计算片段的头部和尾部时间
    const clipStartTime = targetTime;
    const clipEndTime = clipDuration ? targetTime + clipDuration : targetTime;
    
    // 🔍 调试：检查片段时间信息
    console.log('🎯 尾吸附调试 - 片段时间信息:', {
      targetTime,
      clipDuration,
      clipStartTime,
      clipEndTime,
      '有尾部': !!clipDuration && clipDuration > 0
    });
    
    // 按优先级分组
    const highPriorityPoints = points.filter(p => 
      p.type === 'clip-start' || p.type === 'clip-end' || p.type === 'playhead'
    );
    const lowPriorityPoints = points.filter(p => p.type === 'ruler');
    
    // 检查片段头部和尾部与高优先级点的距离
    for (const point of highPriorityPoints) {
      // 检查片段头部到吸附点的距离
      const startDistance = Math.abs(clipStartTime - point.time);
      
      // 检查片段尾部到吸附点的距离（如果有片段长度信息）
      let endDistance = Infinity;
      if (clipDuration && clipDuration > 0) {
        endDistance = Math.abs(clipEndTime - point.time);
      }
      
      // � 调试：每个吸附点的判定过程
      console.log(`🎯 检查吸附点 ${point.time}s (${point.type}):`, {
        点时间: point.time,
        头距离: startDistance,
        尾距离: endDistance,
        阈值: this.config.threshold,
        头符合: startDistance <= this.config.threshold,
        尾符合: clipDuration && endDistance <= this.config.threshold,
        当前最小: minDistance
      });
      
      // �🔧 修复：头部和尾部平等竞争最近距离
      // 头部吸附判定
      if (startDistance <= this.config.threshold && startDistance < minDistance) {
        minDistance = startDistance;
        nearestPoint = point;
        draggedClipEdge = 'start';
        console.log('✅ 头部吸附生效:', { 距离: startDistance, 点: point.time });
      }
      
      // 尾部吸附判定（与头部平等竞争）
      if (clipDuration && endDistance <= this.config.threshold && endDistance < minDistance) {
        minDistance = endDistance;
        nearestPoint = point;
        draggedClipEdge = 'end';
        console.log('✅ 尾部吸附生效:', { 距离: endDistance, 点: point.time });
      }
    }
    
    // 如果没有高优先级点，且距离较远时才考虑刻度线
    if (!nearestPoint || minDistance > this.config.threshold * 0.5) {
      for (const point of lowPriorityPoints) {
        const rulerThreshold = this.config.threshold * 0.3;
        
        // 检查片段头部到刻度线的距离
        const startDistance = Math.abs(clipStartTime - point.time);
        
        // 检查片段尾部到刻度线的距离
        let endDistance = Infinity;
        if (clipDuration && clipDuration > 0) {
          endDistance = Math.abs(clipEndTime - point.time);
        }
        
        // 🔧 修复：头部和尾部平等竞争刻度线吸附
        if (startDistance <= rulerThreshold && startDistance < minDistance) {
          minDistance = startDistance;
          nearestPoint = point;
          draggedClipEdge = 'start';
        }
        
        if (clipDuration && endDistance <= rulerThreshold && endDistance < minDistance) {
          minDistance = endDistance;
          nearestPoint = point;
          draggedClipEdge = 'end';
        }
      }
    }
    
    return { snapPoint: nearestPoint, draggedClipEdge };
  }

  /**
   * 计算水平吸附结果
   */
  calculateTimeSnapResult(
    allSnapPoints: TimeSnapPoint[],
    targetTime: number,
    stateManager: TimeSnapStateManager,
    clipDuration?: number,
    pixelsPerSecond: number = 50
  ): TimeSnapResult & { draggedClipEdge?: 'start' | 'end' | null } {
    const originalEndTime = clipDuration ? targetTime + clipDuration : undefined;
    
    if (allSnapPoints.length === 0) {
      stateManager.reset();
      return {
        snappedTime: targetTime,
        isSnapped: false,
        snapPoint: null,
        originalTime: targetTime,
        originalEndTime,
        pixelsPerSecond,
        draggedClipEdge: null
      };
    }

    const result = this.findNearestTimeSnapPoint(allSnapPoints, targetTime, clipDuration);
    const { snapPoint: nearestPoint, draggedClipEdge } = result;
    
    if (nearestPoint) {
      const processedPoint = stateManager.processStability(nearestPoint);
      if (processedPoint) {
        // 🔧 修正：根据吸附边缘计算正确的 snappedTime
        let finalSnappedTime;
        if (draggedClipEdge === 'end') {
          // 尾部吸附：计算片段头部应该到达的位置
          finalSnappedTime = processedPoint.time - (clipDuration || 0);
        } else {
          // 头部吸附：直接使用吸附点时间
          finalSnappedTime = processedPoint.time;
        }
        
        return {
          snappedTime: finalSnappedTime,
          isSnapped: true,
          snapPoint: processedPoint,
          originalTime: targetTime,
          originalEndTime,
          pixelsPerSecond,
          draggedClipEdge
        };
      }
    } else {
      stateManager.reset();
    }
    
    return {
      snappedTime: targetTime,
      isSnapped: false,
      snapPoint: null,
      originalTime: targetTime,
      originalEndTime,
      pixelsPerSecond,
      draggedClipEdge: null
    };
  }
}

/**
 * 时间轴吸附点收集�?
 */
export class TimeSnapPointCollector {
  private config: TimeSnapConfig;

  constructor(config: TimeSnapConfig = DEFAULT_TIME_SNAP_CONFIG) {
    this.config = config;
  }

  /**
   * 从时间轴数据收集所有吸附点
   */
  collectSnapPoints(
    timeline: any,
    excludeClip?: { trackType: string; trackIndex: number; clipIndex: number },
    currentTime?: number
  ): TimeSnapPoint[] {
    const points: TimeSnapPoint[] = [];

    // 收集片段边界吸附�?
    if (this.config.enableClipSnap) {
      this.collectClipSnapPoints(timeline, points, excludeClip);
    }

    // 收集时间轴刻度吸附点
    if (this.config.enableRulerSnap && currentTime !== undefined) {
      this.collectRulerSnapPoints(points, currentTime);
    }

    // 收集播放头吸附点（如果有播放头位置）
    if (this.config.enablePlayheadSnap) {
      // 这里可以添加播放头位置的吸附�?
      // points.push({ time: playheadTime, type: 'playhead', source: ... });
    }

    return points;
  }

  /**
   * 收集片段边界吸附�?
   */
  private collectClipSnapPoints(
    timeline: any,
    points: TimeSnapPoint[],
    excludeClip?: { trackType: string; trackIndex: number; clipIndex: number }
  ): void {
    
    
    // 收集视频轨道片段（包括字幕轨道）
    if (timeline.VideoTracks) {
      
      timeline.VideoTracks.forEach((track: any, trackIndex: number) => {
        if (track.VideoTrackClips) {
          track.VideoTrackClips.forEach((clip: any, clipIndex: number) => {
            // 确定轨道类型
            const trackType = track.Type === 'Subtitle' ? 'subtitle' : 'video';
            
            // 排除当前拖拽的片�?
            if (excludeClip && 
                excludeClip.trackType === trackType && 
                excludeClip.trackIndex === trackIndex && 
                excludeClip.clipIndex === clipIndex) {
              
              return;
            }

            const startTime = parseFloat(clip.TimelineIn || 0);
            // 修复：正确计算结束时�?
            let endTime;
            if (clip.TimelineOut !== undefined && clip.TimelineOut !== null) {
              endTime = parseFloat(clip.TimelineOut);
            } else if (clip.Duration !== undefined && clip.Duration !== null) {
              // 如果有Duration信息，使�?startTime + Duration
              endTime = startTime + parseFloat(clip.Duration);
            } else {
              // 兜底：假设有最小长�?
              endTime = startTime + 1.0; // 1秒最小长�?
            }

            // 确保开始时间和结束时间不同
            if (Math.abs(endTime - startTime) < 0.01) {
              console.warn(`⚠️  片段时间异常，开始时间和结束时间几乎相同: ${startTime} - ${endTime}`);
              endTime = startTime + 1.0; // 强制设置1秒长�?
            }

            // 添加片段开始点
            points.push({
              time: startTime,
              type: 'clip-start',
              source: { trackType: trackType as 'video' | 'subtitle', trackIndex, clipIndex }
            });

            // 添加片段结束�?
            points.push({
              time: endTime,
              type: 'clip-end',
              source: { trackType: trackType as 'video' | 'subtitle', trackIndex, clipIndex }
            });
          });
        }
      });
    } else {
      
    }

    // 收集音频轨道片段
    if (timeline.AudioTracks) {
      
      timeline.AudioTracks.forEach((track: any, trackIndex: number) => {
        if (track.AudioTrackClips) {
          track.AudioTrackClips.forEach((clip: any, clipIndex: number) => {
            if (excludeClip && 
                excludeClip.trackType === 'audio' && 
                excludeClip.trackIndex === trackIndex && 
                excludeClip.clipIndex === clipIndex) {
              return;
            }

            const startTime = parseFloat(clip.TimelineIn || 0);
            // 修复：正确计算结束时�?
            let endTime;
            if (clip.TimelineOut !== undefined && clip.TimelineOut !== null) {
              endTime = parseFloat(clip.TimelineOut);
            } else if (clip.Duration !== undefined && clip.Duration !== null) {
              // 如果有Duration信息，使�?startTime + Duration
              endTime = startTime + parseFloat(clip.Duration);
            } else {
              // 兜底：假设有最小长�?
              endTime = startTime + 1.0; // 1秒最小长�?
            }

            // 确保开始时间和结束时间不同
            if (Math.abs(endTime - startTime) < 0.01) {
              console.warn(`⚠️  音频片段时间异常: ${startTime} - ${endTime}`);
              endTime = startTime + 1.0;
            }

            

            points.push({
              time: startTime,
              type: 'clip-start',
              source: { trackType: 'audio', trackIndex, clipIndex }
            });

            points.push({
              time: endTime,
              type: 'clip-end',
              source: { trackType: 'audio', trackIndex, clipIndex }
            });
          });
        }
      });
    } else {
      
    }
    
        
    
  }

  /**
   * 收集时间轴刻度吸附点
   */
  private collectRulerSnapPoints(points: TimeSnapPoint[], currentTime: number): void {
    // 在当前时间附近生成刻度吸附点
    const range = this.config.threshold * 5; // 在阈值的5倍范围内生成刻度�?
    const startTime = Math.max(0, currentTime - range);
    const endTime = currentTime + range;

    for (let time = Math.floor(startTime / this.config.rulerInterval) * this.config.rulerInterval; 
         time <= endTime; 
         time += this.config.rulerInterval) {
      if (time >= 0) {
        points.push({
          time: time,
          type: 'ruler',
          source: { trackType: 'video', trackIndex: -1 } // 刻度点没有具体的轨道来源
        });
      }
    }
  }
}



/**
 * 时间轴水平吸附管理器
 */
export class TimelineHorizontalSnapManager {
  private stateManager: TimeSnapStateManager;
  private calculator: TimeSnapCalculator;
  private collector: TimeSnapPointCollector;

  constructor(config: TimeSnapConfig = DEFAULT_TIME_SNAP_CONFIG) {
    this.stateManager = new TimeSnapStateManager(config);
    this.calculator = new TimeSnapCalculator(config);
    this.collector = new TimeSnapPointCollector(config);
  }

  /**
   * 重置所有吸附状�?
   */
  reset(): void {
    this.stateManager.reset();
  }

  /**
   * 计算时间吸附结果的主要入�?
   */
  calculateTimeSnap(
    timeline: any,
    targetTime: number,
    excludeClip?: { trackType: string; trackIndex: number; clipIndex: number },
    clipDuration?: number,
    pixelsPerSecond: number = 50
  ): TimeSnapResult & { draggedClipEdge?: 'start' | 'end' | null } {
    // 收集所有吸附点
    const allSnapPoints = this.collector.collectSnapPoints(timeline, excludeClip, targetTime);
    
    // 计算吸附结果
    const result = this.calculator.calculateTimeSnapResult(
      allSnapPoints,
      targetTime,
      this.stateManager,
      clipDuration,
      pixelsPerSecond
    );
    
    return result;
  }

  /**
   * 获取当前吸附状态信息（用于调试�?
   */
  getStateInfo() {
    return {
      currentSnapPoint: this.stateManager.getCurrentSnapPoint()
    };
  }
}

/**
 * 创建时间轴水平吸附管理器的工厂函�?
 */
export function createTimelineHorizontalSnapManager(config?: TimeSnapConfig): TimelineHorizontalSnapManager {
  return new TimelineHorizontalSnapManager(config);
}
