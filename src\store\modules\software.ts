
import { defineStore } from 'pinia'
import useInfoStore from '@/store/modules/info';
import { getToken } from "@/utils/auth";
import useContext from '@/store/modules/context'
import modal from '@/plugins/modal';
import useUserStore from '@/store/modules/user'
const useSoftWareStore = defineStore(
    'software',
    {
        actions: {
            //打开具体直播控制台
            async openLiveWindow(liveId: number) {
                const infoStore = useInfoStore()
                try {
                    if (infoStore.liveId && infoStore.liveId != liveId) {
                        throw ("直播开启失败,请先关闭当前打开的直播");
                    }
                    const res = await useContext().invoke("openLiveWindow", { liveId, token: getToken() })
                    modal.msgSuccess(res);
                    infoStore.liveId = liveId;
                } catch (error: any) {
                    modal.msgError(error.message);
                    return
                }
            },
            //初始化具体直播数据库
            initLiveDb() {
                try {
                    useContext().send("initData", useUserStore().name)
                } catch (e) {
                    console.error(e)
                }
            },


            //主应用窗口相关操作
            async minimizeIndexWindow(){
                await useContext().invoke("minimizeIndexWindow")
            },
            async maximizeIndexWindow(){
                await useContext().invoke("maximizeIndexWindow")
            },
            async closeIndexWindow(){
                await useContext().invoke("closeIndexWindow")
            },
            async isMaximizedIndexWindow():Promise<boolean>{
                return await useContext().invoke("isMaximizedIndexWindow")
            },
            //直播窗口相关操作
            async isExistLiveWindow():Promise<boolean>{
                return await useContext().invoke("isExistLiveWindow")
            },
            async closeLiveWindow(){
                await useContext().invoke("closeLiveWindow")
            },
            watchDebug(){
                useContext().on("watchDebug",(_event:any,data:any)=>{
                    console.log("[Debug]:",data)
                })
            },
            //关闭直播窗口时通知主应用清空上次存储的直播ID
            watchClearIndexLiveStore(){
                useContext().on("clearIndexLiveStore",(event,data)=>{
                    if(data){
                        useInfoStore().liveId =  null
                    }
                })
            }


        },
        getters: {
            isSoftWare(){
                return useContext().electron !== undefined
            },
        }
    })

export default useSoftWareStore