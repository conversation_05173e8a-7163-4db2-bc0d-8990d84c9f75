/* 弹窗整体样式 */
.phrase-dialog :deep(.el-dialog) {
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.phrase-dialog :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    margin: 0;
}

.phrase-dialog :deep(.el-dialog__title) {
    color: white;
    font-size: 18px;
    font-weight: 600;
}

.phrase-dialog :deep(.el-dialog__headerbtn) {
    top: 20px;
    right: 20px;
}

.phrase-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
    color: white;
    font-size: 20px;
}

.phrase-dialog :deep(.el-dialog__body) {
    padding: 24px;
    background-color: #f8f9fa;
}

.phrase-dialog :deep(.el-dialog__footer) {
    background-color: #f8f9fa;
    padding: 16px 24px;
    border-top: 1px solid #e9ecef;
}

/* 弹窗内容布局 */
.dialog-content {
    display: flex;
    gap: 24px;
    height: 520px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 对话框底部按钮美化 */
.dialog-footer .el-button {
    border-radius: 8px;
    padding: 10px 24px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
}

.dialog-footer .el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.dialog-footer .el-button--primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.dialog-footer .el-button:not(.el-button--primary) {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #6c757d;
}

.dialog-footer .el-button:not(.el-button--primary):hover {
    background: #e9ecef;
    border-color: #dee2e6;
    color: #495057;
    transform: translateY(-1px);
}
