<template>
    <div class="upload-file">
        <el-upload multiple :auto-upload="false" :action="uploadFileUrl" :before-upload="handleBeforeUpload" v-model:file-list="fileList"
            :limit="limit" :on-error="handleUploadError" :on-exceed="handleExceed" :on-success="handleUploadSuccess" :on-change="handleChange"
            :on-remove="handleRemove" :headers="headers" class="upload-file-uploader" ref="fileUpload">
            <!-- 上传按钮 -->
            <el-button type="primary">选取文件</el-button>
        </el-upload>
        <!-- 上传提示 -->
        <div class="el-upload__tip" v-if="showTip">
            请上传
            <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
            <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
            的文件
        </div>
    </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";

const props = defineProps({
    modelValue: [String, Object, Array],
    // 数量限制
    limit: {
        type: Number,
        default: 5,
    },
    // 大小限制(MB)
    fileSize: {
        type: Number,
        default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
        type: Array,
        default: () => ["doc", "xls", "ppt", "txt", "pdf"],
    },
    // 是否显示提示
    isShowTip: {
        type: Boolean,
        default: true
    },
    // 上传文件服务器地址
    uploadFileUrl: {
        type: String,
        default: "/common/upload"
    },
    submitUpload: {
        type: Function,
        default: null
    }
});

const { proxy } = getCurrentInstance();
const emit = defineEmits();
const number = ref(0);
const uploadList = ref([]);
const uploadFileUrl = computed(() => import.meta.env.VITE_APP_BASE_API + props.uploadFileUrl); // 上传文件服务器地址

const headers = ref({ Authorization: "Bearer " + getToken() });
const fileList = ref([]);
const showTip = computed(
    () => props.isShowTip && (props.fileType || props.fileSize)
);

// 文件个数超出
function handleExceed(files) {
  if (props.limit === 1) {
    // 当 limit 为 1 时，自动替换文件
    proxy.$refs.fileUpload.clearFiles();
    proxy.$refs.fileUpload.handleStart(files[0]);
  } else {
    proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
  }
}

// 上传失败
function handleUploadError(err) {
    proxy.$modal.msgError("上传文件失败");
}

// 上传前校检格式和大小
function handleBeforeUpload(file) {
    // 校检文件类型（不区分大小写）
    if (props.fileType.length) {
        const fileName = file.name;
        const fileExt = fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2).toLowerCase();
        const isTypeOk = props.fileType.some(type => type.toLowerCase() === fileExt);
        if (!isTypeOk) {
            proxy.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`);
            emit("update:fileValidationFailed", true);
            return false;
        }
    }
    
    // 校检文件大小
    if (props.fileSize) {
        const fileSizeInMB = file.size / 1024 / 1024;
        const isLt = fileSizeInMB < props.fileSize;
        
        if (!isLt) {
            proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
            emit("update:fileValidationFailed", true);
            
            // 关键修改：从文件列表中移除该文件
            setTimeout(() => {
                const index = fileList.value.findIndex(item => item.uid === file.uid);
                if (index !== -1) {
                    fileList.value.splice(index, 1);
                }
            }, 0);
            
            return false;
        }
    }
    
    emit("update:fileValidationFailed", false);
    number.value++;
    return true;
}

// 上传成功回调
function handleUploadSuccess(res, file) {
    if (res.code === 200) {
        uploadList.value.push({ name: file.name, url: res.data });
        emit("uploadSuccess", uploadList.value);
        uploadedSuccessfully();
        emit("update:modelValue", file);
    } else {
        number.value--;
        proxy.$modal.closeLoading();
        proxy.$modal.msgError(res.msg);
        proxy.$refs.fileUpload.handleRemove(file);
        uploadedSuccessfully();
    }
}

// 执行文件上传
function executeUpload() {
    return new Promise((resolve, reject) => {
        if (fileList.value.length > 0) {
            // 执行上传前再次检查所有文件大小
            const invalidFiles = fileList.value.filter(file => 
                props.fileSize && file.size / 1024 / 1024 > props.fileSize
            );
            
            if (invalidFiles.length > 0) {
                proxy.$modal.msgError(`存在文件大小超过 ${props.fileSize} MB 的文件，请重新选择!`);
                // 移除所有无效文件
                invalidFiles.forEach(file => {
                    const index = fileList.value.findIndex(item => item.uid === file.uid);
                    if (index !== -1) {
                        fileList.value.splice(index, 1);
                    }
                });
                reject(new Error('文件大小超过限制'));
                return;
            }
            
            proxy.$refs.fileUpload.submit();
            // 监听上传完成事件
            const checkUploadStatus = setInterval(() => {
                resolve(uploadList.value);
                clearInterval(checkUploadStatus);
            }, 100); 
        } else {
            resolve([]);
        }
    });
}

function handleChange(file) {
    emit("update:modelValue", file);
}

function handleRemove(file) {
    emit("update:modelValue", null);
}

function clearUploadList() {
    fileList.value = [];
    uploadList.value = [];
    number.value = 0;
}

// 上传结束处理
function uploadedSuccessfully() {
    if (number.value > 0 && uploadList.value.length === number.value) {
        fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
        uploadList.value = [];
        number.value = 0;
        proxy.$modal.closeLoading();
    }
}

defineExpose({
    executeUpload,
    clearUploadList
});

</script>

<style scoped lang="scss">
.upload-file-uploader {
    margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
}

.upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
}

.ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
}
</style>