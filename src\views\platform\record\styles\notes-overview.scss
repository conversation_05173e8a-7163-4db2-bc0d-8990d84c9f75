.notes-overview {
  position: relative;
  z-index: 5;
}

// 纯CSS粒子效果
.css-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
  border-radius: 16px;

  // 使用伪元素创建多个粒子
  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: linear-gradient(45deg, #3b82f6, #22c55e);
    box-shadow:
      // 第一组粒子
      20px 30px 0 0 rgba(59, 130, 246, 0.6),
      80px 60px 0 0 rgba(34, 197, 94, 0.5),
      150px 20px 0 0 rgba(139, 92, 246, 0.4),
      200px 80px 0 0 rgba(6, 182, 212, 0.6),
      300px 40px 0 0 rgba(245, 158, 11, 0.5),
      400px 70px 0 0 rgba(239, 68, 68, 0.4),
      500px 25px 0 0 rgba(16, 185, 129, 0.6),

      // 第二组粒子
      50px 100px 0 0 rgba(59, 130, 246, 0.3),
      120px 140px 0 0 rgba(34, 197, 94, 0.4),
      180px 110px 0 0 rgba(139, 92, 246, 0.5),
      250px 160px 0 0 rgba(6, 182, 212, 0.3),
      350px 130px 0 0 rgba(245, 158, 11, 0.4),
      450px 170px 0 0 rgba(239, 68, 68, 0.5),

      // 第三组粒子
      30px 200px 0 0 rgba(59, 130, 246, 0.4),
      100px 230px 0 0 rgba(34, 197, 94, 0.3),
      170px 210px 0 0 rgba(139, 92, 246, 0.6),
      240px 250px 0 0 rgba(6, 182, 212, 0.4),
      320px 220px 0 0 rgba(245, 158, 11, 0.3),
      420px 260px 0 0 rgba(239, 68, 68, 0.6);

    animation: cssParticleFloat 8s ease-in-out infinite;
  }

  &::after {
    animation-delay: -4s;
    animation-duration: 10s;
    box-shadow:
      // 不同位置的粒子
      60px 50px 0 0 rgba(139, 92, 246, 0.5),
      140px 90px 0 0 rgba(6, 182, 212, 0.4),
      220px 30px 0 0 rgba(245, 158, 11, 0.6),
      280px 120px 0 0 rgba(239, 68, 68, 0.3),
      380px 60px 0 0 rgba(16, 185, 129, 0.5),
      480px 100px 0 0 rgba(59, 130, 246, 0.4),

      90px 180px 0 0 rgba(34, 197, 94, 0.6),
      160px 150px 0 0 rgba(139, 92, 246, 0.3),
      260px 190px 0 0 rgba(6, 182, 212, 0.5),
      340px 160px 0 0 rgba(245, 158, 11, 0.4),
      440px 200px 0 0 rgba(239, 68, 68, 0.6);
  }
}

.notes-overview-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #22c55e 100%);
  border: none;
  border-radius: 4px;
  padding: 12px 10px;
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 8px;
  overflow: hidden;
  margin: 0 20px;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
    background: linear-gradient(135deg, #2563eb 0%, #16a34a 100%);

    &::before {
      left: 100%;
    }
  }

  &.active {
    background: linear-gradient(135deg, #1d4ed8 0%, #15803d 100%);
    box-shadow: 0 6px 20px rgba(29, 78, 216, 0.4);
    animation: activeGlow 2s ease-in-out infinite;
  }

  &.no-notes {
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
    box-shadow: 0 4px 16px rgba(107, 114, 128, 0.3);

    &:hover {
      background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
      box-shadow: 0 8px 24px rgba(75, 85, 99, 0.4);
    }

    &.active {
      background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
      box-shadow: 0 6px 20px rgba(55, 65, 81, 0.4);
    }
  }

  .notes-icon {
    animation: pulse 2s ease-in-out infinite;
  }

  .notes-count {
    background: rgba(255, 255, 255, 0.25);
    border-radius: 12px;
    padding: 4px 10px;
    font-size: 12px;
    font-weight: 700;
    min-width: 24px;
    text-align: center;
    animation: countBounce 0.5s ease-out;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  .no-notes-text {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    padding: 4px 10px;
    font-size: 12px;
    font-weight: 700;
    min-width: 24px;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  .arrow-icon {
    transition: transform 0.3s ease;

    &.rotated {
      transform: rotate(180deg);
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes activeGlow {
  0%,
  100% {
    box-shadow: 0 6px 20px rgba(29, 78, 216, 0.4);
  }
  50% {
    box-shadow: 0 8px 28px rgba(29, 78, 216, 0.6),
      0 0 20px rgba(34, 197, 94, 0.3);
  }
}

@keyframes countBounce {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes deleteSwing {
  0% {
    transform: rotate(0deg);
  }
  15% {
    transform: rotate(-8deg) scale(1.1);
  }
  30% {
    transform: rotate(6deg) scale(1.05);
  }
  45% {
    transform: rotate(-4deg) scale(1.08);
  }
  60% {
    transform: rotate(2deg) scale(1.02);
  }
  75% {
    transform: rotate(-1deg) scale(1.01);
  }
  100% {
    transform: rotate(0deg) scale(1);
  }
}

@keyframes cssParticleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-15px) rotate(90deg) scale(1.1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-30px) rotate(180deg) scale(0.9);
    opacity: 0.4;
  }
  75% {
    transform: translateY(-10px) rotate(270deg) scale(1.05);
    opacity: 0.7;
  }
}

@keyframes gradientMove {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

// 面板动画
.notes-panel-enter-active,
.notes-panel-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notes-panel-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.notes-panel-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}
/* 响应侧边栏状态 */
:global(.hideSidebar) .notes-panel {
  --sidebar-width: 320px;
}

:global(.openSidebar) .notes-panel {
  --sidebar-width: 0px;
}

.notes-panel {
  position: absolute;
  top: calc(100% + 38px);
  right: -150px;
  width: calc(620px + var(--sidebar-width) * 0.2);
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  //   max-height: 600px; /* 调整最大高度 */
  overflow: hidden;
  z-index: 10;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 243px;

  /* 防止滚动穿透 */
  overscroll-behavior: contain;
  touch-action: pan-y;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #22c55e, #8b5cf6, #3b82f6);
    background-size: 200% 100%;
    animation: gradientMove 3s ease-in-out infinite;
  }
}

.notes-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(241, 245, 249, 0.8);
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(34, 197, 94, 0.05) 100%
  );
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1e293b;
  font-size: 16px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-btn-container {
  display: flex;
  align-items: center;

  // 调整搜索组件在头部的样式
  :deep(.notes-search-container) {
    padding: 0;
    background: transparent;
    border: none;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    margin-bottom: 0;
    gap: 4px;
  }

  // 调整搜索按钮样式以适应头部
  :deep(.search-toggle-btn) {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    box-shadow: 0 2px 6px rgba(139, 92, 246, 0.25);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 3px 10px rgba(139, 92, 246, 0.35);
    }
  }

  // 调整粒子效果容器
  :deep(.particles-container) {
    width: 50px;
    height: 50px;
  }

  // 调整搜索框容器在头部的样式
  :deep(.search-box-container) {
    gap: 6px;
  }

  // 调整搜索输入框样式
  :deep(.search-input) {
    max-width: 160px;
  }

  // 调整关闭搜索按钮
  :deep(.close-search-btn) {
    padding: 2px;
    color: #64748b;

    &:hover {
      color: #334155;
      background: rgba(100, 116, 139, 0.1);
    }
  }
}

.delete-all-btn {
  color: #ef4444;
  padding: 6px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 600;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, rgba(239, 68, 68, 0.8) 0%, rgba(239, 68, 68, 0.3) 50%, transparent 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 2px;
    background: rgba(239, 68, 68, 0.4);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &:hover {
    color: #dc2626;
    background: rgba(239, 68, 68, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);

    &::before {
      transform: translate(-50%, -50%) scale(12);
      opacity: 0;
    }

    &::after {
      transform: translate(-50%, -50%) scale(20);
      opacity: 0;
    }

    .el-icon {
      animation: deleteSwing 0.6s ease-in-out;
    }
  }

  &:active {
    transform: translateY(0) scale(0.95);
  }

  .el-icon {
    margin-right: 4px;
    transition: all 0.2s ease;
    position: relative;
    z-index: 1;
  }
}

.close-btn {
  color: #64748b;
  padding: 4px;

  &:hover {
    color: #334155;
    background: rgba(100, 116, 139, 0.1);
  }
}

.notes-list {
  max-height: 600px; /* 调整最大高度，配合面板600px高度 */
  overflow-y: auto;
  padding: 8px 12px; /* 调整内边距 */

  /* 防止滚动穿透 */
  overscroll-behavior: contain;
  touch-action: pan-y;

  &::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  scrollbar-width: none;
  -ms-overflow-style: none;
}

.note-item {
  padding: 16px 20px; /* 调整内边距，减少垂直空间 */
  margin-bottom: 10px; /* 减少间距 */
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(241, 245, 249, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: auto; /* 移除固定最小高度 */
  position: relative;

  &:hover {
    background: rgba(59, 130, 246, 0.05);
    border-color: rgba(59, 130, 246, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);

    .delete-note-btn {
      opacity: 1;
      transform: translateX(0);
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.note-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.speaker-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.speaker-circle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 700;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.speaker-name {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.note-time {
  font-size: 12px;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 2px 8px;
  border-radius: 6px;
  font-family: monospace;
}

.delete-note-btn {
  color: #ef4444;
  padding: 4px;
  border-radius: 6px;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
  min-width: auto;
  height: auto;

  &:hover {
    color: #dc2626;
    background: rgba(239, 68, 68, 0.1);
    transform: translateX(0) scale(1.1);
  }

  &:active {
    transform: translateX(0) scale(0.95);
  }
}

.note-content {
  .original-text {
    font-size: 13px; /* 稍微减小字体 */
    color: #6b7280;
    margin-bottom: 8px; /* 减少间距 */
    line-height: 1.4;
    font-style: italic;
    background: rgba(107, 114, 128, 0.05);
    padding: 6px 10px; /* 减少内边距 */
    border-radius: 6px;
    /* 单行显示，超出显示省略号 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }

  .note-text {
    font-size: 14px; /* 稍微减小字体 */
    color: #374151;
    line-height: 1.4;
    background: linear-gradient(
      135deg,
      rgba(34, 197, 94, 0.08) 0%,
      rgba(59, 130, 246, 0.08) 100%
    );
    padding: 8px 12px; /* 减少内边距 */
    border-radius: 8px; /* 减少圆角 */

    /* 单行显示，超出显示省略号 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    min-height: auto;
  }
}

.empty-notes {
  text-align: center;
  padding: 43px 20px;
  color: #9ca3af;

  .empty-icon {
    margin-bottom: 12px;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

// 搜索相关样式
.notes-search-section {
  position: relative;
  z-index: 3;
}

.search-highlight {
  background: rgba(139, 92, 246, 0.05);
//   border-left: 3px solid #8b5cf6;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(139, 92, 246, 0.08);
  }
}

.search-highlight-text {
  background: linear-gradient(135deg, #fef3c7, #fbbf24);
  color: #92400e;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(251, 191, 36, 0.3);
  animation: highlightPulse 2s ease-in-out infinite;
}

@keyframes highlightPulse {
  0%, 100% {
    box-shadow: 0 1px 3px rgba(251, 191, 36, 0.3);
  }
  50% {
    box-shadow: 0 2px 8px rgba(251, 191, 36, 0.5);
  }
}

.empty-search {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #9ca3af;
  text-align: center;

  .empty-icon {
    color: #d1d5db;
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}
