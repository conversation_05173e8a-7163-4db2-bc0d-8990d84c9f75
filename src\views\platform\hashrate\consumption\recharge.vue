<template>
 <el-dialog :model-value="open" :title="title" width="450px" destroy-on-close custom-class="recharge-dialog" align-center @update:model-value="handleDialogUpdate">
    <div class="recharge-container">
      <!-- 用户信息展示区 -->
      <div class="user-info-section">
        <el-avatar :size="60" :src="userAvatar(form.userName)" class="user-avatar"></el-avatar>
        <div class="user-details">
          <span class="user-name">{{ form.userName }}</span>
          <div class="balance-display">
            <el-icon><wallet /></el-icon>
            <span>当前余额: <b>{{ form.hashrateBalance }}</b></span>
          </div>
        </div>
      </div>

      <!-- 充值表单区 -->
      <el-form ref="hashrateRef" :model="form" :rules="rules" class="amount-form">
        <el-divider content-position="center">充值金额</el-divider>

        <!-- 快捷金额选择 -->
        <div class="quick-amount-grid">
          <div v-for="amount in [100, 500, 1000, 5000, 10000]" :key="amount" class="amount-option" 
               :class="{ active: form.rechargedHashrate === amount }" @click="form.rechargedHashrate = amount">
            <span class="amount-value">{{ amount }}</span>
          </div>
          <div class="amount-option custom" @click="showCustomInput = true">
            <span class="amount-value">自定义</span>
          </div>
        </div>

        <!-- 自定义金额输入 -->
        <div v-if="showCustomInput" class="custom-input-section">
          <el-form-item prop="rechargedHashrate">
            <el-input-number 
              v-model="form.rechargedHashrate" :min="1" :max="100000" :step="100" controls-position="right"
              class="custom-amount-input" @change="showCustomInput = true"/>
          </el-form-item>
        </div>

        <!-- 总金额显示 -->
        <div class="total-amount">
          <span class="label">充值总额:</span>
          <span class="value">{{ form.rechargedHashrate }} 点</span>
        </div>
      </el-form>

      <!-- 按钮区 -->
      <div class="action-buttons">
        <el-button @click="cancel" plain class="cancel-btn">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading" class="submit-btn">
          <el-icon><check /></el-icon>确认充值
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import { topHashrate } from "@/api/platform/hashrate";
import { getCurrentInstance } from 'vue';
import { Wallet, Check } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  form: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: ''
  }
});
const emits = defineEmits(['close', 'rechargeSuccess']);
const submitLoading = ref(false);
const showCustomInput = ref(false);
const rules = {
  rechargedHashrate: [
    { required: true, message: '请输入充值金额', trigger: 'blur' },
    { type: 'number', message: '充值金额必须为数字', trigger: 'blur' }
  ]
};

/** 用户头像生成函数 */
function userAvatar(username) {
  if (!username) return '';
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#9B59B6', '#3498DB'];
  const index = username.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&background=${colors[index].substring(1)}&color=fff`;
}

// 取消按钮
function cancel() {
  emits('close');
}

/** 充值提交操作 */
function submitForm() {
  proxy.$refs["hashrateRef"].validate(valid => {
    if (valid) {
      submitLoading.value = true;
      topHashrate(props.form).then(response => {
        proxy.$modal.msgSuccess("充值成功");
        emits('rechargeSuccess');
        emits('close');
      }).finally(() => {
        submitLoading.value = false;
      });
    }
  });
}
</script>    
  
  <style scoped lang="scss">
  :deep(.recharge-dialog) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    .el-dialog__header {
      background: linear-gradient(135deg, #67C23A, #85ce61);
      padding: 16px 24px;
      margin: 0;
      .el-dialog__title {
        color: white;
        font-weight: 600;
        font-size: 18px;
      }
      .el-dialog__headerbtn {
        top: 16px;
        right: 16px;
        .el-dialog__close {
          color: #fff;
        }
      }
    }
    .el-dialog__body {
      padding: 0;
    }
    .el-dialog__footer {
      display: none;
    }
  }
  
  .recharge-container {
    padding: 0;
    .user-info-section {
      padding: 24px;
      display: flex;
      align-items: center;
      background: #f8f9fa;
      .user-avatar {
        border: 3px solid #fff;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-right: 16px;
      }
      .user-details {
        flex: 1;
        .user-name {
          display: block;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 6px;
        }
        .balance-display {
          display: flex;
          align-items: center;
          color: #67C23A;
          font-size: 14px;
          .el-icon {
            margin-right: 6px;
          }
          b {
            font-weight: 600;
            margin-left: 4px;
          }
        }
      }
    }
    .amount-form {
      padding: 0 24px;
      .el-divider {
        margin: 16px 0;
        .el-divider__text {
          color: #909399;
          font-size: 14px;
          background: #fff;
        }
      }
    }
    .quick-amount-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      margin: 20px 0;
      .amount-option {
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f7fa;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 2px solid transparent;
        &:hover {
          transform: translateY(-2px);
          background: #ecf5ff;
        }
        &.active {
          border-color: #67C23A;
          background: rgba(103, 194, 58, 0.1);
          .amount-value {
            color: #67C23A;
          }
        }
        &.custom {
          background: rgba(64, 158, 255, 0.1);
          .amount-value {
            color: #409EFF;
          }
        }
        .amount-value {
          font-weight: 600;
          font-size: 16px;
          color: #606266;
          &::before {
            content: '¥';
            font-size: 14px;
            margin-right: 2px;
          }
          &::after {
            content: '点';
            font-size: 14px;
            margin-left: 2px;
          }
        }
      }
    }
    .custom-input-section {
      margin: 16px 0;
      text-align: center;
      .custom-amount-input {
        width: 100%;
        :deep(.el-input__wrapper) {
          box-shadow: 0 0 0 1px #dcdfe6 inset;
          padding: 8px;
          &:focus-within {
            box-shadow: 0 0 0 1px #67C23A inset;
          }
        }
      }
    }
    .total-amount {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 8px;
      margin: 20px 0;
      text-align: center;
      .label {
        font-size: 14px;
        color: #909399;
        margin-right: 8px;
      }
      .value {
        font-size: 20px;
        font-weight: 600;
        color: #67C23A;
        &::after {
          content: '';
          font-size: 16px;
        }
      }
    }
    .action-buttons {
      padding: 16px 24px 24px;
      display: flex;
      justify-content: center;
      gap: 16px;
      .cancel-btn, .submit-btn {
        min-width: 120px;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s;
      }
      .submit-btn {
        background: linear-gradient(135deg, #67C23A, #85ce61);
        border: none;
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
        }
        .el-icon {
          margin-right: 6px;
        }
      }
    }
  }
  </style>    