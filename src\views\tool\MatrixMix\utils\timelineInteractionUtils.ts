/**
 * 时间轴交互工具类
 * 负责处理滚动同步、事件处理等交互功能
 */

/**
 * 同步滚动位置
 */
export function syncScrollPosition(
  sourceElement: HTMLElement,
  targetElement: HTMLElement,
  direction: 'horizontal' | 'vertical' | 'both' = 'both'
) {
  if (direction === 'horizontal' || direction === 'both') {
    targetElement.scrollLeft = sourceElement.scrollLeft;
  }
  if (direction === 'vertical' || direction === 'both') {
    targetElement.scrollTop = sourceElement.scrollTop;
  }
}

/**
 * 处理时间轴交互（点击跳转）
 */
export function handleTimelineClick(
  event: MouseEvent,
  rulerElement: HTMLElement,
  seekHandler: (time: number) => void,
  PIXELS_PER_SECOND: number = 50
) {
  const rect = rulerElement.getBoundingClientRect();
  const clickX = event.clientX - rect.left;
  const clickTime = Math.max(0, clickX / PIXELS_PER_SECOND);
  seekHandler(clickTime);
}

/**
 * 创建滚动处理器
 */
export function createScrollHandler(
  mainScrollAreaRef: HTMLElement | null,
  rulerScrollContainerRef: HTMLElement | null
) {
  return function handleScroll() {
    if (!mainScrollAreaRef || !rulerScrollContainerRef) return;
    
    // 同步水平滚动到标尺
    syncScrollPosition(mainScrollAreaRef, rulerScrollContainerRef, 'horizontal');
  };
}

/**
 * 创建标尺滚动处理器
 */
export function createRulerScrollHandler(
  mainScrollAreaRef: HTMLElement | null,
  rulerScrollContainerRef: HTMLElement | null
) {
  return function handleRulerScroll() {
    if (!mainScrollAreaRef || !rulerScrollContainerRef) return;
    
    // 同步水平滚动到主区域
    syncScrollPosition(rulerScrollContainerRef, mainScrollAreaRef, 'horizontal');
  };
}

/**
 * 计算总轨道数
 */
export function calculateTotalTracksCount(
  videoTracksCount: number,
  audioTracksCount: number,
  subtitleTracksCount: number
): number {
  return videoTracksCount + audioTracksCount + subtitleTracksCount;
}

/**
 * 清理拖拽状态
 */
export function cleanupDragState() {
  // 清理相关的状态变量
  return {
    dragOverTrackType: null,
    dragOverTrackIndex: null
  };
}
