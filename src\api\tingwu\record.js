import request from '@/utils/request'


/**
 * 提交实时任务
 */
export function summitRealTimeTask() {
    return request({
        url: '/tingwu/summitRealTimeTask',
        method: 'post'
    })
}
/**
 * 提交离线任务
 */
export function summitOfflineTask(data) {
    return request({
        url: '/tingwu/summitOfflineTask',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'multipart/form-data',
        }
    })
}


/**
 * 获取任务状态和结果
 * @param {string} taskId - 任务ID
 */
export function getStatusAndResultByTaskId(taskId) {
    return request({
        url: '/tingwu/getStatusAndResultByTaskId',
        method: 'get',
        params: {
            taskId: taskId
        },
    })
}

// ==================== 音视频任务管理接口 ====================

/**
 * 查询音视频任务列表
 * @param {Object} query - 查询参数
 */
export function getTingwuTransList(query) {
    return request({
        url: '/tingwu/trans/list',
        method: 'get',
        params: query
    })
}

/**
 * 导出音视频任务列表
 * @param {Object} query - 查询参数
 */
export function exportTingwuTrans(query) {
    return request({
        url: '/tingwu/trans/export',
        method: 'post',
        data: query,
    })
}

/**
 * 获取音视频任务详细信息
 * @param {number} vaId - 任务ID
 */
export function getTingwuTrans(vaId) {
    return request({
        url: `/tingwu/trans/${vaId}`,
        method: 'get'
    })
}

/**
 * 新增音视频任务
 * @param {Object} data - 任务数据
 */
export function addTingwuTrans(data) {
    return request({
        url: '/tingwu/trans',
        method: 'post',
        data: data
    })
}

/**
 * 修改音视频任务
 * @param {Object} data - 任务数据
 */
export function updateTingwuTrans(data) {
    return request({
        url: '/tingwu/trans',
        method: 'put',
        data: data
    })
}

/**
 * 删除音视频任务
 * @param {Array|number} vaIds - 任务ID数组或单个ID
 */
export function delTingwuTrans(vaIds) {
    return request({
        url: `/tingwu/trans/${vaIds}`,
        method: 'delete'
    })
}

/**
 * 根据任务ID修改音视频任务
 * @param {Object} data - 任务数据，必须包含taskId
 */
export function updateTingwuTransByTaskId(data) {
    return request({
        url: '/tingwu/trans/updateByTaskId',
        method: 'put',
        data: data
    })
}