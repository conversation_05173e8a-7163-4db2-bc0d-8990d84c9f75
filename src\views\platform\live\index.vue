<script setup name="live">
import scenecon from "./scenecon.vue";
import goods from "./goods.vue";
import { listCategory, delCategory, addCategory, updateCategory, } from "@/api/platform/category";
import { useRoute } from "vue-router";
const categorys = ref([])
const route = useRoute()
async function getCategory() {
  const res = await listCategory({ projectId: route.params.projectId });
  const _categorys = {};
  res.rows.forEach(item => {
    _categorys[item.categoryId] = item.categoryTitle;
  });
  categorys.value = _categorys;
}
getCategory()
</script>
<template>
  <el-row :gutter="20">
    <el-col :span="7">
      <scenecon :categorys="categorys" />
    </el-col>
    <el-col :span="17">
      <goods :categorys="categorys" />
    </el-col>
  </el-row>
</template>
<style lang="scss" scoped></style>