<template>
  <div class="original-section">
    <div class="section-header">
      <h3 class="section-title">原文</h3>
    </div>
    <div class="conversation-list-container">
      <div class="conversation-list">
        <!-- 真实对话内容 -->
        <div
          v-for="(conversation, index) in formattedConversations"
          :key="index"
          class="conversation-item"
          :class="[
            `speaker-${conversation.speakerId}`,
            {
              'currently-playing': currentPlayingIndex === index,
              'search-result': isSearchResult(index),
              'current-search-result': isCurrentSearchResult(index),
              'highlighted': highlightedIndex === index,
              'clickable': true
            }
          ]"
          @click="handleConversationItemClick(conversation, index)"
          :title="getConversationItemTitle(conversation, index)"
        >
        <div class="conversation-header">
          <div class="left-section">
            <div class="speaker-id-circle" :style="{ backgroundColor: getSpeakerColor(conversation.speakerId) }">
              {{ conversation.speakerId }}
            </div>
            <div class="timestamp">
              <span v-if="currentPlayingIndex === index" class="playing-indicator">▶</span>
              {{ formatTimestamp(conversation.startTime) }} - {{ formatTimestamp(conversation.endTime) }}
            </div>
          </div>
          <div class="header-right">
            <div class="action-buttons">
              <!-- 笔记按钮 -->
              <el-button
                v-if="editingIndex !== index"
                @click.stop="openNotesDialog(conversation, index)"
                type="text"
                size="default"
                class="notes-btn"
                :class="{ 'has-notes': conversation.notes && conversation.notes.trim() }"
                :title="conversation.notes && conversation.notes.trim() ? '查看/编辑笔记' : '添加笔记'"
              >
                <el-icon size="18"><EditPen /></el-icon>
              </el-button>
              <!-- 编辑按钮 -->
              <el-button
                @click.stop="openTextDialog(conversation, index)"
                type="text"
                size="default"
                class="edit-btn"
                title="编辑文本"
              >
                <el-icon size="18"><Edit /></el-icon>
              </el-button>

            </div>
          </div>
        </div>
        <!-- 显示模式 -->
        <p
          class="conversation-text"
          v-html="renderTextWithWordHighlight(conversation)"
          @click="handleWordClickEvent"
        >
        </p>


      </div>

        <!-- 无数据提示 -->
        <div v-if="formattedConversations.length === 0" class="no-data">
          <p>暂无对话内容</p>
        </div>
      </div>
    </div>

    <!-- 笔记编辑弹窗 -->
    <NotesEditDialog
      v-model:visible="notesDialogVisible"
      :conversation-data="currentNotesConversation"
      :conversation-index="currentNotesIndex"
      @save-notes="handleSaveNotes"
    />

    <!-- 原文编辑弹窗 -->
    <TextEditDialog
      v-model:visible="textDialogVisible"
      :conversation-data="currentTextConversation"
      :conversation-index="currentTextIndex"
      @save-text="handleSaveText"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { Edit, EditPen } from '@element-plus/icons-vue'
import NotesEditDialog from './NotesEditDialog.vue'
import TextEditDialog from './TextEditDialog.vue'

const props = defineProps({
  paragraphs: {
    type: String,
    default: ''
  },
  currentPlayingIndex: {
    type: Number,
    default: -1
  },
  searchKeyword: {
    type: String,
    default: ''
  },
  searchResults: {
    type: Array,
    default: () => []
  },
  currentSearchIndex: {
    type: Number,
    default: -1
  },
  currentPlayingTime: {
    type: Number,
    default: 0
  },
  highlightedIndex: {
    type: Number,
    default: -1
  }
})

const emit = defineEmits(['conversation-click', 'text-updated', 'notes-updated'])

// 笔记弹窗相关状态
const notesDialogVisible = ref(false)
const currentNotesConversation = ref({})
const currentNotesIndex = ref(-1)

// 原文编辑弹窗相关状态
const textDialogVisible = ref(false)
const currentTextConversation = ref({})
const currentTextIndex = ref(-1)

// 格式化对话内容 - 处理新的数据格式
const formattedConversations = computed(() => {
  if (!props.paragraphs) return []
  try {
    const paragraphsData = JSON.parse(props.paragraphs)

    // 验证数据结构
    if (!Array.isArray(paragraphsData)) {
      console.error('paragraphsData 不是数组格式:', typeof paragraphsData, paragraphsData)
      return []
    }

    return paragraphsData
  } catch (error) {
    console.error('解析对话内容失败:', error)
    return []
  }
})

// 格式化时间显示（毫秒转换为 MM:SS 格式）
const formatTimestamp = (milliseconds) => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 获取说话人颜色
const getSpeakerColor = (speakerId) => {
  const colors = [
    '#1890ff', // 蓝色
    '#52c41a', // 绿色
    '#fa8c16', // 橙色
    '#eb2f96', // 粉色
    '#722ed1', // 紫色
    '#13c2c2', // 青色
    '#f5222d', // 红色
    '#faad14', // 黄色
  ]
  const index = parseInt(speakerId) % colors.length
  return colors[index]
}

// 点击对话item跳转到对应音频位置
const handleConversationClick = (conversation) => {
  emit('conversation-click', conversation)
}

// 处理对话项点击事件
const handleConversationItemClick = (conversation) => {
  // 正常跳转音频
  handleConversationClick(conversation)
}

// 获取对话项的标题提示
const getConversationItemTitle = (conversation) => {
  return `点击跳转到音频位置: ${formatTimestamp(conversation.startTime)}`
}

// 渲染带有词汇高亮的文本
const renderTextWithWordHighlight = (conversation) => {
  if (!conversation.wordDetails || !Array.isArray(conversation.wordDetails)) {
    // 如果没有词汇详情，使用原始文本并应用搜索高亮
    return highlightSearchKeyword(conversation.text, props.searchKeyword)
  }

  const currentTime = props.currentPlayingTime
  let html = ''

  // 动态调整时间容差，根据词汇长度和播放速度
  const baseTolerance = 50 // 基础容差50ms

  // 找到最匹配的当前词汇
  let currentWordIndex = -1
  let bestMatch = { index: -1, score: Infinity }

  const isInCurrentConversation = props.currentPlayingIndex !== -1 &&
    formattedConversations.value[props.currentPlayingIndex] === conversation

  if (isInCurrentConversation) {
    conversation.wordDetails.forEach((word, index) => {
      // 计算时间距离分数
      const startDistance = Math.abs(currentTime - word.startTime)
      const endDistance = Math.abs(currentTime - word.endTime)
      const minDistance = Math.min(startDistance, endDistance)

      // 如果当前时间在词汇时间范围内，优先选择
      if (currentTime >= word.startTime && currentTime <= word.endTime) {
        currentWordIndex = index
        return
      }

      // 否则选择距离最近的词汇（在容差范围内）
      const tolerance = baseTolerance + (word.endTime - word.startTime) * 0.1 // 根据词汇长度调整容差
      if (minDistance <= tolerance && minDistance < bestMatch.score) {
        bestMatch = { index, score: minDistance }
      }
    })

    // 如果没有完全匹配的词汇，使用最佳匹配
    if (currentWordIndex === -1 && bestMatch.index !== -1) {
      currentWordIndex = bestMatch.index
    }
  }

  conversation.wordDetails.forEach((word, index) => {
    let wordText = word.text
    let wordClass = 'word'

    // 如果是当前播放的词汇，使用高亮样式
    if (index === currentWordIndex) {
      wordClass = 'current-word'
      // 调试信息（减少日志输出）
      console.log(`当前高亮词汇: "${wordText}" (${word.startTime}-${word.endTime}ms), 当前时间: ${currentTime}ms, 匹配分数: ${bestMatch.score}`)
    }

    // 应用搜索关键词高亮（在词汇内部）
    if (props.searchKeyword && props.searchKeyword.trim()) {
      wordText = highlightSearchKeyword(wordText, props.searchKeyword)
    }

    // 添加点击事件处理，使词汇可以跳转到对应音频位置
    const timeTooltip = `点击跳转到 ${formatTimestamp(word.startTime)}`
    html += `<span class="${wordClass} clickable-word" data-start="${word.startTime}" data-end="${word.endTime}" title="${timeTooltip}">${wordText}</span>`
  })

  return html
}

// 高亮搜索关键词
const highlightSearchKeyword = (text, keyword) => {
  if (!keyword || !keyword.trim()) {
    return text
  }

  const regex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(regex, '<mark class="search-highlight">$1</mark>')
}



// 检查当前对话是否是搜索结果
const isSearchResult = (index) => {
  return props.searchResults.some(result => result.conversationIndex === index)
}

// 检查当前对话是否是当前搜索结果
const isCurrentSearchResult = (index) => {
  if (props.currentSearchIndex === -1 || !props.searchResults.length) return false
  const currentResult = props.searchResults[props.currentSearchIndex]
  return currentResult && currentResult.conversationIndex === index
}

// 原文编辑弹窗相关方法
const openTextDialog = (conversation, index) => {
  currentTextConversation.value = conversation
  currentTextIndex.value = index
  textDialogVisible.value = true
}

const handleSaveText = async (data) => {
  try {
    // 发送文本更新事件给父组件并等待完成
    await new Promise((resolve, reject) => {
      emit('text-updated', {
        index: data.index,
        newText: data.newText,
        conversation: data.conversation,
        resolve,
        reject
      })
    })

    // 调用传入的resolve函数表示保存成功
    data.resolve()
  } catch (error) {
    console.error('保存文本失败:', error)
    // 调用传入的reject函数表示保存失败
    data.reject(error)
  }
}

// 笔记弹窗相关方法
const openNotesDialog = (conversation, index) => {
  currentNotesConversation.value = conversation
  currentNotesIndex.value = index
  notesDialogVisible.value = true
}

const handleSaveNotes = async (data) => {
  try {
    // 发送笔记更新事件给父组件并等待完成
    await new Promise((resolve, reject) => {
      emit('notes-updated', {
        index: data.index,
        notes: data.notes,
        resolve,
        reject
      })
    })

    // 调用传入的resolve函数表示保存成功
    data.resolve()
  } catch (error) {
    console.error('保存笔记失败:', error)
    // 调用传入的reject函数表示保存失败
    data.reject(error)
  }
}

// 处理词汇点击事件
const handleWordClickEvent = (event) => {
  // 检查点击的是否是词汇元素或其子元素（如搜索高亮的mark标签）
  let target = event.target

  // 如果点击的是搜索高亮标签，向上查找词汇元素
  if (target.tagName === 'MARK' && target.classList.contains('search-highlight')) {
    target = target.parentElement
  }

  if (target && target.classList.contains('clickable-word')) {
    const startTime = parseInt(target.getAttribute('data-start'))
    const endTime = parseInt(target.getAttribute('data-end'))

    if (!isNaN(startTime)) {
      console.log(`点击词汇跳转到音频位置: ${startTime}ms - ${endTime}ms`)

      // 创建一个模拟的对话对象来触发跳转
      const mockConversation = {
        startTime: startTime,
        endTime: endTime,
        text: target.textContent || target.innerText
      }

      // 触发对话点击事件，跳转到音频位置
      emit('conversation-click', mockConversation)

      // 阻止事件冒泡，避免触发对话项的点击事件
      event.stopPropagation()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../styles/conversation-list.scss';
</style>
