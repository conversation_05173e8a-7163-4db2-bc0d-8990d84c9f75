<template>
  <div class="auth-form-wrapper">
    <h2 class="form-title">登录</h2>
    <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="auth-form">
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          placeholder="请输入账号"
          :prefix-icon="User"
        />
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          placeholder="请输入密码"
          :prefix-icon="Lock"
          @keyup.enter="handleLogin"
        />
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <div class="captcha-wrapper">
          <el-input
            v-model="loginForm.code"
            placeholder="验证码"
            :prefix-icon="Key"
            @keyup.enter="handleLogin"
          />
          <img :src="codeUrl" @click="getCode" class="captcha-img" />
        </div>
      </el-form-item>
      <div class="form-options">
        <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
        <a class="form-link">忘记密码？</a>
      </div>
      <el-button :loading="loading" type="primary" class="submit-btn" @click.prevent="handleLogin">
        {{ loading ? '登录中...' : '登录' }}
      </el-button>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { getCodeImg } from "@/api/login"
import { getConfigKey } from '@/api/system/config'
import { encrypt, decrypt } from "@/utils/jsencrypt"
import useUserStore from '@/store/modules/user'
import { User, Lock, Key } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { getCurrentInstance } from 'vue'

const emit = defineEmits(['switch-mode'])
const userStore = useUserStore()
const router = useRouter()
const { proxy } = getCurrentInstance()

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: ""
})

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
}

const codeUrl = ref("")
const loading = ref(false)
const captchaEnabled = ref(false)
const redirect = ref(undefined)

// 初始化验证码配置
getConfigKey("sys.account.captchaEnabled").then(res => {
  captchaEnabled.value = res.msg === 'true'
})

// 处理登录
async function handleLogin() {
  try {
    const valid = await proxy.$refs.loginRef.validate()
    if (!valid) return
    
    loading.value = true
    
    // 处理记住密码
    if (loginForm.value.rememberMe) {
      localStorage.setItem("username", loginForm.value.username)
      localStorage.setItem("password", encrypt(loginForm.value.password))
      localStorage.setItem("rememberMe", loginForm.value.rememberMe)
    } else {
      localStorage.removeItem("username")
      localStorage.removeItem("password")
      localStorage.removeItem("rememberMe")
    }
    
    // 登录
    await userStore.login(loginForm.value)
    router.push({ path: redirect.value || "/" })
  } catch (error) {
    loading.value = false
    if (captchaEnabled.value) {
      getCode()
    }
  }
}

// 获取验证码
function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img
      loginForm.value.uuid = res.uuid
    }
  })
}

// 获取记住的账号密码
function getCookie() {
  const username = localStorage.getItem("username")
  const password = localStorage.getItem("password")
  const rememberMe = localStorage.getItem("rememberMe")
  loginForm.value = {
    username: username || loginForm.value.username,
    password: password ? decrypt(password) : loginForm.value.password,
    rememberMe: Boolean(rememberMe),
    code: "",
    uuid: ""
  }
}

// 初始化
getCode()
getCookie()
</script>

<style lang="scss" scoped>
@use '@/assets/styles/auth-form.scss';
</style> 