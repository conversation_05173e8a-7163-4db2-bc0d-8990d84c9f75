<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val: boolean) => emit('update:visible', val)"
    title="导出云剪辑工程"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="export-form"
    >
      <div class="form-section">
        <h4 class="section-title">基本设置</h4>
        
        <el-form-item label="导出类型" prop="exportType">
          <el-select v-model="form.exportType" placeholder="请选择导出类型" style="width: 100%">
            <el-option label="时间线" value="BaseTimeline" />
            <el-option label="Adobe PR 工程" value="AdobePremierePro" />
          </el-select>
        </el-form-item>

        <el-form-item label="OSS存储桶" prop="bucket">
          <el-input v-model="form.bucket" placeholder="请输入OSS Bucket名称" />
        </el-form-item>

        <el-form-item label="文件路径前缀" prop="prefix">
          <el-input v-model="form.prefix" placeholder="可选，不填时默认为根目录" />
        </el-form-item>
      </div>

      <div class="form-section">
        <h4 class="section-title">视频设置</h4>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="视频宽度" prop="width">
              <el-input-number
                v-model="form.width"
                :min="1"
                :max="7680"
                placeholder="可选，自动估算"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="视频高度" prop="height">
              <el-input-number
                v-model="form.height"
                :min="1"
                :max="4320"
                placeholder="可选，自动估算"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          开始导出
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { submitProjectExportJob } from '../../api/videoEdit';
import type { SubmitProjectExportRequest, ProjectInfo } from '../../api/videoEdit';

interface Props {
  visible: boolean;
  projectInfo?: ProjectInfo | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success', jobId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const loading = ref(false);

const form = reactive<SubmitProjectExportRequest>({
  projectId: '',
  exportType: 'BaseTimeline',
  bucket: '',
  prefix: '',
  width: undefined,
  height: undefined,
  notifyAddress: ''
});

const rules: FormRules = {
  exportType: [
    { required: true, message: '请选择导出类型', trigger: 'change' }
  ],
  bucket: [
    { required: true, message: '请输入OSS Bucket名称', trigger: 'blur' },
    { pattern: /^[a-z0-9][a-z0-9-]{1,61}[a-z0-9]$/, message: 'Bucket名称格式不正确', trigger: 'blur' }
  ]
};

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.projectInfo) {
    form.projectId = props.projectInfo.ProjectId;
    // 重置表单
    resetForm();
  }
});

const resetForm = () => {
  form.exportType = 'BaseTimeline';
  form.bucket = 'szb-pc';
  form.prefix = '';
  form.width = undefined;
  form.height = undefined;
  form.notifyAddress = '';
  formRef.value?.clearValidate();
};

const handleClose = () => {
  emit('update:visible', false);
  resetForm();
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    loading.value = true;
    
    const exportData: SubmitProjectExportRequest = {
      projectId: form.projectId,
      exportType: form.exportType,
      bucket: form.bucket,
      prefix: form.prefix || undefined,
      width: form.width || undefined,
      height: form.height || undefined,
      notifyAddress: form.notifyAddress || undefined
    };

    const response = await submitProjectExportJob(exportData);
    
    if (response.code === 200) {
      ElMessage.success('导出任务提交成功');
      emit('success', response.data.JobId);
      handleClose();
    } else {
      ElMessage.error(`导出任务提交失败: ${response.msg}`);
    }
  } catch (error) {
    console.error('提交导出任务失败:', error);
    ElMessage.error('提交导出任务失败');
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.export-form {
  .form-section {
    margin-bottom: 24px;
    
    .section-title {
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      border-bottom: 1px solid #e4e7ed;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number) {
  width: 100%;
}
</style>
