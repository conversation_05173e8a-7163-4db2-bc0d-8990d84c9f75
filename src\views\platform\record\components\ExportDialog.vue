<template>
  <div>
    <!-- 导出按钮 -->
    <div class="export-button-container">
      <el-button
        @click="showExportDialog"
        type="success"
        :icon="Download"
        size="default"
        class="export-btn animated-export-btn"
        title="导出原文为TXT文件"
        @mouseenter="startParticleAnimation"
        @mouseleave="stopParticleAnimation"
      >
        <span class="btn-text">导出原文</span>
        <div class="particles-container" ref="particlesContainer">
          <div
            v-for="n in 12"
            :key="n"
            class="particle"
            :style="{ animationDelay: (n * 0.1) + 's' }"
          ></div>
        </div>
        <div class="btn-glow"></div>
      </el-button>
    </div>

    <!-- 导出配置弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="导出原文配置"
      width="400px"
      :before-close="cancelExport"
      :z-index="10000"
      append-to-body
    >
      <div class="export-config">
        <div class="config-section">
          <h4 class="section-title">选择导出格式</h4>
          <div class="config-options">
            <el-checkbox v-model="exportOptions.includeTime" size="large">
              包含音频时间 (如：[00:15 - 00:30])
            </el-checkbox>
            <el-checkbox v-model="exportOptions.includeSpeaker" size="large">
              包含说话人信息 (如：说话人1：)
            </el-checkbox>
          </div>
        </div>

        <div class="preview-section">
          <h4 class="section-title">预览效果</h4>
          <div class="preview-content">
            <div v-if="exportOptions.includeTime || exportOptions.includeSpeaker" class="preview-with-info">
              <div class="preview-header">
                <span v-if="exportOptions.includeTime" class="preview-time">[00:15 - 00:30] </span>
                <span v-if="exportOptions.includeSpeaker" class="preview-speaker">说话人1：</span>
              </div>
              <div class="preview-text">这是一段示例对话内容</div>
            </div>
            <div v-else class="preview-simple">
              这是一段示例对话内容
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelExport">取消</el-button>
          <el-button type="primary" @click="confirmExport">
            确认导出
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 粒子容器引用
const particlesContainer = ref(null)

// 定义 props
const props = defineProps({
  // 录音记录数据
  record: {
    type: Object,
    required: true
  },
  // 格式化的对话数据
  conversations: {
    type: Array,
    required: true
  }
})

// 弹窗显示状态
const dialogVisible = ref(false)

// 导出配置选项
const exportOptions = ref({
  includeTime: true, // 是否包含音频时间
  includeSpeaker: true // 是否包含说话人信息
})

// 格式化时间显示（毫秒转换为 MM:SS 格式）
const formatTimestamp = (milliseconds) => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 显示导出弹窗
const showExportDialog = () => {
  if (!props.record || !props.conversations.length) {
    ElMessage.warning('暂无原文内容可导出')
    return
  }

  dialogVisible.value = true
}

// 确认导出
const confirmExport = () => {
  try {
    // 构建导出内容
    let content = `${props.record.vaName}\n`
    content += `导出时间：${new Date().toLocaleString()}\n`
    content += `${'='.repeat(50)}\n\n`

    // 添加对话内容 - 根据用户选择决定格式
    props.conversations.forEach((conversation) => {
      let line = ''

      // 是否包含时间信息
      if (exportOptions.value.includeTime) {
        const startTime = formatTimestamp(conversation.startTime)
        const endTime = formatTimestamp(conversation.endTime)
        line += `[${startTime} - ${endTime}] `
      }

      // 是否包含说话人信息
      if (exportOptions.value.includeSpeaker) {
        const speakerName = conversation.speakerName || `说话人${conversation.speakerId}`
        line += `${speakerName}：`
      }

      // 如果包含时间或说话人信息，需要换行
      if (exportOptions.value.includeTime || exportOptions.value.includeSpeaker) {
        content += `${line}\n${conversation.text}\n\n`
      } else {
        content += `${conversation.text}\n`
      }
    })

    // 创建Blob对象
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 设置文件名
    const fileName = `${props.record.vaName}_原文_${new Date().toISOString().slice(0, 10)}.txt`
    link.download = fileName

    // 触发下载
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    // 关闭弹窗
    dialogVisible.value = false

    ElMessage.success('原文导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 取消导出
const cancelExport = () => {
  dialogVisible.value = false
}

// 粒子动画控制
const startParticleAnimation = () => {
  if (particlesContainer.value) {
    particlesContainer.value.classList.add('active')
  }
}

const stopParticleAnimation = () => {
  if (particlesContainer.value) {
    particlesContainer.value.classList.remove('active')
  }
}
</script>

<style scoped>
/* 导出按钮容器 */
.export-button-container {
  position: relative;
  display: inline-block;
}

/* 导出按钮样式 */
.export-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.animated-export-btn {
  animation: buttonPulse 3s ease-in-out infinite;
}

.export-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.5);
  animation-play-state: paused;
}

.export-btn:active {
  transform: translateY(-1px) scale(1.02);
  transition: all 0.1s ease;
}

/* 按钮文字动画 */
.btn-text {
  position: relative;
  z-index: 3;
  display: inline-block;
  animation: textShimmer 2s ease-in-out infinite;
}

/* 按钮发光效果 */
.btn-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #10b981, #059669, #047857);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.export-btn:hover .btn-glow {
  opacity: 0.7;
  animation: glowPulse 1.5s ease-in-out infinite;
}

/* 粒子容器 */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
  overflow: hidden;
  border-radius: inherit;
}

/* 粒子样式 */
.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  opacity: 0;
  animation: particleFloat 2s ease-in-out infinite;
}

/* 粒子位置分布 */
.particle:nth-child(1) { left: 10%; top: 20%; }
.particle:nth-child(2) { left: 20%; top: 80%; }
.particle:nth-child(3) { left: 30%; top: 40%; }
.particle:nth-child(4) { left: 40%; top: 70%; }
.particle:nth-child(5) { left: 50%; top: 30%; }
.particle:nth-child(6) { left: 60%; top: 60%; }
.particle:nth-child(7) { left: 70%; top: 20%; }
.particle:nth-child(8) { left: 80%; top: 75%; }
.particle:nth-child(9) { left: 90%; top: 45%; }
.particle:nth-child(10) { left: 15%; top: 55%; }
.particle:nth-child(11) { left: 75%; top: 35%; }
.particle:nth-child(12) { left: 85%; top: 65%; }

/* 激活状态下的粒子 */
.particles-container.active .particle {
  animation: particleFloatActive 1.5s ease-in-out infinite;
}

/* 动画关键帧 */
@keyframes buttonPulse {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  }
}

@keyframes textShimmer {
  0%, 100% {
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
  }
  50% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.6), 0 0 15px rgba(255, 255, 255, 0.4);
  }
}

@keyframes glowPulse {
  0%, 100% {
    opacity: 0.7;
    filter: blur(2px);
  }
  50% {
    opacity: 1;
    filter: blur(4px);
  }
}

@keyframes particleFloat {
  0%, 100% {
    opacity: 0;
    transform: translateY(0px) scale(0.5);
  }
  50% {
    opacity: 0.6;
    transform: translateY(-10px) scale(1);
  }
}

@keyframes particleFloatActive {
  0% {
    opacity: 0;
    transform: translateY(0px) scale(0.5) rotate(0deg);
  }
  25% {
    opacity: 0.8;
    transform: translateY(-8px) scale(1.2) rotate(90deg);
  }
  50% {
    opacity: 1;
    transform: translateY(-15px) scale(1.5) rotate(180deg);
  }
  75% {
    opacity: 0.8;
    transform: translateY(-8px) scale(1.2) rotate(270deg);
  }
  100% {
    opacity: 0;
    transform: translateY(0px) scale(0.5) rotate(360deg);
  }
}

/* 导出配置弹窗样式 */
.export-config {
  padding: 0;
}

.config-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.config-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-options .el-checkbox {
  margin: 0;
  font-size: 14px;
}

.config-options .el-checkbox__label {
  color: #374151;
  font-weight: 500;
}

.preview-section {
  background: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.preview-content {
  background: #ffffff;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.preview-with-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.preview-header {
  display: flex;
  align-items: center;
}

.preview-time {
  color: #3b82f6;
  font-weight: 600;
}

.preview-speaker {
  color: #10b981;
  font-weight: 600;
}

.preview-text {
  color: #374151;
  margin-left: 0;
}

.preview-simple {
  color: #374151;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 确保弹窗层级 */
:deep(.el-dialog) {
  z-index: 10000 !important;
}

:deep(.el-overlay) {
  z-index: 9999 !important;
}
</style>
