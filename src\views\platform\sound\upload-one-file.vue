<template>
    <el-upload ref="upload" class="upload-demo" :show-file-list="false" :limit="1" :on-exceed="handleExceed"
        :auto-upload="false" :on-change="handleChange">
        <template #trigger>
            <el-button type="primary">请选择文件</el-button>
        </template>
    </el-upload>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { genFileId } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'

const upload = ref<UploadInstance>()
const model = defineModel<File>()
const handleExceed: UploadProps['onExceed'] = (files) => {
    upload.value!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    upload.value!.handleStart(file)
}
const handleChange: UploadProps['onChange'] = (file, files) => {
    model.value = file.raw
}
</script>