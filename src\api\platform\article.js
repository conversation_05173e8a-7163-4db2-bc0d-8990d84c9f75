import request from '@/utils/request'

// 查询文案列表
export function listArticle(query) {
  return request({
    url: '/platform/article/list',
    method: 'get',
    params: query
  })
}

// 查询文案详细
export function getArticle(articleId) {
  return request({
    url: '/platform/article/' + articleId,
    method: 'get'
  })
}

// 新增文案
export function addArticle(data) {
  return request({
    url: '/platform/article',
    method: 'post',
    data: data,
    headers: {
      'repeatSubmit': false
    },
  })
}

// 修改文案
export function updateArticle(data) {
  return request({
    url: '/platform/article',
    method: 'put',
    data: data
  })
}

// 删除文案
export function delArticle(articleId) {
  return request({
    url: '/platform/article/' + articleId,
    method: 'delete'
  })
}

//智能生成文案
export function generateArticle(articles) {
  return request({
    url: '/platform/article/generate',
    method: 'post', 
    data: articles, // 传递数组，与后端@RequestBody List<String>对应
    timeout: 60000
  })
}

//导出当前项目数据
export function exportTxt(projectId) {
  return request({
    url: '/platform/article/exportTxt/'+projectId,
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }
  })
}

//批量新增文案
export function batchAddArticles(data) {
  return request({
    url: '/platform/article/batch',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json',
      'repeatSubmit': false
    },
  });
}

// 根据文案Ids查询文案信息
export function getMutipleArticle(articleIds) {
  return request({
    url: '/platform/article/getMutipleArticle',
    method: 'post',
    data: articleIds,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}