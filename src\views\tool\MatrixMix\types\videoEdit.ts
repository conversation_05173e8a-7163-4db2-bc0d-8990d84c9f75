export interface VideoEditProject {
  ProjectId: string;
  Title: string;
  Status: string;
  CreateTime?: string;
  ModifiedTime?: string;
  CoverURL?: string;
  Description?: string;
  Timeline?: any; // Consider defining a detailed Timeline interface later
  Duration?: number;
  TemplateId?: string;
  ClipsParam?: string;
  TemplateType?: string;
  ProjectType?: string;
  BusinessConfig?: any;
  BusinessStatus?: string;
  TimelineConvertStatus?: string;
  TimelineConvertErrorMessage?: string;
  CreateSource?: string;
  ModifiedSource?: string;
  ProduceMediaId?: string;
}

export interface VideoEditListParams {
  pageNum: number;
  pageSize: number;
  keyword?: string;
  status?: string;
  nextToken?: string;
}

export interface VideoEditApiResponse {
  RequestId: string;
  NextToken?: string;
  MaxResults: number;
  ProjectList: VideoEditProject[];
  TotalCount?: number; // 假设后端会添加这个字段用于分页
}

export interface VideoEditDetailApiResponse {
  Project: VideoEditProject;
  RequestId?: string;
}

export interface TimelineClip {
  Id?: number;
  TrackId?: number;
  MediaId: string;
  Type: string;
  TimelineIn: number;
  TimelineOut: number;
  In: number;
  Out: number;
  Duration?: number;
  VirginDuration?: number;
  Title?: string;
  FileUrl?: string;
  FileName?: string;
  Content?: string;
  // 视频特有属性
  X?: number;
  Y?: number;
  Width?: number;
  Height?: number;
  // 其他属性
  Effects?: any[];
  [key: string]: any; // 其他可能的属性
}

export interface VideoTrack {
  Id?: number;
  Type?: string;
  Visible?: boolean;
  Disabled?: boolean;
  Hidden?: boolean;
  Count?: number;
  VideoTrackClips: TimelineClip[];
  [key: string]: any; // 其他阿里云特有属性
}

export interface AudioTrack {
  Id?: number;
  Type?: string;
  Visible?: boolean;
  Disabled?: boolean;
  Count?: number;
  AudioTrackClips: TimelineClip[];
  [key: string]: any; // 其他阿里云特有属性
}

export interface SubtitleTrack {
  Id?: number;
  Type?: string;
  Visible?: boolean;
  Disabled?: boolean;
  Count?: number;
  SubtitleTrackClips: TimelineClip[];
  [key: string]: any; // 其他阿里云特有属性
}

export interface ImageTrack {
  Id?: number;
  Type?: string;
  Visible?: boolean;
  Disabled?: boolean;
  Count?: number;
  ImageTrackClips: TimelineClip[];
  [key: string]: any; // 其他阿里云特有属性
}

export interface Timeline {
  VideoTracks?: VideoTrack[];
  AudioTracks?: AudioTrack[];
  SubtitleTracks?: SubtitleTrack[];
  ImageTracks?: ImageTrack[];
  [key: string]: any; // 其他可能的顶层属性
}

export interface CreateEditingProjectDTO {
  Title: string;
  Description?: string;
  Timeline?: Timeline;
}

/**
 * @interface SelectedClip
 * @description 用于表示时间轴上当前选中的素材片段的信息。
 * @property {('video' | 'audio' | 'subtitle' | null)} type - 选中片段的轨道类型，可以是 'video'、'audio'、'subtitle' 或 null（如果未选中任何片段）。
 * @property {number | null} trackIndex - 选中片段在所属轨道类型数组中的索引，如果未选中则为 null。
 * @property {number | null} clipIndex - 选中片段在其所属轨道数组中的索引，如果未选中则为 null。
 */
export interface SelectedClip {
  type: 'video' | 'audio' | 'subtitle' | null;
  trackIndex: number | null;
  clipIndex: number | null;
}

/**
 * @interface DraggedClipData
 * @description 用于表示正在拖拽的片段信息，支持多轨道拖拽
 * @property {('video' | 'audio' | 'subtitle')} type - 拖拽片段的轨道类型
 * @property {number} trackIndex - 拖拽片段在所属轨道类型数组中的索引
 * @property {number} clipIndex - 拖拽片段在其所属轨道数组中的索引
 */
export interface DraggedClipData {
  type: 'video' | 'audio' | 'subtitle';
  trackIndex: number;
  clipIndex: number;
}

/**
 * @interface ProcessedClip
 * @description 处理后的片段数据，用于UI渲染和操作
 * @property {string} name - 片段显示名称
 * @property {number} start - 片段在时间轴上的开始时间（秒）
 * @property {number} duration - 片段的持续时间（秒）
 */
export interface ProcessedClip {
  name: string;
  start: number;
  duration: number;
}

/**
 * @interface RulerMarker
 * @description 定义时间标尺上每个刻度标记的数据结构。
 * @property {number} second - 该标记所代表的秒数。
 * @property {boolean} showLabel - 是否在该标记处显示时间文本标签。
 */
export interface RulerMarker {
  second: number;
  showLabel: boolean;
}