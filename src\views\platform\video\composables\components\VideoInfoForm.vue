<template>
  <div class="video-info-section">
    <h4 class="section-title">视频信息</h4>
    <div class="video-info-form">
      <div class="form-row">
        <label class="form-label">视频标题</label>
        <el-input
          :model-value="videoInfo.title"
          @update:model-value="updateTitle"
          placeholder="请输入视频标题"
          maxlength="50"
          show-word-limit
          class="form-input"
        />
      </div>
      <div class="form-row">
        <label class="form-label">视频描述</label>
        <el-input
          :model-value="videoInfo.description"
          @update:model-value="updateDescription"
          type="textarea"
          :rows="3"
          placeholder="请输入视频描述（可选）"
          maxlength="200"
          show-word-limit
          class="form-textarea"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  videoInfo: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:videoInfo'])

const updateTitle = (value) => {
  emit('update:videoInfo', { ...props.videoInfo, title: value })
}

const updateDescription = (value) => {
  emit('update:videoInfo', { ...props.videoInfo, description: value })
}
</script>

<style scoped>
.video-info-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.video-info-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.form-row {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.form-input,
.form-textarea {
  width: 100%;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}
</style>
