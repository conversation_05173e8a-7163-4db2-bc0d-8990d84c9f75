<template>
  <div class="video-info-section">
    <h4 class="section-title">视频信息</h4>
    <div class="video-info-form">
      <div class="form-row">
        <div class="form-label-row">
          <label class="form-label">视频标题</label>
          <el-button
            v-if="!videoInfo.title.trim()"
            type="text"
            size="small"
            @click="useSuggestedTitle"
            class="suggestion-btn"
          >
            使用建议标题
          </el-button>
        </div>
        <el-input
          :model-value="videoInfo.title"
          @update:model-value="updateTitle"
          :placeholder="suggestedTitle"
          maxlength="50"
          show-word-limit
          class="form-input"
        />
      </div>
      <div class="form-row">
        <div class="form-label-row">
          <label class="form-label">视频描述</label>
          <el-button
            v-if="!videoInfo.description.trim()"
            type="text"
            size="small"
            @click="useSuggestedDescription"
            class="suggestion-btn"
          >
            使用建议描述
          </el-button>
        </div>
        <el-input
          :model-value="videoInfo.description"
          @update:model-value="updateDescription"
          type="textarea"
          :rows="3"
          :placeholder="suggestedDescription"
          maxlength="200"
          show-word-limit
          class="form-textarea"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'

const props = defineProps({
  videoInfo: {
    type: Object,
    required: true
  },
  digitalHumans: {
    type: Array,
    default: () => []
  },
  dialogueContent: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:videoInfo', 'info-changed'])

// 自动生成标题建议
const suggestedTitle = computed(() => {
  const humanNames = props.digitalHumans.map(h => h.name || h.humanName).filter(Boolean)
  const date = new Date().toLocaleDateString('zh-CN')

  if (humanNames.length >= 2) {
    return `${humanNames.slice(0, 2).join('与')}的对话_${date}`
  } else if (humanNames.length === 1) {
    return `${humanNames[0]}的数字人视频_${date}`
  } else {
    return `数字人对话视频_${date}`
  }
})

// 自动生成描述建议
const suggestedDescription = computed(() => {
  const humanCount = props.digitalHumans.length
  const dialogueCount = props.dialogueContent.length

  if (humanCount > 0 && dialogueCount > 0) {
    return `包含${humanCount}个数字人，共${dialogueCount}条对话内容的AI生成视频。`
  }
  return '由AI数字人技术生成的对话视频。'
})

const updateTitle = (value) => {
  const newVideoInfo = { ...props.videoInfo, title: value }
  emit('update:videoInfo', newVideoInfo)
  emit('info-changed', { type: 'title', value, videoInfo: newVideoInfo })
}

const updateDescription = (value) => {
  const newVideoInfo = { ...props.videoInfo, description: value }
  emit('update:videoInfo', newVideoInfo)
  emit('info-changed', { type: 'description', value, videoInfo: newVideoInfo })
}

// 使用建议标题
const useSuggestedTitle = () => {
  updateTitle(suggestedTitle.value)
}

// 使用建议描述
const useSuggestedDescription = () => {
  updateDescription(suggestedDescription.value)
}

// 监听数据变化，自动更新空标题
watch([suggestedTitle], () => {
  if (!props.videoInfo.title.trim()) {
    updateTitle(suggestedTitle.value)
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.video-info-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
  }
}

.video-info-form {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;
  margin-top: 20px;
}

.form-row {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.suggestion-btn {
  font-size: 12px;
  color: #667eea;
  padding: 0;

  &:hover {
    color: #764ba2;
  }
}

.form-input,
.form-textarea {
  width: 100%;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}
</style>
