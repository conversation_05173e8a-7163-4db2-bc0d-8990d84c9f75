import request from '@/utils/request'

// 查询直播管理列表
export function listLive(query) {
  return request({
    url: '/platform/live/list',
    method: 'get',
    params: query
  })
}

// 查询直播管理详细
export function getLive(liveId) {
  return request({
    url: '/platform/live/' + liveId,
    method: 'get'
  })
}

// 新增直播管理
export function addLive(data) {
  return request({
    url: '/platform/live',
    method: 'post',
    data: data
  })
}

// 修改直播管理
export function updateLive(data) {
  return request({
    url: '/platform/live',
    method: 'put',
    data: data
  })
}

// 删除直播管理
export function delLive(liveId) {
  return request({
    url: '/platform/live/' + liveId,
    method: 'delete'
  })
}

// 查询直播相关音频列表
export function getLiveOption(liveId) {
  return request({
    url: `/platform/live/${liveId}/audioList`,
    method: 'get'
  })
}

// 获取单场直播所有信息
export function getLiveInfo(liveId) {
  return request({
    url: `/platform/live/vo/${liveId}`,
    method: 'get'
  })
}

// 根据创建者查询直播信息
export function listCreateBy(createBy) {
  return request({
    url: `/platform/live/listCreateBy/${encodeURIComponent(createBy)}`, 
    method: 'get'
  });
}

// 根据修改人查询直播信息
export function listUpdateBy(updateBy) {
  return request({
    url: `/platform/live/listUpdateBy/${encodeURIComponent(updateBy)}`, 
    method: 'get'
  });
}