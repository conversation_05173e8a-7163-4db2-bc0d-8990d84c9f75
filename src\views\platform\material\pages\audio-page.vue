<script setup>
import modal from '@/plugins/modal'
import { listAudio, delAudio, addAudio, updateAudio, uploadAudio, downloadAudio } from "@/api/platform/audio";
import { getArticle } from "@/api/platform/article";
import { getCategory } from "@/api/platform/category";
import { watch, computed, onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import audioPlayer from '../components/audio-player';
import UploadCard from '../components/upload-card.vue'
import { stringToColor } from '@/utils/geek'

const props = defineProps({
  categoryId: {
    type: Number,
  }
})

const { proxy } = getCurrentInstance();
const { platform_audio_audio_from } = proxy.useDict("platform_audio_audio_from");

// 状态变量
const loading = ref(false)
const audioList = ref([])
const total = ref(0)
const categoryTitle = ref('')
const audioVisiable = ref(false)
const searchContent = ref('')
const articleContent = ref('')

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  categoryId: null,
})

// 添加选中相关的状态
const selectedIds = ref([])

// 切换选中状态
function toggleSelection(item) {
  const index = selectedIds.value.indexOf(item.audioId)
  if (index === -1) {
    selectedIds.value.push(item.audioId)
  } else {
    selectedIds.value.splice(index, 1)
  }
}

// 列表相关方法
function getList() {
  queryParams.value.categoryId = props.categoryId
  loading.value = true
  listAudio(queryParams.value).then((response) => {
    audioList.value = response.rows
    audioList.value.forEach(item => {
      item.isPlay = false
      item.isEdit = false
    })
    total.value = response.total
    loading.value = false
  })
}

// 搜索相关方法
function searchAudio() {
  const query = {
    pageNum: 1,
    pageSize: 10,
    audioName: searchContent.value,
    categoryId: props.categoryId,
  }
  loading.value = true
  listAudio(query).then((response) => {
    audioList.value = response.rows
    audioList.value.forEach(item => {
      item.isPlay = false
      item.isEdit = false
    })
    total.value = response.total
    loading.value = false
  })
}

function resetAudio() {
  searchContent.value = ''
  selectedIds.value = []
  getList()
}

// 上传相关方法
function uploadAudios(x, signal, e) {
  if (!props.categoryId || props.categoryId == 0 || props.categoryId == -1) {
    proxy.$modal.msgError("请先选择音频分类")
    return
  }

  const formData = new FormData()
  formData.append('file', x.file)
  formData.append('categoryId', props.categoryId)

  uploadAudio(formData, x, signal).then(res => {
    e(res.data)
    ElMessage({ message: '上传成功', plain: true, grouping: true, type: 'success' })
  })
}

function closeAudio() {
  audioVisiable.value = false
  getList()
}

// 删除相关方法
function handleDelete(row) {
  const audioIds = row?.audioId || selectedIds.value
  if (!audioIds.length && !row) {
    proxy.$modal.msgError("请选择要删除的音频")
    return
  }
  delAudio(audioIds).then(() => {
    proxy.$modal.msgSuccess("删除成功")
    selectedIds.value = []
    getList()
  })
}

function deleteUploadFile(o, e) {
  delAudio(o.audioAid).then(() => {
    modal.msgSuccess("删除成功")
    getList()
    e()
  })
}

// 下载相关方法
function handleBatchDownload() {
  if (selectedIds.value.length === 0) {
    proxy.$modal.msgError('请选择要下载的音频文件!')
    return
  }

  const selectedAudios = audioList.value.filter(item =>
    selectedIds.value.includes(item.audioId)
  )

  selectedAudios.forEach((item, index) => {
    setTimeout(() => {
      commonDownload(item)
    }, (index + 1) * 200)
  })
}

function commonDownload(item) {
  downloadAudio(item.audioId).then(res => {
    const url = URL.createObjectURL(new Blob([res]))
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    const extension = item.audioPath.split('.').pop()
    a.download = categoryTitle.value ? `${categoryTitle.value}-${item.audioName}.${extension}` : `${item.audioName}.${extension}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
    ElMessage({ message: '下载成功', plain: true, grouping: true, type: 'success' })
  })
}

// 修改相关方法
function handleUpdate(row) {
  const query = {
    audioId: row.audioId,
    audioName: row.audioName,
  }
  updateAudio(query).then(() => {
    proxy.$modal.msgSuccess("修改成功")
    row.isEdit = false
    getList()
  })
}

// 文案相关方法
function handleArticle(row) {
  if (row.audioFrom == 1) {
    if (!row.articleId) {
      articleContent.value = '未找到关联的文案ID'
      return
    }

    getArticle(row.articleId).then(res => {
      if (res.data && res.data.content) {
        articleContent.value = res.data.content
      } else {
        articleContent.value = '获取文案内容失败或无相关文案'
      }
    }).catch(() => {
      articleContent.value = '获取文案内容出错'
    })
  } else {
    articleContent.value = '该音频没有对应的文案'
  }
}

// 监听分类变化
watch(() => props.categoryId, (newId) => {
  if (props.categoryId !== undefined || props.categoryId == -1) {
    getList()
    getCategory(props.categoryId).then(res => categoryTitle.value = res.data.categoryTitle)
  }
}, { immediate: true })

// 选择模式相关状态
const isSelectionMode = ref(false)

// 计算是否全选
const isAllSelected = computed(() => {
  return audioList.value.length > 0 && selectedIds.value.length === audioList.value.length
})

// 进入选择模式
function enterSelectionMode() {
  isSelectionMode.value = true
  selectedIds.value = []
}

// 退出选择模式
function exitSelectionMode() {
  isSelectionMode.value = false
  selectedIds.value = []
}

// 切换全选状态
function toggleSelectAll() {
  if (isAllSelected.value) {
    selectedIds.value = []
  } else {
    selectedIds.value = audioList.value.map(item => item.audioId)
  }
}

// 处理卡片点击
function handleCardClick(item, event) {
  if (isSelectionMode.value) {
    toggleSelection(item)
    return // 选择模式下直接返回,不执行其他操作
  }

  // 非选择模式下才检查是否点击特殊区域
  if (event.target.closest('.title-section, .action-menu, .player-section')) {
    return
  }
}
</script>

<template>
  <div class="audio-page" :class="{ 'selection-mode': isSelectionMode }">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <!-- 常规模式工具栏 -->
      <div v-if="!isSelectionMode" class="toolbar-normal">
        <div class="left">
          <el-input v-model="searchContent" placeholder="请输入内容" clearable prefix-icon="Search"
            style="margin-right: 1px;" />
          <el-button @click="searchAudio" type="primary" icon="Search">搜索</el-button>
          <el-button icon="Refresh" @click="resetAudio">重置</el-button>
        </div>
        <div class="right">
          <el-button icon="Plus" type="primary" @click="audioVisiable = true">添加音频</el-button>
          <el-button icon="Select" @click="enterSelectionMode">批量操作</el-button>
        </div>
      </div>

      <!-- 选择模式工具栏 -->
      <div v-else class="toolbar-selection">
        <div class="left">
          <el-button icon="Close" @click="exitSelectionMode">取消</el-button>
          <span class="selection-count">已选择 {{ selectedIds.length }} 个文件</span>
        </div>
        <div class="right">
          <el-button type="primary" icon="Download" @click="handleBatchDownload" :disabled="!selectedIds.length">
            下载
          </el-button>
          <el-button type="danger" icon="Delete" @click="handleDelete" :disabled="!selectedIds.length">
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 全选区域 -->
    <div v-if="isSelectionMode" class="select-all-bar">
      <div class="checkbox" @click="toggleSelectAll">
        <div class="checkbox-inner" :class="{ 'is-checked': isAllSelected }"></div>
      </div>
      <span>全选</span>
      <span class="total-count">(共 {{ audioList.length }} 个)</span>
    </div>

    <!-- 上传组件 -->
    <UploadCard v-model="audioVisiable" @closeAudio="closeAudio" @deleteUploadFile="deleteUploadFile"
      @uploadAudios="uploadAudios" />

    <!-- 音频列表 -->
    <div class="audio-grid">
      <div class="audio-card" :class="{
        'is-selected': selectedIds.includes(item.audioId),
        'selection-mode': isSelectionMode
      }" @click="handleCardClick(item, $event)" v-for="item in audioList" :key="item.audioId">
        <!-- 选择模式下的复选框 -->
        <div v-if="isSelectionMode" class="selection-overlay">
          <div class="checkbox">
            <div class="checkbox-inner" :class="{ 'is-checked': selectedIds.includes(item.audioId) }"></div>
          </div>
        </div>

        <div class="card-header">
          <div class="content-section">
            <el-popover placement="top-start" :width="300" trigger="click" :content="articleContent"
              @before-enter="handleArticle(item)" hide-after="0" show-after="200">
              <template #reference>
                <div class="title-section">
                  <div class="audio-title">
                    <el-input v-if="item.isEdit" v-model="item.audioName" @keydown.enter.native="$event.target.blur()"
                      @blur="handleUpdate(item)" size="small" />
                    <span v-else class="title-text">{{ item.audioName }}</span>
                  </div>
                  <div class="source-tag" :class="item.audioFrom === 1 ? 'source-article' : 'source-upload'">
                    {{ platform_audio_audio_from.find(dict => dict.value === item.audioFrom)?.label }}
                  </div>
                </div>
              </template>
            </el-popover>
          </div>

          <div class="action-menu">
            <div class="action-button edit" @click.stop="item.isEdit ? handleUpdate(item) : item.isEdit = true">
              <el-icon :size="18">
                <component :is="item.isEdit ? 'Select' : 'Edit'" />
              </el-icon>
            </div>
            <div class="action-button download" @click.stop="commonDownload(item)">
              <el-icon :size="18">
                <Download />
              </el-icon>
            </div>
            <div class="action-button delete" @click.stop="handleDelete(item)">
              <el-icon :size="18">
                <Delete />
              </el-icon>
            </div>
          </div>
        </div>

        <div class="player-section">
          <audioPlayer :audioSrc="item.audioId" />
        </div>
      </div>
    </div>

    <!-- 分页器 -->
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" :layout="'prev, pager, next, jumper'" />
  </div>
</template>
<style lang="scss" scoped>
.audio-page {
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;

  :deep(.pagination-container) {
    background: #F8FAFC;
    position: relative;
    right: 10px;
  }

  &.selection-mode {
    background: #f1f5f9;

    :deep(.pagination-container) {
      background: #f1f5f9;
    }
  }
}

.toolbar {
  margin-bottom: 20px;

  .toolbar-normal,
  .toolbar-selection {
    background: white;
    padding: 16px 24px;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left,
    .right {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }

  .toolbar-selection {
    background: #1d4ed8;

    .selection-count {
      color: white;
      font-size: 15px;
      font-weight: 500;
    }
  }
}

.select-all-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: white;
  border-radius: 16px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  font-weight: 500;

  .total-count {
    color: #64748b;
    font-size: 14px;
  }
}

.audio-grid {
  display: grid;
  gap: 24px;
  padding: 16px;
  margin-bottom: 24px;

  grid-template-columns: minmax(320px, 1fr);

  @media screen and (min-width: 1220px) {
    grid-template-columns: repeat(2, minmax(320px, 1fr));
  }

  @media screen and (min-width: 1630px) {
    grid-template-columns: repeat(3, minmax(320px, 1fr));
    max-width: 1400px;
    margin: 0 auto 24px auto;
  }
}

.audio-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  min-height: 100%;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    .action-menu {
      opacity: 1;
      visibility: visible;

      .action-button {
        opacity: 1;
        transform: translateX(0);

        &.edit {
          transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) 0s;
        }

        &.download {
          transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) 0.05s;
        }

        &.delete {
          transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s;
        }
      }
    }
  }

  &.selection-mode {
    &:hover {
      transform: none;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      border: 2px solid #3b82f6;
      background: #eff6ff;
      cursor: pointer;

      .selection-overlay .checkbox {
        border-color: #3b82f6;
      }
    }

    .action-menu {
      display: none;
    }
  }

  &.is-selected {
    border: 2px solid #3b82f6;
    background: #eff6ff;

    .selection-overlay .checkbox {
      border-color: #3b82f6;
      background: #3b82f6;

      .checkbox-inner {
        transform: scale(1);
      }
    }
  }

  .card-header {
    padding: 20px;
    background: linear-gradient(to right, #f8fafc, white);
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
    height: 100%;
  }

  .content-section {
    flex: 1;
    min-width: 0;
  }

  .title-section {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      background: #eef4fa;
    }

    .audio-title {
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 8px;
    }
  }

  .source-tag {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;

    &.source-article {
      background: #dbeafe;
      color: #2563eb;
    }

    &.source-upload {
      background: #e2e8f0;
      color: #475569;
    }
  }

  .action-menu {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
    opacity: 0;
    visibility: hidden;
    margin-top: 15px;

    .action-button {
      transform: translateX(20px);
      opacity: 0;
    }
  }

  .action-button {
    width: 34px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    will-change: transform, opacity;

    &.edit {
      background: #e0e7ff;

      :deep(.el-icon) {
        color: #4f46e5;
      }

      &:hover {
        background: #4f46e5;
        transform: translateY(-1px) scale(1.05);
        box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2);

        :deep(.el-icon) {
          color: white;
        }
      }
    }

    &.download {
      background: #dcfce7;

      :deep(.el-icon) {
        color: #15803d;
      }

      &:hover {
        background: #15803d;
        transform: translateY(-1px) scale(1.05);
        box-shadow: 0 4px 6px -1px rgba(21, 128, 61, 0.2);

        :deep(.el-icon) {
          color: white;
        }
      }
    }

    &.delete {
      background: #fee2e2;

      :deep(.el-icon) {
        color: #dc2626;
      }

      &:hover {
        background: #dc2626;
        transform: translateY(-1px) scale(1.05);
        box-shadow: 0 4px 6px -1px rgba(220, 38, 38, 0.2);

        :deep(.el-icon) {
          color: white;
        }
      }
    }

    :deep(.el-icon) {
      transition: all 0.2s ease;
      transform: scale(0.9);
    }

    &:hover {
      :deep(.el-icon) {
        transform: scale(1);
      }
    }
  }

  .player-section {
    padding: 20px;
    background: linear-gradient(to bottom, white, #f8fafc);
    display: flex;
    flex-direction: column;
  }

  &:not(:hover) .action-menu {
    .action-button {
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
  }
}

.checkbox {
  width: 24px;
  height: 24px;
  border: 2px solid #cbd5e1;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    border-color: #3b82f6;
    transform: scale(1.05);
  }

  .checkbox-inner {
    width: 14px;
    height: 14px;
    background: #3b82f6;
    border-radius: 3px;
    transform: scale(0);
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &.is-checked {
      transform: scale(1);
    }
  }
}

.selection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  .checkbox {
    transform: scale(1.2);

    &:hover {
      transform: scale(1.3);
    }
  }
}
</style>
