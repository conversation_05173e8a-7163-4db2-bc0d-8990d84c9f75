import axios, { AxiosRequestConfig, AxiosRequestHeaders, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { ElNotification, ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { tansParams, blobValidate } from '@/utils/ruoyi'
import cache from '@/plugins/cache'
import { saveAs } from 'file-saver'
import useUserStore from '@/store/modules/user'
import router from '@/router'

let downloadLoadingInstance: any;
// 是否显示重新登录
export let isRelogin = { show: false };
//@ts-ignore
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
import { GeekRequestConfig, GeekResponse } from '@/types/request'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 超时
  timeout: 10000
})

// request拦截器
service.interceptors.request.use((config) => {
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  // 是否需要防止数据重复提交
  const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
  if (getToken() && !isToken) {
    // 让每个请求携带自定义token 请根据实际情况自行修改
    (<AxiosRequestHeaders>config.headers)['Authorization'] = 'Bearer ' + getToken()
  }
  // get请求映射params参数
  if (config.method === 'get' && config.params) {
    let url = config.url + '?' + tansParams(config.params);
    url = url.slice(0, -1);
    config.params = {};
    config.url = url;
  }
  if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
    const requestObj = {
      url: config.url,
      data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
      time: new Date().getTime()
    }
    const sessionObj = cache.session.getJSON('sessionObj')
    if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
      cache.session.setJSON('sessionObj', requestObj)
    } else {
      const s_url = sessionObj.url;                // 请求地址
      const s_data = sessionObj.data;              // 请求数据
      const s_time = sessionObj.time;              // 请求时间
      const interval = 1000;                       // 间隔时间(ms)，小于此时间视为重复提交
      if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
        const message = '数据正在处理，请勿重复提交';
        console.warn(`[${s_url}]: ` + message)
        return Promise.reject(new Error(message))
      } else {
        cache.session.setJSON('sessionObj', requestObj)
      }
    }
  }
  return config
}, error => {
  Promise.reject(error)
})

type ResponseType = {
  code: string | number,
  msg: string,
  data?: any,
  rows?: Array<any>,
  total?: any,
}

// 响应拦截器
// (value: V) => V | Promise<V>) | null) | null, options?: AxiosInterceptorOptions
service.interceptors.response.use(<T>(res: AxiosResponse<GeekResponse<T>, any>) => {
  // 未设置状态码则默认成功状态
  const code = String(res.data.code || 200);
  //获取错误信息
  const msg = errorCode[code] || res.data.msg || errorCode['default']
  // 二进制数据则直接返回
  if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
    return res
  }
  if (code === '401') {
    if (!isRelogin.show) {
      isRelogin.show = true;
      ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false,
        closeOnPressEscape: false
      }).then(() => {
        isRelogin.show = false;
        useUserStore().logOut().then(() => {
          // 跳转到登录页面，保留当前页面路径
          const currentPath = router.currentRoute.value.fullPath
          const loginPath = currentPath !== '/index' ? `/auth/login?redirect=${encodeURIComponent(currentPath)}` : '/login'
          router.push(loginPath)
        }).catch(() => {
          // 如果登出失败，直接跳转到登录页
          router.push('/login')
        })
      }).catch(() => {
        isRelogin.show = false;
      });
    }
    return Promise.reject(new Error('无效的会话，或者会话已过期，请重新登录。'))
  } else if (code === '500') {
    ElMessage({ message: msg, type: 'error' })
    return Promise.reject(new Error(msg))
  } else if (code === '601') {
    ElMessage({ message: msg, type: 'warning' })
    return Promise.reject(new Error(msg))
  } else if (code !== '200') {
    ElNotification.error({ title: msg })
    return Promise.reject('error')
  } else {
    return Promise.resolve(res)
  }
},
  error => {
    let { message } = error;
    if (message == "Network Error") {
      message = "后端接口连接异常";
    } else if (message.includes("timeout")) {
      message = "系统接口请求超时";
    } else if (message.includes("Request failed with status code")) {
      message = "系统接口" + message.substr(message.length - 3) + "异常";
    }
    ElMessage({ message: message, type: 'error', duration: 5 * 1000 })
    return Promise.reject(error)
  }
)

// 通用下载方法
export async function download(url: string, params: any, filename: string, config: any) {
  downloadLoadingInstance = ElLoading.service({ text: "正在下载数据，请稍候", background: "rgba(0, 0, 0, 0.7)", })
  try {
    const res = await service.post(url, params, {
      transformRequest: [(params_1) => { return tansParams(params_1) }],
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob',
      ...config
    })
    const data: Blob = res.data
    const isLogin = await blobValidate(data)
    if (isLogin) {
      const blob = new Blob([data])
      saveAs(blob, filename)
    } else {
      const resText = await data.text()
      const rspObj: ResponseType = JSON.parse(resText)
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
      ElMessage.error(errMsg)
    }
    downloadLoadingInstance.close()
  } catch (r) {
    console.error(r)
    ElMessage.error('下载文件出现错误，请联系管理员！')
    downloadLoadingInstance.close()
  }
}
const request = <T>(config: GeekRequestConfig) => service<GeekRequestConfig, AxiosResponse<GeekResponse<T>>>(config).then(res => res.data)
export function postAction(url: string, data?: any, isToken: boolean = true) {
  return request({ data, url, method: 'POST', headers: { isToken }, })
}
export function getAction(url: string, params?: any, isToken: boolean = true) {
  return request({ params, url, method: 'GET', headers: { isToken }, })
}
export function putAction(url: string, data?: any, isToken: boolean = true) {
  return request({ data, url, method: 'PUT', headers: { isToken }, })
}
export function deleteAction(url: string, data?: any, isToken: boolean = true) {
  return request({ data, url, method: 'DELETE', headers: { isToken }, })
}

const configIntercetors = (config: InternalAxiosRequestConfig<any>) => {
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  // 是否需要防止数据重复提交
  const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
  config.timeout = config?.timeout || 10000
  if (getToken() && !isToken) {
    // 让每个请求携带自定义token 请根据实际情况自行修改
    (<AxiosRequestHeaders>config.headers)['Authorization'] = 'Bearer ' + getToken()
  }
  // get请求映射params参数
  if (config.method === 'get' && config.params) {
    let url = config.url + '?' + tansParams(config.params);
    url = url.slice(0, -1);
    config.params = {};
    config.url = url;
  }
  if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
    const requestObj = {
      url: config.url,
      data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
      time: new Date().getTime()
    }
    const sessionObj = cache.session.getJSON('sessionObj')
    if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
      cache.session.setJSON('sessionObj', requestObj)
    } else {
      const s_url = sessionObj.url;                // 请求地址
      const s_data = sessionObj.data;              // 请求数据
      const s_time = sessionObj.time;              // 请求时间
      const interval = 1000;                       // 间隔时间(ms)，小于此时间视为重复提交
      if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
        const message = '数据正在处理，请勿重复提交';
        console.warn(`[${s_url}]: ` + message)
        return Promise.reject(new Error(message))
      } else {
        cache.session.setJSON('sessionObj', requestObj)
      }
    }
  }
  return config
}

export async function streamAction(config: any, readerCallback: (text: string) => void) {
  config.headers = (config.headers || {})
  config.headers['Content-Type'] = 'application/json;charset=utf-8'
  configIntercetors(config);
  const response = await fetch(import.meta.env.VITE_APP_BASE_API + config.url, {
    headers: config.headers,
    method: config.method || 'GET',
    body: JSON.stringify(config.data || {}),
  })
  if (!response.body) throw new Error('response body is null')
  const reader = response.body.getReader();
  const decoder = new TextDecoder('utf-8');
  let allText = '';
  while (true) {
    const { value, done } = await reader.read();
    if (done) {
      try {
        JSON.parse(allText);
        return null
      } catch (e) {
        return allText
      }
    } else {
      const text = decoder.decode(value, { stream: true });
      const lines = text.split('\n').filter(line => line.trim() !== '');
      let message = '';
      for (const line of lines) {
        if (line.startsWith('data:')) {
          message += line.slice(5).trim();
        } else if (line.startsWith('event:')) {
          console.log('Event type:', line.slice(6).trim());
        } else if (line.startsWith('id:')) {
          console.log('Event ID:', line.slice(3).trim());
        } else if (line.startsWith('retry:')) {
          console.log('Retry interval:', line.slice(6).trim());
        } else {
          message += line;
        }
      }
      if (message) {
        allText += message
        if (readerCallback) readerCallback(message)
      }
    }
  }
}

export default request
