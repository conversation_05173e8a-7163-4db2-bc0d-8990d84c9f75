<template>
  <div class="upload-test-container">
    <el-form :model="form" ref="formRef" :rules="rules" label-width="120px">
      <el-form-item label="形象名称" prop="imageName">
        <el-input v-model="form.imageName" placeholder="请输入形象名称"></el-input>
      </el-form-item>
      <el-form-item label="选择形象素材" prop="imageAddress">
        <el-upload :auto-upload="false" :on-change="handleFileChange" ref="uploadRef" :accept="'.mp4,.avi,.mov'">
          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button :loading="isUploading" @click="uploadFile">
          {{ isUploading ? '上传中...' : '上传' }}
        </el-button>
      </el-form-item>
    </el-form>
    <div v-if="isUploading" class="total-progress">
      <span>上传进度:</span>
      <el-progress :percentage="totalProgress" :status="totalProgressStatus"></el-progress>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineEmits, onUnmounted } from 'vue';
import { initImageChunk } from '@/api/platform/image';
import { ElMessage } from 'element-plus';

const emits = defineEmits(['upload-success']);

const form = ref({
  imageName: '',
  imageAddress: null
});
const selectedFile = ref(null);
const isUploading = ref(false);
const formRef = ref(null);
const uploadRef = ref(null);
const totalProgress = ref(0);
const totalProgressStatus = computed(() => {
  if (totalProgress.value === 100) return 'success';
  return '';
});

const rules = {
  imageName: [
    { required: true, message: '请输入形象名称', trigger: 'blur' },
    { min: 2, max: 30, message: '形象名称长度在2 - 30个字符之间', trigger: 'blur' }
  ],
  imageAddress: [
    {
      validator: (rule, value, callback) => {
        if (selectedFile.value) callback();
        else callback(new Error('请提供形象素材'));
      },
      trigger: 'change'
    }
  ]
};

// 处理文件选择
const handleFileChange = (file) => {
  const maxSize = 500 * 1024 * 1024; // 500MB
  const allowedExtensions = ['.mp4', '.avi', '.mov'];
  const fileExtension = file.raw.name.slice(((file.raw.name.lastIndexOf('.') - 1) >>> 0) + 2).toLowerCase();
  
  if (!allowedExtensions.includes(`.${fileExtension}`)) {
    ElMessage.error('仅支持 MP4、AVI、MOV 格式的视频文件');
    clearFileSelection();
    return;
  }
  
  if (file.raw.size > maxSize) {
    ElMessage.error('文件大小不能超过 500MB');
    clearFileSelection();
    return;
  }

  selectedFile.value = file.raw;
  form.value.imageAddress = file.raw.name; // 只保存文件名
};

// 清除文件选择
const clearFileSelection = () => {
  selectedFile.value = null;
  form.value.imageAddress = null;
  uploadRef.value.clearFiles();
};

// 重置表单
const resetForm = () => {
  form.value = {
    imageName: '',
    imageAddress: null
  };
  clearFileSelection();
  totalProgress.value = 0;
  isUploading.value = false;
};

// 上传文件
const uploadFile = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      isUploading.value = true;
      totalProgress.value = 0;
      
      try {
        const formData = new FormData();
        formData.append('imageName', form.value.imageName);
        formData.append('imageAddress', selectedFile.value);

        await initImageChunk(formData, {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.lengthComputable) {
              totalProgress.value = Math.round((progressEvent.loaded / progressEvent.total) * 100);
              // console.log('2',progressEvent.loaded);
              // console.log('1',progressEvent.total);
            }
          }
        });
        setTimeout(() => {
          emits('upload-success');
          resetForm();
        }, 1000);
      } catch (error) {
        ElMessage.error(`上传失败: ${error.message}`);
        resetForm();
      }
    }
  });
};

onUnmounted(() => {
  resetForm();
});
</script>

<style scoped>
.total-progress {
  margin-top: 20px;
}
</style>