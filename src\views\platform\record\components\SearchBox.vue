<template>
  <div class="search-container">
    <!-- 搜索按钮 -->
    <div v-if="!showSearchBox" class="search-btn-wrapper">
      <el-button
        @click="toggleSearch"
        type="primary"
        :icon="Search"
        circle
        size="default"
        class="search-toggle-btn"
        title="搜索原文"
      />
      <!-- 粒子效果容器 -->
      <div class="particles-container">
        <div
          v-for="i in 12"
          :key="i"
          class="particle"
          :style="{
            '--delay': `${i * 0.1}s`,
            '--angle': `${i * 30}deg`
          }"
        ></div>
      </div>
      <!-- 脉冲环效果 -->
      <div class="pulse-ring"></div>
      <div class="pulse-ring pulse-ring-delayed"></div>
    </div>

    <!-- 搜索框区域 -->
    <div v-if="showSearchBox" class="search-box-container">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索原文内容..."
        size="default"
        class="search-input"
        clearable
        @input="performSearch"
        @keyup.enter="performSearch"
        @clear="clearSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>

      <!-- 搜索结果导航 -->
      <div v-if="searchResults.length > 0" class="search-navigation">
        <span class="search-count">
          {{ currentSearchIndex + 1 }}/{{ searchResults.length }}
        </span>
        <el-button-group class="search-nav-buttons">
          <el-button
            @click="prevSearchResult"
            :icon="ArrowUp"
            size="small"
            :disabled="searchResults.length === 0"
          />
          <el-button
            @click="nextSearchResult"
            :icon="ArrowDown"
            size="small"
            :disabled="searchResults.length === 0"
          />
        </el-button-group>
      </div>

      <!-- 关闭搜索按钮 -->
      <el-button
        @click="toggleSearch"
        :icon="Close"
        size="small"
        circle
        class="search-close-btn"
        title="关闭搜索"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Search, ArrowUp, ArrowDown, Close } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  conversations: {
    type: Array,
    required: true,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'search-result-change',
  'scroll-to-conversation',
  'search-keyword-change'
])

// 搜索相关状态
const showSearchBox = ref(false) // 控制搜索框显示
const searchKeyword = ref('') // 搜索关键词
const searchResults = ref([]) // 搜索结果
const currentSearchIndex = ref(-1) // 当前搜索结果索引

// 搜索相关方法
const toggleSearch = () => {
  showSearchBox.value = !showSearchBox.value
  if (!showSearchBox.value) {
    clearSearch()
  }
}

const performSearch = () => {
  if (!searchKeyword.value.trim()) {
    clearSearch()
    return
  }

  const keyword = searchKeyword.value.toLowerCase()
  const results = []

  props.conversations.forEach((conversation, index) => {
    if (conversation.text.toLowerCase().includes(keyword)) {
      results.push({
        conversationIndex: index,
        conversation: conversation
      })
    }
  })

  searchResults.value = results
  currentSearchIndex.value = results.length > 0 ? 0 : -1

  // 发送搜索结果变化事件
  emit('search-result-change', {
    results: results,
    currentIndex: currentSearchIndex.value,
    keyword: searchKeyword.value
  })

  // 如果有搜索结果，滚动到第一个结果
  if (results.length > 0) {
    scrollToSearchResult(0)
  }
}

const clearSearch = () => {
  searchKeyword.value = ''
  searchResults.value = []
  currentSearchIndex.value = -1

  // 发送搜索结果变化事件
  emit('search-result-change', {
    results: [],
    currentIndex: -1,
    keyword: ''
  })
}

const scrollToSearchResult = (index) => {
  if (index >= 0 && index < searchResults.value.length) {
    const conversationIndex = searchResults.value[index].conversationIndex
    emit('scroll-to-conversation', conversationIndex)
  }
}

const nextSearchResult = () => {
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length
    scrollToSearchResult(currentSearchIndex.value)

    // 发送搜索结果变化事件
    emit('search-result-change', {
      results: searchResults.value,
      currentIndex: currentSearchIndex.value,
      keyword: searchKeyword.value
    })
  }
}

const prevSearchResult = () => {
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = currentSearchIndex.value <= 0
      ? searchResults.value.length - 1
      : currentSearchIndex.value - 1
    scrollToSearchResult(currentSearchIndex.value)

    // 发送搜索结果变化事件
    emit('search-result-change', {
      results: searchResults.value,
      currentIndex: currentSearchIndex.value,
      keyword: searchKeyword.value
    })
  }
}

// 监听搜索关键词变化
watch(searchKeyword, (newKeyword) => {
  emit('search-keyword-change', newKeyword)
})

// 暴露方法供外部调用
const setSearchKeyword = (keyword) => {
  showSearchBox.value = true
  searchKeyword.value = keyword
  performSearch()
}

// 暴露给父组件的方法
defineExpose({
  setSearchKeyword,
  clearSearch,
  toggleSearch
})
</script>

<style scoped>
/* 搜索功能样式 */
.search-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-btn-wrapper {
  position: relative;
  display: inline-block;
}

.search-toggle-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.search-toggle-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.search-toggle-btn:hover::before {
  width: 100px;
  height: 100px;
}

.search-toggle-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.5);
}

/* 粒子效果样式 */
.particles-container {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8, #06b6d4);
  border-radius: 50%;
  transform-origin: 0 0;
  animation: particleFloat 3s infinite ease-in-out;
  animation-delay: var(--delay);
  opacity: 0;
}

.search-btn-wrapper:hover .particle {
  animation: particleFloatActive 2s infinite ease-in-out;
  animation-delay: var(--delay);
}

@keyframes particleFloat {
  0%, 100% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateX(0) scale(0);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateX(25px) scale(1);
  }
}

@keyframes particleFloatActive {
  0%, 100% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateX(0) scale(0);
  }
  25% {
    opacity: 0.8;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateX(35px) scale(1.2);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateX(45px) scale(1);
  }
  75% {
    opacity: 0.6;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateX(35px) scale(0.8);
  }
}

.search-box-container {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  backdrop-filter: blur(8px);
  animation: searchBoxSlideIn 0.3s ease-out;
}

@keyframes searchBoxSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.search-input {
  width: 280px;
}

.search-input :deep(.el-input__wrapper) {
  border: none;
  box-shadow: none;
  background: transparent;
}

.search-input :deep(.el-input__inner) {
  font-size: 14px;
  color: #374151;
}

.search-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px;
  border-left: 1px solid #e5e7eb;
}

.search-count {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  min-width: 40px;
  text-align: center;
}

.search-nav-buttons {
  display: flex;
}

.search-nav-buttons .el-button {
  padding: 4px 8px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  color: #6b7280;
  transition: all 0.2s ease;
}

.search-nav-buttons .el-button:hover:not(:disabled) {
  background: #f3f4f6;
  color: #374151;
  border-color: #d1d5db;
}

.search-nav-buttons .el-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.search-close-btn {
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  color: #6b7280;
  transition: all 0.2s ease;
}

.search-close-btn:hover {
  background: #ef4444;
  border-color: #ef4444;
  color: #ffffff;
}
</style>
