<script setup>
import { onMounted, reactive } from 'vue';
import useDrag from '@/hook/drag';
const size = 50
const sideLength = size / 2 / Math.cos(Math.PI / 6);
const menu = reactive({ x: 500, y: 500 })
const centerMenu = reactive({ style: { left: - sideLength + 'px', top: - sideLength + 'px' } })
const drag = useDrag(({ x, y }) => { menu.x = y, menu.y = x })

const menus = reactive([])
function findLayer(n) {
    let layer = 1;
    const layerNum = 6;
    let min = 0;
    let max = layerNum * layer - 1;
    while (!(min <= n && n <= max)) {
        layer++;
        min = max + 1;
        max += layer * layerNum;
    }
    return { layer, min, max };
}

function lineSegmentLength(d, theta) {
    const thetaRad = (theta * Math.PI) / 180;
    const sin120MinusTheta = Math.sin((120 * Math.PI) / 180 - thetaRad);
    const AP = (d * Math.sqrt(3)) / 2 / sin120MinusTheta;
    return AP;
}

onMounted(() => {
    const icons = ["🏠", "🔍", "🔔", "💬", "⚙️", "👥", "📚", "🎵", "🎬", "🎨", "💻", "📱", "🎮", "📸", "🚗", "🍔", "🛍️", "🏠", "🔍", "🔔", "💬", "⚙️", "👥", "📚", "🎵", "🎬", "🎨", "💻", "📱", "🎮", "📸", "🚗", "🍔", "🛍️", "🏠", "🔍", "🔔", "💬", "⚙️", "👥", "📚", "🎵", "🎬", "🎨", "💻", "📱", "🎮", "📸", "�", "🍔", "🛍️", "🏠", "🔍", "🔔", "💬", "⚙️", "👥", "📚", "🎵", "🎬", "🎨", "💻", "📱", "🎮", "📸", "🚗", "🍔", "🛍️", "🏠", "🔍", "🔔", "💬", "⚙️", "👥", "📚", "🎵", "🎬", "🎨", "💻", "📱", "🎮", "📸", "🚗", "🍔", "🛍️", "🏠", "🔍", "🔔", "💬", "⚙️", "👥", "📚", "🎵", "🎬", "🎨", "💻", "📱", "🎮", "📸", "�", "🍔", "🛍️",];


    function generateMenu(icons) {
        for (let n = 0; n < icons.length - 1; n++) {
            const { layer, min, max } = findLayer(n);
            const angle = (((n - min) * (360 / (max - min + 1)))) - 180;
            const absA = Math.abs(angle % 60);
            const distance = lineSegmentLength(layer * 2 * sideLength, absA);
            const radians = (angle * Math.PI) / 180;
            const x = distance * Math.cos(radians) - sideLength;
            const y = distance * Math.sin(radians) - sideLength;
            const hexagon = {
                icon: icons[n],
                label: `(${layer},${n})(${angle})`,
                style: {
                    left: `${x}px`,
                    top: `${y}px`
                }
            }
            menus.push(hexagon);
        }
    }

    generateMenu(icons);
})
</script>
<template>
    <div class="honeycomb" id="menu" ref="menuRef" :style="{ top: menu.x + 'px', left: menu.y + 'px' }">
        <div :style="centerMenu['style']" class="center hexagon" @mousedown="drag">
            <div class="label"> Center </div>
        </div>
        <div v-for="menu in menus" :style="menu['style']" class="item hexagon">
            <div class="icon">{{ menu['icon'] }}</div>
            <div class="label">{{ menu['label'] }}</div>
        </div>
    </div>
</template>
<style scoped lang="scss">
$--size: 50px;
$--background: #f0f0f0;

.honeycomb {
    position: fixed;
}

.hexagon {
    position: absolute;
    width: $--size;
    height: $--size;
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    background-color: $--background;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    &.center {
        z-index: 10;
    }

    &.item {
        transform-origin: center bottom;
        transition: transform 0.3s ease;

        &:hover {
            transform: scale(1.1);
        }
    }
}


.icon {
    font-size: 16px;
}

.label {
    font-size: 12px;
    text-align: center;
}
</style>