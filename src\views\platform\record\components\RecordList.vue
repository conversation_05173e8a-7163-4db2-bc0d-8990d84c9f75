<template>
    <div class="record-list" :class="{ 'list-view': currentView === 'list', 'grid-view': currentView === 'grid' }">
        <div v-for="item in dataList" :key="item.vaId" class="record-item-wrapper"
            :class="{ 'selection-mode': isSelectionMode, 'selected': selectedItems.includes(item.vaId), 'list-item': currentView === 'list' }">
            <!-- 选择框 -->
            <div v-if="isSelectionMode" class="selection-checkbox">
                <el-checkbox :model-value="selectedItems.includes(item.vaId)"
                    @change="handleItemSelect(item.vaId, $event)" />
            </div>
            <!-- 卡片内容 -->
            <RecordCard :record="item" :class="{ 'clickable': !isSelectionMode }" @click="handleCardClick(item)"
                :selection-mode="isSelectionMode" :view-mode="currentView" />
        </div>
    </div>
</template>

<script setup>
import RecordCard from './RecordCard.vue'

const props = defineProps({
    dataList: {
        type: Array,
        default: () => []
    },
    isSelectionMode: {
        type: Boolean,
        default: false
    },
    selectedItems: {
        type: Array,
        default: () => []
    },
    currentView: {
        type: String,
        default: 'grid'
    }
})

const emit = defineEmits(['item-select', 'card-click'])

const handleItemSelect = (vaId, checked) => {
    emit('item-select', vaId, checked)
}

const handleCardClick = (item) => {
    emit('card-click', item)
}
</script>

<style lang="scss" scoped>
.record-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    transition: all 0.3s ease;

    // 网格视图样式（默认）
    &.grid-view {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 16px;
    }

    // 列表视图样式
    &.list-view {
        display: flex;
        flex-direction: column;
        gap: 12px;
        // 设置左右边距，与高级筛选框保持一致
        margin: 0 32px;

        .record-item-wrapper {
            &.list-item {
                width: 100%;

                :deep(.record-card) {
                    height: 140px; // 固定高度确保一致性
                    min-height: 140px;
                    display: flex;
                    flex-direction: row;
                    align-items: stretch; // 改为stretch确保子元素填满高度
                    padding: 16px 20px;
                    overflow: hidden; // 防止内容溢出

                    .record-card-header {
                        flex: 0 0 200px;
                        margin-bottom: 0;
                        margin-right: 20px;
                        display: flex;
                        flex-direction: column;
                        justify-content: flex-start; // 改为从顶部开始，避免重叠
                        padding: 8px 0; // 添加上下内边距

                        .record-title {
                            font-size: 15px;
                            -webkit-line-clamp: 2; // 允许显示2行标题
                            line-clamp: 2;
                            max-height: 42px; // 限制标题最大高度
                            overflow: hidden;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            margin-bottom: 6px; // 添加与状态标签的间距
                        }

                        .record-dot {
                            align-self: flex-start;
                            flex-shrink: 0; // 防止状态标签被压缩
                        }
                    }

                    .record-card-content {
                        flex: 1;
                        height: 100%;
                        margin-right: 20px;
                        display: flex;
                        flex-direction: column;
                        justify-content: flex-start; // 改为从顶部开始排列，避免重叠
                        padding: 8px 0; // 添加上下内边距
                        overflow: hidden;

                        .record-card-tags {
                            margin-bottom: 6px; // 减少底部间距
                            max-height: 24px; // 限制为一行的高度
                            overflow: hidden;
                            display: flex;
                            flex-wrap: nowrap; // 不换行，只显示一行
                            gap: 4px;
                            line-height: 1.2;
                            flex-shrink: 0; // 防止标签区域被压缩
                            position: relative;

                            // 添加右侧渐变遮罩效果
                            &::after {
                                content: '';
                                position: absolute;
                                top: 0;
                                right: 0;
                                width: 20px;
                                height: 100%;
                                background: linear-gradient(to left, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
                                pointer-events: none;
                                z-index: 1;
                            }

                            // 标签项样式调整
                            :deep(.el-tag) {
                                flex-shrink: 0; // 防止标签被压缩
                                white-space: nowrap; // 防止标签内文字换行
                            }
                        }

                        .record-source-text {
                            flex: 1;
                            overflow: hidden;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;

                            &.summary-truncate {
                                -webkit-line-clamp: 2; // 严格限制为2行
                                line-clamp: 2;
                                max-height: 42px; // 精确控制高度 (21px * 2行)
                                line-height: 1.5; // 调整行高为1.5
                                font-size: 14px; // 确保字体大小一致
                                text-overflow: ellipsis;
                                word-break: break-word;
                            }
                        }

                        // 处理空状态
                        .el-empty {
                            padding: 8px 0;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            min-height: 80px;

                            :deep(.el-empty__image) {
                                width: 40px;
                                height: 40px;
                                margin-bottom: 8px;
                            }

                            :deep(.el-empty__description) {
                                font-size: 12px;
                                color: #909399;
                                margin-top: 0;
                                line-height: 1.4;
                                text-align: center;
                                z-index: 10;
                                position: relative;
                            }
                        }
                    }

                    .record-card-footer {
                        flex: 0 0 150px;
                        margin-top: 0;
                        padding: 8px 0; // 添加上下内边距
                        border-top: none;
                        display: flex;
                        flex-direction: column;
                        justify-content: flex-start; // 改为从顶部开始
                        align-items: flex-end;
                        gap: 6px; // 减少间距
                        height: 100%;

                        .record-time {
                            margin-bottom: 0;
                            flex-shrink: 0; // 防止被压缩
                        }

                        .record-date {
                            font-size: 12px;
                            flex-shrink: 0; // 防止被压缩
                        }
                    }
                }
            }
        }
    }

    // 卡片包装器
    .record-item-wrapper {
        position: relative;
        transition: all 0.3s ease;

        // 选择模式样式
        &.selection-mode {
            cursor: pointer;

            &:hover {
                transform: translateY(-2px);
            }

            &.selected {
                transform: translateY(-4px);

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    border-radius: 16px;
                    box-shadow: 0 0 0 2px #409eff, 0 8px 20px rgba(0, 0, 0, 0.1);
                    pointer-events: none;
                    z-index: 1;
                }
            }
        }

        // 选择框样式
        .selection-checkbox {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 10;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
                background: rgba(255, 255, 255, 1);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            // 简化复选框样式
            :deep(.el-checkbox) {
                margin-right: 0;
                margin-left: 0;

                .el-checkbox__input {
                    .el-checkbox__inner {
                        width: 18px;
                        height: 18px;
                        border: 1.5px solid #d1d5db;
                        border-radius: 4px;
                        background: #ffffff;
                        transition: all 0.2s ease;

                        &:hover {
                            border-color: #409eff;
                        }
                    }

                    &.is-checked .el-checkbox__inner {
                        background: #409eff;
                        border-color: #409eff;

                        &::after {
                            border-color: #ffffff;
                            border-width: 1.5px;
                        }
                    }
                }

                // 移除标签间距
                .el-checkbox__label {
                    display: none;
                }
            }
        }

        // 可点击卡片
        :deep(.clickable) {
            cursor: pointer;
        }
    }
}

@media (max-width: 1400px) {
    .record-list>.record-card {
        flex: 0 0 calc((100% - 96px) / 5);
    }
}
</style>
