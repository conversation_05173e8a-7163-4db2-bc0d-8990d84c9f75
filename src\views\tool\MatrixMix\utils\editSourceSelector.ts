/**
 * 编辑来源选择器工具类
 * 根据编辑来源（云剪辑 vs 模板工厂）智能选择对应的值
 * 提供通用的选择逻辑，避免在各处重复判断
 */

/**
 * 编辑来源类型
 */
export type EditSource = 'project' | 'template';

/**
 * 根据编辑来源选择对应的值
 * @param editSource 编辑来源
 * @param cloudEditingValue 云剪辑场景下的值
 * @param templateFactoryValue 模板工厂场景下的值
 * @returns 根据来源选择的值
 */
export function selectBySource<T>(
  editSource: EditSource,
  cloudEditingValue: T,
  templateFactoryValue: T
): T {
  return editSource === 'project' ? cloudEditingValue : templateFactoryValue;
}

/**
 * 根据编辑来源选择ID（项目ID或模板ID）
 * @param editSource 编辑来源
 * @param projectId 云剪辑工程ID
 * @param templateId 模板工厂模板ID
 * @returns 根据来源选择的ID
 */
export function selectId(
  editSource: EditSource,
  projectId: string,
  templateId: string
): string {
  return selectBySource(editSource, projectId, templateId);
}

/**
 * 根据编辑来源选择API方法
 * @param editSource 编辑来源
 * @param cloudEditingApi 云剪辑API方法
 * @param templateFactoryApi 模板工厂API方法
 * @returns 根据来源选择的API方法
 */
export function selectApi<T extends (...args: any[]) => any>(
  editSource: EditSource,
  cloudEditingApi: T,
  templateFactoryApi: T
): T {
  return selectBySource(editSource, cloudEditingApi, templateFactoryApi);
}

/**
 * 根据编辑来源选择配置对象
 * @param editSource 编辑来源
 * @param cloudEditingConfig 云剪辑配置
 * @param templateFactoryConfig 模板工厂配置
 * @returns 根据来源选择的配置
 */
export function selectConfig<T extends Record<string, any>>(
  editSource: EditSource,
  cloudEditingConfig: T,
  templateFactoryConfig: T
): T {
  return selectBySource(editSource, cloudEditingConfig, templateFactoryConfig);
}

/**
 * 根据编辑来源选择文本描述
 * @param editSource 编辑来源
 * @param cloudEditingText 云剪辑文本
 * @param templateFactoryText 模板工厂文本
 * @returns 根据来源选择的文本
 */
export function selectText(
  editSource: EditSource,
  cloudEditingText: string,
  templateFactoryText: string
): string {
  return selectBySource(editSource, cloudEditingText, templateFactoryText);
}

/**
 * 创建编辑来源选择器实例
 * 用于在特定上下文中多次选择时避免重复传递 editSource
 */
export class EditSourceSelector {
  constructor(private editSource: EditSource) {}

  /**
   * 选择值
   */
  select<T>(cloudEditingValue: T, templateFactoryValue: T): T {
    return selectBySource(this.editSource, cloudEditingValue, templateFactoryValue);
  }

  /**
   * 选择ID
   */
  selectId(projectId: string, templateId: string): string {
    return selectId(this.editSource, projectId, templateId);
  }

  /**
   * 选择API
   */
  selectApi<T extends (...args: any[]) => any>(
    cloudEditingApi: T,
    templateFactoryApi: T
  ): T {
    return selectApi(this.editSource, cloudEditingApi, templateFactoryApi);
  }

  /**
   * 选择配置
   */
  selectConfig<T extends Record<string, any>>(
    cloudEditingConfig: T,
    templateFactoryConfig: T
  ): T {
    return selectConfig(this.editSource, cloudEditingConfig, templateFactoryConfig);
  }

  /**
   * 选择文本
   */
  selectText(cloudEditingText: string, templateFactoryText: string): string {
    return selectText(this.editSource, cloudEditingText, templateFactoryText);
  }

  /**
   * 检查是否为云剪辑模式
   */
  isCloudEditing(): boolean {
    return this.editSource === 'project';
  }

  /**
   * 检查是否为模板工厂模式
   */
  isTemplateFactory(): boolean {
    return this.editSource === 'template';
  }
}

/**
 * 创建选择器实例的便捷函数
 */
export function createSelector(editSource: EditSource): EditSourceSelector {
  return new EditSourceSelector(editSource);
}
