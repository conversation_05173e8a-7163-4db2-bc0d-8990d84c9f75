/**
 * @file clipTimeAdjustmentUtils.ts
 * @description 片段时间调整工具类 - 专门处理片段时间范围的调整（拉伸开始时间和结束时间）
 * <AUTHOR>
 * @date 2025-07-23
 */

import type { Timeline, TimelineClip } from '../types/videoEdit';
import { getTrackClips, validateClipIndex, deepCopyTimeline, updateClipTimeRange, type TrackType } from './timelineUtils';

export interface TimeAdjustmentResult {
  success: boolean;
  timeline?: Timeline;
  error?: string;
}

export interface ClipTimeAdjustment {
  /** 新的开始时间（秒），null 表示不调整 */
  newStartTime?: number;
  /** 新的结束时间（秒），null 表示不调整 */  
  newEndTime?: number;
  /** 新的持续时间（秒），null 表示不调整 */
  newDuration?: number;
}

/**
 * @class ClipTimeAdjustmentManager
 * @description 片段时间调整管理器，负责处理片段的时间范围调整
 */
export class ClipTimeAdjustmentManager {
  
  /**
   * @method adjustClipTimeRange
   * @description 调整片段的时间范围（开始时间、结束时间或持续时间）
   * @param timeline - 时间轴数据
   * @param type - 轨道类型
   * @param trackIndex - 轨道索引
   * @param clipIndex - 片段索引
   * @param adjustment - 时间调整参数
   * @returns 调整结果
   */
  adjustClipTimeRange(
    timeline: Timeline,
    type: TrackType,
    trackIndex: number,
    clipIndex: number,
    adjustment: ClipTimeAdjustment
  ): TimeAdjustmentResult {
    console.log(`🛠️ 开始片段时间调整:`, { type, trackIndex, clipIndex, adjustment });

    // 1. 深拷贝时间轴数据
    const newTimeline = deepCopyTimeline(timeline);

    // 2. 获取对应轨道的片段数组
    const { trackClips, found } = getTrackClips(newTimeline, type, trackIndex);
    console.log(`📋 查找轨道结果:`, { found, trackClipsLength: trackClips?.length });
    
    if (!found) {
      const error = `未找到 ${type} 轨道 ${trackIndex}`;
      console.warn(`❌ ${error}`);
      return { success: false, error };
    }

    // 3. 验证片段索引
    if (!validateClipIndex(trackClips, clipIndex, '时间调整')) {
      const error = `片段索引 ${clipIndex} 超出范围（总共 ${trackClips?.length || 0} 个片段）`;
      console.warn(`❌ ${error}`);
      return { success: false, error };
    }

    // 4. 获取要调整的片段
    const clipToAdjust = trackClips![clipIndex];
    console.log(`🎯 找到要调整的片段:`, {
      TimelineIn: clipToAdjust.TimelineIn,
      TimelineOut: clipToAdjust.TimelineOut,
      Type: clipToAdjust.Type,
      Content: (clipToAdjust as any).Content || (clipToAdjust as any).Title
    });
    
    const originalStartTime = clipToAdjust.TimelineIn;
    const originalEndTime = clipToAdjust.TimelineOut;
    const originalDuration = originalEndTime - originalStartTime;

    // 5. 计算新的时间参数
    let finalStartTime = originalStartTime;
    let finalEndTime = originalEndTime;

    if (adjustment.newStartTime !== undefined) {
      finalStartTime = Math.max(0, adjustment.newStartTime); // 开始时间不能为负
    }

    if (adjustment.newEndTime !== undefined) {
      finalEndTime = adjustment.newEndTime;
    }

    if (adjustment.newDuration !== undefined) {
      finalEndTime = finalStartTime + adjustment.newDuration;
    }

    console.log(`🎯 计算新的时间参数:`, {
      原始: { start: originalStartTime, end: originalEndTime, duration: originalDuration },
      调整参数: adjustment,
      计算结果: { finalStartTime, finalEndTime, finalDuration: finalEndTime - finalStartTime }
    });

    // 6. 验证时间范围的合理性
    if (finalEndTime <= finalStartTime) {
      const error = `结束时间 ${finalEndTime} 必须大于开始时间 ${finalStartTime}`;
      console.error(`❌ ${error}`);
      return { success: false, error };
    }

    // 7. 应用时间调整
    console.log(`📝 应用时间调整到片段...`);
    clipToAdjust.TimelineIn = finalStartTime;
    clipToAdjust.TimelineOut = finalEndTime;
    
    console.log(`✅ 片段时间已更新:`, {
      TimelineIn: clipToAdjust.TimelineIn,
      TimelineOut: clipToAdjust.TimelineOut,
      Duration: clipToAdjust.TimelineOut - clipToAdjust.TimelineIn
    });

    // 8. 对于有媒体内容的片段（视频、音频），需要调整 In 和 Out 属性
    // 字幕片段不需要调整 In/Out，因为它们没有原始媒体时间概念
    if (type !== 'subtitle' && clipToAdjust.In !== undefined && clipToAdjust.Out !== undefined) {
      const mediaInOut = this.calculateMediaInOut(
        originalStartTime,
        originalEndTime,
        finalStartTime, 
        finalEndTime,
        clipToAdjust.In,
        clipToAdjust.Out
      );
      clipToAdjust.In = mediaInOut.newIn;
      clipToAdjust.Out = mediaInOut.newOut;
    }

    console.log(`🎉 片段时间调整完成！`);

    return { success: true, timeline: newTimeline };
  }

  /**
   * @method adjustClipStartTime
   * @description 调整片段的开始时间（左边界拉伸）
   * @param timeline - 时间轴数据
   * @param type - 轨道类型
   * @param trackIndex - 轨道索引
   * @param clipIndex - 片段索引
   * @param newStartTime - 新的开始时间
   * @returns 调整结果
   */
  adjustClipStartTime(
    timeline: Timeline,
    type: TrackType,
    trackIndex: number,
    clipIndex: number,
    newStartTime: number
  ): TimeAdjustmentResult {
    return this.adjustClipTimeRange(timeline, type, trackIndex, clipIndex, {
      newStartTime
    });
  }

  /**
   * @method adjustClipEndTime
   * @description 调整片段的结束时间（右边界拉伸）
   * @param timeline - 时间轴数据
   * @param type - 轨道类型
   * @param trackIndex - 轨道索引
   * @param clipIndex - 片段索引
   * @param newEndTime - 新的结束时间
   * @returns 调整结果
   */
  adjustClipEndTime(
    timeline: Timeline,
    type: TrackType,
    trackIndex: number,
    clipIndex: number,
    newEndTime: number
  ): TimeAdjustmentResult {
    return this.adjustClipTimeRange(timeline, type, trackIndex, clipIndex, {
      newEndTime
    });
  }

  /**
   * @method adjustClipDuration
   * @description 调整片段的持续时间（保持开始时间不变，调整结束时间）
   * @param timeline - 时间轴数据
   * @param type - 轨道类型
   * @param trackIndex - 轨道索引
   * @param clipIndex - 片段索引
   * @param newDuration - 新的持续时间
   * @returns 调整结果
   */
  adjustClipDuration(
    timeline: Timeline,
    type: TrackType,
    trackIndex: number,
    clipIndex: number,
    newDuration: number
  ): TimeAdjustmentResult {
    return this.adjustClipTimeRange(timeline, type, trackIndex, clipIndex, {
      newDuration
    });
  }

  /**
   * @method calculateMediaInOut
   * @description 计算媒体片段的 In/Out 时间点（用于有原始媒体内容的片段）
   * @private
   */
  private calculateMediaInOut(
    originalStartTime: number,
    originalEndTime: number, 
    newStartTime: number,
    newEndTime: number,
    originalIn: number,
    originalOut: number
  ): { newIn: number; newOut: number } {
    // 计算时间轴上的变化比例
    const originalDuration = originalEndTime - originalStartTime;
    const newDuration = newEndTime - newStartTime;
    
    // 对于字幕等文本片段，通常不需要调整媒体 In/Out
    // 这里提供一个基础实现，可以根据具体需求调整
    const startTimeChange = newStartTime - originalStartTime;
    const endTimeChange = newEndTime - originalEndTime;
    
    // 如果是拉伸操作，保持媒体内容不变
    let newIn = originalIn;
    let newOut = originalOut;
    
    // 对于有具体媒体内容的片段（如视频、音频），可能需要更复杂的逻辑
    // 目前主要针对字幕片段，所以保持简单
    
    return { newIn, newOut };
  }
}

/**
 * @function createClipTimeAdjustmentManager
 * @description 创建片段时间调整管理器实例
 * @returns ClipTimeAdjustmentManager 实例
 */
export function createClipTimeAdjustmentManager(): ClipTimeAdjustmentManager {
  return new ClipTimeAdjustmentManager();
}

/**
 * @function calculateTimeFromPixels
 * @description 将像素距离转换为时间（秒）
 * @param pixels - 像素距离
 * @param pixelsPerSecond - 每秒对应的像素数
 * @returns 时间（秒）
 */
export function calculateTimeFromPixels(pixels: number, pixelsPerSecond: number): number {
  return pixels / pixelsPerSecond;
}

/**
 * @function calculatePixelsFromTime
 * @description 将时间（秒）转换为像素距离
 * @param time - 时间（秒）
 * @param pixelsPerSecond - 每秒对应的像素数
 * @returns 像素距离
 */
export function calculatePixelsFromTime(time: number, pixelsPerSecond: number): number {
  return time * pixelsPerSecond;
}
