/**
 * 时间轴吸附工具类
 * 负责处理片段拖拽时的吸附逻辑和稳定性控制
 */

// 吸附点类型定义
export interface SnapPoint {
  y: number;                                          // Y坐标
  trackIndex: number;                                 // 轨道索引
  position: 'top' | 'center' | 'bottom';            // 位置类型
  trackType: 'video' | 'audio' | 'subtitle';        // 轨道类型
}

// 吸附结果类型
export interface SnapResult {
  snapY: number;                                     // 吸附后的Y坐标
  isSnapped: boolean;                                // 是否已吸附
  snapPoint: SnapPoint | null;                      // 吸附点信息
}

// 吸附配置
export interface SnapConfig {
  threshold: number;                                 // 垂直吸附距离阈值
  stabilityThreshold: number;                       // 稳定性阈值
  horizontalThreshold: number;                      // 水平吸附距离阈值
  horizontalSnapStrength: number;                   // 水平吸附强度 (0-1, 0=无吸附, 1=强制吸附)
}

// 默认吸附配置
export const DEFAULT_SNAP_CONFIG: SnapConfig = {
  threshold: 50,                                    // 垂直吸附：50px范围内吸附
  stabilityThreshold: 3,
  horizontalThreshold: 8,                           // 水平吸附：8px范围内吸附（精确范围）
  horizontalSnapStrength: 0.15                     // 水平吸附强度15%（视觉引导，保持自由度）
};

/**
 * 吸附状态管理器
 * 管理当前的吸附状态和稳定性计数
 */
export class SnapStateManager {
  private currentSnapPoint: SnapPoint | null = null;
  private stabilityCounter = 0;
  private config: SnapConfig;

  constructor(config: SnapConfig = DEFAULT_SNAP_CONFIG) {
    this.config = config;
  }

  /**
   * 重置吸附状态
   */
  reset(): void {
    this.currentSnapPoint = null;
    this.stabilityCounter = 0;
  }

  /**
   * 处理吸附稳定性逻辑
   * 避免在相近的吸附点之间频繁跳跃
   */
  processStability(nearestPoint: SnapPoint): SnapPoint | null {
    const isSameSnapPoint = this.currentSnapPoint && 
      this.currentSnapPoint.y === nearestPoint.y && 
      this.currentSnapPoint.position === nearestPoint.position;
    
    if (isSameSnapPoint) {
      this.stabilityCounter = 0;
      return nearestPoint;
    } else {
      this.stabilityCounter++;
      
      if (this.currentSnapPoint && this.stabilityCounter < this.config.stabilityThreshold) {
        return this.currentSnapPoint;
      } else {
        this.currentSnapPoint = nearestPoint;
        this.stabilityCounter = 0;
        return nearestPoint;
      }
    }
  }

  /**
   * 获取当前吸附点
   */
  getCurrentSnapPoint(): SnapPoint | null {
    return this.currentSnapPoint;
  }

  /**
   * 获取稳定性计数器
   */
  getStabilityCounter(): number {
    return this.stabilityCounter;
  }
}

/**
 * 吸附计算器
 * 处理吸附点查找和对齐计算
 */
export class SnapCalculator {
  private config: SnapConfig;

  constructor(config: SnapConfig = DEFAULT_SNAP_CONFIG) {
    this.config = config;
  }

  /**
   * 查找最近的吸附点
   */
  findNearestSnapPoint(points: SnapPoint[], mouseY: number): SnapPoint | null {
    let nearestPoint: SnapPoint | null = null;
    let minDistance = Infinity;
    
    for (const point of points) {
      const distance = Math.abs(mouseY - point.y);
      if (distance < minDistance) {
        minDistance = distance;
        nearestPoint = point;
      }
    }
    
    return (nearestPoint && minDistance <= this.config.threshold) ? nearestPoint : null;
  }

  /**
   * 计算对齐后的位置
   * 基于片段中心线对齐到吸附点
   */
  calculateAlignedPosition(
    snapPoint: SnapPoint, 
    currentClipCenterY: number, 
    relativeMouseY: number
  ): SnapResult {
    const centerOffset = snapPoint.y - currentClipCenterY;
    const alignedMouseY = relativeMouseY + centerOffset;
    
    return {
      snapY: alignedMouseY,
      isSnapped: true,
      snapPoint: snapPoint
    };
  }

  /**
   * 计算吸附结果
   * 主要的吸附逻辑入口
   */
  calculateSnapResult(
    relevantPoints: SnapPoint[],
    relativeMouseY: number,
    currentClipCenterY: number,
    stateManager: SnapStateManager
  ): SnapResult {
    if (relevantPoints.length === 0) {
      stateManager.reset();
      return {
        snapY: relativeMouseY,
        isSnapped: false,
        snapPoint: null
      };
    }

    const nearestPoint = this.findNearestSnapPoint(relevantPoints, relativeMouseY);
    
    if (nearestPoint) {
      const processedPoint = stateManager.processStability(nearestPoint);
      if (processedPoint) {
        return this.calculateAlignedPosition(processedPoint, currentClipCenterY, relativeMouseY);
      }
    } else {
      stateManager.reset();
    }
    
    return {
      snapY: relativeMouseY,
      isSnapped: false,
      snapPoint: null
    };
  }
}

/**
 * 拖拽变换计算器
 * 处理拖拽位置和样式变换
 */
export class DragTransformCalculator {
  /**
   * 计算拖拽变换坐标（支持水平和垂直吸附）
   */
  calculateTransform(
    event: MouseEvent, 
    snapResult: SnapResult, 
    scrollAreaRect: DOMRect, 
    scrollTop: number, 
    startX: number, 
    startY: number,
    clipRect?: DOMRect,  // 添加片段矩形信息
    horizontalSnapResult?: any,  // 水平吸附结果
    config?: SnapConfig  // 吸附配置
  ): { deltaX: number; deltaY: number } {
    const snapConfig = config || DEFAULT_SNAP_CONFIG;
    
    // 计算水平变换（简化为px直接吸附）
    let deltaX;
    if (horizontalSnapResult?.isSnapped && horizontalSnapResult.snapPoint && clipRect) {
      // 原始鼠标移动（保持控制权）
      const rawDeltaX = event.clientX - startX;
      
      // 🔧 简化逻辑：直接使用px吸附，避免时间转换的复杂性
      const snapTime = horizontalSnapResult.snapPoint.time;
      const draggedEdge = horizontalSnapResult.draggedClipEdge;
      const pixelsPerSecond = horizontalSnapResult.pixelsPerSecond || 50;
      
      // 计算吸附点的px位置（相对于时间轴内容）
      const snapPositionPx = snapTime * pixelsPerSecond;
      
      // 计算片段当前的px位置（相对于时间轴内容）
      const currentClipStartPx = horizontalSnapResult.originalTime * pixelsPerSecond;
      // 🔧 修正：正确计算片段尾部的px位置
      const currentClipEndPx = (horizontalSnapResult.originalEndTime || (horizontalSnapResult.originalTime + 1.0)) * pixelsPerSecond;
      
      // 🔍 调试：检查尾部吸附计算
      console.log('🎯 尾部吸附坐标计算:', {
        snapTime,
        draggedEdge,
        snapPositionPx,
        currentClipStartPx,
        currentClipEndPx,
        'originalTime': horizontalSnapResult.originalTime,
        'originalEndTime': horizontalSnapResult.originalEndTime,
        pixelsPerSecond
      });
      
      // 计算需要的偏移量
      let targetOffset;
      if (draggedEdge === 'end') {
        // 🔧 尾部吸附：让片段尾部对齐到吸附点
        targetOffset = snapPositionPx - currentClipEndPx;
        console.log('✅ 尾部吸附偏移:', { targetOffset, snapPositionPx, currentClipEndPx });
      } else {
        // 片段头部吸附（默认）
        targetOffset = snapPositionPx - currentClipStartPx;
        console.log('✅ 头部吸附偏移:', { targetOffset, snapPositionPx, currentClipStartPx });
      }
      
      // 吸附强度控制
      const snapStrength = 0.3; // 固定30%强度
      deltaX = rawDeltaX + (targetOffset * snapStrength);
    } else {
      // 未水平吸附时完全由鼠标控制
      deltaX = event.clientX - startX;
    }
    
    // 计算垂直变换（强力垂直吸附，保持原有逻辑）
    let deltaY;
    if (snapResult.isSnapped && snapResult.snapPoint && clipRect) {
      // 垂直吸附：让片段中心对齐到吸附点（强制对齐）
      const snapPointScreenY = snapResult.snapPoint.y + scrollAreaRect.top - scrollTop;
      const originalClipCenterY = clipRect.top + clipRect.height / 2;
      deltaY = snapPointScreenY - originalClipCenterY;
    } else {
      // 未垂直吸附时使用原始鼠标移动距离
      deltaY = event.clientY - startY;
    }
    
    return { deltaX, deltaY };
  }

  /**
   * 应用拖拽样式到元素
   */
  applyDragStyles(element: HTMLElement, deltaX: number, deltaY: number): void {
    element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
    element.style.zIndex = '9999';
    element.style.pointerEvents = 'none';
    element.style.opacity = '0.8';
  }

  /**
   * 恢复元素原始样式
   */
  resetElementStyles(element: HTMLElement): void {
    element.style.transform = '';
    element.style.opacity = '';
    element.style.zIndex = '';
    element.style.pointerEvents = '';
  }
}

/**
 * 拖拽目标更新器
 * 处理拖拽目标状态的更新
 */
export class DragTargetUpdater {
  /**
   * 更新拖拽目标状态
   */
  updateDragTarget(
    event: MouseEvent,
    scrollAreaRect: DOMRect,
    snapResult: SnapResult,
    type: 'video' | 'audio' | 'subtitle',
    callbacks: {
      setDragOverTrackType: (type: 'video' | 'audio' | 'subtitle' | null) => void;
      setDragOverTrackIndex: (index: number | null) => void;
    }
  ): void {
    const isInTrackArea = event.clientX >= scrollAreaRect.left && 
                         event.clientX <= scrollAreaRect.right &&
                         event.clientY >= scrollAreaRect.top && 
                         event.clientY <= scrollAreaRect.bottom;
    
    if (isInTrackArea && snapResult.snapPoint) {
      callbacks.setDragOverTrackType(type);
      callbacks.setDragOverTrackIndex(snapResult.snapPoint.trackIndex);
    } else if (isInTrackArea) {
      callbacks.setDragOverTrackType(type);
      callbacks.setDragOverTrackIndex(null);
    } else {
      callbacks.setDragOverTrackType(null);
      callbacks.setDragOverTrackIndex(null);
    }
  }
}

/**
 * 时间轴吸附管理器
 * 整合所有吸附相关功能的主要接口
 */
export class TimelineSnapManager {
  private config: SnapConfig;
  private stateManager: SnapStateManager;
  private calculator: SnapCalculator;
  private transformCalculator: DragTransformCalculator;
  private targetUpdater: DragTargetUpdater;

  constructor(config: SnapConfig = DEFAULT_SNAP_CONFIG) {
    this.config = config;
    this.stateManager = new SnapStateManager(config);
    this.calculator = new SnapCalculator(config);
    this.transformCalculator = new DragTransformCalculator();
    this.targetUpdater = new DragTargetUpdater();
  }

  /**
   * 重置所有吸附状态
   */
  reset(): void {
    this.stateManager.reset();
  }

  /**
   * 计算吸附结果的主要入口
   */
  calculateSnapResult(
    allSnapPoints: SnapPoint[],
    trackType: 'video' | 'audio' | 'subtitle',
    relativeMouseY: number,
    currentClipCenterY: number
  ): SnapResult {
    const relevantPoints = allSnapPoints.filter(point => point.trackType === trackType);
    return this.calculator.calculateSnapResult(
      relevantPoints,
      relativeMouseY,
      currentClipCenterY,
      this.stateManager
    );
  }

  /**
   * 计算并应用拖拽变换（支持水平和垂直吸附）
   */
  applyDragTransform(
    element: HTMLElement,
    event: MouseEvent,
    snapResult: SnapResult,
    scrollAreaRect: DOMRect,
    scrollTop: number,
    startX: number,
    startY: number,
    clipRect?: DOMRect,  // 添加片段矩形信息
    horizontalSnapResult?: any  // 水平吸附结果
  ): { deltaX: number; deltaY: number } {
    const { deltaX, deltaY } = this.transformCalculator.calculateTransform(
      event, snapResult, scrollAreaRect, scrollTop, startX, startY, clipRect, horizontalSnapResult, this.config
    );
    
    this.transformCalculator.applyDragStyles(element, deltaX, deltaY);
    return { deltaX, deltaY };
  }

  /**
   * 更新拖拽目标状态
   */
  updateDragTarget(
    event: MouseEvent,
    scrollAreaRect: DOMRect,
    snapResult: SnapResult,
    type: 'video' | 'audio' | 'subtitle',
    callbacks: {
      setDragOverTrackType: (type: 'video' | 'audio' | 'subtitle' | null) => void;
      setDragOverTrackIndex: (index: number | null) => void;
    }
  ): void {
    this.targetUpdater.updateDragTarget(event, scrollAreaRect, snapResult, type, callbacks);
  }

  /**
   * 恢复元素样式
   */
  resetElementStyles(element: HTMLElement): void {
    this.transformCalculator.resetElementStyles(element);
  }

  /**
   * 获取当前吸附状态信息（用于调试）
   */
  getStateInfo() {
    return {
      currentSnapPoint: this.stateManager.getCurrentSnapPoint(),
      stabilityCounter: this.stateManager.getStabilityCounter()
    };
  }
}

/**
 * 创建时间轴吸附管理器的工厂函数
 */
export function createTimelineSnapManager(config?: SnapConfig): TimelineSnapManager {
  return new TimelineSnapManager(config);
}
