
import { listSound } from "@/api/platform/sound";
import { defineStore } from 'pinia'
type modelMap = {
    [key: number]: boolean;
};
type SoundVo ={
  soundId:number;
  promptText:string;
  text:string
}
const useModelStore = defineStore(
  'model',
  {
    state: () => ({
        serverStatus:false,
        soundServerStatus:Array<modelMap>(),
        sounds:{} as SoundVo
    })  
  })

export default useModelStore