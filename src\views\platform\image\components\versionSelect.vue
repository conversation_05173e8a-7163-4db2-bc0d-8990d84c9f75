<template>
  <el-dialog
    v-model="visible"
    title="选择合成版本"
    width="600px"
    :close-on-click-modal="false"
    :show-close="true"
    @close="onCancel"
  >
    <div class="version-select-title">选择您需要的视频合成版本</div>
    <div class="version-select-desc">不同版本提供不同的功能特性，请根据需求选择</div>
    <div class="version-cards">
      <div
        v-for="item in versions"
        :key="item.key"
        :class="['version-card', { recommended: item.key === 'V' }]"
      >
        <div class="icon" :style="{ background: item.bg }">
          <el-icon :size="32"><component :is="item.icon" /></el-icon>
        </div>
        <div class="title">
          {{ item.label }}
          <span v-if="item.key === 'V'" class="tag">推荐</span>
        </div>
        <div class="desc">{{ item.desc }}</div>
        <ul class="features">
          <li v-for="f in item.features" :key="f">{{ f }}</li>
        </ul>
        <el-button
          type="primary"
          :plain="item.key !== 'V'"
          @click="selectVersion(item.key)"
          class="select-btn"
        >选择此版本</el-button>
      </div>
    </div>
    <template #footer>
      <el-button @click="onCancel">取消</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { VideoCamera, Grid, Sunny } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'

const props = defineProps({
  visible: Boolean,
  imageData: Object // { imageAddress, imageName, ... }
})
const emit = defineEmits(['update:visible'])

const visible = ref(props.visible)
watch(() => props.visible, v => visible.value = v)
watch(visible, v => emit('update:visible', v))

const router = useRouter()
const route = useRoute()

const versions = [
  {
    key: 'M',
    label: 'M版',
    icon: VideoCamera,
    bg: '#409EFF',
    desc: '基础合成 · 简单操作 · 快速生成',
    features: ['快速生成', '简单易用', '基础功能'],
    route: '/szbVideo/createMusetalk'
  },
  {
    key: 'V',
    label: 'V版',
    icon: Grid,
    bg: '#67C23A',
    desc: '高效特效 · 多模型 · 互动能力强',
    features: ['高效特效', '多模型支持', '互动能力强'],
    route: '/szbVideo/synthesisCreate'
  },
  {
    key: 'H',
    label: 'H版',
    icon: Sunny,
    bg: '#E6A23C',
    desc: '高渲染画质 · 多语言 · AI增强',
    features: ['高渲染画质', '多语言', 'AI增强'],
    route: '/szbVideo/createHeygem'
  }
]

function selectVersion(key) {
  const version = versions.find(v => v.key === key)
  if (version) {
    // 构建查询参数，保留当前路由的相关参数
    const query = {
      imageAddress: props.imageData.imageAddress,
      imageName: props.imageData.imageName,
      imageId: props.imageData.imageId,
      from: 'image'
    }

    // 如果当前路由有保留的音频参数，也要传递过去
    if (route.query.keepAudioPath) {
      query.keepAudioPath = route.query.keepAudioPath
    }
    if (route.query.audioPath) {
      query.audioPath = route.query.audioPath
    }

    router.push({
      path: version.route,
      query: query
    })
    visible.value = false
  }
}

function onCancel() {
  visible.value = false
}
</script>

<style scoped>
.version-select-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
  text-align: center;
}
.version-select-desc {
  font-size: 14px;
  color: #888;
  margin-bottom: 24px;
  text-align: center;
}
.version-cards {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 12px;
}
.version-card {
  flex: 1;
  background: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 24px 16px 16px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 2px solid transparent;
  transition: border 0.2s;
}
.version-card.recommended {
  border: 2px solid #67C23A;
  position: relative;
}
.version-card .icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  color: #fff;
}
.version-card .title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.version-card .tag {
  background: #67C23A;
  color: #fff;
  font-size: 12px;
  border-radius: 8px;
  padding: 2px 8px;
  margin-left: 4px;
}
.version-card .desc {
  font-size: 13px;
  color: #666;
  margin-bottom: 10px;
  text-align: center;
}
.version-card .features {
  list-style: none;
  padding: 0;
  margin: 0 0 16px 0;
  color: #409EFF;
  font-size: 13px;
  text-align: left;
  width: 100%;
}
.version-card .features li {
  margin-bottom: 2px;
}
.select-btn {
  width: 100%;
  margin-top: auto;
}
</style>