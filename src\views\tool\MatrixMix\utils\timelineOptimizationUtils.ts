/**
 * @file timelineOptimizationUtils.ts
 * @description 时间轴性能优化工具函数集合
 *              提供局部更新、滚动位置保持、批量操作等优化功能
 *              解决时间调整时的滚动重置和性能问题
 */

import type { Timeline, TimelineClip } from '../types/videoEdit';
import { deepClone } from '@/utils/index';

/**
 * @interface ClipUpdateInfo
 * @description 片段更新信息
 */
export interface ClipUpdateInfo {
  type: 'video' | 'audio' | 'subtitle';
  trackIndex: number;
  clipIndex: number;
  updates: {
    TimelineIn?: number;
    TimelineOut?: number;
    Duration?: number;
    In?: number;
    Out?: number;
  };
}

/**
 * @interface ScrollPositionInfo
 * @description 滚动位置信息
 */
export interface ScrollPositionInfo {
  scrollLeft: number;
  scrollTop: number;
  timestamp: number;
}

/**
 * @class TimelineOptimizationManager
 * @description 时间轴优化管理器，提供局部更新和性能优化功能
 */
export class TimelineOptimizationManager {
  private lastScrollPosition: ScrollPositionInfo | null = null;
  private updateQueue: ClipUpdateInfo[] = [];
  private isProcessingUpdates = false;
  
  /**
   * 保存滚动位置
   */
  saveScrollPosition(scrollElement: HTMLElement): void {
    if (!scrollElement) return;
    
    this.lastScrollPosition = {
      scrollLeft: scrollElement.scrollLeft,
      scrollTop: scrollElement.scrollTop,
      timestamp: Date.now()
    };
    
    console.log('💾 滚动位置已保存:', this.lastScrollPosition);
  }
  
  /**
   * 恢复滚动位置
   */
  restoreScrollPosition(scrollElement: HTMLElement, rulerElement?: HTMLElement): void {
    if (!this.lastScrollPosition || !scrollElement) return;
    
    // 检查是否是最近保存的位置（避免恢复过期的位置）
    const isRecent = Date.now() - this.lastScrollPosition.timestamp < 5000; // 5秒内
    if (!isRecent) {
      console.log('⏰ 滚动位置已过期，跳过恢复');
      this.lastScrollPosition = null;
      return;
    }
    
    scrollElement.scrollLeft = this.lastScrollPosition.scrollLeft;
    scrollElement.scrollTop = this.lastScrollPosition.scrollTop;
    
    // 同步标尺滚动
    if (rulerElement) {
      rulerElement.scrollLeft = this.lastScrollPosition.scrollLeft;
    }
    
    console.log('🔄 滚动位置已恢复:', this.lastScrollPosition);
    this.lastScrollPosition = null;
  }
  
  /**
   * 局部更新单个片段
   * @param timeline 时间轴数据
   * @param updateInfo 更新信息
   * @returns 是否成功更新
   */
  updateClipLocally(timeline: Timeline, updateInfo: ClipUpdateInfo): boolean {
    try {
      const { type, trackIndex, clipIndex, updates } = updateInfo;
      
      let targetClip: any = null;
      
      if (type === 'subtitle') {
        // 字幕片段在 VideoTracks 中，Type 为 'Subtitle'
        const subtitleTracks = timeline.VideoTracks?.filter(t => t.Type === 'Subtitle');
        if (subtitleTracks && subtitleTracks[trackIndex] && subtitleTracks[trackIndex].VideoTrackClips[clipIndex]) {
          targetClip = subtitleTracks[trackIndex].VideoTrackClips[clipIndex];
        }
      } else if (type === 'video') {
        // 视频片段在 VideoTracks 中，非 Subtitle 类型
        const videoTracks = timeline.VideoTracks?.filter(t => t.Type !== 'Subtitle');
        if (videoTracks && videoTracks[trackIndex] && videoTracks[trackIndex].VideoTrackClips[clipIndex]) {
          targetClip = videoTracks[trackIndex].VideoTrackClips[clipIndex];
        }
      } else if (type === 'audio') {
        // 音频片段在 AudioTracks 中
        if (timeline.AudioTracks && timeline.AudioTracks[trackIndex] && timeline.AudioTracks[trackIndex].AudioTrackClips[clipIndex]) {
          targetClip = timeline.AudioTracks[trackIndex].AudioTrackClips[clipIndex];
        }
      }
      
      if (!targetClip) {
        console.warn(`⚠️ 未找到目标片段: ${type} 轨道 ${trackIndex} 片段 ${clipIndex}`);
        return false;
      }
      
      // 应用更新
      Object.keys(updates).forEach(key => {
        if (updates[key as keyof typeof updates] !== undefined) {
          targetClip[key] = updates[key as keyof typeof updates];
        }
      });
      
      console.log(`✅ 局部更新成功: ${type} 轨道 ${trackIndex} 片段 ${clipIndex}`, updates);
      return true;
      
    } catch (error) {
      console.error(`❌ 局部更新失败:`, error);
      return false;
    }
  }
  
  /**
   * 批量局部更新多个片段
   * @param timeline 时间轴数据
   * @param updates 更新信息数组
   * @returns 成功更新的数量
   */
  batchUpdateClips(timeline: Timeline, updates: ClipUpdateInfo[]): number {
    let successCount = 0;
    
    updates.forEach(updateInfo => {
      if (this.updateClipLocally(timeline, updateInfo)) {
        successCount++;
      }
    });
    
    console.log(`📊 批量更新完成: ${successCount}/${updates.length} 个片段更新成功`);
    return successCount;
  }
  
  /**
   * 添加更新到队列
   */
  queueUpdate(updateInfo: ClipUpdateInfo): void {
    this.updateQueue.push(updateInfo);
  }
  
  /**
   * 处理队列中的更新
   */
  async processUpdateQueue(timeline: Timeline): Promise<number> {
    if (this.isProcessingUpdates || this.updateQueue.length === 0) {
      return 0;
    }
    
    this.isProcessingUpdates = true;
    
    try {
      const updates = [...this.updateQueue];
      this.updateQueue = [];
      
      const successCount = this.batchUpdateClips(timeline, updates);
      
      console.log(`🔄 队列更新处理完成: ${successCount}/${updates.length} 个更新成功`);
      return successCount;
      
    } finally {
      this.isProcessingUpdates = false;
    }
  }
  
  /**
   * 清空更新队列
   */
  clearUpdateQueue(): void {
    this.updateQueue = [];
    console.log('🧹 更新队列已清空');
  }
  
  /**
   * 获取队列状态
   */
  getQueueStatus() {
    return {
      queueLength: this.updateQueue.length,
      isProcessing: this.isProcessingUpdates,
      hasScrollPosition: !!this.lastScrollPosition
    };
  }
}

/**
 * 创建时间轴优化管理器实例
 */
export function createTimelineOptimizationManager(): TimelineOptimizationManager {
  return new TimelineOptimizationManager();
}

/**
 * 辅助函数：比较两个片段的时间属性是否相等
 */
export function compareClipTimeProperties(clip1: any, clip2: any): boolean {
  return (
    clip1.TimelineIn === clip2.TimelineIn &&
    clip1.TimelineOut === clip2.TimelineOut &&
    clip1.Duration === clip2.Duration &&
    clip1.In === clip2.In &&
    clip1.Out === clip2.Out
  );
}

/**
 * 辅助函数：提取片段的时间属性
 */
export function extractClipTimeProperties(clip: any) {
  return {
    TimelineIn: clip.TimelineIn,
    TimelineOut: clip.TimelineOut,
    Duration: clip.Duration,
    In: clip.In,
    Out: clip.Out
  };
}

/**
 * 辅助函数：验证时间属性的有效性
 */
export function validateTimeProperties(timeProps: any): boolean {
  const { TimelineIn, TimelineOut, Duration } = timeProps;
  
  // 基本数值验证
  if (typeof TimelineIn !== 'number' || typeof TimelineOut !== 'number') {
    return false;
  }
  
  // 逻辑验证
  if (TimelineIn < 0 || TimelineOut < 0 || TimelineIn >= TimelineOut) {
    return false;
  }
  
  // 持续时间验证
  if (Duration !== undefined && Math.abs((TimelineOut - TimelineIn) - Duration) > 0.001) {
    console.warn('⚠️ 持续时间不匹配:', {
      calculated: TimelineOut - TimelineIn,
      provided: Duration,
      diff: Math.abs((TimelineOut - TimelineIn) - Duration)
    });
  }
  
  return true;
}
