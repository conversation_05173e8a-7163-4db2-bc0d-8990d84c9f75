<template>
  <div class="notes-overview">

    <!-- 笔记展示按钮 -->
    <el-button
      @click="toggleNotesPanel"
      type="primary"
      class="notes-overview-btn"
      :class="{
        'active': isPanelOpen,
        'no-notes': !hasNotes
      }"
    >
      <el-icon size="18" class="notes-icon">
        <EditPen />
      </el-icon>
      <span class="notes-text">笔记概览</span>
      <span class="notes-count" v-if="hasNotes">{{ notesCount }}</span>
      <span class="no-notes-text" v-else>暂无</span>
      <el-icon size="14" class="arrow-icon" :class="{ 'rotated': isPanelOpen }">
        <ArrowDown />
      </el-icon>
    </el-button>

    <!-- 笔记面板 -->
    <transition name="notes-panel">
      <div v-if="isPanelOpen" class="notes-panel" @wheel="handlePanelWheel">
        <!-- 纯CSS粒子背景 -->
        <div class="css-particles"></div>
        <div class="notes-panel-header">
          <div class="panel-title">
            <el-icon size="20"><EditPen /></el-icon>
            <span>笔记概览</span>
          </div>
          <div class="header-actions">
            <!-- 搜索按钮组件 -->
            <div v-if="hasNotes" class="search-btn-container">
              <NotesSearchBox
                ref="notesSearchBoxRef"
                :notes="notesWithContent"
                @search-result-change="handleSearchResultChange"
                @scroll-to-note="handleScrollToNote"
              />
            </div>
            <el-button
              v-if="hasNotes"
              @click="deleteAllNotes"
              type="text"
              size="small"
              class="delete-all-btn"
              :title="'删除所有笔记'"
            >
              <el-icon size="16"><Delete /></el-icon>
              <span>清空</span>
            </el-button>
            <el-button
              @click="closePanel"
              type="text"
              size="small"
              class="close-btn"
            >
              <el-icon size="16"><Close /></el-icon>
            </el-button>
          </div>
        </div>

        <div class="notes-list" ref="notesListRef" @wheel="handleNotesListWheel">
          <div
            v-for="(note, index) in filteredNotes"
            :key="index"
            class="note-item"
            :class="{ 'search-highlight': isNoteHighlighted(note) }"
            @click="scrollToConversation(note.index)"
          >
            <div class="note-header">
              <div class="speaker-info">
                <div
                  class="speaker-circle"
                  :style="{ backgroundColor: getSpeakerColor(note.speakerId) }"
                >
                  {{ note.speakerId }}
                </div>
                <span class="speaker-name">{{ note.speakerName }}</span>
              </div>
              <div class="note-actions">
                <div class="note-time">{{ formatTimestamp(note.startTime) }}</div>
                <el-button
                  @click.stop="deleteNote(note.index)"
                  type="text"
                  size="small"
                  class="delete-note-btn"
                  :title="'删除这条笔记'"
                >
                  <el-icon size="14"><Delete /></el-icon>
                </el-button>
              </div>
            </div>

            <div class="note-content">
              <div class="original-text" :title="note.text">{{ note.text }}</div>
              <div class="note-text" :title="note.notes" v-html="highlightSearchKeyword(note.notes)"></div>
            </div>
          </div>
        </div>

        <div v-if="filteredNotes.length === 0 && notesWithContent.length > 0" class="empty-search">
          <el-icon size="48" class="empty-icon"><Search /></el-icon>
          <p>未找到匹配的笔记内容</p>
        </div>

        <div v-if="notesWithContent.length === 0" class="empty-notes">
          <el-icon size="48" class="empty-icon"><Document /></el-icon>
          <p>暂无笔记内容</p>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted, nextTick } from 'vue'
import { EditPen, ArrowDown, Close, Document, Delete, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { updateTingwuTrans } from '@/api/tingwu/record'
import NotesSearchBox from './NotesSearchBox.vue'

const props = defineProps({
  conversations: {
    type: Array,
    default: () => []
  },
  recordId: {
    type: String,
    required: true
  },
  record: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['scroll-to-conversation', 'notes-updated', 'panel-state-change', 'record-updated'])

// 状态管理
const isPanelOpen = ref(false)
const notesListRef = ref(null)
const notesSearchBoxRef = ref(null)

// 搜索相关状态
const searchKeyword = ref('')
const searchResults = ref([])
const currentSearchIndex = ref(-1)

// 计算有笔记的对话
const notesWithContent = computed(() => {
  return props.conversations
    .map((conversation, index) => ({ ...conversation, index }))
    .filter(conversation => conversation.notes && conversation.notes.trim())
})

// 计算笔记数量
const notesCount = computed(() => notesWithContent.value.length)

// 是否有笔记
const hasNotes = computed(() => notesCount.value > 0)

// 过滤后的笔记列表（根据搜索关键词）
const filteredNotes = computed(() => {
  if (!searchKeyword.value.trim()) {
    return notesWithContent.value
  }

  const keyword = searchKeyword.value.toLowerCase()
  return notesWithContent.value.filter(note =>
    note.notes.toLowerCase().includes(keyword)
  )
})

// 清除搜索状态
const clearSearchState = () => {
  // 清除本地搜索状态
  searchKeyword.value = ''
  searchResults.value = []
  currentSearchIndex.value = -1

  // 清除搜索组件的状态
  if (notesSearchBoxRef.value) {
    notesSearchBoxRef.value.clearSearch()
  }
}

// 切换面板显示
const toggleNotesPanel = () => {
  isPanelOpen.value = !isPanelOpen.value

  // 发送面板状态变化事件
  emit('panel-state-change', isPanelOpen.value)

  if (isPanelOpen.value) {
    nextTick(() => {
      // 防止背景滚动
      document.body.style.overflow = 'hidden'
    })
  } else {
    // 关闭面板时清除搜索状态
    clearSearchState()
    // 恢复背景滚动
    document.body.style.overflow = ''
  }
}

// 关闭面板
const closePanel = () => {
  isPanelOpen.value = false

  // 清除搜索状态
  clearSearchState()

  // 发送面板状态变化事件
  emit('panel-state-change', false)

  // 恢复背景滚动
  document.body.style.overflow = ''
}

// 滚动到指定对话
const scrollToConversation = (index) => {
  emit('scroll-to-conversation', index)
  // 不自动关闭面板，让用户可以继续查看其他笔记
}

// 保存文本更改到数据库
const saveTextToDatabase = async (updatedParagraphs) => {
  try {
    const updateData = {
      vaId: props.recordId,
      paragraphs: updatedParagraphs
    }

    const response = await updateTingwuTrans(updateData)

    if (response.code === 200) {
      console.log('数据库更新成功')
    } else {
      throw new Error(response.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存到数据库失败:', error)
    throw error
  }
}

// 更新record数据并通知父组件
const updateRecordData = (updatedParagraphs) => {
  const updatedRecord = { ...props.record, paragraphs: updatedParagraphs }
  emit('record-updated', updatedRecord)
}

// 处理笔记更新
const handleNotesUpdate = async (updateData) => {
  try {
    // 解析当前的 paragraphs 数据
    const paragraphsData = JSON.parse(props.record.paragraphs)

    // 验证数据结构
    if (!Array.isArray(paragraphsData)) {
      throw new Error('paragraphs 数据格式不正确，应该是数组')
    }

    // 验证索引有效性
    if (updateData.index < 0 || updateData.index >= paragraphsData.length) {
      throw new Error(`索引 ${updateData.index} 超出范围，数组长度为 ${paragraphsData.length}`)
    }

    // 更新指定索引的笔记内容
    const targetItem = paragraphsData[updateData.index]
    if (!targetItem) {
      throw new Error(`无法找到索引 ${updateData.index} 对应的数据`)
    }

    // 更新笔记内容
    targetItem.notes = updateData.notes

    // 更新数据
    const updatedParagraphs = JSON.stringify(paragraphsData)
    updateRecordData(updatedParagraphs)

    // 保存到数据库
    await saveTextToDatabase(updatedParagraphs)

    // 如果有回调函数，调用成功回调
    if (updateData.resolve) {
      updateData.resolve()
    }

    // 通知父组件笔记已更新
    emit('notes-updated', updateData)
  } catch (error) {
    console.error('更新笔记失败:', error)
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      updateData: updateData
    })

    // 如果有回调函数，调用失败回调
    if (updateData.reject) {
      updateData.reject(error)
    } else {
      ElMessage.error('保存笔记失败，请重试')
    }
  }
}

// 暴露方法给父组件使用
defineExpose({
  handleNotesUpdate
})

// 删除单个笔记
const deleteNote = async (conversationIndex) => {
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      '确定要删除这条笔记吗？删除后无法恢复。',
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 解析当前的 paragraphs 数据
    const paragraphsData = JSON.parse(props.record.paragraphs)

    // 验证数据结构
    if (!Array.isArray(paragraphsData)) {
      throw new Error('paragraphs 数据格式不正确，应该是数组')
    }

    // 验证索引有效性
    if (conversationIndex < 0 || conversationIndex >= paragraphsData.length) {
      throw new Error(`索引 ${conversationIndex} 超出范围，数组长度为 ${paragraphsData.length}`)
    }

    // 清空指定索引的笔记内容
    const targetItem = paragraphsData[conversationIndex]
    if (!targetItem) {
      throw new Error(`无法找到索引 ${conversationIndex} 对应的数据`)
    }

    // 删除笔记内容（设置为空字符串）
    targetItem.notes = ''

    // 更新数据
    const updatedParagraphs = JSON.stringify(paragraphsData)
    updateRecordData(updatedParagraphs)

    // 保存到数据库
    await saveTextToDatabase(updatedParagraphs)

    ElMessage.success('笔记删除成功')
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消删除
      return
    }

    console.error('删除笔记失败:', error)
    ElMessage.error('删除笔记失败，请重试')
  }
}

// 删除所有笔记
const deleteAllNotes = async () => {
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      '确定要删除所有笔记吗？此操作将清空所有对话的笔记内容，删除后无法恢复。',
      '删除所有笔记',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 解析当前的 paragraphs 数据
    const paragraphsData = JSON.parse(props.record.paragraphs)

    // 验证数据结构
    if (!Array.isArray(paragraphsData)) {
      throw new Error('paragraphs 数据格式不正确，应该是数组')
    }

    // 清空所有笔记内容
    paragraphsData.forEach(item => {
      if (item.notes) {
        item.notes = ''
      }
    })

    // 更新数据
    const updatedParagraphs = JSON.stringify(paragraphsData)
    updateRecordData(updatedParagraphs)

    // 保存到数据库
    await saveTextToDatabase(updatedParagraphs)

    ElMessage.success('所有笔记删除成功')
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消删除
      return
    }

    console.error('删除所有笔记失败:', error)
    ElMessage.error('删除所有笔记失败，请重试')
  }
}

// 获取说话人颜色
const getSpeakerColor = (speakerId) => {
  const colors = [
    '#1890ff', '#52c41a', '#fa8c16', '#eb2f96',
    '#722ed1', '#13c2c2', '#f5222d', '#faad14'
  ]
  return colors[parseInt(speakerId) % colors.length]
}

// 格式化时间
const formatTimestamp = (milliseconds) => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 处理笔记面板滚动事件，防止滚动穿透
const handlePanelWheel = (event) => {
  // 阻止事件冒泡到父容器
  event.stopPropagation()
}

// 处理笔记列表滚动事件
const handleNotesListWheel = (event) => {
  const element = notesListRef.value
  if (!element) {
    event.preventDefault()
    event.stopPropagation()
    return
  }

  const { scrollTop, scrollHeight, clientHeight } = element
  const isAtTop = scrollTop <= 0
  const isAtBottom = scrollTop + clientHeight >= scrollHeight - 2 // 增加一点容差

  // 如果内容不需要滚动（内容高度小于容器高度）
  if (scrollHeight <= clientHeight) {
    event.preventDefault()
    event.stopPropagation()
    return
  }

  // 如果在顶部向上滚动，或在底部向下滚动，阻止事件传播
  if ((isAtTop && event.deltaY < 0) || (isAtBottom && event.deltaY > 0)) {
    event.preventDefault()
    event.stopPropagation()
  }
}

// 搜索相关方法
const handleSearchResultChange = (searchData) => {
  searchKeyword.value = searchData.keyword
  searchResults.value = searchData.results
  currentSearchIndex.value = searchData.currentIndex
}

const handleScrollToNote = (noteIndex) => {
  // 滚动到指定笔记对应的对话
  const note = notesWithContent.value[noteIndex]
  if (note) {
    scrollToConversation(note.index)
  }
}

const highlightSearchKeyword = (text) => {
  if (!searchKeyword.value.trim()) {
    return text
  }

  const keyword = searchKeyword.value
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark class="search-highlight-text">$1</mark>')
}

const isNoteHighlighted = (note) => {
  if (!searchKeyword.value.trim()) {
    return false
  }

  return note.notes.toLowerCase().includes(searchKeyword.value.toLowerCase())
}

onUnmounted(() => {
  // 确保恢复背景滚动
  document.body.style.overflow = ''
})
</script>

<style lang="scss" scoped>
@import '../styles/notes-overview.scss';
</style>
