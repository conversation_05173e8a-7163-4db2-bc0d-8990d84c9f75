<template>
  <div class="app-container task-dashboard">
    <div class="search-panel">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px" class="compact-form">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable @keyup.enter="handleQuery" class="compact-input"/>
        </el-form-item>
        <el-form-item label="机器码" prop="machineCode">
          <el-input v-model="queryParams.machineCode" placeholder="请输入机器码" clearable @keyup.enter="handleQuery" class="compact-input"/>
        </el-form-item>
        <el-form-item label="模型名称" prop="modelName">
          <el-input v-model="queryParams.modelName" placeholder="请输入模型名称" clearable @keyup.enter="handleQuery" class="compact-input"/>
        </el-form-item>
        <el-form-item label="任务类型" prop="type" class="select-item">
          <el-select v-model="queryParams.type" placeholder="请选择任务类型" clearable class="compact-select">
            <el-option v-for="dict in platform_task_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务状态" prop="taskStatus" class="select-item">
          <el-select v-model="queryParams.taskStatus" placeholder="请选择状态" clearable class="compact-select">
            <el-option v-for="dict in platform_task_task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="action-item">
          <div class="search-btn-group">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <div class="action-toolbar">
      <div class="left-actions">
        <el-button type="danger" :icon="Delete" :disabled="multiple" @click="handleDelete" class="action-btn delete-btn">删除数据</el-button>
        <el-button type="warning" :icon="Download" @click="handleExport" class="action-btn export-btn">导出数据</el-button>
      </div>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </div>

    <div class="data-container">
      <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange" class="custom-table"
        stripe>
        <el-table-column type="selection" width="45" align="center" />
        <el-table-column label="任务ID" align="center" prop="taskId" width="70" />
        <el-table-column label="任务名称" align="center" prop="taskName" min-width="120" show-overflow-tooltip />
        <el-table-column label="任务类型" align="center" prop="type" width="100">
          <template #default="scope">
            <dict-tag :options="platform_task_type" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="模型名称" align="center" prop="modelName" min-width="100" show-overflow-tooltip />
        <el-table-column label="声音名称" align="center" min-width="120" show-overflow-tooltip>
          <template #default="scope">
            <span class="sound-name">
              {{ soundList.find(dict => dict.soundId == scope.row.modelName)?.soundName || '未知声音' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="机器码" align="center" prop="machineCode" min-width="120" show-overflow-tooltip />
        <el-table-column label="状态" align="center" prop="taskStatus" width="90">
          <template #default="scope">
            <dict-tag :options="platform_task_task_status" :value="scope.row.taskStatus" />
          </template>
        </el-table-column>
        <el-table-column label="操作人" align="center" prop="createBy" min-width="100" show-overflow-tooltip/>
        <el-table-column label="操作时间" align="center" prop="createTime" width="160" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" plain size="small" @click="handleUpdate(scope.row)">详情</el-button>
              <el-button type="danger" plain size="small" @click="handleDelete(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-wrapper">
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body draggable class="task-detail-dialog">
      <el-scrollbar height="500px">
        <div class="task-detail-content">
          <div class="detail-header">
            <div class="task-id">任务ID: {{ form.taskId }}</div>
            <div class="task-status">
              <dict-tag :options="platform_task_task_status" :value="form.taskStatus" />
            </div>
          </div>

          <div class="detail-section">
            <div class="section-title">基础信息</div>
            <el-form ref="taskRef" :model="form" :rules="rules" label-width="100px" class="detail-form">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="任务名称" prop="taskName">
                    <el-input v-model="form.taskName" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="任务类型" prop="type">
                    <el-input v-model="form.type" disabled />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="机器码" prop="machineCode">
                    <el-input v-model="form.machineCode" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="模型名称" prop="modelName">
                    <el-input v-model="form.modelName" disabled />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="任务内容">
                <el-button type="primary" @click="openContent(form.taskContent)" class="view-content-btn">
                  <el-icon><Document /></el-icon>查看详细内容
                </el-button>
              </el-form-item>
              
              <el-form-item label="任务结果" prop="taskResult">
                <el-input v-model="form.taskResult" type="textarea" rows="3" disabled />
              </el-form-item>
              
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" rows="3" disabled />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看内容对话框 -->
    <el-dialog title="任务详细内容" v-model="openContentDialog" width="900px" append-to-body draggable class="content-dialog">
      <el-scrollbar height="600px">
        <div class="content-wrapper">
          <div class="content-section">
            <div class="section-title">回调信息</div>
            <el-form-item label="回调地址">
              <el-input v-model="content.callback" disabled />
            </el-form-item>
          </div>
          
          <div class="content-section audio-section">
            <div class="section-title">参考音频</div>
            <div class="audio-player-wrapper">
              <audio controls class="audio-player">
                <source :src="content.configs?.tts?.ref_audio" type="audio/ogg">
                您的浏览器不支持音频播放
              </audio>
            </div>
          </div>
          
          <div class="content-section">
            <div class="section-title">推理文案</div>
            <div class="text-data-table">
              <el-table 
                :data="content.configs?.tts?.text || []" 
                style="width: 100%" 
                border 
                stripe
                header-cell-class-name="custom-header"
              >
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="article" label="文章内容">
                  <template #default="scope">
                    <div class="article-content">{{ scope.row.article }}</div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="openContentDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Task">
import { listSound } from "@/api/platform/sound";
import { delTask, getTask, listTask } from "@/api/platform/task";
import { Delete, Download, Document } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const { platform_task_type, platform_task_task_status } = proxy.useDict("platform_task_type", "platform_task_task_status");

const taskList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const soundList = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: null,
    taskStatus: null,
    taskContent: null,
    machineCode: null,
    taskResult: null,
    type: null,
    modelName: null
  },
  rules: {}
});
const { queryParams, form, rules } = toRefs(data);

/** 查询任务管理列表 */
function getList() {
  loading.value = true;
  listTask(queryParams.value).then(response => {
    taskList.value = response.rows;
    total.value = response.total;
    listSound().then(response => {
      soundList.value = response.rows;
      loading.value = false;
    });
  });
}

// 表单重置
function reset() {
  form.value = {
    taskId: null,
    taskName: null,
    taskStatus: null,
    taskContent: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null,
    machineCode: null,
    taskResult: null,
    type: null,
    modelName: null
  };
  proxy.resetForm("taskRef");
}

/** 搜索按钮操作 */
function handleQuery() { queryParams.value.pageNum = 1; getList(); }

/** 重置按钮操作 */
function resetQuery() { proxy.resetForm("queryRef"); handleQuery(); }

// 多选框选中数据
function handleSelectionChange(selection) { ids.value = selection.map(item => item.taskId); single.value = selection.length != 1; multiple.value = !selection.length; }

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _taskId = row.taskId || ids.value
  getTask(_taskId).then(response => {
    form.value = response.data;
    form.value.type = platform_task_type.value.find(dict => dict.value == form.value.type)?.label;
    open.value = true;
    title.value = "任务详情";
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _taskIds = row.taskId || ids.value;
  proxy.$modal.confirm('是否确认删除任务管理编号为"' + _taskIds + '"的数据项？').then(() => delTask(_taskIds)).then(() => { getList(); proxy.$modal.msgSuccess("删除成功"); }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() { proxy.download('platform/task/export', { ...queryParams.value }, `task_${new Date().getTime()}.xlsx`) }

const openContentDialog = ref(false);
const content = ref({});
function openContent(taskContent) {
  openContentDialog.value = true;
  const parsedContent = JSON.parse(taskContent);
  const transformedText = parsedContent.configs.tts.text.map(item => {
    const [id, article] = Object.entries(item)[0];
    return { id, article };
  });
  content.value = {
    ...parsedContent,
    configs: {
      ...parsedContent.configs,
      tts: {
        ...parsedContent.configs.tts,
        text: transformedText
      }
    }
  };
}

getList();
</script>

<style lang="scss" scoped>
.task-dashboard { background-color: #f6f8fc; border-radius: 8px; padding: 20px; min-height: calc(100vh - 110px); }
.search-panel { background: #ffffff; border-radius: 10px; padding: 15px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03); margin-bottom: 20px; border-top: 3px solid #6366f1; }
.compact-form { display: flex; flex-wrap: wrap; gap: 5px 15px; align-items: flex-start; }
.compact-form :deep(.el-form-item) { margin-bottom: 10px; margin-right: 0; }
.compact-form :deep(.el-form-item__label) { font-weight: 500; color: #374151; padding-right: 8px; line-height: 32px; }
.compact-form :deep(.el-form-item__content) { line-height: 32px; }
.compact-input { width: 160px; }
.compact-select { width: 160px; }
.search-btn-group { display: flex; gap: 8px; }
.action-item { margin-left: 5px !important; }
.search-panel :deep(.el-input__wrapper) { border-radius: 6px; box-shadow: 0 0 0 1px #d1d5db inset; transition: box-shadow 0.3s ease, transform 0.3s ease; padding: 1px 11px; }
.search-panel :deep(.el-input__wrapper:focus-within) { box-shadow: 0 0 0 1px #6366f1 inset; transform: translateY(-1px); }
.search-panel :deep(.el-input__wrapper:hover) { box-shadow: 0 0 0 1px #9ca3af inset; }
.search-panel :deep(.el-button) { border-radius: 6px; transition: all 0.3s ease; padding: 8px 15px; height: 32px; }
.search-panel :deep(.el-button--primary) { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); border-color: #6366f1; font-weight: 500; }
.search-panel :deep(.el-button--primary:hover) { background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); transform: translateY(-2px); box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4); }
.select-item :deep(.el-select) { width: 160px; }
.action-toolbar { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding: 0 10px; }
.left-actions { display: flex; gap: 10px; }
.action-btn { border-radius: 6px; padding: 10px 16px; font-weight: 500; transition: all 0.3s ease; }
.delete-btn { background-color: #fee2e2; border-color: #fecaca; color: #ef4444; }
.delete-btn:hover:not(:disabled) { background-color: #fecaca; border-color: #ef4444; color: #b91c1c; transform: translateY(-2px); }
.export-btn { background-color: #fef3c7; border-color: #fde68a; color: #f59e0b; }
.export-btn:hover { background-color: #fde68a; border-color: #f59e0b; color: #d97706; transform: translateY(-2px); }
.data-container { background: #ffffff; border-radius: 10px; padding: 5px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03); margin-bottom: 20px; overflow: hidden; }
.custom-table { border-radius: 8px; overflow: hidden; }
.custom-table :deep(thead tr th) { background-color: #f3f4f6 !important; color: #374151; font-weight: 600; padding-top: 14px; padding-bottom: 14px; border-bottom: 2px solid #e5e7eb; }
.custom-table :deep(.el-table__row) { transition: background-color 0.3s ease; }
.custom-table :deep(.el-table__row:hover) { background-color: #f8fafc; }
.custom-table :deep(.el-table__row) td { padding: 12px 0; color: #4b5563; }
.status-tag { font-weight: 500; border-radius: 4px; padding: 2px 8px; }
.sound-name { color: #4f46e5; font-weight: 500; }
.action-buttons { display: flex; justify-content: center; gap: 8px; }
.action-buttons :deep(.el-button) { padding: 6px 12px; font-size: 12px; border-radius: 4px; }
.pagination-wrapper { background: #ffffff; border-radius: 10px; padding: 15px 20px; display: flex; justify-content: flex-end; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03); }
/* 详情弹窗样式 */
.task-detail-dialog :deep(.el-dialog__header) { background: linear-gradient(90deg, #6366f1, #8b5cf6); color: white; padding: 15px 20px; margin-right: 0; }
.task-detail-dialog :deep(.el-dialog__title) { color: white; font-weight: 600; font-size: 18px; }
.task-detail-dialog :deep(.el-dialog__headerbtn:hover .el-dialog__close) { color: rgba(255, 255, 255, 0.9); }
.task-detail-dialog :deep(.el-dialog__body) { padding: 0; }
.task-detail-dialog :deep(.el-dialog__footer) { border-top: 1px solid #e5e7eb; padding: 15px 20px; }
.task-detail-content { padding: 20px; }
.detail-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; padding-bottom: 15px; border-bottom: 1px solid #e5e7eb; }
.task-id { font-size: 16px; font-weight: 600; color: #4b5563; }
.detail-section { margin-bottom: 20px; }
.section-title { font-size: 16px; font-weight: 600; color: #4b5563; margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid #e5e7eb; }
.detail-form :deep(.el-form-item__label) { font-weight: 500; color: #6b7280; }
.detail-form :deep(.el-input__wrapper) { box-shadow: 0 0 0 1px #d1d5db inset; border-radius: 6px; background-color: #f9fafb; }
.detail-form :deep(.el-textarea__inner) { box-shadow: 0 0 0 1px #d1d5db inset; border-radius: 6px; background-color: #f9fafb; padding: 8px 12px; height: auto; }
.view-content-btn { background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%); border-color: #4f46e5; border-radius: 6px; padding: 10px 16px; transition: all 0.3s ease; display: flex; align-items: center; gap: 6px; font-weight: 500; }
.view-content-btn:hover { background: linear-gradient(135deg, #4338ca 0%, #4f46e5 100%); transform: translateY(-2px); box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4); }
/* 内容弹窗样式 */
.content-dialog :deep(.el-dialog__header) { background: linear-gradient(90deg, #4f46e5, #6366f1); color: white; padding: 15px 20px; margin-right: 0; }
.content-dialog :deep(.el-dialog__title) { color: white; font-weight: 600; font-size: 18px; }
.content-wrapper { padding: 20px; }
.content-section { margin-bottom: 25px; }
.audio-section { background-color: #f9fafb; padding: 15px; border-radius: 8px; }
.audio-player-wrapper { display: flex; justify-content: center; padding: 15px 0; }
.audio-player { width: 100%; max-width: 500px; height: 40px; }
.text-data-table { margin-top: 10px; }
.text-data-table :deep(.custom-header) { background-color: #f3f4f6; color: #374151; font-weight: 600; }
.article-content { padding: 8px 0; line-height: 1.5; }
</style>