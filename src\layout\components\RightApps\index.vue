<template>
  <div class="right-apps">
    <div 
      v-for="(app, index) in apps" 
      :key="index"
      class="app-icon"
      @click="handleClick(app)"
      @mouseenter="app.active = true"
      @mouseleave="app.active = false"
    >
      <div class="icon-wrapper">
        <el-icon class="icon" :color="app.color">
          <component :is="app.icon" />
        </el-icon>
      </div>
      <span class="tooltip">{{ app.title }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ChromeFilled, Monitor, User } from '@element-plus/icons-vue'

const emit = defineEmits(['openBrowser', 'openLiveConsole', 'openDyRobot'])

const colors = [
  '#3B82F6',
  '#10B981',
  '#8B5CF6',
  '#F59E0B',
  '#EC4899',
  '#6366F1',
  '#14B8A6',
  '#F97316',
  '#8B5CF6',
  '#06B6D4'
]

// 随机获取颜色，确保相邻的图标颜色不重复
const getRandomColor = (excludeColors = []) => {
  const availableColors = colors.filter(color => !excludeColors.includes(color))
  const randomIndex = Math.floor(Math.random() * availableColors.length)
  return availableColors[randomIndex]
}

const apps = ref([
  {
    title: '浏览器涡轮',
    icon: ChromeFilled,
    active: false,
    action: 'openBrowser',
    color: ''
  },
  {
    title: '直播控制台',
    icon: Monitor,
    active: false,
    action: 'openLiveConsole',
    color: ''
  },
  {
    title: '抖音机器人',
    icon: User,
    active: false,
    action: 'openDyRobot',
    color: ''
  }
])

// 初始化时分配颜色
onMounted(() => {
  const usedColors = []
  apps.value.forEach(app => {
    const color = getRandomColor(usedColors)
    app.color = color
    usedColors.push(color)
  })
})

const handleClick = async (app) => {
  emit(app.action)
}
</script>

<style lang="scss" scoped>
.right-apps {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px 8px;
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  margin: 4px;
  animation: slideIn 0.5s ease forwards;
}

.app-icon {
  position: relative;
  cursor: pointer;
  
  .icon-wrapper {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 14px;
    background: #ffffff;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  
  .icon {
    font-size: 24px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
  }
  
  .tooltip {
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translate(-100%, -50%);
    background: rgba(15, 23, 42, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 13px;
    opacity: 0;
    pointer-events: none;
    transition: all 0.2s ease;
    backdrop-filter: blur(8px);
    white-space: nowrap;
    
    &::after {
      content: '';
      position: absolute;
      right: -4px;
      top: 50%;
      transform: translateY(-50%);
      border-left: 4px solid rgba(15, 23, 42, 0.8);
      border-top: 4px solid transparent;
      border-bottom: 4px solid transparent;
    }
  }
  
  &:hover {
    .icon-wrapper {
      background: #f8fafc;
      transform: translateY(-2px);
      box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.05),
        0 2px 4px rgba(0, 0, 0, 0.05);
    }
    
    .icon {
      transform: scale(1.15);
    }
    
    .tooltip {
      opacity: 1;
      transform: translate(-100%, -50%);
    }
  }
  
  &:active {
    .icon-wrapper {
      transform: translateY(0);
      transition: all 0.1s ease;
      background: #f1f5f9;
    }
    
    .icon {
      transform: scale(0.95);
    }
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>