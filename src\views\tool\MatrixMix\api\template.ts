/**
 * 阿里云ICE模板相关API
 * 通过后端代理调用阿里云ICE SDK
 */
import request from '@/utils/request';

// ============================================================================
// 类型定义 - 根据阿里云ICE官方文档
// ============================================================================

export interface CreateTemplateRequest {
  Name: string;        // @JsonProperty("Name")
  Type?: string;       // @JsonProperty("Type")
  Config: string;      // @JsonProperty("Config") - 模板配置JSON字符串
  CoverUrl?: string;   // @JsonProperty("CoverUrl") - 封面URL
  PreviewMedia?: string; // @JsonProperty("PreviewMedia")
  Status?: string;     // @JsonProperty("Status")
  Source?: string;     // @JsonProperty("Source")
  RelatedMediaids?: string; // @JsonProperty("RelatedMediaids")
}

export interface UpdateTemplateRequest {
  TemplateId: string;    // @JsonProperty("TemplateId")
  Name?: string;         // @JsonProperty("Name")
  Config: string;        // @JsonProperty("Config") - 模板配置JSON字符串
  CoverUrl?: string;     // @JsonProperty("CoverUrl")
  PreviewMedia?: string; // @JsonProperty("PreviewMedia")
  Status?: string;       // @JsonProperty("Status")
  Source?: string;       // @JsonProperty("Source")
  RelatedMediaids?: string; // @JsonProperty("RelatedMediaids")
}

export interface TemplateInfo {
  TemplateId: string;
  Name: string;
  Type: string;
  Config: string; // Timeline配置
  PreviewMedia?: string;
  Status: string;
  CreateSource?: string;
  ModifiedSource?: string;
  PreviewMediaStatus?: string;
  CreationTime?: string;
  ModifiedTime?: string;
  CoverURL?: string;
  ClipsParam?: any;
  RelatedMediaids?: {
    video?: string[];
    audio?: string[];
    image?: string[];
  };
}

export interface TemplateListParams {
  pageNo?: number;
  pageSize?: number;
  type?: string;
  status?: string;
  createSource?: string;
  keyword?: string;
  sortType?: string;
}

export interface TemplateListResponse {
  Templates: TemplateInfo[];
  TotalCount: number;
}

export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// ============================================================================
// 兼容性类型（保持向后兼容）
// ============================================================================

export interface AddTemplateRequest extends CreateTemplateRequest {}
export interface AddTemplateResponse extends TemplateInfo {}
export interface ListTemplatesRequest extends TemplateListParams {}
export interface ListTemplatesResponse extends TemplateListResponse {}
export interface DeleteTemplateRequest {
  templateId: string;
}
export interface DeleteTemplateResponse {
  success: boolean;
}
export interface UpdateTemplateResponse {
  success: boolean;
}

// ============================================================================
// API接口
// ============================================================================

/**
 * 创建模板
 */
export function addTemplate(data: CreateTemplateRequest): Promise<ApiResponse<TemplateInfo>> {
  console.log('📤 API发送模板创建请求:', data);
  // 直接传递数据，字段名已经匹配后端 @JsonProperty 注解
  const requestData = {
    Name: data.Name,
    Type: data.Type || 'Timeline',
    Config: data.Config,
    CoverUrl: data.CoverUrl,
    PreviewMedia: data.PreviewMedia,
    Status: data.Status,
    Source: data.Source || 'WebSDK',
    RelatedMediaids: data.RelatedMediaids
  };

  return request({
    url: '/video/template/add',
    method: 'post',
    data: requestData
  });
}

/**
 * 获取模板详情
 */
export function getTemplate(templateId: string, params?: { relatedMediaidFlag?: string }): Promise<ApiResponse<{ RequestId: string; Template: TemplateInfo }>> {
  return request({
    url: `/video/template/${templateId}`,
    method: 'get',
    params: {
      relatedMediaidFlag: params?.relatedMediaidFlag || '0'
    }
  });
}

/**
 * 获取模板列表
 */
export function listTemplates(params: TemplateListParams = {}): Promise<ApiResponse<TemplateListResponse>> {
  return request({
    url: '/video/template/list',
    method: 'get',
    params: {
      pageNo: params.pageNo,
      pageSize: params.pageSize,
      type: params.type,
      status: params.status,
      createSource: params.createSource,
      keyword: params.keyword,
      sortType: params.sortType || 'CreationTime:Desc'
    }
  });
}

/**
 * 更新模板
 */
export function updateTemplate(data: UpdateTemplateRequest): Promise<ApiResponse<UpdateTemplateResponse>> {
  // 直接传递数据，字段名已经匹配后端 @JsonProperty 注解
  const requestData = {
    TemplateId: data.TemplateId,
    Name: data.Name,
    Config: data.Config,
    CoverUrl: data.CoverUrl,
    PreviewMedia: data.PreviewMedia,
    Status: data.Status,
    Source: data.Source,
    RelatedMediaids: data.RelatedMediaids
  };

  return request({
    url: '/video/template/update',
    method: 'put',
    data: requestData
  });
}


/**
 * 删除模板
 */
export function deleteTemplate(templateIds: string[]): Promise<ApiResponse<DeleteTemplateResponse>> {
  return request({
    url: '/video/template/delete',
    method: 'post',
    data: {
      templateIds: templateIds.join(',')
    }
  });
}

/**
 * 获取模板素材地址
 */
export function getTemplateMaterials(templateId: string, fileList?: string): Promise<ApiResponse<any>> {
  return request({
    url: `/video/template/${templateId}/materials`,
    method: 'get',
    params: {
      fileList
    }
  });
}

// ============================================================================
// 兼容性接口（保持向后兼容）
// ============================================================================

/**
 * 获取模板列表（兼容旧接口）
 */
export function getTemplateList(params: TemplateListParams): Promise<ApiResponse<TemplateListResponse>> {
  return listTemplates(params);
}