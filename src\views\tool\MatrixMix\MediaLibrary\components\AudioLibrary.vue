<template>
  <div class="audio-library">
    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button type="primary" @click="handleUpload">
          <el-icon><Plus /></el-icon>
          上传音频
        </el-button>
        <el-button type="danger" @click="handleDelete" :disabled="selectedItems.length === 0">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="right-actions">
        <el-select v-model="businessType" placeholder="业务类型" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="source" placeholder="来源" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="status" placeholder="资源状态" style="width: 120px; margin-right: 10px;">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px; margin-right: 10px;"
          @change="handleDateChange"
        />
        <el-select v-model="sortBy" placeholder="排序方式" style="width: 120px; margin-right: 10px;">
          <el-option label="升序" value="utcCreate:Asc" />
          <el-option label="降序" value="utcCreate:Desc" />
        </el-select>
        <!-- 搜索框 -->
        <el-input
          v-model="searchKeyword"
          placeholder="搜索音频..."
          style="width: 200px; margin: 0 10px;"
          @input="handleSearchInput"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="handleFilter">筛选</el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <!-- 右侧音频列表 -->
      <div class="audio-panel">
        <div v-if="props.loading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
        
        <div v-else-if="props.mediaList?.length === 0" class="empty-state">
          <el-empty description="暂无数据">
            <el-button type="primary" @click="handleUpload">上传音频</el-button>
          </el-empty>
        </div>

        <div v-else class="audio-table">
          <el-table :data="props.mediaList || []" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="音频名称" prop="title" min-width="200">
              <template #default="{ row }">
                <div class="audio-title">
                  <el-icon class="audio-icon"><Microphone /></el-icon>
                  <span>{{ row.title }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="时长" width="100">
              <template #default="{ row }">
                {{ formatDuration(row.duration || 0) }}
              </template>
            </el-table-column>
            <el-table-column label="文件大小" width="120">
              <template #default="{ row }">
                {{ formatFileSize(row.size || 0) }}
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                {{ getStatusText(row.Status) }}
              </template>
            </el-table-column>
            <el-table-column label="创建日期" width="150">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250">
              <template #default="{ row }">
                <el-button size="small" @click="handlePlay(row)">
                  <el-icon><VideoPlay /></el-icon>
                  播放
                </el-button>
                <el-button size="small" type="primary" @click="handleDownload(row)">下载</el-button>
                <el-button size="small" type="danger" @click="handleDeleteSingle(row.mediaId)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 音频播放器 -->
    <div v-if="currentAudio" class="audio-player">
      <div class="player-info">
        <el-icon><Microphone /></el-icon>
        <span>{{ currentAudio.title }}</span>
      </div>
      <div class="player-controls">
        <el-button :icon="isPlaying ? 'VideoPause' : 'VideoPlay'" circle @click="togglePlay" />
        <div class="progress-container">
          <div class="progress-bar" @click="handleProgressClick">
            <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
          </div>
        </div>
        <span class="time">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</span>
      </div>
      <div class="player-actions">
        <el-button size="small" @click="closePlayer">关闭</el-button>
      </div>
      <!-- 隐藏的音频元素 -->
      <audio 
        ref="audioRef"
        :src="getAudioUrl(currentAudio)"
        @loadedmetadata="handleLoadedMetadata"
        @timeupdate="handleTimeUpdate"
        @ended="handleEnded"
        @error="handleAudioError"
        style="display: none;"
      ></audio>
    </div>

    <!-- 上传组件 -->
    <MediaUploader
      v-model:visible="uploadDialogVisible"
      media-type="audio"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Delete,
  Refresh,
  Loading,
  VideoPlay,
  Microphone,
  Search
} from '@element-plus/icons-vue'
import type { MediaInfo, Material } from '../../types/media'
import { formatFileSize, formatDuration, downloadFileByUrl } from '../../utils/commonUtils'
import { BusinessTypeMap, StatusMap, SourceMap, mapToOptions, getStatusText } from '../../types/media'
import MediaUploader from './add/MediaUploader.vue'

// 定义组件的事件
const emit = defineEmits<{
  upload: [uploadParams: { file: File; MediaMetaData?: any }],
  refreshMediaList: [page?: number],
  sizeChange: [size: number],
  pageChange: [current: number],
  search: [keyword: string],
  filter: [params: any]
}>()

// 响应式数据
const selectedItems = ref<string[]>([])
const uploadDialogVisible = ref(false)
const currentAudio = ref<Material | null>(null)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const audioRef = ref<HTMLAudioElement>()

// 筛选相关变量和选项
const businessType = ref('')
const source = ref('')
const status = ref('')
const dateRange = ref<[Date, Date] | null>(null)
const sortBy = ref('utcCreate:Desc')
const startTime = ref<string>('')
const endTime = ref<string>('')
const searchKeyword = ref('')

const businessTypeOptions = mapToOptions(BusinessTypeMap)
const statusOptions = mapToOptions(StatusMap)
const sourceOptions = mapToOptions(SourceMap)

// 计算属性
const progressPercentage = computed(() => {
  if (duration.value === 0) return 0
  return (currentTime.value / duration.value) * 100
})

// 获取音频URL
const getAudioUrl = (audio: Material): string => {
  return audio.FileUrl || ''
}

// 日期筛选处理
const handleDateChange = (val: [Date, Date] | null) => {
  if (val && val.length === 2) {
    startTime.value = val[0].toISOString()
    endTime.value = val[1].toISOString()
  } else {
    startTime.value = ''
    endTime.value = ''
  }
}

// 搜索处理
const handleSearchInput = () => {
  emit('search', searchKeyword.value)
}

// 筛选按钮处理
const handleFilter = () => {
  emit('filter', {
    businessType: businessType.value,
    source: source.value,
    status: status.value,
    startTime: startTime.value,
    endTime: endTime.value,
    sortBy: sortBy.value,
    searchKeyword: searchKeyword.value
  })
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 方法
const handleUpload = () => {
  uploadDialogVisible.value = true
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除选中的音频吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    selectedItems.value = []
    emit('refreshMediaList')
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleRefresh = () => {
  emit('refreshMediaList')
}

const handleSelectionChange = (selection: Material[]) => {
  selectedItems.value = selection.map(item => item.mediaId)
}

const handlePlay = (audio: Material) => {
  if (currentAudio.value?.mediaId === audio.mediaId && isPlaying.value) {
    // 如果是同一个音频且正在播放，暂停
    pauseAudio()
  } else {
    // 播放新音频或恢复播放
    currentAudio.value = audio
    playAudio()
  }
}

const playAudio = () => {
  if (audioRef.value) {
    audioRef.value.play()
    isPlaying.value = true
  }
}

const pauseAudio = () => {
  if (audioRef.value) {
    audioRef.value.pause()
    isPlaying.value = false
  }
}

const togglePlay = () => {
  if (isPlaying.value) {
    pauseAudio()
  } else {
    playAudio()
  }
}

const handleProgressClick = (event: MouseEvent) => {
  if (audioRef.value && duration.value > 0) {
    const progressBar = event.currentTarget as HTMLElement
    const rect = progressBar.getBoundingClientRect()
    const clickX = event.clientX - rect.left
    const percentage = clickX / rect.width
    const newTime = percentage * duration.value
    audioRef.value.currentTime = newTime
    currentTime.value = newTime
  }
}

const handleLoadedMetadata = () => {
  if (audioRef.value) {
    duration.value = audioRef.value.duration
  }
}

const handleTimeUpdate = () => {
  if (audioRef.value) {
    currentTime.value = audioRef.value.currentTime
  }
}

const handleEnded = () => {
  isPlaying.value = false
  currentTime.value = 0
}

const handleAudioError = (e: Event) => {
  console.error('音频播放错误:', e)
  ElMessage.error('音频播放失败，请检查音频文件')
  isPlaying.value = false
}

const closePlayer = () => {
  if (audioRef.value) {
    audioRef.value.pause()
    audioRef.value.currentTime = 0
  }
  currentAudio.value = null
  isPlaying.value = false
  currentTime.value = 0
  duration.value = 0
}

const handleDownload = (audio: Material) => {
  const url = getAudioUrl(audio)
  if (url) {
    try {
      downloadFileByUrl(url, audio.title)
      ElMessage.success('开始下载音频文件')
    } catch (error) {
      console.error('下载音频失败:', error)
      ElMessage.error('无法下载音频文件')
    }
  } else {
    ElMessage.error('无法获取音频下载链接')
  }
}

const handleDeleteSingle = async (audioId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个音频吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    emit('refreshMediaList')
  } catch {
    ElMessage.info('已取消删除')
  }
}

// 上传成功处理
const handleUploadSuccess = (result: any) => {
  ElMessage.success('音频上传成功！')
  uploadDialogVisible.value = false
  emit('refreshMediaList')
}

// 上传错误处理
const handleUploadError = (error: any) => {
  ElMessage.error('音频上传失败: ' + (error.message || '未知错误'))
}

// 监听上传成功事件
const handleMediaUploaded = (event: Event) => {
  const customEvent = event as CustomEvent
  const { category } = customEvent.detail
  if (category === 'audio') {
    emit('refreshMediaList')
  }
}

// props
const props = defineProps<{
  mediaResponse?: any,
  mediaList?: Material[],
  currentPage?: number,
  pageSize?: number,
  total?: number,
  loading?: boolean
}>()

onMounted(() => {
  window.addEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})

onUnmounted(() => {
  window.removeEventListener('mediaUploaded', handleMediaUploaded as EventListener)
  closePlayer()
})
</script>

<style lang="scss" scoped>
.audio-library {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .left-actions {
    display: flex;
    gap: 10px;
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.content-wrapper {
  display: flex;
  flex: 1;
  gap: 20px;
  min-height: 0;
}

.category-panel {
  width: 280px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;

  .category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 600;
  }

  .search-box {
    margin-bottom: 15px;
  }

  .category-list {
    .category-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      cursor: pointer;
      border-radius: 6px;
      transition: all 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }

      &.active {
        background-color: #409eff;
        color: white;
      }

      .count {
        font-size: 12px;
        opacity: 0.7;
      }
    }
  }
}

.audio-panel {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 10px;
    color: #666;

    .el-icon {
      font-size: 32px;
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
  }

  .audio-table {
    flex: 1;

    .audio-title {
      display: flex;
      align-items: center;
      gap: 8px;

      .audio-icon {
        color: #409eff;
      }
    }
  }
}

.audio-player {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  min-width: 600px;
  z-index: 1000;
  border: 1px solid #e4e7ed;

  .player-info {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 140px;

    .el-icon {
      color: #409eff;
      font-size: 18px;
    }

    span {
      font-weight: 500;
      color: #303133;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 120px;
    }
  }

  .player-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;

    .el-button {
      --el-button-size: 40px;
      
      &.is-circle {
        background: #409eff;
        border-color: #409eff;
        color: white;
        
        &:hover {
          background: #337ecc;
          border-color: #337ecc;
        }
      }
    }

    .progress-container {
      flex: 1;
      margin: 0 12px;
    }

    .progress-bar {
      height: 6px;
      background: #e4e7ed;
      border-radius: 3px;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
        border-radius: 3px;
        transition: width 0.1s ease;
      }

      &:hover {
        .progress-fill {
          background: linear-gradient(90deg, #337ecc 0%, #5a9dff 100%);
        }
      }
    }

    .time {
      font-size: 14px;
      color: #606266;
      min-width: 100px;
      text-align: center;
      font-family: 'Courier New', monospace;
    }
  }

  .player-actions {
    .el-button {
      --el-button-text-color: #606266;
      --el-button-hover-text-color: #409eff;
    }
  }
}

@media (max-width: 768px) {
  .audio-player {
    min-width: 400px;
    padding: 12px 16px;
    gap: 12px;

    .player-info {
      min-width: 100px;
      
      span {
        max-width: 80px;
      }
    }

    .player-controls {
      gap: 12px;

      .progress-container {
        margin: 0 8px;
      }

      .time {
        font-size: 12px;
        min-width: 80px;
      }
    }
  }
}
</style>
