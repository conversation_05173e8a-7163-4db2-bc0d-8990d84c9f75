/**
 * @file materialUtils.ts
 * @description 媒资素材管理相关的工具函数集合
 *              包含MediaId提取、批量获取媒资信息、素材格式转换等通用功能
 *              用于简化 useVideoEditor.ts 中的长函数，提高可维护性
 */

import type { MediaPayload, ProjectMediaInfo, FileBasicInfo } from '../types/media';
import type { GetTemplateMaterialsResponse, TemplateMaterialFile } from '../types/template';
import { ElMessage } from 'element-plus';
import { cleanArray, uniqueArr } from '@/utils/index';
import { inferMediaType, safeJsonParse } from './commonUtils';

// 定义通用的媒资信息类型，用于统一不同来源的媒资数据
export type MaterialInfo = ProjectMediaInfo | MediaPayload;



/**
 * 从模板的RelatedMediaids中提取所有MediaId
 * @param relatedMediaids - 模板的RelatedMediaids字段
 * @returns 提取的MediaId数组
 */
export function extractMediaIdsFromRelatedMediaids(relatedMediaids: string | object): string[] {
  if (!relatedMediaids) return [];
  
  const mediaIds = safeJsonParse(relatedMediaids, 'RelatedMediaids');
  if (!mediaIds) return [];
  
  // 提取所有有效的MediaId，使用 cleanArray 过滤空值
  const allMediaIds = [
    ...(mediaIds.video || []),
    ...(mediaIds.audio || []),
    ...(mediaIds.image || [])
  ];
  
  // 过滤掉空值并去重
  const validIds = cleanArray(allMediaIds.filter(id => id && id.trim() !== ''));
  const uniqueIds = uniqueArr(validIds);
  
  console.log('🔍 从模板RelatedMediaids中提取的MediaId:', uniqueIds);
  return uniqueIds;
}

/**
 * 从模板Config中解析实际使用的MediaId
 * @param config - 模板配置对象或JSON字符串
 * @returns 提取的MediaId数组
 */
export function extractMediaIdsFromConfig(config: string | object): string[] {
  if (!config) return [];
  
  const parsedConfig = safeJsonParse(config, '模板Config');
  if (!parsedConfig) return [];
  
  const mediaIds = new Set<string>();
  
  // 遍历视频轨道提取MediaId
  if (parsedConfig.VideoTracks) {
    parsedConfig.VideoTracks.forEach((track: any) => {
      if (track.VideoTrackClips) {
        track.VideoTrackClips.forEach((clip: any) => {
          if (clip.MediaId && !clip.MediaId.startsWith('$')) {
            // 过滤掉以$开头的占位符MediaId
            mediaIds.add(clip.MediaId);
          }
        });
      }
    });
  }
  
  // 遍历音频轨道提取MediaId
  if (parsedConfig.AudioTracks) {
    parsedConfig.AudioTracks.forEach((track: any) => {
      if (track.AudioTrackClips) {
        track.AudioTrackClips.forEach((clip: any) => {
          if (clip.MediaId && !clip.MediaId.startsWith('$')) {
            mediaIds.add(clip.MediaId);
          }
        });
      }
    });
  }
  
  // 遍历图片轨道提取MediaId
  if (parsedConfig.ImageTracks) {
    parsedConfig.ImageTracks.forEach((track: any) => {
      if (track.ImageTrackClips) {
        track.ImageTrackClips.forEach((clip: any) => {
          if (clip.MediaId && !clip.MediaId.startsWith('$')) {
            mediaIds.add(clip.MediaId);
          }
        });
      }
    });
  }
  
  const result = Array.from(mediaIds);
  console.log('🔍 从模板Config中解析的MediaId:', result);
  return result;
}

/**
 * 批量获取媒资信息
 * @param mediaIds - MediaId数组
 * @returns 获取到的媒资信息数组
 */
export async function batchGetMediaInfo(mediaIds: string[]): Promise<MediaPayload[]> {
  if (!mediaIds || mediaIds.length === 0) {
    console.log('📭 没有有效的MediaId需要获取媒资信息');
    return [];
  }
  
  const { getMediaInfo } = await import('../api/media');
  
  const mediaPromises = mediaIds.map(mediaId => 
    getMediaInfo({ mediaId }).catch(err => {
      console.warn(`获取媒资信息失败 (MediaId: ${mediaId}):`, err);
      return null;
    })
  );
  
  const mediaResponses = await Promise.all(mediaPromises);
  
  // 过滤掉失败的请求
  const validMediaResponses = mediaResponses.filter(res => res !== null);
  const result = validMediaResponses.map(res => res.MediaInfo);
  
  console.log(`✅ 批量获取媒资信息完成，成功: ${validMediaResponses.length}/${mediaIds.length}`);
  if (validMediaResponses.length < mediaIds.length) {
    console.warn(`⚠️ 部分素材获取失败，成功: ${validMediaResponses.length}/${mediaIds.length}`);
  }
  
  return result;
}

/**
 * 将模板素材地址响应转换为标准MediaInfo格式
 * @param materialsResponse - 模板素材地址API响应
 * @returns 标准格式的MaterialInfo数组
 */
export function convertTemplateMaterialsToMediaInfo(materialsResponse: any): MaterialInfo[] {
  // 兼容两种响应格式：新的标准格式和旧的 data 格式
  let materialUrls: Record<string, string>;
  
  if (materialsResponse?.MaterialUrls) {
    // 新的标准格式：直接使用 MaterialUrls
    materialUrls = materialsResponse.MaterialUrls;
  } else if (materialsResponse?.data) {
    // 旧的格式：使用 data 字段
    materialUrls = materialsResponse.data;
  } else {
    throw new Error('模板素材地址响应格式异常：缺少 MaterialUrls 或 data 字段');
  }
  
  if (typeof materialUrls !== 'object') {
    throw new Error('素材地址数据格式异常：应为对象类型');
  }
  
  const materialsList = Object.entries(materialUrls).map(([fileName, fileUrl]) => ({
    MediaId: `template_${fileName}`, // 生成虚拟MediaId
    MediaBasicInfo: {
      MediaId: `template_${fileName}`,
      Title: fileName,
      MediaType: inferMediaType(fileName),
      CoverURL: fileUrl as string,
      Status: 'Normal'
    },
    FileInfoList: [{
      FileBasicInfo: {
        FileName: fileName,
        FileUrl: fileUrl as string,
        FileStatus: 'Normal'
      } as FileBasicInfo
    }]
  })) as MaterialInfo[];
  
  console.log(`✅ 模板素材地址转换完成，素材数量:`, materialsList.length);
  return materialsList;
}



/**
 * 解析模板素材地址响应，转换为结构化的文件信息数组
 * @param materialsResponse - 模板素材地址API响应
 * @returns 结构化的模板素材文件信息数组
 */
export function parseTemplateMaterialFiles(materialsResponse: any): TemplateMaterialFile[] {
  // 兼容两种响应格式：新的标准格式和旧的 data 格式
  let materialUrls: Record<string, string>;
  
  if (materialsResponse?.MaterialUrls) {
    // 新的标准格式：直接使用 MaterialUrls
    materialUrls = materialsResponse.MaterialUrls;
  } else if (materialsResponse?.data) {
    // 旧的格式：使用 data 字段
    materialUrls = materialsResponse.data;
  } else {
    throw new Error('模板素材地址响应格式异常：缺少 MaterialUrls 或 data 字段');
  }
  
  if (typeof materialUrls !== 'object') {
    throw new Error('素材地址数据格式异常：应为对象类型');
  }
  
  const files = Object.entries(materialUrls).map(([fileName, fileUrl]) => ({
    fileName,
    fileUrl,
    mediaType: inferMediaType(fileName) as 'video' | 'audio' | 'image'
  }));
  
  console.log(`✅ 解析模板素材文件信息完成，文件数量:`, files.length);
  return files;
}

/**
 * 创建文件列表的JSON字符串
 * @param fileNames - 文件名数组
 * @returns JSON格式的文件列表字符串
 */
export function createFileListJson(fileNames: string[]): string {
  return JSON.stringify(fileNames);
}

/**
 * 验证文件列表JSON字符串的格式
 * @param fileListJson - JSON格式的文件列表字符串
 * @returns 验证结果和解析后的文件名数组
 */
export function validateFileListJson(fileListJson: string): { 
  valid: boolean; 
  fileNames?: string[]; 
  error?: string; 
} {
  try {
    const parsed = JSON.parse(fileListJson);
    
    if (!Array.isArray(parsed)) {
      return { valid: false, error: '文件列表必须是数组格式' };
    }
    
    if (!parsed.every(item => typeof item === 'string')) {
      return { valid: false, error: '文件列表中的每个项目必须是字符串' };
    }
    
    if (parsed.length > 400) {
      return { valid: false, error: '文件列表最多支持400个文件' };
    }
    
    return { valid: true, fileNames: parsed };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    return { valid: false, error: `JSON解析失败: ${errorMessage}` };
  }
}

/**
 * 根据媒体类型过滤素材数组
 * @param materials - 素材数组
 * @param mediaType - 要过滤的媒体类型
 * @returns 过滤后的素材数组
 */
export function filterMaterialsByType(materials: any[], mediaType: 'video' | 'audio' | 'image'): any[] {
  return materials.filter(media => {
    const type = media.MediaBasicInfo?.MediaType?.toLowerCase();
    return type === mediaType;
  });
}

/**
 * 批量处理素材加载错误
 * @param error - 错误对象
 * @param context - 错误上下文
 * @returns 格式化的错误信息
 */
export function handleMaterialLoadError(error: unknown, context: string = '素材加载'): string {
  if (error instanceof Error) {
    return `${context}失败: ${error.message}`;
  }
  return `${context}失败`;
}
