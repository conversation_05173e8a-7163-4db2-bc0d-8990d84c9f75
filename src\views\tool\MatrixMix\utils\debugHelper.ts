/**
 * @file debugHelper.ts
 * @description 调试助手工具，用于检查数据状态和问题排查
 */

/**
 * 检查Timeline数据结构
 */
export function debugTimelineData(timeline: any) {
  console.group('🔍 Timeline数据调试');
  
  if (!timeline) {
    console.error('❌ Timeline数据为空或未定义');
    console.groupEnd();
    return false;
  }
  
  console.log('✅ Timeline基本信息:', {
    hasVideoTracks: !!timeline.VideoTracks,
    hasAudioTracks: !!timeline.AudioTracks,
    hasSubtitleTracks: !!timeline.SubtitleTracks,
    videoTracksCount: timeline.VideoTracks?.length || 0,
    audioTracksCount: timeline.AudioTracks?.length || 0,
    subtitleTracksCount: timeline.SubtitleTracks?.length || 0
  });
  
  // 检查视频轨道
  if (timeline.VideoTracks && timeline.VideoTracks.length > 0) {
    timeline.VideoTracks.forEach((track: any, index: number) => {
      console.log(`📹 视频轨道 ${index}:`, {
        hasClips: !!track.VideoTrackClips,
        clipsCount: track.VideoTrackClips?.length || 0,
        clips: track.VideoTrackClips?.map((clip: any) => ({
          title: clip.Title || clip.FileName || clip.MediaId,
          timelineIn: clip.TimelineIn,
          timelineOut: clip.TimelineOut,
          duration: clip.TimelineOut - clip.TimelineIn
        }))
      });
    });
  } else {
    console.warn('⚠️ 没有视频轨道数据');
  }
  
  // 检查音频轨道
  if (timeline.AudioTracks && timeline.AudioTracks.length > 0) {
    timeline.AudioTracks.forEach((track: any, index: number) => {
      console.log(`🎵 音频轨道 ${index}:`, {
        hasClips: !!track.AudioTrackClips,
        clipsCount: track.AudioTrackClips?.length || 0,
        clips: track.AudioTrackClips?.map((clip: any) => ({
          title: clip.Title || clip.FileName || clip.MediaId,
          timelineIn: clip.TimelineIn,
          timelineOut: clip.TimelineOut,
          duration: clip.TimelineOut - clip.TimelineIn
        }))
      });
    });
  } else {
    console.warn('⚠️ 没有音频轨道数据');
  }
  
  console.groupEnd();
  return true;
}

/**
 * 检查Store状态
 */
export function debugStoreState(store: any) {
  console.group('🏪 Store状态调试');
  
  console.log('Store基本状态:', {
    hasTimeline: !!store.timeline,
    timelineType: typeof store.timeline,
    isVideoPlaying: store.isVideoPlaying,
    currentTime: store.currentTime,
    videoDurationSeconds: store.videoDurationSeconds
  });
  
  if (store.timeline) {
    debugTimelineData(store.timeline);
  }
  
  console.groupEnd();
}

/**
 * 检查计算属性状态
 */
export function debugComputedTracks(tracks: any[], trackType: string) {
  console.group(`📊 ${trackType}轨道计算属性调试`);
  
  console.log(`${trackType}轨道数量:`, tracks.length);
  
  tracks.forEach((track, index) => {
    console.log(`轨道 ${index}:`, {
      originalIndex: track.originalIndex,
      hasClips: !!track.clips,
      clipsCount: track.clips?.length || 0,
      clips: track.clips?.map((clip: any, clipIndex: number) => ({
        index: clipIndex,
        name: clip.name,
        start: clip.start,
        duration: clip.duration
      })),
      isPreviewTrack: track.isPreviewTrack,
      isHighlighted: track.isHighlighted
    });
  });
  
  console.groupEnd();
}

/**
 * 全面检查Timeline组件状态
 */
export function debugTimelineComponent(componentInstance: any) {
  console.group('🎬 Timeline组件全面调试');
  
  // 检查Store状态
  debugStoreState(componentInstance.videoEditorStore);
  
  // 检查计算属性
  if (componentInstance.allVideoTracks) {
    debugComputedTracks(componentInstance.allVideoTracks, '视频');
  }
  
  if (componentInstance.allAudioTracks) {
    debugComputedTracks(componentInstance.allAudioTracks, '音频');
  }
  
  if (componentInstance.allSubtitleTracks) {
    debugComputedTracks(componentInstance.allSubtitleTracks, '字幕');
  }
  
  console.groupEnd();
}

// 将调试函数暴露到全局，方便在控制台中使用
if (typeof window !== 'undefined') {
  (window as any).debugTimeline = {
    debugTimelineData,
    debugStoreState,
    debugComputedTracks,
    debugTimelineComponent
  };
  
  console.log('🔧 调试工具已挂载到 window.debugTimeline');
}
