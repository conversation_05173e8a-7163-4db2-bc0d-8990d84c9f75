<template>
  <div class="basic-config-panel">
    <!-- 任务信息配置 -->
    <el-card class="config-card" shadow="never">
      <template #header>
        <div class="card-header">
          <i class="el-icon-document"></i>
          <span>任务信息</span>
        </div>
      </template>
      
      <el-form :model="taskInfo" label-width="100px" size="default">
        <el-form-item label="任务名称" required>
          <el-input 
            v-model="taskInfo.taskName"
            placeholder="请输入任务名称"
            maxlength="50"
            show-word-limit
            @input="handleTaskInfoChange"
          />
        </el-form-item>
        
        <el-form-item label="任务描述">
          <el-input 
            v-model="taskInfo.taskDescription"
            type="textarea"
            placeholder="请输入任务描述（可选）"
            :rows="3"
            maxlength="200"
            show-word-limit
            @input="handleTaskInfoChange"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 文本配置 -->
    <el-card class="config-card" shadow="never">
      <template #header>
        <div class="card-header">
          <i class="el-icon-edit"></i>
          <span>文本配置</span>
        </div>
      </template>
      
      <el-form :model="textConfig" label-width="100px" size="default">
        <!-- 标题配置 -->
        <el-form-item label="视频标题">
          <div class="text-list-container">
            <div 
              v-for="(title, index) in textConfig.titles" 
              :key="index"
              class="text-item"
            >
              <el-input 
                v-model="textConfig.titles[index]"
                placeholder="请输入视频标题"
                maxlength="50"
                @input="handleTextConfigChange"
              />
              <el-button 
                type="danger" 
                size="small" 
                icon="el-icon-delete"
                @click="removeTitle(index)"
                :disabled="textConfig.titles.length <= 1"
              />
            </div>
            <el-button 
              type="primary" 
              size="small" 
              icon="el-icon-plus"
              @click="addTitle"
              :disabled="textConfig.titles.length >= 10"
            >
              添加标题
            </el-button>
          </div>
        </el-form-item>

        <!-- 旁白配置 -->
        <el-form-item label="旁白文案">
          <div class="text-list-container">
            <div 
              v-for="(speech, index) in textConfig.speechTexts" 
              :key="index"
              class="text-item"
            >
              <el-input 
                v-model="textConfig.speechTexts[index]"
                type="textarea"
                placeholder="请输入旁白文案"
                :rows="2"
                maxlength="500"
                show-word-limit
                @input="handleTextConfigChange"
              />
              <el-button 
                type="danger" 
                size="small" 
                icon="el-icon-delete"
                @click="removeSpeech(index)"
                :disabled="textConfig.speechTexts.length <= 1"
              />
            </div>
            <el-button 
              type="primary" 
              size="small" 
              icon="el-icon-plus"
              @click="addSpeech"
              :disabled="textConfig.speechTexts.length >= 10"
            >
              添加旁白
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import type { TaskInfo, TextConfig } from '../../../types/batchProducing';

// Props定义
interface Props {
  taskInfo: TaskInfo;
  textConfig: TextConfig;
}

// Emits定义
interface Emits {
  (e: 'update:taskInfo', value: TaskInfo): void;
  (e: 'update:textConfig', value: TextConfig): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 任务信息变更处理
const handleTaskInfoChange = () => {
  emit('update:taskInfo', { ...props.taskInfo });
};

// 文本配置变更处理
const handleTextConfigChange = () => {
  emit('update:textConfig', { ...props.textConfig });
};

// 标题管理
const addTitle = () => {
  const newTitles = [...props.textConfig.titles, ''];
  emit('update:textConfig', { ...props.textConfig, titles: newTitles });
};

const removeTitle = (index: number) => {
  const newTitles = props.textConfig.titles.filter((_, i) => i !== index);
  emit('update:textConfig', { ...props.textConfig, titles: newTitles });
};

// 旁白管理
const addSpeech = () => {
  const newSpeeches = [...props.textConfig.speechTexts, ''];
  emit('update:textConfig', { ...props.textConfig, speechTexts: newSpeeches });
};

const removeSpeech = (index: number) => {
  const newSpeeches = props.textConfig.speechTexts.filter((_, i) => i !== index);
  emit('update:textConfig', { ...props.textConfig, speechTexts: newSpeeches });
};
</script>

<style scoped>
.basic-config-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.text-list-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.text-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.text-item .el-input,
.text-item .el-textarea {
  flex: 1;
}

.text-item .el-button {
  margin-top: 4px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-card__header) {
  background-color: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}
</style>
