<template>
  <div class="statistics-container">
    <div class="search-card">
      <div class="card-header">
        <i class="el-icon-search"></i>
        <span>查询条件</span>
      </div>
      <el-form :model="queryParams" label-width="90px" v-show="showSearch" :inline="true" class="form-content">
        <el-form-item label="计量单位:">
          <el-select v-model="queryParams.timeUnit" placeholder="请选择计量单位" style="width: 150px;" @change="handleChangeTimeUnit">
            <el-option label="日" value="day"></el-option>
            <el-option label="月" value="month"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围:">
          <el-date-picker v-model="queryParams.dateRange" :type="datePickerType" range-separator="-" start-placeholder="开始日期" 
            end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="debouncedHandleQuery" style="width: 300px;"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="switchChart" class="query-btn">切换图表</el-button>
        </el-form-item>
      </el-form>
    </div>
   
    <div class="data-card">
      <div class="card-header">
        <i class="el-icon-data-line"></i>
        <span>{{ chartType === 'hashrate' ? '算力花费统计' : '剩余算力点趋势' }}</span>
      </div>
      <div class="chart-content" ref="chartContentRef">
        <template v-if="hasData">
          <div ref="chartContainer" class="chart-container"></div>
        </template>
        <template v-else>
          <div class="empty-data">暂无数据</div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup name="Statistics">
import { ref, onMounted, watch, computed, nextTick, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { fetchConsumptionData, processHashrateForChart, processResidueForChart } from '@/utils/consumptionService';
import { debounce } from '@/utils/index';

const props = defineProps({
  userData: Array,
  userName: String
});

const showSearch = ref(true);
const consumptionList = ref([]);
const hasData = ref(false);
const chartContentRef = ref(null);
const chartContainer = ref(null);
let myChart = null;

const data = reactive({
  queryParams: {
    dateRange: null,
    timeUnit: 'day'
  }
});
const { queryParams } = toRefs(data);
const chartType = ref('hashrate');
const datePickerType = computed(() => queryParams.value.timeUnit === 'day' ? 'daterange' : 'monthrange');

async function fetchAndSetConsumption(userName) {
  try {
    destroyChart();
    // 构建查询参数
    const params = { ...queryParams.value, userName };
    
    // 确保日期范围正确传递
    if (params.dateRange && params.dateRange.length === 2) {
      params.startDate = params.dateRange[0];
      params.endDate = params.dateRange[1];
    }
    
    const response = await fetchConsumptionData(params);
    // 处理数据为空的情况
    if (!response || response.length === 0) {
      if (queryParams.value.dateRange) {
        console.log("指定日期范围内无数据，尝试获取所有历史数据");
        
        // 清除日期限制，获取所有数据
        const allHistoryParams = { ...params };
        delete allHistoryParams.dateRange;
        delete allHistoryParams.createTime;
        delete allHistoryParams.updateTime;
        
        const historyResponse = await fetchConsumptionData(allHistoryParams);
        if (historyResponse && historyResponse.length > 0) {
          const historyData = historyResponse.filter(item => item.consumptionStaus === "1");
          
          if (historyData.length > 0) {
            // 显示提示信息
            ElMessage({
              message: `当前时间段无消费记录，正在显示历史数据（${historyData.length}条）`,
              type: 'info',
              duration: 5000
            });
            
            consumptionList.value = historyData;
            hasData.value = true;
            nextTick(() => {
              createAndUpdateChart();
            });
            return;
          }
        }
      }
      hasData.value = false;
      return;
    }
    
    consumptionList.value = response.filter(item => item.consumptionStaus === "1");
    hasData.value = consumptionList.value && consumptionList.value.length > 0;
    
    if (hasData.value) {
      nextTick(() => {
        createAndUpdateChart();
      });
    }
  } catch (error) {
    console.error('获取消费数据失败:', error);
    hasData.value = false;
    destroyChart();
  }
}

function destroyChart() {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
}

// 修改图表相关的样式和配置
// 修改createChartOption函数，添加标题指示只显示最近20天/月数据
function createChartOption(keys, values, chartType) {
  const titleText = chartType === 'hashrate' ? '算力花费情况' : '剩余算力点下降趋势';
  const seriesName = chartType === 'hashrate' ? '花费算力点' : '剩余算力点';
  const yAxisName = chartType === 'hashrate' ? '算力点花费' : '剩余算力点';
  
  // 检测数据是否跨年
  let years = new Set();
  if (keys && keys.length > 0) {
    keys.forEach(key => {
      const year = key.substring(0, 4);
      years.add(year);
    });
  }
  
  // 构建标题，如果跨年则显示年份范围
  let titleYear = '';
  if (years.size > 1) {
    const yearArray = Array.from(years).sort();
    titleYear = `${yearArray[0]}-${yearArray[yearArray.length-1]}年`;
  } else if (years.size === 1) {
    titleYear = `${Array.from(years)[0]}年`;
  } else {
    titleYear = `${new Date().getFullYear()}年`;
  }

  // 增加副标题，指示数据范围
  const subTitleText = `最近${20}${queryParams.value.timeUnit === 'day' ? '天' : '个月'}数据`;
  
  // 优化X轴标签格式
  const formattedLabels = keys.map(key => {
    if (queryParams.value.timeUnit === 'day') {
      // 对日期格式化：显示月-日，如"03-15"
      const parts = key.split('-');
      if (parts.length === 3) {
        // 年份只在第一个和跨年点显示
        return `${parts[1]}-${parts[2]}`;
      }
      return key;
    } else {
      return key;
    }
  });

  return {
    title: {
      text: `${titleYear} ${(queryParams.value.timeUnit === 'day' ? '每日' : '每月')}${titleText}`,
      subtext: subTitleText,
      left: 'center',
      top: 10,
      textStyle: { fontSize: 18, fontWeight: 'bold', color: '#333' },
      subtextStyle: { fontSize: 12, color: '#666' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        // 找到对应的原始日期（带年份）
        const index = params[0].dataIndex;
        const originalDate = keys[index];
        const value = params[0].value.toFixed(2);
        
        // 根据是日期还是月份模式返回不同格式
        if (queryParams.value.timeUnit === 'day') {
          return `${originalDate}<br>${params[0].seriesName}: ${value}`;
        } else {
          return `${originalDate}<br>${params[0].seriesName}: ${value}`;
        }
      },
      textStyle: { fontSize: 14 },
      padding: 10,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      extraCssText: 'box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);'
    },
    grid: {
      left: '5%', right: '5%', bottom: '15%', top: '15%', containLabel: true},
    xAxis: {
      type: 'category', boundaryGap: false, data: formattedLabels,
      axisLabel: { 
        rotate: 0, interval: 0, margin: 12, fontSize: 12, color: '#666', align: 'center',},
      axisLine: {
        lineStyle: { color: '#ddd', width: 1}
      },
      splitLine: {
        show: true, 
        lineStyle: { color: '#eee', type: 'dashed', width: 1}
      },
      // 添加在跨年点增加强调线
      axisTick: {
        show: true, alignWithLabel: true,
        lineStyle: { color: '#ddd' }
      }
    },
    yAxis: { 
      type: 'value', 
      name: yAxisName,
      nameTextStyle: {
        padding: [0, 0, 0, 40], fontSize: 14, color: '#666'},
      axisLabel: {
        fontSize: 12, color: '#666', formatter: value => value.toFixed(1)
      },
      axisLine: {
        show: true, // 显示Y轴线
        lineStyle: { color: '#ddd', width: 1}
      },
      splitLine: {
        show: true, // 显示水平网格线
        lineStyle: { color: '#eee', type: 'dashed', width: 1}
      }
    },
    series: [{ 
      name: seriesName, 
      type: 'line', 
      data: values, 
      smooth: true,
      symbolSize: 6,
      symbol: 'circle', // 使用圆形数据点
      showSymbol: true, // 显示所有数据点
      lineStyle: {
        width: 3, color: chartType === 'hashrate' ? '#1890ff' : '#52c41a'},
      itemStyle: {
        color: chartType === 'hashrate' ? '#1890ff' : '#52c41a', borderColor: '#fff', borderWidth: 1,
        shadowColor: 'rgba(0, 0, 0, 0.1)', shadowBlur: 3 },
      areaStyle: {
        color: {
          type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [{
            offset: 0,  color: chartType === 'hashrate' ? 'rgba(24, 144, 255, 0.3)' : 'rgba(82, 196, 26, 0.3)'
          }, {
            offset: 1,  color: chartType === 'hashrate' ? 'rgba(24, 144, 255, 0.05)' : 'rgba(82, 196, 26, 0.05)'
          }]
        }
      },
      markLine: years.size > 1 ? {
        silent: true,
        symbol: 'none',
        lineStyle: { color: '#ccc', type: 'dashed' },
        data: []
      } : undefined
    }]
  };
}

// 额外添加一个辅助函数，用于标记图表中的跨年点
function createAndUpdateChart() {
  if (!hasData.value) return;
  if (!chartContainer.value) return;
  
  myChart = echarts.init(chartContainer.value);
  
  let processedData;
  if (chartType.value === 'hashrate') {
    processedData = processHashrateForChart(consumptionList.value, queryParams.value.timeUnit);
  } else {
    processedData = processResidueForChart(consumptionList.value, queryParams.value.timeUnit);
  }
  
  // 检查处理后的数据是否为空
  if (!processedData.keys || processedData.keys.length === 0) {
    hasData.value = false;
    destroyChart();
    return;
  }
  
  const option = createChartOption(processedData.keys, processedData.values, chartType.value);
  
  // 为跨年点添加标记线
  if (processedData.keys.length > 1) {
    let yearChanges = [];
    let prevYear = processedData.keys[0].substring(0, 4);
    
    for (let i = 1; i < processedData.keys.length; i++) {
      const currentYear = processedData.keys[i].substring(0, 4);
      if (currentYear !== prevYear) {
        yearChanges.push({
          xAxis: i,
          lineStyle: { color: '#ff9800', type: 'dashed', width: 1},
          label: { formatter: `${currentYear}年`, position: 'start', color: '#ff9800', fontSize: 10 }
        });
        prevYear = currentYear;
      }
    }
    
    if (yearChanges.length > 0 && option.series[0].markLine) {
      option.series[0].markLine.data = yearChanges;
    }
  }
  
  myChart.setOption(option);
  window.addEventListener('resize', handleResize);
}

function handleResize() {
  if (myChart) {
    myChart.resize();
  }
}

function handleChangeTimeUnit() {
  queryParams.value.dateRange = null;
  debouncedHandleQuery();
}

// 添加处理日期范围的提示信息
function handleQuery() {
  const { dateRange } = queryParams.value;
  if (dateRange && dateRange.length === 2) {
    // 设置开始日期
    queryParams.value.createTime = dateRange[0];
    
    // 处理结束日期，确保包含结束日期（或月份）的所有时间
    let endDate = dateRange[1];
    
    if (queryParams.value.timeUnit === 'day') {
      // 日期模式：添加一天到结束日期
      let endDateObj = new Date(endDate);
      endDateObj.setDate(endDateObj.getDate() + 1);
      let nextDay = endDateObj.toISOString().split('T')[0];
      queryParams.value.updateTime = nextDay;
      
      // 计算日期范围跨度
      const startDate = new Date(dateRange[0]);
      const dayDiff = Math.floor((endDateObj - startDate) / (24 * 60 * 60 * 1000));
      
      // 如果日期范围超过20天，显示提示信息
      if (dayDiff > 20) {
        ElMessage({
          message: `选择的日期范围跨度为${dayDiff}天，图表将只显示最近20天数据`,
          type: 'warning',
          duration: 5000
        });
      }
    } else {
      // 月份模式处理，同上
      let [yearStart, monthStart] = dateRange[0].split('-').map(Number);
      let [yearEnd, monthEnd] = dateRange[1].split('-').map(Number);
      
      let monthDiff = (yearEnd - yearStart) * 12 + (monthEnd - monthStart);
      
      if (monthDiff > 20) {
        ElMessage({
          message: `选择的日期范围跨度为${monthDiff}个月，图表将只显示最近20个月数据`,
          type: 'warning',
          duration: 5000
        });
      }
      
      // 月份模式：找到结束月份的下个月第一天
      let [year, month] = endDate.split('-').map(Number);
      let nextMonth = month === 12 ? 1 : month + 1;
      let nextMonthYear = month === 12 ? year + 1 : year;
      let nextMonthFirstDay = `${nextMonthYear}-${String(nextMonth).padStart(2, '0')}-01`;
      queryParams.value.updateTime = nextMonthFirstDay;
    }
  } else {
    queryParams.value.createTime = null;
    queryParams.value.updateTime = null;
  }
  
  fetchAndSetConsumption(props.userName);
}

const debouncedHandleQuery = debounce(handleQuery, 100, false);

function switchChart() {
  chartType.value = chartType.value === 'hashrate' ? 'residue' : 'hashrate';
  
  if (hasData.value) {
    destroyChart();
    nextTick(() => {
      createAndUpdateChart();
    });
  }
}

// 移除默认日期范围设置，只保留基础初始化
onMounted(() => {
  fetchAndSetConsumption(props.userName);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  destroyChart();
});

watch([() => queryParams.value.dateRange, () => queryParams.value.timeUnit], ([newDateRange, newTimeUnit]) => {
  if ((Array.isArray(newDateRange) && newDateRange.length === 2) || newTimeUnit) {
    debouncedHandleQuery();
  }
}, { deep: true });

watch(() => props.userData, (newUserData) => {
  if (newUserData) {
    consumptionList.value = newUserData;
    nextTick(() => {
      fetchAndSetConsumption(props.userName);
    });
  } else {
    consumptionList.value = [];
    hasData.value = false;
    destroyChart();
  }
}, { immediate: true });

</script>

<style scoped>
.statistics-container {
  padding: 15px;
  background-color: #f0f2f5;
}

.search-card, .data-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 15px;
}

.card-header {
  padding: 10px 15px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
  font-weight: 600;
  font-size: 15px;
  color: #333;
  display: flex;
  align-items: center;
  border-radius: 4px 4px 0 0;
}

.card-header i {
  margin-right: 8px;
  color: #1890ff;
}

.form-content {
  padding: 15px 15px 5px;
}

.chart-content {
  padding: 20px 10px;
  position: relative;
  min-height: 450px; /* // 进一步增大图表容器高度 */
}

.chart-container {
  width: 100%;
  height: 420px; /* // 增大图表高度 */
  background-color: #fff;
  border-radius: 4px;
  box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.05);
}

.query-btn {
  background-color: #1890ff;
  border-color: #1890ff;
}

.query-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

:deep(.el-form-item__label) {
  font-weight: 400;
}

:deep(.el-select .el-input__inner),
:deep(.el-date-editor .el-input__inner) {
  border-radius: 3px;
}

.no-data-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  z-index: 10; /* 确保显示在图表上方 */
}

.no-data-text {
  font-size: 16px;
  color: #909399;
}

.empty-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
  color: #909399;
}
</style>