/**
 * @file playerControlUtils.ts
 * @description 播放器控制相关的工具类，用于管理视频播放器的状态和控制逻辑
 *              这个文件包含了播放器实例管理、状态格式化、交互控制等核心功能
 *              从主Store中抽离，提供可重用的播放器控制能力
 * <AUTHOR>
 * @date 2024
 */

import { formatTime, formatDisplayTime } from './timeUtils';

/**
 * TimelinePlayer 组件实例接口定义
 * 
 * 这个接口定义了时间轴播放器组件必须实现的基本方法。
 * 它确保了播放器控制类能够与具体的播放器组件进行交互。
 */
export interface TimelinePlayerInstance {
  /** 开始播放视频 */
  play: () => void;
  /** 暂停视频播放 */
  pause: () => void;
  /** 
   * 跳转到指定时间点
   * @param time 目标时间，单位为秒
   */
  seek: (time: number) => void;
}

/**
 * 播放器控制类
 * 
 * 这个类封装了对视频播放器的基本控制操作，包括播放、暂停、跳转等功能。
 * 它作为播放器实例的包装器，提供了更高级的控制接口和状态管理能力。
 * 
 * 主要特性：
 * - 播放器实例的安全管理
 * - 播放前时间记录（用于毫秒冻结显示）
 * - 统一的错误处理和状态检查
 * 
 * @example
 * ```typescript
 * const controller = new PlayerController();
 * controller.setPlayerRef(playerInstance);
 * const lastTime = controller.play(currentTime);
 * controller.seek(10.5);
 * ```
 */
export class PlayerController {
  /** 播放器实例引用，初始为null */
  private playerInstance: TimelinePlayerInstance | null = null;

  /**
   * 设置播放器实例引用
   * 
   * 这个方法用于绑定具体的播放器组件实例。
   * 通常在组件挂载完成后调用，建立控制器与播放器的连接。
   * 
   * @param player 播放器实例，可以为null（用于清除引用）
   */
  setPlayerRef(player: TimelinePlayerInstance | null): void {
    this.playerInstance = player;
  }

  /**
   * 播放视频
   * 
   * 启动视频播放，并返回播放前的时间点。
   * 返回的时间值主要用于实现毫秒数冻结显示的视觉效果。
   * 
   * @param currentTime 当前播放时间（用于记录播放前的时间）
   * @returns 记录的时间值，用于毫秒冻结显示
   * 
   * @example
   * ```typescript
   * const lastTime = controller.play(5.234);
   * // lastTime = 5.234，用于formatDisplayTime函数
   * ```
   */
  play(currentTime: number): number {
    // 播放前记录当前时间，用于 formatDisplayTime 实现毫秒冻结显示
    const lastCurrentTime = currentTime;
    this.playerInstance?.play();
    return lastCurrentTime;
  }

  /**
   * 暂停视频
   * 
   * 暂停当前正在播放的视频。
   * 这个方法是安全的，即使播放器实例不存在也不会报错。
   */
  pause(): void {
    this.playerInstance?.pause();
  }

  /**
   * 跳转到指定时间
   * 
   * 将播放器的播放位置跳转到指定的时间点。
   * 支持精确到秒的时间定位。
   * 
   * @param time 目标时间（秒），支持小数
   * 
   * @example
   * ```typescript
   * controller.seek(10.5);  // 跳转到10.5秒
   * controller.seek(0);     // 跳转到开头
   * ```
   */
  seek(time: number): void {
    this.playerInstance?.seek(time);
  }

  /**
   * 重置播放器到开头
   * 
   * 这是seek(0)的便捷方法，将播放位置重置到视频开始。
   * 常用于重播功能的实现。
   */
  reset(): void {
    this.seek(0);
  }

  /**
   * 检查播放器实例是否可用
   * 
   * 这个方法用于验证播放器是否已正确初始化并可以使用。
   * 在执行播放器操作前可以先检查状态。
   * 
   * @returns 播放器是否可用
   * 
   * @example
   * ```typescript
   * if (controller.isPlayerReady()) {
   *   controller.play(currentTime);
   * }
   * ```
   */
  isPlayerReady(): boolean {
    return this.playerInstance !== null;
  }
}

/**
 * 播放器状态管理工具类
 * 
 * 这个类提供了与播放器状态相关的静态工具方法，包括时间格式化、
 * 状态验证、进度计算等功能。所有方法都是静态的，可以直接调用。
 * 
 * 主要功能：
 * - 时间格式化（支持毫秒冻结显示）
 * - 播放器状态验证
 * - 播放进度计算
 * - 剩余时间计算
 * 
 * @example
 * ```typescript
 * const formatted = PlayerStateManager.formatCurrentTime(5.234, 5.200, true);
 * const progress = PlayerStateManager.calculateProgress(30, 100); // 30%
 * ```
 */
export class PlayerStateManager {
  /**
   * 格式化视频总时长
   * 
   * 将秒数格式化为可读的时间字符串，通常用于显示视频的总长度。
   * 格式为 "MM:SS.mmm" 或 "HH:MM:SS.mmm"（超过1小时时）。
   * 
   * @param durationSeconds 视频总时长（秒）
   * @returns 格式化后的时长字符串 (例如 "02:19.007")
   * 
   * @example
   * ```typescript
   * PlayerStateManager.formatVideoDuration(139.007);
   * // 返回: "02:19.007"
   * ```
   */
  static formatVideoDuration(durationSeconds: number): string {
    return formatTime(durationSeconds);
  }

  /**
   * 格式化当前播放时间，支持毫秒冻结显示
   * 
   * 这个方法的特殊之处在于支持"毫秒冻结"效果：
   * 当视频正在播放时，毫秒部分会保持在播放开始时的值，
   * 避免快速跳动的数字影响用户体验。
   * 
   * @param currentTime 当前播放时间（秒）
   * @param lastCurrentTime 上一帧的播放时间（秒，用于冻结效果）
   * @param isPlaying 是否正在播放
   * @returns 格式化后的时间字符串 (例如 "00:16.597")
   * 
   * @example
   * ```typescript
   * // 播放时，毫秒部分保持不变
   * PlayerStateManager.formatCurrentTime(16.623, 16.597, true);
   * // 返回: "00:16.597" (使用lastCurrentTime的毫秒部分)
   * 
   * // 暂停时，显示实际时间
   * PlayerStateManager.formatCurrentTime(16.623, 16.597, false);
   * // 返回: "00:16.623" (显示实际的currentTime)
   * ```
   */
  static formatCurrentTime(
    currentTime: number,
    lastCurrentTime: number,
    isPlaying: boolean
  ): string {
    return formatDisplayTime(currentTime, lastCurrentTime, isPlaying);
  }

  /**
   * 检查播放器状态是否有效
   * 
   * 对播放器的关键状态进行验证，识别可能的异常情况。
   * 用于调试和错误排查，确保播放器状态的合理性。
   * 
   * @param isPlaying 播放状态
   * @param currentTime 当前时间
   * @param duration 总时长
   * @returns 状态检查结果，包含有效性标志和警告信息
   * 
   * @example
   * ```typescript
   * const result = PlayerStateManager.validatePlayerState(true, -5, 100);
   * console.log(result.isValid);   // false
   * console.log(result.warnings);  // ["当前播放时间不能为负数"]
   * ```
   */
  static validatePlayerState(
    isPlaying: boolean,
    currentTime: number,
    duration: number
  ): {
    isValid: boolean;
    warnings: string[];
  } {
    const warnings: string[] = [];

    // 检查时间范围的合理性
    if (currentTime < 0) {
      warnings.push('当前播放时间不能为负数');
    }

    if (duration > 0 && currentTime > duration) {
      warnings.push('当前播放时间超过视频总时长');
    }

    // 检查时长的有效性
    if (duration < 0) {
      warnings.push('视频总时长不能为负数');
    }

    return {
      isValid: warnings.length === 0,
      warnings
    };
  }

  /**
   * 计算播放进度百分比
   * 
   * 根据当前播放时间和总时长计算播放进度，
   * 返回0-100之间的百分比值，用于进度条显示。
   * 
   * @param currentTime 当前播放时间（秒）
   * @param duration 视频总时长（秒）
   * @returns 播放进度百分比 (0-100)
   * 
   * @example
   * ```typescript
   * PlayerStateManager.calculateProgress(30, 100);  // 返回: 30
   * PlayerStateManager.calculateProgress(0, 100);   // 返回: 0
   * PlayerStateManager.calculateProgress(150, 100); // 返回: 100 (自动限制)
   * ```
   */
  static calculateProgress(currentTime: number, duration: number): number {
    if (duration <= 0) return 0;
    return Math.min(100, Math.max(0, (currentTime / duration) * 100));
  }

  /**
   * 计算剩余播放时间
   * 
   * 根据当前播放时间和总时长计算剩余的播放时间。
   * 用于显示"还剩XX分钟"等信息。
   * 
   * @param currentTime 当前播放时间（秒）
   * @param duration 视频总时长（秒）
   * @returns 剩余时间（秒）
   * 
   * @example
   * ```typescript
   * PlayerStateManager.calculateRemainingTime(30, 100);  // 返回: 70
   * PlayerStateManager.calculateRemainingTime(90, 100);  // 返回: 10
   * PlayerStateManager.calculateRemainingTime(120, 100); // 返回: 0 (已播完)
   * ```
   */
  static calculateRemainingTime(currentTime: number, duration: number): number {
    if (duration <= 0) return 0;
    return Math.max(0, duration - currentTime);
  }
}

/**
 * 播放器交互控制工具类
 * 
 * 这个类提供更高级的播放器交互控制功能，专门处理用户交互场景。
 * 它基于基础的PlayerController，添加了智能的交互逻辑，
 * 比如自动暂停、安全跳转等功能。
 * 
 * 主要特性：
 * - 智能的播放/暂停切换
 * - 时间轴交互时的自动暂停
 * - 安全的跳转操作（带暂停保护）
 * - 可配置的交互行为
 * 
 * @example
 * ```typescript
 * const interaction = new PlayerInteractionController(controller, true);
 * interaction.togglePlayPause(isPlaying, currentTime);
 * interaction.safeSeek(30, isPlaying, true);
 * ```
 */
export class PlayerInteractionController {
  /** 基础播放器控制器实例 */
  private controller: PlayerController;
  /** 是否在交互时自动暂停 */
  private pauseOnInteraction: boolean;

  /**
   * 构造函数
   * 
   * @param controller 基础播放器控制器实例
   * @param pauseOnInteraction 是否在交互时自动暂停，默认为true
   */
  constructor(controller: PlayerController, pauseOnInteraction = true) {
    this.controller = controller;
    this.pauseOnInteraction = pauseOnInteraction;
  }

  /**
   * 切换播放/暂停状态
   * 
   * 根据当前播放状态智能地切换到相反状态。
   * 如果当前正在播放则暂停，如果暂停则开始播放。
   * 
   * @param isPlaying 当前播放状态
   * @param currentTime 当前播放时间（用于播放时记录）
   * @returns 新的 lastCurrentTime（如果开始播放），否则返回null
   * 
   * @example
   * ```typescript
   * const lastTime = interaction.togglePlayPause(false, 10.5);
   * // 如果从暂停变为播放，lastTime = 10.5
   * // 如果从播放变为暂停，lastTime = null
   * ```
   */
  togglePlayPause(isPlaying: boolean, currentTime: number): number | null {
    if (isPlaying) {
      this.controller.pause();
      return null;
    } else {
      return this.controller.play(currentTime);
    }
  }

  /**
   * 处理时间轴交互时的自动暂停
   * 
   * 当用户与时间轴进行交互（如拖拽、点击）时，
   * 根据配置决定是否自动暂停播放器。
   * 这样可以避免在调整播放位置时的混乱。
   * 
   * @param isPlaying 当前播放状态
   * 
   * @example
   * ```typescript
   * // 用户开始拖拽时间轴时调用
   * interaction.handleTimelineInteraction(isPlaying);
   * ```
   */
  handleTimelineInteraction(isPlaying: boolean): void {
    if (this.pauseOnInteraction && isPlaying) {
      this.controller.pause();
    }
  }

  /**
   * 安全跳转（带暂停检查）
   * 
   * 执行播放位置跳转，可选择在跳转前暂停播放。
   * 这是推荐的跳转方式，特别是在响应用户交互时。
   * 
   * @param targetTime 目标时间（秒）
   * @param isPlaying 当前播放状态
   * @param shouldPauseBeforeSeek 跳转前是否暂停，默认为true
   * 
   * @example
   * ```typescript
   * // 响应进度条点击，跳转到30秒并暂停
   * interaction.safeSeek(30, isPlaying, true);
   * 
   * // 快速跳转，不暂停
   * interaction.safeSeek(45, isPlaying, false);
   * ```
   */
  safeSeek(targetTime: number, isPlaying: boolean, shouldPauseBeforeSeek = true): void {
    if (shouldPauseBeforeSeek && isPlaying) {
      this.controller.pause();
    }
    this.controller.seek(targetTime);
  }

  /**
   * 设置是否在交互时自动暂停
   * 
   * 动态配置交互行为，允许在运行时改变暂停策略。
   * 
   * @param pause 是否自动暂停
   * 
   * @example
   * ```typescript
   * // 禁用自动暂停
   * interaction.setPauseOnInteraction(false);
   * 
   * // 启用自动暂停
   * interaction.setPauseOnInteraction(true);
   * ```
   */
  setPauseOnInteraction(pause: boolean): void {
    this.pauseOnInteraction = pause;
  }
}

/**
 * 创建播放器控制器实例
 * 
 * 这是一个工厂函数，用于创建新的播放器控制器实例。
 * 使用工厂函数而不是直接new的好处是可以在未来扩展创建逻辑，
 * 比如添加配置参数、依赖注入等。
 * 
 * @returns 新的播放器控制器实例
 * 
 * @example
 * ```typescript
 * const controller = createPlayerController();
 * controller.setPlayerRef(playerInstance);
 * ```
 */
export function createPlayerController(): PlayerController {
  return new PlayerController();
}

/**
 * 创建播放器交互控制器实例
 * 
 * 这个工厂函数创建一个更高级的交互控制器，
 * 它基于基础控制器提供智能的交互功能。
 * 
 * @param controller 基础播放器控制器实例
 * @param pauseOnInteraction 是否在交互时自动暂停，默认为true
 * @returns 新的播放器交互控制器实例
 * 
 * @example
 * ```typescript
 * const baseController = createPlayerController();
 * const interactionController = createPlayerInteractionController(
 *   baseController, 
 *   true  // 启用交互时自动暂停
 * );
 * ```
 */
export function createPlayerInteractionController(
  controller: PlayerController,
  pauseOnInteraction = true
): PlayerInteractionController {
  return new PlayerInteractionController(controller, pauseOnInteraction);
}
