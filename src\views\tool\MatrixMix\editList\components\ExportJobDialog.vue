<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val: boolean) => emit('update:visible', val)"
    title="导出任务状态"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="export-job-content">
      <div v-if="jobInfo" class="job-info">
        <!-- 基本信息 -->
        <div class="info-section">
          <h4 class="section-title">任务信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务ID">{{ jobInfo.JobId }}</el-descriptions-item>
            <el-descriptions-item label="工程ID">{{ jobInfo.ProjectId }}</el-descriptions-item>
            <el-descriptions-item label="导出类型">
              <el-tag :type="jobInfo.ExportType === 'BaseTimeline' ? 'primary' : 'success'">
                {{ jobInfo.ExportType === 'BaseTimeline' ? '时间线' : 'Adobe PR 工程' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="任务状态">
              <el-tag :type="getStatusType(jobInfo.Status)">
                {{ getStatusText(jobInfo.Status) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 错误信息 -->
        <div v-if="jobInfo.Status === 'Failed'" class="info-section">
          <h4 class="section-title error-title">错误信息</h4>
          <el-alert
            :title="jobInfo.Message || '导出任务失败'"
            type="error"
            :description="jobInfo.Code ? `错误代码: ${jobInfo.Code}` : ''"
            show-icon
            :closable="false"
          />
        </div>

        <!-- 导出结果 -->
        <div v-if="jobInfo.Status === 'Success' && jobInfo.ExportResult" class="info-section">
          <h4 class="section-title success-title">导出结果</h4>
          
          <!-- 时间线导出结果 -->
          <div v-if="jobInfo.ExportType === 'BaseTimeline' && jobInfo.ExportResult.Timeline">
            <el-card class="result-card">
              <template #header>
                <span>时间线配置</span>
              </template>
              <el-input
                v-model="timelineDisplay"
                type="textarea"
                :rows="8"
                readonly
                placeholder="时间线配置信息"
              />
              <div class="result-actions">
                <el-button size="small" @click="copyTimeline">复制时间线</el-button>
              </div>
            </el-card>
          </div>

          <!-- Adobe PR 工程导出结果 -->
          <div v-if="jobInfo.ExportType === 'AdobePremierePro' && jobInfo.ExportResult.ProjectUrl">
            <el-card class="result-card">
              <template #header>
                <span>工程文件下载</span>
              </template>
              <div class="download-info">
                <el-icon class="download-icon"><Download /></el-icon>
                <span>Adobe PR 工程文件已准备就绪</span>
              </div>
              <div class="result-actions">
                <el-button type="primary" @click="downloadProject">
                  <el-icon><Download /></el-icon>
                  下载工程文件
                </el-button>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 处理中状态 -->
        <div v-if="jobInfo.Status === 'Processing'" class="info-section">
          <h4 class="section-title">处理进度</h4>
          <div class="processing-info">
            <el-progress :percentage="50" :show-text="false" />
            <p>正在处理导出任务，请稍候...</p>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="jobInfo?.Status === 'Processing'" type="primary" @click="refreshStatus">
          刷新状态
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Download } from '@element-plus/icons-vue';
import { getProjectExportJob } from '../../api/videoEdit';
import type { ProjectExportJob } from '../../api/videoEdit';

interface Props {
  visible: boolean;
  jobId?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const loading = ref(false);
const jobInfo = ref<ProjectExportJob>();

const timelineDisplay = computed(() => {
  if (jobInfo.value?.ExportResult?.Timeline) {
    try {
      return JSON.stringify(JSON.parse(jobInfo.value.ExportResult.Timeline), null, 2);
    } catch {
      return jobInfo.value.ExportResult.Timeline;
    }
  }
  return '';
});

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.jobId) {
    fetchJobInfo();
  }
});

const fetchJobInfo = async () => {
  if (!props.jobId) return;
  
  loading.value = true;
  try {
    const response = await getProjectExportJob(props.jobId);
    if (response.code === 200) {
      jobInfo.value = response.data.ProjectExportJob;
    } else {
      ElMessage.error(`获取任务信息失败: ${response.msg}`);
    }
  } catch (error) {
    console.error('获取导出任务信息失败:', error);
    ElMessage.error('获取任务信息失败');
  } finally {
    loading.value = false;
  }
};

const getStatusType = (status: string) => {
  switch (status) {
    case 'Success': return 'success';
    case 'Failed': return 'danger';
    case 'Processing': return 'warning';
    case 'Init': return 'info';
    default: return 'info';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'Success': return '成功';
    case 'Failed': return '失败';
    case 'Processing': return '处理中';
    case 'Init': return '初始化';
    default: return status;
  }
};

const copyTimeline = async () => {
  try {
    await navigator.clipboard.writeText(timelineDisplay.value);
    ElMessage.success('时间线配置已复制到剪贴板');
  } catch {
    ElMessage.error('复制失败，请手动复制');
  }
};

const downloadProject = () => {
  if (jobInfo.value?.ExportResult?.ProjectUrl) {
    window.open(jobInfo.value.ExportResult.ProjectUrl, '_blank');
  }
};

const refreshStatus = () => {
  fetchJobInfo();
};

const handleClose = () => {
  emit('update:visible', false);
  jobInfo.value = undefined;
};
</script>

<style lang="scss" scoped>
.export-job-content {
  min-height: 200px;
}

.job-info {
  .info-section {
    margin-bottom: 24px;
    
    .section-title {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      
      &.error-title {
        color: #f56c6c;
      }
      
      &.success-title {
        color: #67c23a;
      }
    }
  }
}

.result-card {
  .result-actions {
    margin-top: 16px;
    text-align: right;
  }
}

.download-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  
  .download-icon {
    font-size: 20px;
    color: #409eff;
  }
}

.processing-info {
  text-align: center;
  padding: 24px;
  
  p {
    margin-top: 16px;
    color: #909399;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
