<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { VideoPlay, VideoPause, DArrowLeft, DArrowRight } from '@element-plus/icons-vue'
import useAppStore from '@/store/modules/app'

// 定义props
const props = defineProps({
  url: {
    type: String,
    required: true
  },
  conversations: {
    type: Array,
    default: () => []
  }
})

// 定义emits
const emit = defineEmits(['timeUpdate'])

// 获取应用状态
const appStore = useAppStore()

// 音频元素引用
const audioRef = ref(null)

// 内部状态管理
const isPlaying = ref(false)
const currentTime = ref(0)
const totalTime = ref(0) // 总时长（秒）
const playbackSpeed = ref('1.0x') // 播放倍速显示
const isLoading = ref(false)
const isError = ref(false)

// 高频时间更新定时器
let timeUpdateTimer = null

// 章节速览相关状态
const showChapterPreview = ref(false)
const chapterPreviewData = ref(null)
const previewPosition = ref({ x: 0, y: 0 })

// 监听侧边栏状态变化
const sidebarWidth = computed(() => {
  return appStore.sidebar.opened ? '200px' : '54px'
})

// 动态更新CSS变量
watch(sidebarWidth, (newWidth) => {
  const audioPlayer = document.querySelector('.audio-player')
  if (audioPlayer) {
    audioPlayer.style.setProperty('--sidebar-width', newWidth)
  }
}, { immediate: true })

// 格式化时间函数（显示为 MM:SS 格式）
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 音频事件处理
const handleLoadedMetadata = () => {
  if (audioRef.value) {
    totalTime.value = Math.floor(audioRef.value.duration)
    isLoading.value = false
  }
}

const handleTimeUpdate = () => {
  if (audioRef.value) {
    // 保持更高精度的时间显示
    currentTime.value = Math.floor(audioRef.value.currentTime)
    // 发出时间更新事件，传递精确的当前时间（秒），保留小数点精度
    emit('timeUpdate', audioRef.value.currentTime)
  }
}

// 启动高频时间更新定时器
const startTimeUpdateTimer = () => {
  if (timeUpdateTimer) {
    clearInterval(timeUpdateTimer)
  }
  // 每50ms更新一次时间，确保词汇高亮更加精确
  timeUpdateTimer = setInterval(() => {
    if (audioRef.value && isPlaying.value) {
      emit('timeUpdate', audioRef.value.currentTime)
    }
  }, 50)
}

// 停止高频时间更新定时器
const stopTimeUpdateTimer = () => {
  if (timeUpdateTimer) {
    clearInterval(timeUpdateTimer)
    timeUpdateTimer = null
  }
}

const handlePlay = () => {
  isPlaying.value = true
  startTimeUpdateTimer()
}

const handlePause = () => {
  isPlaying.value = false
  stopTimeUpdateTimer()
}

const handleEnded = () => {
  isPlaying.value = false
  currentTime.value = 0
  // 同步音频元素的currentTime到0，确保快进/回退功能正常
  if (audioRef.value) {
    audioRef.value.currentTime = 0
  }
  stopTimeUpdateTimer()
}

const handleError = () => {
  isError.value = true
  isLoading.value = false
  console.error('音频加载失败')
}

// 播放/暂停切换
const handleTogglePlay = async () => {
  if (!audioRef.value) return

  try {
    if (isPlaying.value) {
      audioRef.value.pause()
    } else {
      await audioRef.value.play()
    }
  } catch (error) {
    console.error('播放失败:', error)
    isError.value = true
  }
}

// 快退功能
const handleRewind = () => {
  if (!audioRef.value) return
  audioRef.value.currentTime = Math.max(0, audioRef.value.currentTime - 10)
}

// 快进功能
const handleFastForward = () => {
  if (!audioRef.value) return
  audioRef.value.currentTime = Math.min(totalTime.value, audioRef.value.currentTime + 10)
}

// 进度条拖拽
const handleProgressChange = (event) => {
  if (!audioRef.value) return

  const progressBar = event.currentTarget
  const rect = progressBar.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percentage = clickX / rect.width
  const newTime = percentage * totalTime.value

  audioRef.value.currentTime = newTime
}

// 根据时间查找对应的对话章节
const findConversationByTime = (timeInSeconds) => {
  if (!props.conversations || props.conversations.length === 0) return null

  const timeInMs = timeInSeconds * 1000

  // 查找包含该时间点的对话
  for (let i = 0; i < props.conversations.length; i++) {
    const conversation = props.conversations[i]
    if (timeInMs >= conversation.startTime && timeInMs <= conversation.endTime) {
      return {
        ...conversation,
        index: i
      }
    }
  }

  // 如果没有找到精确匹配，找最近的对话
  let closestConversation = null
  let minDistance = Infinity

  for (let i = 0; i < props.conversations.length; i++) {
    const conversation = props.conversations[i]
    const startDistance = Math.abs(timeInMs - conversation.startTime)
    const endDistance = Math.abs(timeInMs - conversation.endTime)
    const distance = Math.min(startDistance, endDistance)

    if (distance < minDistance) {
      minDistance = distance
      closestConversation = {
        ...conversation,
        index: i
      }
    }
  }

  return closestConversation
}

// 处理进度条鼠标悬浮
const handleProgressHover = (event) => {
  if (!audioRef.value || totalTime.value === 0) return

  const progressBar = event.currentTarget
  const rect = progressBar.getBoundingClientRect()
  const hoverX = event.clientX - rect.left
  const percentage = Math.max(0, Math.min(1, hoverX / rect.width))
  const hoverTime = percentage * totalTime.value

  // 查找对应的对话章节
  const conversation = findConversationByTime(hoverTime)

  if (conversation && conversation.text && conversation.text.trim()) {
    // 有文字内容，显示章节速览
    chapterPreviewData.value = {
      ...conversation,
      hoverTime: hoverTime,
      percentage: percentage
    }

    // 设置预览框位置 - 显示在进度条上方，避免覆盖
    const previewWidth = 250 // 预估预览框宽度
    const screenWidth = window.innerWidth
    let xPosition = event.clientX

    // 防止预览框超出屏幕右边界
    if (xPosition + previewWidth / 2 > screenWidth) {
      xPosition = screenWidth - previewWidth / 2 - 10
    }
    // 防止预览框超出屏幕左边界
    if (xPosition - previewWidth / 2 < 0) {
      xPosition = previewWidth / 2 + 10
    }

    previewPosition.value = {
      x: xPosition,
      y: rect.top - 10 // 显示在进度条上方，留出10px间距
    }

    showChapterPreview.value = true
  } else {
    // 没有文字内容，隐藏预览
    showChapterPreview.value = false
  }
}

// 处理进度条鼠标离开
const handleProgressLeave = () => {
  showChapterPreview.value = false
  chapterPreviewData.value = null
}

// 格式化时间显示（毫秒转换为 MM:SS 格式）
const formatTimestamp = (milliseconds) => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 倍速切换
const toggleSpeed = () => {
  if (!audioRef.value) return

  const speeds = ['1.0x', '1.25x', '1.5x', '2.0x']
  const speedValues = [1.0, 1.25, 1.5, 2.0]
  const currentIndex = speeds.indexOf(playbackSpeed.value)
  const nextIndex = (currentIndex + 1) % speeds.length

  playbackSpeed.value = speeds[nextIndex]
  audioRef.value.playbackRate = speedValues[nextIndex]
}

// 监听播放状态变化
const togglePlay = () => {
  handleTogglePlay()
}

// 跳转到指定时间（毫秒）
const seekToTime = (milliseconds) => {
  if (!audioRef.value) return

  const seconds = milliseconds / 1000
  audioRef.value.currentTime = Math.min(seconds, totalTime.value)

  // 如果音频没有在播放，则开始播放
  if (!isPlaying.value) {
    handleTogglePlay()
  }
}

// 暴露方法给父组件
defineExpose({
  seekToTime,
  togglePlay,
  isPlaying: () => isPlaying.value,
  getCurrentTime: () => currentTime.value,
  getTotalTime: () => totalTime.value
})

// 初始化音频
const initAudio = () => {
  if (audioRef.value && props.url) {
    isLoading.value = true
    isError.value = false
    audioRef.value.src = props.url
    audioRef.value.load()
  }
}

// 监听URL变化
watch(() => props.url, (newUrl) => {
  if (newUrl) {
    initAudio()
  }
}, { immediate: true })

// 组件挂载时初始化CSS变量和音频
onMounted(() => {
  const audioPlayer = document.querySelector('.audio-player')
  if (audioPlayer) {
    audioPlayer.style.setProperty('--sidebar-width', sidebarWidth.value)
  }

  // 初始化音频
  if (props.url) {
    initAudio()
  }
})

// 组件卸载时清理
onUnmounted(() => {
  stopTimeUpdateTimer()
  if (audioRef.value) {
    audioRef.value.pause()
    audioRef.value.src = ''
  }
})
</script>

<template>
  <div class="audio-player">
    <!-- 隐藏的音频元素 -->
    <audio
      ref="audioRef"
      @loadedmetadata="handleLoadedMetadata"
      @timeupdate="handleTimeUpdate"
      @play="handlePlay"
      @pause="handlePause"
      @ended="handleEnded"
      @error="handleError"
      preload="metadata"
    ></audio>

    <!-- 顶部进度条 -->
    <div class="progress-section">
      <div
        class="progress-container"
        @click="handleProgressChange"
        @mousemove="handleProgressHover"
        @mouseleave="handleProgressLeave"
      >
        <div class="progress-track">
          <div
            class="progress-fill"
            :style="{ width: totalTime > 0 ? (currentTime / totalTime * 100) + '%' : '0%' }"
          ></div>
          <div
            class="progress-thumb"
            :style="{ left: totalTime > 0 ? (currentTime / totalTime * 100) + '%' : '0%' }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 章节速览预览框 -->
    <teleport to="body">
      <div
        v-if="showChapterPreview && chapterPreviewData"
        class="chapter-preview"
        :style="{
          left: previewPosition.x + 'px',
          top: previewPosition.y + 'px'
        }"
      >
        <div class="preview-header">
          <div class="preview-time">
            {{ formatTimestamp(chapterPreviewData.startTime) }} - {{ formatTimestamp(chapterPreviewData.endTime) }}
          </div>
          <div class="preview-speaker" v-if="chapterPreviewData.speakerName">
            {{ chapterPreviewData.speakerName }}
          </div>
        </div>
        <div class="preview-content">
          {{ chapterPreviewData.text }}
        </div>
        <!-- 指向进度条的小箭头 -->
        <div class="preview-arrow"></div>
      </div>
    </teleport>

    <!-- 底部控制区域 -->
    <div class="controls-row">
      <!-- 左侧：喇叭图标和时间显示 -->
      <div class="left-section">
        <div class="volume-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11 5L6 9H2V15H6L11 19V5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M19.07 4.93C20.9447 6.80528 21.9979 9.34836 21.9979 12C21.9979 14.6516 20.9447 17.1947 19.07 19.07" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M15.54 8.46C16.4774 9.39764 17.0039 10.6692 17.0039 12C17.0039 13.3308 16.4774 14.6024 15.54 15.54" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="time-display">
          {{ formatTime(currentTime) }}/{{ formatTime(totalTime) }}
        </div>
      </div>

      <!-- 中间：播放控制按钮 -->
      <div class="control-section">
        <button class="control-btn rewind-btn" @click="handleRewind">
          <el-icon>
            <DArrowLeft />
          </el-icon>
        </button>
        <button class="play-btn" @click="togglePlay" :disabled="isLoading || isError || !props.url">
          <el-icon v-if="!isLoading">
            <VideoPlay v-if="!isPlaying" />
            <VideoPause v-else />
          </el-icon>
          <div v-else class="loading-spinner">⏳</div>
        </button>
        <button class="control-btn forward-btn" @click="handleFastForward">
          <el-icon>
            <DArrowRight />
          </el-icon>
        </button>
      </div>

      <!-- 右侧：倍速按钮 -->
      <div class="right-section">
        <button class="speed-btn" @click="toggleSpeed" :disabled="isLoading || isError || !props.url">
          {{ playbackSpeed }}
        </button>
      </div>
    </div>

    <!-- 错误状态显示 -->
    <div v-if="isError" class="error-message">
      音频加载失败，请检查音频链接
    </div>
  </div>
</template>

<style scoped>
@import '../styles/audio-player.scss';
</style>
