//算力花费统计图业务抽离
import { userConsumption } from "@/api/platform/consumption";

export async function fetchConsumptionData(params) {
  try {
    const response = await userConsumption(params);
    return response.rows;
  } catch (error) {
    console.error('无法获取消费数据', error);
    throw error;
  }
}

export function processHashrateForChart(consumptionList, timeUnit) {
  if (!consumptionList || consumptionList.length === 0) {
    return { keys: [], values: [] };
  }

  let dataMap = {};
  
  // 计算日期限制 - 只显示最近20天或20个月的数据
  const limitDays = timeUnit === 'day' ? 20 : 20;
  
  consumptionList.forEach(item => {
    if (item.consumptionStaus !== "1") return; // 0充值 1消费
    const date = new Date(item.createTime);
    const key = timeUnit === 'day'
      ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
      : `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    if (!dataMap[key]) {
      dataMap[key] = 0;
    }
    dataMap[key] += parseFloat(item.consumptionHashrate);
  });

  // 按日期排序并只保留最近的数据点
  let keys = Object.keys(dataMap).sort();
  
  // 如果数据点超过限制，只保留最近的几个
  if (keys.length > limitDays) {
    keys = keys.slice(-limitDays);
  }
  
  const values = keys.map(key => dataMap[key]);

  return { keys, values };
}

// 处理剩余算力点数，选择每天最低余额
export function processResidueForChart(consumptionList, timeUnit) {
  if (!consumptionList || consumptionList.length === 0) {
    return { keys: [], values: [] };
  }

  // 创建一个映射，用于存储每天的最低剩余算力点数
  let dailyMinResidue = {};
  
  // 计算日期限制 - 只显示最近20天或20个月的数据
  const limitDays = timeUnit === 'day' ? 15 : 15;
  
  consumptionList.forEach(item => {
    if (item.consumptionStaus !== "1") return; // 只考虑消费记录
    
    const date = new Date(item.createTime);
    const key = timeUnit === 'day'
      ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
      : `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    const residue = parseFloat(item.consumptionResidue);
    if (!dailyMinResidue[key] || residue < parseFloat(dailyMinResidue[key].residue)) {
      dailyMinResidue[key] = { residue: item.consumptionResidue, time: item.createTime };
    }
  });

  // 按日期排序并只保留最近的数据点
  let keys = Object.keys(dailyMinResidue).sort();
  
  // 如果数据点超过限制，只保留最近的几个
  if (keys.length > limitDays) {
    keys = keys.slice(-limitDays);
  }
  
  const values = keys.map(key => parseFloat(dailyMinResidue[key].residue));

  return { keys, values };
}