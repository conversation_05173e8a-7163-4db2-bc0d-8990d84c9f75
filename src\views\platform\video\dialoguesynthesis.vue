<template>
  <div class="dialogue-synthesis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><VideoCamera /></el-icon>
            数字人对话合成
          </h1>
          <p class="page-subtitle">创建多人对话视频，让数字人进行自然对话交流</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="goToVideoPage" class="action-btn">
            <el-icon><VideoPlay /></el-icon>查看作品
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <div class="content-wrapper">
        <!-- 进度指示器 -->
        <div class="progress-section">
          <el-steps :active="currentStep" finish-status="success" align-center class="custom-steps">
            <el-step title="选择形象" description="选择数字人形象" />
            <el-step title="选择声音" description="配置声音素材" />
            <el-step title="配置数字人" description="设置名称和匹配" />
            <el-step title="编写对话" description="创建对话内容" />
            <el-step title="一键合成" description="配置并自动完成合成" />
          </el-steps>
        </div>

        <!-- 步骤内容 -->
        <div class="step-content">
          <!-- 步骤1: 选择数字人形象 -->
          <AvatarSelection
            v-show="currentStep === 0"
            :selected-avatars="selectedAvatars"
            @update-avatars="updateSelectedAvatars"
          />

          <!-- 步骤2: 选择数字人声音 -->
          <VoiceSelection
            v-show="currentStep === 1"
            :selected-voices="selectedVoices"
            :avatar-count="selectedAvatars.length"
            @update-voices="updateSelectedVoices"
          />

          <!-- 步骤3: 配置数字人信息 -->
          <DigitalHumanConfig
            v-show="currentStep === 2"
            :selected-avatars="selectedAvatars"
            :selected-voices="selectedVoices"
            :digital-humans="digitalHumans"
            @update-digital-humans="updateDigitalHumans"
          />

          <!-- 步骤4: 编写对话内容 -->
          <DialogueEditor
            v-show="currentStep === 3"
            :digital-humans="digitalHumans"
            :dialogue-content="dialogueContent"
            @update-dialogue="updateDialogueContent"
          />

          <!-- 步骤5: 一键合成 -->
          <OneClickSynthesis
            v-show="currentStep === 4"
            :digital-humans="digitalHumans"
            :dialogue-content="dialogueContent"
            @synthesis-complete="handleSynthesisResult"
          />
        </div>

        <!-- 步骤导航 -->
        <div class="step-navigation">
          <el-button :disabled="currentStep === 0" @click="prevStep" class="nav-btn prev-btn">
            <el-icon><ArrowLeft /></el-icon>上一步
          </el-button>
          <el-button type="primary" :disabled="!canNextStep" @click="nextStep" class="nav-btn next-btn">
            {{ getNextButtonText() }}<el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="DialogueSynthesis">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { VideoCamera, VideoPlay, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import AvatarSelection from './composables/AvatarSelection.vue'
import VoiceSelection from './composables/VoiceSelection.vue'
import DigitalHumanConfig from './composables/DigitalHumanConfig.vue'
import DialogueEditor from './composables/DialogueEditor.vue'
import OneClickSynthesis from './composables/OneClickSynthesis.vue'

const router = useRouter()
const route = useRoute()

const currentStep = ref(0)
const selectedAvatars = ref([])
const selectedVoices = ref([])
const digitalHumans = ref([])
const dialogueContent = ref([])
const synthesisResult = ref(null)

const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0: // 选择形象
      return selectedAvatars.value.length >= 2
    case 1: // 选择声音
      return selectedVoices.value.length >= selectedAvatars.value.length
    case 2: // 配置数字人
      return digitalHumans.value.length >= 2 &&
             digitalHumans.value.every(h => h.name && h.name.trim())
    case 3: // 编写对话
      return dialogueContent.value.length > 0 &&
             dialogueContent.value.every(d => d.speaker && d.text)
    default:
      return false
  }
})

// 步骤控制方法
const nextStep = () => {
  if (canNextStep.value && currentStep.value < 4) {
    // 如果从步骤2（选择声音）进入步骤3（配置数字人），确保生成数字人数据
    if (currentStep.value === 1) {
      generateDigitalHumans()
    }
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const getNextButtonText = () => {
  if (currentStep.value === 4) {
    return '开始合成'
  } else {
    return '下一步'
  }
}

// 数据更新方法 - 父组件只负责数据传递
const updateSelectedAvatars = (avatars) => {
  selectedAvatars.value = avatars
}

const updateSelectedVoices = (voices) => {
  selectedVoices.value = voices
  // 当声音选择完成后，自动生成数字人配置
  generateDigitalHumans()
}

const updateDigitalHumans = (humans) => {
  digitalHumans.value = humans
}

const updateDialogueContent = (content) => {
  dialogueContent.value = content
}

// 自动生成数字人配置
const generateDigitalHumans = () => {
  if (selectedAvatars.value.length > 0 && selectedVoices.value.length > 0) {
    digitalHumans.value = selectedAvatars.value.map((avatar, index) => {
      const voice = selectedVoices.value[index]
      return {
        id: `human_${avatar.imageId}`,
        name: `数字人${index + 1}`,
        avatarId: avatar.imageId,
        avatarName: avatar.imageName,
        avatarAddress: avatar.imageAddress,
        voiceId: voice ? voice.soundId : '',
        voiceName: voice ? voice.soundName : '',
        voiceType: voice ? (voice.voiceType || 'builtin') : 'builtin'
      }
    })
  }
}

const handleSynthesisResult = (result) => {
  synthesisResult.value = result
}

// 页面跳转方法
const goToVideoPage = () => {
  router.push('/szbVideo/video')
}

// 内置音色列表
const builtinVoices = ['zhiyuan', 'zhiyue', 'zhisha', 'zhida', 'aiqi', 'aicheng', 'aijia', 'siqi', 'sijia', 'mashu', 'yueer', 'ruoxi', 'aida', 'sicheng', 'ninger', 'xiaoyun', 'xiaogang', 'ruilin']

// 处理从其他页面返回的选择结果
const handleReturnFromSelection = async () => {
  const selectedData = route.query.selectedData
  const selectionType = route.query.type

  if (selectedData && selectionType) {
    try {
      const data = JSON.parse(decodeURIComponent(selectedData))

      if (selectionType === 'avatars') {
        selectedAvatars.value = data.map(item => ({
          imageId: item.imageId,
          imageName: item.imageName,
          imageAddress: item.imageAddress,
          createBy: item.createBy,
          createTime: item.createTime
        }))
        ElMessage.success(`已选择 ${data.length} 个数字人形象`)
      } else if (selectionType === 'voices') {
        selectedVoices.value = data.map(item => {
          if (item.audioId) {
            // 用户上传的音频（系统声音）
            return {
              soundId: item.audioId.toString(),
              soundName: item.content,
              soundRef: item.audioPath || '',
              createBy: 'user',
              voiceType: 'system',
              createTime: item.createTime || new Date().toISOString()
            }
          } else {
            // 内置音色或其他类型
            const soundId = item.soundId || item.value
            const soundName = item.soundName || item.label
            const isBuiltin = builtinVoices.includes(soundId) || builtinVoices.includes(soundName)

            return {
              soundId: soundId,
              soundName: soundName,
              soundRef: item.soundRef || '',
              createBy: isBuiltin ? 'admin' : 'user',
              voiceType: isBuiltin ? 'builtin' : 'system',
              createTime: item.createTime || new Date().toISOString()
            }
          }
        })
        ElMessage.success(`已选择 ${data.length} 个声音`)
      }
      router.replace({ path: route.path })
    } catch (error) {
      console.error('处理返回数据失败:', error)
      ElMessage.error('处理选择结果失败')
      router.replace({ path: route.path })
    }
  }
}

// 监听路由查询参数变化
watch(() => route.query, async (newQuery) => {
  if (newQuery.selectedData && newQuery.type) {
    await nextTick()
    await handleReturnFromSelection()
  }
}, { immediate: true, deep: true })

onMounted(async () => {
  await handleReturnFromSelection()
  // 确保数字人数据正确生成
  if (selectedAvatars.value.length > 0 && selectedVoices.value.length > 0) {
    generateDigitalHumans()
  }
})
</script>

<style lang="scss" scoped>
.dialogue-synthesis {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;

  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        .page-title {
          display: flex;
          align-items: center;
          gap: 16px;
          font-size: 32px;
          font-weight: 700;
          color: white;
          margin: 0 0 8px 0;

          .title-icon {
            font-size: 36px;
          }
        }

        .page-subtitle {
          font-size: 18px;
          color: rgba(255, 255, 255, 0.9);
          margin: 0;
          line-height: 1.5;
        }
      }

      .header-actions {
        .action-btn {
          background: rgba(255, 255, 255, 0.2);
          border: 2px solid rgba(255, 255, 255, 0.3);
          color: white;
          border-radius: 12px;
          padding: 12px 24px;
          font-weight: 600;
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 255, 255, 0.2);
          }
        }
      }
    }
  }

  .main-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .content-wrapper {
      padding: 40px;
    }
  }

  .progress-section {
    margin-bottom: 40px;

    .custom-steps {
      :deep(.el-step__title) {
        font-size: 16px;
        font-weight: 600;
      }

      :deep(.el-step__description) {
        font-size: 14px;
      }

      :deep(.el-step__icon) {
        width: 32px;
        height: 32px;
        font-size: 16px;
      }

      :deep(.el-step__icon.is-text) {
        border-width: 2px;
      }

      :deep(.el-step.is-process .el-step__icon) {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
      }

      :deep(.el-step.is-finish .el-step__icon) {
        background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
        border-color: #67c23a;
      }

      :deep(.el-step.is-finish .el-step__line) {
        background: #67c23a;
      }
    }
  }

  .step-content {
    margin-bottom: 40px;
  }

  .step-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 1px solid #e9ecef;

    .nav-btn {
      border-radius: 12px;
      padding: 12px 24px;
      font-weight: 600;
      transition: all 0.3s ease;

      &.prev-btn {
        background: white;
        border: 2px solid #e9ecef;
        color: #666;

        &:hover:not(:disabled) {
          border-color: #667eea;
          color: #667eea;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      &.next-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;

        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .dialogue-synthesis {
    padding: 10px;

    .page-header {
      padding: 24px;
      margin-bottom: 24px;

      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;

        .title-section .page-title {
          font-size: 24px;
        }
      }
    }

    .main-container .content-wrapper {
      padding: 20px;
    }

    .step-navigation {
      flex-direction: column;
      gap: 16px;

      .nav-btn {
        width: 100%;
      }
    }
  }
}
</style>