<template>
  <div class="app-container">
    <!-- 顶部区域 -->
    <!-- <div class="top-section">
      <div class="card-group">
        <div>
          <Cardbutton title="浏览器涡轮" icon="ChromeFilled" fontSize="90" @click="openBrowser()" />
        </div>
        <div>
          <Cardbutton title="直播控制台" icon="monitor" fontSize="70" @click="handleLiveConsoleClick()" />
        </div>
        <div>
          <Cardbutton title="抖音机器人" icon="User" fontSize="70" @click="openDyRobot()" />
        </div>
      </div>
    </div> -->

    <!-- 下方网格布局区域 -->
    <div class="bottom-section">
      <div class="left-column">
        <div class="vip-card-wrapper">
          <VipCard />
        </div>
        <!-- <div class="new-chart-wrapper">
          <div class="placeholder-chart">新图表区域</div>
        </div> -->
      </div>
      <div class="right-column">
        <statisticsIndex :user-name="userStore.name" />
      </div>
    </div>

    <el-dialog v-model="showModal" width="55%" :before-close="handleClose">
      <div class="live-tabs">
        <div class="recent-created">
          <h3 style="margin: 0px; padding-bottom: 8px;">最近创建</h3>
          <el-table :data="recentCreatedLives" style="width: 100%" @row-click="selectLive">
            <el-table-column prop="projectTitle" label="所属项目" width="120" align="center" />
            <el-table-column prop="liveName" label="直播名称" width="130" align="center" />
            <el-table-column prop="updateTime" label="最近创建" width="165" align="center" />
          </el-table>
        </div>
        <div class="recent-started">
          <h3 style="margin: 0px; padding-bottom: 8px;">最近开播</h3>
          <el-table :data="recentStartedLives" style="width: 100%" @row-click="selectLive">
            <el-table-column prop="projectTitle" label="所属项目" width="120" align="center" />
            <el-table-column prop="liveName" label="直播名称" width="130" align="center" />
            <el-table-column prop="updateTime" label="开播时间" width="165" align="center" />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Index">
import { ref } from 'vue';
import Cardbutton from '@/views/components/cardbutton.vue';
import statisticsIndex from '@/views/components/statistics-index.vue';
import useUserStore from '@/store/modules/user';
import modal from '@/plugins/modal';
import useContext from '@/store/modules/context';
import { fetchRecentLives, openLive } from '@/utils/liveService';
import VipCard from '@/views/components/vip-card.vue';

const showModal = ref(false);
const recentCreatedLives = ref([]);
const recentStartedLives = ref([]);
const userStore = useUserStore();
async function openDyRobot() {
  await useContext().invoke("openDyWindow")
}
async function handleLiveConsoleClick() {
  try {
    if (await checkAndShowLiveWindow()) return;
    const { recentCreatedLives: tempRecentCreatedLives, recentStartedLives: tempRecentStartedLives } = await fetchRecentLives(userStore.name);
    recentCreatedLives.value = tempRecentCreatedLives;
    recentStartedLives.value = tempRecentStartedLives;
    // 如果有正在直播的，则直接打开当前直播控制台，没有就显示弹窗让用户选择
    const isLiveStarted = recentStartedLives.value.some(live => live.status === 'started');
    if (isLiveStarted) {
      await openLive(recentStartedLives.value[0]);
    } else if (tempRecentCreatedLives.length || tempRecentStartedLives.length) {
      showModal.value = true;
    } else {
      modal.alertError("请先选择一场直播，请稍后再试！");
    }
  } catch (error) {
    console.error('未能获取直播信息', error);
  }
}

function selectLive(live) {
  if (live && live.liveId) {
    console.log('Selected live:', live);
    openLive({ liveId: live.liveId, projectId: live.projectId });
  }
  showModal.value = false;
}

// 浏览器涡轮
async function openBrowser() {
  await useContext().invoke('openChildBrowser');
}

async function checkAndShowLiveWindow() {
  const liveWindowExists = await useContext().invoke('checkLiveWindow');
  if (liveWindowExists) {
    await useContext().invoke('openChildLive');
    return true;
  }
  return false;
}
</script>

<style scoped lang="scss">
.app-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 15px;
  min-width: 0;
  max-width: 1800px;
  margin: 0 auto;
  transition: padding 0.3s ease;

  @media (min-width: 1200px) {
    padding: 25px;
    gap: 30px;
  }
}

// 顶部区域样式
.top-section {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 15px 0;

  .card-group {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 30px;
    justify-items: center;
    background: linear-gradient(135deg,
        #ffffff 0%,
        #f5f7fa 50%,
        #e8eef5 100%);
    border-radius: 24px;
    padding: clamp(20px, 4vw, 40px);
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.06),
      0 6px 12px rgba(0, 0, 0, 0.04),
      inset 0 0 0 1px rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
      max-width: 1200px;
    }

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: radial-gradient(circle at 30% 30%,
          rgba(255, 255, 255, 0.9) 0%,
          rgba(255, 255, 255, 0.6) 30%,
          transparent 70%);
      pointer-events: none;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 90%;
      height: 40px;
      background: linear-gradient(to bottom,
          transparent,
          rgba(220, 228, 237, 0.4));
      filter: blur(15px);
      border-radius: 50%;
      pointer-events: none;
    }

    >div {
      position: relative;
      z-index: 1;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      width: 100%;
      display: flex;
      justify-content: center;

      &:hover {
        transform: translateY(-5px);
      }
    }
  }
}

// 下方网格布局区域
.bottom-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 25px;
  margin-top: 20px; // 增加顶部间距
  transition: all 0.3s ease;

  @media (min-width: 1200px) {
    grid-template-columns: minmax(300px, 570px) 1fr; // 修改左侧列的最大宽度
    gap: 30px;
    align-items: start; // 确保左右两列顶部对齐
  }

  .left-column {
    display: flex;
    flex-direction: column;
    gap: 25px;
    height: fit-content; // 适应内容高度

    .vip-card-wrapper {
      margin: 0; // 移除margin
      width: 100%;
      max-width: 570px; // 确保不会超过容器
      transition: transform 0.3s ease;

      &:hover {
        transform: translateY(-3px);
      }
    }
  }

  .right-column {
    min-width: 0;
    height: fit-content; // 适应内容高度
    background: rgba(255, 255, 255, 0.6);
    border-radius: 20px;
    padding: 20px;
    box-shadow:
      0 8px 24px rgba(0, 0, 0, 0.04),
      inset 0 0 0 1px rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 12px 30px rgba(0, 0, 0, 0.06),
        inset 0 0 0 1px rgba(255, 255, 255, 0.8);
    }
  }
}

// 确保卡片组内的按钮大小一致
.card-group {
  >div {
    width: 220px !important; // 固定宽度
    height: 280px !important; // 固定高度
  }
}

:deep(.card) {
  &.human-resources {
    --bg-color: #E3ECFD;
    --bg-color-light: #F0F5FF;
    --text-color-hover: #2C5282;
    --box-shadow-color: rgba(66, 153, 225, 0.48);
    backdrop-filter: blur(5px);
    max-width: 220px;
    width: 100%;

    &:hover {
      --bg-color: #D1E2FC;
      --bg-color-light: #E3ECFD;
    }
  }

  &:hover {
    cursor: pointer;
  }
}

// 优化对话框样式
.live-tabs {
  padding: 20px;
  background: #fff;
  border-radius: 12px;

  h3 {
    color: #2c3e50;
    font-size: 1.1em;
    margin-bottom: 15px;
  }

  .recent-created,
  .recent-started {
    margin-bottom: 25px;
    background: #f8fafc;
    padding: 15px;
    border-radius: 8px;
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }
}

@media screen and (max-width: 768px) {
  .top-section .card-group {
    width: 95%;
    padding: 20px;
    gap: 30px;
    flex-direction: column;
    align-items: center;
  }
}

.placeholder-chart {
  height: 300px;
  background-color: #f5f7fa;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 16px;
}

@media screen and (max-width: 1400px) {
  .bottom-section {
    grid-template-columns: 1fr;

    .right-column {
      min-width: 0;
    }

    .left-column {
      align-items: center;

      .vip-card-wrapper {
        width: 100%;
        max-width: 570px;
      }
    }
  }
}
</style>