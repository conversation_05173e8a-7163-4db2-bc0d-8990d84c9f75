import request from '@/utils/request'

// 查询形象管理列表
export function listImage(query) {
  return request({
    url: '/platform/image/list',
    method: 'get',
    params: query
  })
}

// 查询形象管理详细
export function getImage(imageId) {
  return request({
    url: '/platform/image/' + imageId,
    method: 'get'
  })
}

// 删除形象管理
export function delImage(imageId) {
  return request({
    url: '/platform/image/' + imageId,
    method: 'delete'
  })
}

// 根据形象地址生成临时凭证
export function imageDetail(data) { 
  return request({
    url: '/platform/image/imageDetail', 
    method: 'get',
    params:{
      imageAddress: data
    },
    timeout: 600000
  });
}

// 初始化分片
export function initImageChunk(imageName, fileName, fileSize) {
  return request({
    url: '/platform/image/chunk/init',
    method: 'post',
    params: {
      imageName,
      fileName,
      fileSize
    },
    timeout: 600000
  })
}

// 上传分片
export function uploadImageChunk(uploadId, filePath, chunkIndex, chunk) {
  const formData = new FormData()
  formData.append('chunk', chunk)
  return request({
    url: '/platform/image/chunk/upload',
    method: 'post',
    params: {
      uploadId,
      filePath,
      chunkIndex
    },
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
       repeatSubmit: false,
    },
    timeout: 600000
  })
}

// 完成分片上传
export function completeImageChunk(imageName, uploadId, filePath, partETags) {
  return request({
    url: '/platform/image/chunk/complete',
    method: 'post',
    params: {
      imageName,
      uploadId,
      filePath
    },
    data: partETags,
    timeout: 600000
  })
}

/**
 * 编辑形象
 * @param {number} imageId - 形象ID
 * @param {string} fileName - 文件名
 * @param {number} fileSize - 文件大小
 */
export function startEditImage(imageId, fileName, fileSize) {
  return request({
    url: `/platform/image/${imageId}/edit/init`,
    method: 'post',
    params: {
      fileName,
      fileSize
    },
    timeout: 600000
  })
}

/**
 * 完成形象编辑
 * @param {number} imageId - 形象ID
 * @param {string} uploadId - 上传ID
 * @param {string} filePath - 文件路径
 * @param {Array} partETags - 分片ETag信息
 */
export function finishEditImage(imageId, uploadId, filePath, partETags, imageName) {
  return request({
    url: `/platform/image/${imageId}/edit/complete`,
    method: 'post',
    params: {
      uploadId,
      filePath,
      imageName
    },
    data: partETags,
    timeout: 600000
  })
}    