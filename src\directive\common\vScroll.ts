import { Directive } from "vue"
interface ElScroll extends HTMLElement {
    $bottom: boolean;
    $interval: ReturnType<typeof setInterval>;
}
const vScroll: Directive = {
    mounted(el: ElScroll) {
        el.$bottom = true;
        el.onscroll = () => {
            el.$bottom = el.scrollTop + el.clientHeight === el.scrollHeight;
        }
        el.$interval = setInterval(() => {
            if (el.$bottom) {
                el.scrollTop = el.scrollHeight;
            }
        }, 100)
    },

    unmounted(el: ElScroll) {
        clearInterval(el.$interval)
    }
}


export default vScroll