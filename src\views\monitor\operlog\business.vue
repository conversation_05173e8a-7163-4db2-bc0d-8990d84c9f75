<template>
<div class="app-container">
    <!-- 筛选条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
        <el-form-item label="系统模块" prop="title">
            <el-input v-model="queryParams.title" placeholder="请输入系统模块" clearable style="width: 200px;" @keyup.enter="getList"/>
        </el-form-item>
        <el-form-item label="请求方式" prop="requestMethod">
            <el-input v-model="queryParams.requestMethod" placeholder="请输入请求方式" clearable style="width: 200px;" @keyup.enter="getList"/>
        </el-form-item>
        <el-form-item label="操作类型" prop="businessType">
            <el-select v-model="queryParams.businessType" placeholder="操作类型" clearable style="width: 200px" @change="getList">
                <el-option v-for="dict in sys_oper_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="操作状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="操作状态" clearable style="width: 200px" @change="getList">
                <el-option v-for="dict in sys_common_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="操作日期" style="width: 300px">
            <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
            start-placeholder="开始日期" end-placeholder="结束日期" @change="getList" />
        </el-form-item>
    </el-form>
    <!-- 饼图和表格 -->
    <ChartAndTables :queryParams="queryParams" :dateRange="dateRange" />
    <!-- 平台模块堆叠柱状图 -->
    <ModuleBarChart :queryParams="queryParams" :dateRange="dateRange" />
</div>
</template>

<script setup name="NewBusiness">
import { reactive, ref, toRefs, onMounted } from 'vue';
import { business } from "@/api/monitor/operlog";
import ChartAndTables from './chartAndTables.vue';
import ModuleBarChart from './moduleBarChart.vue';

const { proxy } = getCurrentInstance();
const { sys_oper_type, sys_common_status } = proxy.useDict("sys_oper_type", "sys_common_status");

const dateRange = ref([]);
const data = reactive({
    form: {},
    queryParams: {
        title: undefined,
        businessType: undefined,
        status: undefined,
        requestMethod: undefined
    }
});
const { queryParams } = toRefs(data);

/** 查询业务信息 */
function getList() {
    business(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {});
}

onMounted(() => {
    getList(); 
});
</script>
<style scoped>
.app-container {
    padding: 20px;
    background-color: #f0f2f5;
}
</style>
