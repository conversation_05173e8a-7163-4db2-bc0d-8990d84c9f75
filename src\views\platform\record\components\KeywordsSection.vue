<template>
  <div class="keywords-section" v-if="formattedTags.length > 0 || isEditing">
    <div class="keywords-header">
      <h4 class="keywords-title">关键词</h4>
      <div class="keywords-actions">
        <el-button
          v-if="!isEditing"
          type="primary"
          size="small"
          @click="startEdit"
          :icon="Edit"
        >
          编辑
        </el-button>
        <div v-else class="edit-actions">
          <el-button
            type="success"
            size="small"
            @click="saveKeywords"
            :loading="saving"
            :icon="Check"
          >
            保存
          </el-button>
          <el-button
            size="small"
            @click="cancelEdit"
            :icon="Close"
          >
            取消
          </el-button>
        </div>
      </div>
    </div>
    <div class="keywords-content" :class="{ 'editing': isEditing }">
      <!-- 编辑模式 -->
      <template v-if="isEditing">
        <div class="keywords-edit-grid">
          <div
            v-for="(tag, index) in editableTags"
            :key="index"
            class="keyword-edit-card"
          >
            <el-input
              v-model="editableTags[index]"
              size="small"
              class="keyword-input"
              @keyup.enter="saveKeywords"
              @keyup.esc="cancelEdit"
              placeholder="输入关键词"
            />
            <button
              @click="removeKeyword(index)"
              class="keyword-delete-btn"
              title="删除关键词"
            >
              <el-icon><Close /></el-icon>
            </button>
          </div>

          <!-- 添加新关键词卡片 -->
          <div class="keyword-add-card">
            <el-input
              v-model="newKeyword"
              size="small"
              placeholder="添加新关键词"
              class="keyword-input"
              @keyup.enter="addKeyword"
            />
            <button
              @click="addKeyword"
              :disabled="!newKeyword.trim()"
              class="keyword-add-btn"
              title="添加关键词"
            >
              <el-icon><Plus /></el-icon>
            </button>
          </div>
        </div>
      </template>

      <!-- 显示模式 -->
      <template v-else>
        <div class="keywords-display-grid">
          <div
            v-for="(tag, index) in formattedTags"
            :key="index"
            class="keyword-display-card clickable"

            @click="handleKeywordClick(tag)"
            :title="`点击搜索关键词: ${tag}`"
          >
            <span class="keyword-text">{{ tag }}</span>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, Close, Plus } from '@element-plus/icons-vue'

const props = defineProps({
  tags: {
    type: String,
    default: ''
  },
  recordId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['update:tags', 'keyword-click'])

// 编辑状态
const isEditing = ref(false)
const saving = ref(false)
const editableTags = ref([])
const newKeyword = ref('')

// 格式化标签，将JSON字符串转换为数组
const formattedTags = computed(() => {
  if (!props.tags) return []
  try {
    return JSON.parse(props.tags)
  } catch (error) {
    console.error('解析关键词失败:', error)
    return []
  }
})

// 监听tags变化，更新编辑数据
watch(() => props.tags, () => {
  if (!isEditing.value) {
    editableTags.value = [...formattedTags.value]
  }
}, { immediate: true })

// 开始编辑
const startEdit = () => {
  isEditing.value = true
  editableTags.value = [...formattedTags.value]
  newKeyword.value = ''
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  editableTags.value = []
  newKeyword.value = ''
}

// 添加关键词
const addKeyword = () => {
  const keyword = newKeyword.value.trim()
  if (keyword && !editableTags.value.includes(keyword)) {
    editableTags.value.push(keyword)
    newKeyword.value = ''
  } else if (editableTags.value.includes(keyword)) {
    ElMessage.warning('关键词已存在')
  }
}

// 删除关键词
const removeKeyword = (index) => {
  editableTags.value.splice(index, 1)
}

// 处理关键词点击事件
const handleKeywordClick = (keyword) => {
  emit('keyword-click', keyword)
}

// 保存关键词
const saveKeywords = async () => {
  try {
    saving.value = true

    // 过滤空值并去重
    const filteredTags = [...new Set(editableTags.value.filter(tag => tag.trim()))]
    const tagsJson = JSON.stringify(filteredTags)

    // 触发更新事件，让父组件处理保存逻辑
    emit('update:tags', {
      recordId: props.recordId,
      tags: tagsJson,
      originalTags: props.tags
    })

    isEditing.value = false
    ElMessage.success('关键词保存成功')
  } catch (error) {
    console.error('保存关键词失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}
</script>

<style lang="scss" scoped>
/* 关键词区域样式（位于左侧原文上方） */
.keywords-section {
  margin-bottom: 32px;
  padding: 24px;
  border-bottom: 2px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.keywords-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.keywords-title {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 20px;
}

.keywords-title::before {
  content: "🏷️";
  position: absolute;
  left: 0;
  font-size: 16px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.keywords-actions {
  display: flex;
  gap: 8px;
}

.edit-actions {
  display: flex;
  gap: 8px;
}

/* 关键词内容区域 */
.keywords-content {
  min-height: 40px;
}

/* 显示模式网格布局 */
.keywords-display-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.keyword-display-card {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 120px;
  min-width: 60px;
}

.keyword-display-card.clickable {
  cursor: pointer;
  user-select: none;
}

.keyword-display-card.clickable:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.3);
}

.keyword-display-card.danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.keyword-display-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.keyword-display-card.danger:hover {
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.keyword-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

/* 编辑模式网格布局 */
.keywords-edit-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-start;
}

.keyword-edit-card,
.keyword-add-card {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  min-width: 140px;
  max-width: 180px;
}

.keyword-edit-card:hover,
.keyword-add-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.1);
}

.keyword-input {
  flex: 1;
  border: none !important;
  box-shadow: none !important;
  background: transparent;
}

.keyword-input :deep(.el-input__wrapper) {
  border: none !important;
  box-shadow: none !important;
  background: transparent;
  padding: 0;
}

.keyword-input :deep(.el-input__inner) {
  font-size: 12px;
  color: #374151;
  font-weight: 500;
}

/* 删除按钮样式 */
.keyword-delete-btn {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ef4444;
  color: white;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.keyword-delete-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
}

/* 添加按钮样式 */
.keyword-add-btn {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #10b981;
  color: white;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.keyword-add-btn:hover:not(:disabled) {
  background: #059669;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.keyword-add-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  box-shadow: 0 2px 4px rgba(156, 163, 175, 0.3);
}

.keyword-add-card {
  border-style: dashed;
  border-color: #d1d5db;
  background: #f9fafb;
}

.keyword-add-card:hover {
  border-color: #10b981;
  background: #f0fdf4;
}
</style>
