<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" @close="handleClose">
    <div class="uploader-container">
      <!-- 拖拽上传区域 -->
      <div 
        class="upload-area"
        :class="{ 'drag-over': isDragOver }"
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragleave="handleDragLeave"
        @click="triggerFileInput"
      >
        <input
          ref="fileInputRef"
          type="file"
          multiple
          :accept="acceptTypes"
          style="display: none"
          @change="handleFileSelect"
        />
        
        <div class="upload-icon">
          <el-icon size="48">
            <component :is="iconComponent" />
          </el-icon>
        </div>
        <div class="upload-text">
          <p>将{{ mediaTypeName }}文件拖到此处，或<span class="click-text">点击上传</span></p>
          <p class="upload-tip">{{ uploadTip }}</p>
        </div>
      </div>

      <!-- 文件列表 -->
      <div v-if="fileList.length > 0" class="file-list">
        <div class="file-list-header">
          <span>待上传文件 ({{ fileList.length }})</span>
          <el-button size="small" text @click="clearFiles" :disabled="uploading">清空</el-button>
        </div>

        <div class="file-items">
          <div v-for="(file, index) in fileList" :key="index" class="file-item">
            <div class="file-info">
              <el-icon class="file-icon">
                <component :is="iconComponent" />
              </el-icon>
              <div class="file-details">
                <div class="file-name">{{ file.name }}</div>
                <div class="file-size">{{ formatFileSize(file.size) }}</div>
                <!-- 显示文件状态 -->
                <div class="file-status">
                  <span v-if="fileUploadStatus[index] === 'waiting'" class="status-waiting">等待上传</span>
                  <span v-else-if="fileUploadStatus[index] === 'uploading'" class="status-uploading">上传中</span>
                  <span v-else-if="fileUploadStatus[index] === 'success'" class="status-success">上传成功</span>
                  <span v-else-if="fileUploadStatus[index] === 'error'" class="status-error">上传失败</span>
                </div>
              </div>
            </div>

            <!-- 进度条和统计信息 -->
            <div v-if="fileUploadStatus[index] === 'uploading' || fileUploadStatus[index] === 'success'" class="progress-section">
              <el-progress
                :percentage="fileUploadStatus[index] === 'success' ? 100 : (fileProgress[index]?.percentage || 0)"
                :status="fileUploadStatus[index] === 'success' ? 'success' : undefined"
                :stroke-width="6"
              />
              <div v-if="fileUploadStatus[index] === 'uploading'" class="progress-stats">
                <span class="upload-speed">{{ formatSpeed(fileProgress[index]?.speed || 0) }}</span>
                <span class="remaining-time">剩余时间: {{ formatRemainingTime(fileProgress[index]?.remainingTime || 0) }}</span>
              </div>
            </div>

            <!-- 删除按钮 -->
            <div class="file-actions">
              <el-button 
                size="small" 
                text 
                type="danger" 
                @click="removeFile(index)"
                :disabled="uploading && fileUploadStatus[index] === 'uploading'"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 总体进度 -->
      <div v-if="uploading" class="total-progress">
        <div class="progress-header">
          <span>总体进度</span>
          <span>{{ currentFileIndex + 1 }} / {{ fileList.length }}</span>
        </div>
        <el-progress :percentage="totalProgress" :stroke-width="8" />
        <div class="current-file">
          <span>当前文件: {{ currentFileName }}</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="uploading">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleUpload" 
          :disabled="fileList.length === 0 || uploading"
          :loading="uploading"
        >
          {{ uploading ? '上传中...' : '开始上传' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay, Microphone, Picture, Document } from '@element-plus/icons-vue'
import { ChunkUploader, formatFileSize, formatSpeed, formatRemainingTime } from '../../../utils/chunkUploader'

// 媒体类型定义
type MediaType = 'video' | 'audio' | 'image' | 'font'

// Props
const props = defineProps<{
  visible: boolean
  mediaType: MediaType
}>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'upload-success': [result: any]
  'upload-error': [error: any]
}>()

// 文件上传状态类型
type FileUploadStatus = 'waiting' | 'uploading' | 'success' | 'error'

// 响应式数据
const fileInputRef = ref<HTMLInputElement>()
const fileList = ref<File[]>([])
const isDragOver = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)
const currentFileIndex = ref(0)
const currentFileName = ref('')

// 分片上传相关状态
const fileUploadStatus = ref<FileUploadStatus[]>([])
const fileProgress = ref<Record<number, any>>({})
const uploaders = ref<ChunkUploader[]>([])
const totalProgress = ref(0)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 媒体类型配置
const mediaConfig = computed(() => {
  const configs = {
    video: {
      title: '上传视频',
      name: '视频',
      icon: VideoPlay,
      accept: 'video/*',
      tip: '支持 mp4, avi, mov, mkv 格式，单个文件不超过 500MB',
      category: 'video'
    },
    audio: {
      title: '上传音频',
      name: '音频',
      icon: Microphone,
      accept: 'audio/*',
      tip: '支持 mp3, wav, flac, aac 格式，单个文件不超过 100MB',
      category: 'audio'
    },
    image: {
      title: '上传图片',
      name: '图片',
      icon: Picture,
      accept: 'image/*',
      tip: '支持 jpg, png, gif, webp 格式，单个文件不超过 50MB',
      category: 'image'
    },
    font: {
      title: '上传字体',
      name: '字体',
      icon: Document,
      accept: '.ttf,.otf,.woff,.woff2',
      tip: '支持 ttf, otf, woff, woff2 格式，单个文件不超过 20MB',
      category: 'font'
    }
  }
  return configs[props.mediaType]
})

const dialogTitle = computed(() => mediaConfig.value.title)
const mediaTypeName = computed(() => mediaConfig.value.name)
const iconComponent = computed(() => mediaConfig.value.icon)
const acceptTypes = computed(() => mediaConfig.value.accept)
const uploadTip = computed(() => mediaConfig.value.tip)



// 验证文件
const validateFile = (file: File): boolean => {
  const config = mediaConfig.value
  
  // 检查文件类型
  const isValidType = (() => {
    switch (props.mediaType) {
      case 'video':
        return file.type.startsWith('video/')
      case 'audio':
        return file.type.startsWith('audio/')
      case 'image':
        return file.type.startsWith('image/')
      case 'font':
        return ['.ttf', '.otf', '.woff', '.woff2'].some(ext => 
          file.name.toLowerCase().endsWith(ext)
        )
      default:
        return false
    }
  })()
  
  if (!isValidType) {
    ElMessage.error(`文件 ${file.name} 不是${config.name}文件`)
    return false
  }
  
  // 检查文件大小
  const maxSizes = {
    video: 500 * 1024 * 1024, // 500MB
    audio: 100 * 1024 * 1024, // 100MB
    image: 50 * 1024 * 1024,  // 50MB
    font: 20 * 1024 * 1024    // 20MB
  }
  
  const maxSize = maxSizes[props.mediaType]
  if (file.size > maxSize) {
    ElMessage.error(`文件 ${file.name} 超过 ${formatFileSize(maxSize)} 限制`)
    return false
  }
  
  return true
}

// 添加文件到列表
const addFiles = (files: File[]) => {
  const validFiles = files.filter(validateFile)

  // 检查重复文件
  const newFiles = validFiles.filter(file =>
    !fileList.value.some(existingFile =>
      existingFile.name === file.name && existingFile.size === file.size
    )
  )

  if (newFiles.length < validFiles.length) {
    ElMessage.warning('已过滤重复文件')
  }

  // 添加文件和初始化状态
  const startIndex = fileList.value.length
  fileList.value.push(...newFiles)

  // 初始化文件状态
  newFiles.forEach((_, index) => {
    fileUploadStatus.value[startIndex + index] = 'waiting'
    fileProgress.value[startIndex + index] = {
      percentage: 0,
      speed: 0,
      remainingTime: 0
    }
  })

  if (newFiles.length > 0) {
    ElMessage.success(`已添加 ${newFiles.length} 个文件`)
  }
}

// 拖拽事件处理
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false

  const files = Array.from(e.dataTransfer?.files || [])
  addFiles(files)
}

// 点击选择文件
const triggerFileInput = () => {
  fileInputRef.value?.click()
}

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = Array.from(target.files || [])
  addFiles(files)

  // 清空input，允许重复选择同一文件
  if (target) {
    target.value = ''
  }
}

// 移除文件
const removeFile = (index: number) => {
  fileList.value.splice(index, 1)
  delete fileUploadStatus.value[index]
  delete fileProgress.value[index]

  // 重新整理索引
  const newStatus: FileUploadStatus[] = []
  const newProgress: Record<number, any> = {}

  fileList.value.forEach((_, i) => {
    const oldIndex = i >= index ? i + 1 : i
    newStatus[i] = fileUploadStatus.value[oldIndex] || 'waiting'
    newProgress[i] = fileProgress.value[oldIndex] || { percentage: 0, speed: 0, remainingTime: 0 }
  })

  fileUploadStatus.value = newStatus
  fileProgress.value = newProgress
}

// 清空文件列表
const clearFiles = () => {
  fileList.value = []
  fileUploadStatus.value = []
  fileProgress.value = {}
  uploaders.value = []
}

// 更新总体进度
const updateTotalProgress = () => {
  if (fileList.value.length === 0) {
    totalProgress.value = 0
    return
  }

  let totalPercentage = 0

  // 计算每个文件的进度贡献
  for (let i = 0; i < fileList.value.length; i++) {
    const status = fileUploadStatus.value[i]
    const progress = fileProgress.value[i]

    if (status === 'success') {
      // 已完成的文件贡献100%
      totalPercentage += 100
    } else if (status === 'uploading' && progress) {
      // 正在上传的文件贡献当前进度
      totalPercentage += progress.percentage || 0
    } else if (status === 'error') {
      // 失败的文件贡献0%
      totalPercentage += 0
    } else {
      // 等待中的文件贡献0%
      totalPercentage += 0
    }
  }

  totalProgress.value = Math.round(totalPercentage / fileList.value.length)
}

// 上传处理
const handleUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }

  uploading.value = true
  currentFileIndex.value = 0
  totalProgress.value = 0
  const errors: string[] = []

  try {
    // 初始化所有文件状态为等待
    fileList.value.forEach((_, index) => {
      fileUploadStatus.value[index] = 'waiting'
      fileProgress.value[index] = { percentage: 0, speed: 0, remainingTime: 0 }
    })

    let successCount = 0
    let failureCount = 0

    // 真正的串行上传：一个文件完成后再上传下一个
    for (let index = 0; index < fileList.value.length; index++) {
      const file = fileList.value[index]
      currentFileIndex.value = index
      currentFileName.value = file.name

      try {
        // 创建当前文件的上传器
        const uploader = new ChunkUploader(
          file,
          mediaConfig.value.category,
          {
            chunkSize: 5 * 1024 * 1024, // 5MB分片
            maxConcurrent: 1, // 串行上传分片
            retryTimes: 3 // 重试3次
          },
          // 进度回调
          (progress) => {
            fileProgress.value[index] = progress
            updateTotalProgress()
          }
        )

        // 设置当前文件为上传中状态
        fileUploadStatus.value[index] = 'uploading'

        // 等待当前文件上传完成
        const result = await uploader.upload()

        // 上传成功
        fileUploadStatus.value[index] = 'success'
        successCount++

        console.log(`文件 ${file.name} 上传成功:`, result)

      } catch (error: any) {
        // 上传失败
        fileUploadStatus.value[index] = 'error'
        failureCount++

        const errorMsg = `文件 ${file.name} 上传失败: ${error.message || error}`
        console.error(errorMsg, error)
        errors.push(errorMsg)

        // 继续上传下一个文件，不中断整个流程
      }

      // 更新总体进度
      updateTotalProgress()
    }

    // 显示最终结果
    if (successCount > 0 && failureCount === 0) {
      ElMessage.success(`全部 ${successCount} 个文件上传成功！`)
      emit('upload-success', { successCount, failureCount })
    } else if (successCount > 0 && failureCount > 0) {
      ElMessage.warning(`${successCount} 个文件上传成功，${failureCount} 个文件上传失败`)
      emit('upload-success', { successCount, failureCount })
      emit('upload-error', { errors })
    } else if (failureCount > 0) {
      ElMessage.error(`全部 ${failureCount} 个文件上传失败`)
      emit('upload-error', { errors })
    }

    // 最终更新总体进度为100%（如果有成功的文件）
    if (successCount > 0) {
      totalProgress.value = Math.round((successCount / fileList.value.length) * 100)
    }

  } catch (error: any) {
    console.error('上传过程中发生错误:', error)
    ElMessage.error('上传失败: ' + (error.message || error))
    emit('upload-error', { errors: [error.message || error] })
  } finally {
    uploading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  if (uploading.value) {
    ElMessage.warning('上传进行中，无法关闭')
    return
  }

  // 清空状态
  clearFiles()
  emit('update:visible', false)
}

// 监听对话框关闭
watch(() => props.visible, (newVal) => {
  if (!newVal && !uploading.value) {
    clearFiles()
  }
})
</script>

<style scoped>
.uploader-container {
  padding: 20px 0;
}

.upload-area {
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafbfc;
}

.upload-area:hover,
.upload-area.drag-over {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.upload-icon {
  color: #409eff;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
  color: #606266;
}

.click-text {
  color: #409eff;
  cursor: pointer;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
}

.file-list {
  margin-top: 20px;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.file-items {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #fff;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  font-size: 24px;
  color: #409eff;
  margin-right: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.file-status {
  font-size: 12px;
}

.status-waiting {
  color: #909399;
}

.status-uploading {
  color: #409eff;
}

.status-success {
  color: #67c23a;
}

.status-error {
  color: #f56c6c;
}

.progress-section {
  flex: 1;
  margin: 0 16px;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.file-actions {
  margin-left: 12px;
}

.total-progress {
  margin-top: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-weight: 500;
}

.current-file {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
