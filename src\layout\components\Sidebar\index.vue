<template>
  <div :class="{ 'has-logo': showLogo }" class="sidebar-wrapper">
    <el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="sideTheme === 'theme-dark' ? variables.menuBackground : 'transparent'"
        :text-color="sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
        :unique-opened="true"
        :active-text-color="theme"
        :collapse-transition="false"
        mode="vertical"
        class="sidebar-menu"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import SidebarItem from './SidebarItem'
import variables from '@/assets/styles/variables.module.scss'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

const route = useRoute();
const appStore = useAppStore()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()

const sidebarRouters =  computed(() => permissionStore.sidebarRouters);
const showLogo = computed(() => settingsStore.sidebarLogo);
const sideTheme = computed(() => settingsStore.sideTheme);
const theme = computed(() => settingsStore.theme);
const isCollapse = computed(() => !appStore.sidebar.opened);

const activeMenu = computed(() => {
  const { meta, path } = route;
  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
})
</script>

<style lang="scss" scoped>
.sidebar-wrapper {
  height: 100%;
  background: #ffffff;
  transition: all 0.3s ease;
  overflow: hidden;

  .el-scrollbar {
    height: 100%;
  }
}

.sidebar-menu {
  border: none;
  height: 100%;
  width: 100%;
  background: transparent;
  
  :deep(.el-menu-item),
  :deep(.el-sub-menu__title) {
    padding: 0 16px !important;
    margin: 4px 8px;
    display: flex;
    align-items: center;
    
    .svg-icon {
      margin-right: 12px;
      font-size: 18px;
      color: inherit;
      transition: all 0.3s ease;
      flex-shrink: 0;
    }

    .el-sub-menu__icon-arrow {
      color: currentColor;
      font-size: 12px;
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  :deep(.el-sub-menu) {
    .el-menu {
      background: transparent;
      
      .el-menu-item {
        padding-left: 30px !important;
      }
    }
  }
}

.scrollbar-wrapper {
  overflow-x: hidden !important;
  
  :deep(.el-scrollbar__view) {
    height: 100%;
  }
  
  :deep(.el-scrollbar__bar) {
    &.is-horizontal {
      display: none;
    }
    
    &.is-vertical {
      width: 4px;
      
      .el-scrollbar__thumb {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
      }
    }
  }
}

// 暗色主题适配
.theme-dark {
  background: #202124;
  
  .sidebar-menu {
    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      color: #e8eaed;
      
      &:hover {
        background: rgba(255, 255, 255, 0.05);
        color: #8ab4f8;
        
        .svg-icon {
          color: #8ab4f8;
        }
      }
      
      &.is-active {
        background: rgba(138, 180, 248, 0.12);
        color: #8ab4f8;
        
        .svg-icon {
          color: #8ab4f8;
        }
      }

      .svg-icon {
        color: inherit;
      }

      .el-sub-menu__icon-arrow {
        color: currentColor;
      }
    }
  }
}
</style>
