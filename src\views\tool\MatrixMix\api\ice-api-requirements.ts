/**
 * 阿里云ICE后端API需求清单
 * 
 * 基于前端重构需求，整理出需要后端实现的所有阿里云ICE API接口
 * 所有接口都通过后端代理调用阿里云ICE SDK，避免前端存储秘钥
 */

// ============================================================================
// 1. 服务状态和连接测试相关API
// ============================================================================

/**
 * 后端健康检查
 * GET /api/ice/health
 * 
 * 响应:
 * {
 *   "status": "ok" | "error",
 *   "message": "ICE服务正常" | "错误信息",
 *   "timestamp": "2024-01-01T00:00:00.000Z"
 * }
 */

/**
 * 测试阿里云ICE连接
 * GET /api/ice/test-connection
 * 
 * 响应:
 * {
 *   "success": true | false,
 *   "message": "连接成功" | "连接失败原因",
 *   "endpoint": "ice.cn-beijing.aliyuncs.com",
 *   "regionId": "cn-beijing"
 * }
 */

// ============================================================================
// 2. 剪辑工程管理API (对应阿里云ICE EditingProject相关接口)
// ============================================================================

/**
 * 创建剪辑工程
 * POST /api/ice/projects
 * 
 * 请求体:
 * {
 *   "title": "工程标题",
 *   "timeline": "时间轴JSON字符串（可选）",
 *   "description": "工程描述（可选）"
 * }
 * 
 * 响应:
 * {
 *   "ProjectId": "project-id-123",
 *   "Title": "工程标题", 
 *   "Timeline": "时间轴JSON",
 *   "CoverURL": "封面地址",
 *   "Status": "Normal",
 *   "CreateTime": "2024-01-01T00:00:00.000Z",
 *   "ModifiedTime": "2024-01-01T00:00:00.000Z"
 * }
 */

/**
 * 获取剪辑工程详情
 * GET /api/ice/projects/{projectId}
 * 
 * 响应: 同创建工程响应格式
 */

/**
 * 更新剪辑工程
 * PUT /api/ice/projects/{projectId}
 * 
 * 请求体:
 * {
 *   "timeline": "更新的时间轴JSON",
 *   "title": "更新的标题（可选）"
 * }
 * 
 * 响应:
 * {
 *   "success": true,
 *   "message": "更新成功"
 * }
 */

/**
 * 删除剪辑工程
 * DELETE /api/ice/projects
 * 
 * 请求体:
 * {
 *   "projectIds": ["project-id-1", "project-id-2"]
 * }
 * 
 * 响应:
 * {
 *   "success": true,
 *   "message": "删除成功"
 * }
 */

/**
 * 列出用户的剪辑工程
 * GET /api/ice/projects?pageNo=1&pageSize=20&sortBy=CreationTime:Desc
 * 
 * 响应:
 * {
 *   "projects": [工程对象数组],
 *   "total": 总数量,
 *   "pageNo": 当前页码,
 *   "pageSize": 每页大小
 * }
 */

// ============================================================================
// 3. 模板管理API (对应阿里云ICE Template相关接口)
// ============================================================================

/**
 * 创建模板
 * POST /api/ice/templates
 * 
 * 请求体:
 * {
 *   "name": "模板名称",
 *   "type": "Timeline",
 *   "config": "模板配置JSON字符串"
 * }
 * 
 * 响应:
 * {
 *   "TemplateId": "template-id-123",
 *   "Name": "模板名称",
 *   "Timeline": "模板配置JSON",
 *   "CoverURL": "封面地址",
 *   "Status": "Normal"
 * }
 */

/**
 * 获取模板详情
 * GET /api/ice/templates/{templateId}
 * 
 * 响应: 同创建模板响应格式
 */

/**
 * 更新模板
 * PUT /api/ice/templates/{templateId}
 * 
 * 请求体:
 * {
 *   "config": "更新的模板配置JSON",
 *   "name": "更新的名称（可选）"
 * }
 * 
 * 响应:
 * {
 *   "success": true,
 *   "message": "更新成功"
 * }
 */

/**
 * 列出用户的模板
 * GET /api/ice/templates?pageNo=1&pageSize=20&sortBy=CreationTime:Desc
 * 
 * 响应:
 * {
 *   "templates": [模板对象数组],
 *   "total": 总数量
 * }
 */

/**
 * 删除模板
 * DELETE /api/ice/templates/{templateId}
 * 
 * 响应:
 * {
 *   "success": true,
 *   "message": "删除成功"
 * }
 */

// ============================================================================
// 4. 媒资管理API (对应阿里云ICE Media相关接口)
// ============================================================================

/**
 * 获取媒资信息
 * GET /api/ice/media/{mediaId}
 * 
 * 响应:
 * {
 *   "MediaId": "media-id-123",
 *   "Title": "媒资标题",
 *   "MediaURL": "播放地址",
 *   "CoverURL": "封面地址",
 *   "MediaType": "Video|Audio|Image",
 *   "Duration": 123.45,
 *   "Size": 文件大小,
 *   "Width": 宽度,
 *   "Height": 高度,
 *   "CreateTime": "创建时间"
 * }
 */

/**
 * 搜索媒资
 * GET /api/ice/media/search?keyword=关键词&mediaType=Video&pageNo=1&pageSize=20
 * 
 * 响应:
 * {
 *   "medias": [媒资对象数组],
 *   "total": 总数量
 * }
 */

/**
 * 批量获取媒资信息
 * POST /api/ice/media/batch
 * 
 * 请求体:
 * {
 *   "mediaIds": ["media-id-1", "media-id-2"],
 *   "additionType": "video"
 * }
 * 
 * 响应:
 * {
 *   "medias": [媒资对象数组]
 * }
 */

/**
 * 上传媒资文件
 * POST /api/ice/media/upload
 * 
 * 请求体: FormData包含文件
 * 
 * 响应:
 * {
 *   "MediaId": "新创建的媒资ID",
 *   "UploadURL": "上传地址",
 *   "UploadAuth": "上传凭证"
 * }
 */

/**
 * 注册媒资
 * POST /api/ice/media/register
 * 
 * 请求体:
 * {
 *   "inputURL": "媒资文件URL",
 *   "mediaMetaData": {
 *     "Title": "媒资标题",
 *     "Description": "描述"
 *   }
 * }
 * 
 * 响应:
 * {
 *   "MediaId": "注册后的媒资ID"
 * }
 */

// ============================================================================
// 5. 视频制作API (对应阿里云ICE Job相关接口)
// ============================================================================

/**
 * 提交剪辑作业
 * POST /api/ice/jobs/editing
 * 
 * 请求体:
 * {
 *   "timeline": "时间轴JSON字符串",
 *   "outputConfig": {
 *     "mediaURL": "输出地址",
 *     "width": 1920,
 *     "height": 1080
 *   },
 *   "editingConfig": {}
 * }
 * 
 * 响应:
 * {
 *   "JobId": "job-id-123",
 *   "Status": "Processing",
 *   "OutputMediaConfig": {},
 *   "EditingConfig": {}
 * }
 */

/**
 * 查询剪辑作业状态
 * GET /api/ice/jobs/{jobId}
 * 
 * 响应:
 * {
 *   "JobId": "job-id-123",
 *   "Status": "Success|Failed|Processing",
 *   "ErrorCode": "错误码（失败时）",
 *   "ErrorMessage": "错误信息（失败时）",
 *   "OutputMediaURL": "输出视频地址（成功时）",
 *   "CreateTime": "创建时间",
 *   "CompleteTime": "完成时间"
 * }
 */

/**
 * 取消剪辑作业
 * DELETE /api/ice/jobs/{jobId}
 * 
 * 响应:
 * {
 *   "success": true,
 *   "message": "取消成功"
 * }
 */

// ============================================================================
// 6. 工程素材管理API
// ============================================================================

/**
 * 获取剪辑工程关联的素材
 * GET /api/ice/projects/{projectId}/materials
 * 
 * 响应:
 * {
 *   "materials": [
 *     {
 *       "MaterialId": "material-id-123",
 *       "MediaId": "media-id-123", 
 *       "MaterialType": "Video|Audio|Image",
 *       "Source": "OSS|URL",
 *       "FileURL": "文件地址",
 *       "CoverURL": "封面地址",
 *       "Duration": 123.45,
 *       "Title": "素材标题"
 *     }
 *   ]
 * }
 */

/**
 * 添加素材到剪辑工程
 * POST /api/ice/projects/{projectId}/materials
 * 
 * 请求体:
 * {
 *   "mediaIds": ["media-id-1", "media-id-2"]
 * }
 * 
 * 响应:
 * {
 *   "success": true,
 *   "addedCount": 2
 * }
 */

/**
 * 从剪辑工程移除素材
 * DELETE /api/ice/projects/{projectId}/materials
 * 
 * 请求体:
 * {
 *   "materialIds": ["material-id-1", "material-id-2"]
 * }
 * 
 * 响应:
 * {
 *   "success": true,
 *   "removedCount": 2
 * }
 */

// ============================================================================
// 7. 智能服务API (可选，用于AI功能)
// ============================================================================

/**
 * 智能标签分析
 * POST /api/ice/ai/smarttag
 * 
 * 请求体:
 * {
 *   "mediaId": "media-id-123",
 *   "analysisType": "video|audio|image"
 * }
 * 
 * 响应:
 * {
 *   "JobId": "smarttag-job-id",
 *   "Status": "Processing"
 * }
 */

/**
 * 智能裁剪
 * POST /api/ice/ai/crop
 * 
 * 请求体:
 * {
 *   "mediaId": "media-id-123",
 *   "aspectRatio": "16:9|9:16|1:1"
 * }
 * 
 * 响应:
 * {
 *   "JobId": "crop-job-id",
 *   "Status": "Processing"
 * }
 */

// ============================================================================
// 8. 错误处理规范
// ============================================================================

/**
 * 所有API的错误响应格式:
 * HTTP状态码: 400/401/403/404/500等
 * 
 * 响应体:
 * {
 *   "success": false,
 *   "errorCode": "ICE_API_ERROR",
 *   "message": "具体错误信息",
 *   "details": "详细错误描述（可选）",
 *   "timestamp": "2024-01-01T00:00:00.000Z"
 * }
 */

// ============================================================================
// 9. 请求头规范
// ============================================================================

/**
 * 所有请求应包含以下请求头:
 * 
 * Content-Type: application/json
 * X-ICE-Region: cn-beijing (指定阿里云区域)
 * Authorization: Bearer {token} (如果需要用户认证)
 * X-Request-ID: {unique-id} (用于追踪请求)
 */

// ============================================================================
// 10. 分页参数规范
// ============================================================================

/**
 * 支持分页的接口统一使用以下参数:
 * 
 * pageNo: 页码，从1开始
 * pageSize: 每页大小，默认20，最大100
 * sortBy: 排序方式，格式为 "字段名:ASC|DESC"，如 "CreationTime:DESC"
 */

export {}; // 确保这是一个模块文件
