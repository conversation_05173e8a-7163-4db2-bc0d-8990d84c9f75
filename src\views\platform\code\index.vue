<template>
  <div class="app-container bright-dashboard">
    <!-- 顶部统计卡片 -->
    <div class="stats-container">
      <div class="stat-card total-card">
        <div class="stat-icon"><el-icon><Connection /></el-icon></div>
        <div class="stat-content">
          <div class="stat-value">{{ total || 0 }}</div>
          <div class="stat-label">机器码总数</div>
        </div>
      </div>
      <div class="stat-card active-card">
        <div class="stat-icon"><el-icon><CircleCheck /></el-icon></div>
        <div class="stat-content">
          <div class="stat-value">{{ activeCount || 0 }}</div>
          <div class="stat-label">正常运行</div>
        </div>
      </div>
      <div class="stat-card inactive-card">
        <div class="stat-icon"><el-icon><CircleClose /></el-icon></div>
        <div class="stat-content">
          <div class="stat-value">{{ inactiveCount || 0 }}</div>
          <div class="stat-label">已停用</div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="control-panel">
      <div class="panel-header">
        <div class="panel-title">
          <el-icon class="title-icon"><Search /></el-icon>
          <span>机器码管理</span>
        </div>
        <div class="panel-actions">
          <el-button-group class="view-toggle">
            <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'" class="toggle-btn"><el-icon><Grid /></el-icon></el-button>
            <el-button :type="viewMode === 'cards' ? 'primary' : ''" @click="viewMode = 'cards'" class="toggle-btn"><el-icon><Menu /></el-icon></el-button>
          </el-button-group>
          <el-button type="primary" :icon="Plus" @click="handleAdd" class="add-button">新增机器</el-button>
        </div>
      </div>
      
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="84px" class="search-form">
        <el-form-item label="机器名称" prop="machineCodeName">
          <el-input v-model="queryParams.machineCodeName" placeholder="搜索机器名称" clearable @keyup.enter="handleQuery" class="search-input">
            <template #prefix><el-icon><Search /></el-icon></template>
          </el-input>
        </el-form-item>
        <el-form-item label="机器状态" prop="machineCodeStatus">
          <el-select v-model="queryParams.machineCodeStatus" placeholder="选择状态" clearable class="search-select">
            <el-option v-for="dict in platform_machine_code_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="search-actions">
          <el-button type="primary" :icon="Search" @click="handleQuery" class="btn-query">查询</el-button>
          <el-button :icon="Refresh" @click="resetQuery" class="btn-reset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格视图 -->
    <div v-if="viewMode === 'table'" class="data-table-container">
      <div class="table-action-bar">
        <div class="left-actions">
          <el-button type="danger" plain :icon="Delete" :disabled="multiple" @click="handleDelete" class="action-btn">批量删除</el-button>
          <el-button type="warning" plain :icon="Download" @click="handleExport" class="action-btn">导出数据</el-button>
        </div>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </div>
      
      <el-table v-loading="loading" :data="codeList" @selection-change="handleSelectionChange" class="dark-table">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="序号" prop="machineCodeId" width="100" align="center" />
        <el-table-column label="机器名称" prop="machineCodeName" min-width="180" align="center" show-overflow-tooltip>
          <template #default="scope">
            <div class="machine-name">
              <el-icon><Monitor /></el-icon>
              <span>{{ scope.row.machineCodeName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="machineCodeStatus" width="100">
          <template #default="scope">
            <dict-tag :options="platform_machine_code_status" :value="scope.row.machineCodeStatus" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="200">
          <template #default="scope">
            <div class="time-info">
              <div>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}-{h}:{i}:{s}') }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备注信息" align="center" prop="remark" min-width="180" show-overflow-tooltip />
        <el-table-column label="操作" align="center" width="140">
          <template #default="scope">
            <div class="table-actions">
              <el-tooltip content="编辑机器" placement="top">
                <el-button circle type="primary" :icon="Edit" @click="handleUpdate(scope.row)" class="circle-btn edit-btn"></el-button>
              </el-tooltip>
              <el-tooltip content="删除机器" placement="top">
                <el-button circle type="danger" :icon="Delete" @click="handleDelete(scope.row)" class="circle-btn delete-btn"></el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" background />
      </div>
    </div>

    <!-- 卡片视图 -->
    <div v-else class="cards-container">
      <el-empty v-if="codeList.length === 0" description="暂无数据" />
      <div v-else class="cards-grid">
        <div v-for="item in codeList" :key="item.machineCodeId" class="machine-card">
          <div class="card-header">
            <div class="card-title">{{ item.machineCodeName }}</div>
            <dict-tag :options="platform_machine_code_status" :value="item.machineCodeStatus" class="card-tag" />
          </div>
          <div class="card-body">
            <div class="card-info">
              <div class="info-item">
                <div class="info-label">机器码:</div>
                <div class="info-value">{{ item.machineCodeId }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">创建时间:</div>
                <div class="info-value">{{ parseTime(item.createTime) }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">备注:</div>
                <div class="info-value remark-text">{{ item.remark || '暂无备注' }}</div>
              </div>
            </div>
          </div>
          <div class="card-actions">
            <el-button type="primary" :icon="Edit" @click="handleUpdate(item)" class="card-btn">编辑</el-button>
            <el-button type="danger" :icon="Delete" @click="handleDelete(item)" class="card-btn">删除</el-button>
          </div>
        </div>
      </div>
      
      <div class="pagination-container">
        <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" background />
      </div>
    </div>

    <!-- 新增或修改对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body destroy-on-close class="bright-dialog">
      <div class="dialog-content">
        <el-form ref="codeRef" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="机器名称" prop="machineCodeName">
            <el-input v-model="form.machineCodeName" placeholder="请输入机器名称">
              <template #prefix><el-icon><Monitor /></el-icon></template>
            </el-input>
          </el-form-item>
          <el-form-item label="机器状态" prop="machineCodeStatus" v-if="form.machineCodeId">
            <el-radio-group v-model="form.machineCodeStatus" class="status-radio">
              <el-radio label="1">正常</el-radio>
              <el-radio label="0">停用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注信息" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" rows="4" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel" plain>取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Code">
import { addCode, delCode, getCode, listCode, updateCode } from "@/api/platform/code";
import { Search, Refresh, Plus, Delete, Edit, Download, Monitor, Connection, CircleCheck, CircleClose, Grid, Menu } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const { platform_machine_code_status } = proxy.useDict("platform_machine_code_status");

// 数据定义
const codeList = ref([]);
const open = ref(false);
const loading = ref(true);
const submitLoading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const viewMode = ref('table');
const activeCount = ref(0);
const inactiveCount = ref(0);

const data = reactive({
  form: {},
  queryParams: { pageNum: 1, pageSize: 10, machineCodeName: null, machineCodeStatus: null },
  rules: { machineCodeName: [{ required: true, message: "机器名称不能为空", trigger: "blur" }] }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询机器管理列表 */
function getList() {
  loading.value = true;
  listCode(queryParams.value).then(response => {
    codeList.value = response.rows;
    total.value = response.total;
    // 修正统计数据计算 - 添加更多日志和防错处理
    let active = 0;
    let inactive = 0;
    // 遍历数据，确保正确统计
    codeList.value.forEach(item => {
      const status = String(item.machineCodeStatus);
      if (status === '1') {
        active++;
      } else if (status === '0') {
        inactive++;
      }
    });
    // 更新统计数据
    activeCount.value = active;
    inactiveCount.value = inactive;
    loading.value = false;
  }).catch(error => {
    console.error("获取机器码列表失败:", error);
    loading.value = false;
  });
}

// 表单重置
function reset() {
  form.value = {
    machineCodeId: null,
    machineCodeName: null,
    machineCodeStatus: "1", // 默认改为"正常"状态
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  };
  proxy.resetForm("codeRef");
}

// 取消按钮
function cancel() { open.value = false; reset(); }

/** 搜索按钮操作 */
function handleQuery() { queryParams.value.pageNum = 1; getList(); }

/** 重置按钮操作 */
function resetQuery() { proxy.resetForm("queryRef"); handleQuery(); }

// 多选框选中数据
function handleSelectionChange(selection) { 
  ids.value = selection.map(item => item.machineCodeId); 
  single.value = selection.length != 1; 
  multiple.value = !selection.length; 
}

/** 新增按钮操作 */
function handleAdd() { reset(); open.value = true; title.value = "新增机器码"; }

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _machineCodeId = row.machineCodeId || ids.value
  getCode(_machineCodeId).then(response => {
    let data = response.data;
    if (data.machineCodeStatus !== null && data.machineCodeStatus !== undefined) {
      data.machineCodeStatus = String(data.machineCodeStatus);
    }
    form.value = data;
    open.value = true;
    title.value = "修改机器码";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["codeRef"].validate(valid => {
    if (valid) {
      submitLoading.value = true;
      
      if (form.value.machineCodeId != null) {
        updateCode(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
          submitLoading.value = false;
        }).catch(() => { submitLoading.value = false; });
      } else {
        addCode(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
          submitLoading.value = false;
        }).catch(() => { submitLoading.value = false; });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _machineCodeIds = row.machineCodeId || ids.value;
  proxy.$modal.confirm('是否确认删除机器码编号为"' + _machineCodeIds + '"的数据项？').then(() => {
    return delCode(_machineCodeIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() { proxy.download('platform/code/export', {...queryParams.value}, `machine_code_${new Date().getTime()}.xlsx`) }

// 确保页面加载后立即获取数据
onMounted(() => {
  getList();
});

getList();
</script>

<style lang="scss" scoped>
.bright-dashboard { background-color: #f2f5fc; border-radius: 12px; padding: 24px; min-height: calc(100vh - 110px); color: #36395a; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05); }
/* 统计卡片区域 */
.stats-container { display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px; margin-bottom: 24px; }
.stat-card { display: flex; align-items: center; background: #ffffff; border-radius: 14px; padding: 20px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); transition: transform 0.3s ease, box-shadow 0.3s ease; border-left: 5px solid transparent; }
.stat-card:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08); }
.total-card { border-left-color: #6366f1; }
.active-card { border-left-color: #10b981; }
.inactive-card { border-left-color: #f43f5e; }
.stat-icon { display: flex; justify-content: center; align-items: center; font-size: 24px; width: 56px; height: 56px; border-radius: 50%; margin-right: 20px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); }
.total-card .stat-icon { background: linear-gradient(135deg, #6366f1 0%, #818cf8 100%); color: white; }
.active-card .stat-icon { background: linear-gradient(135deg, #10b981 0%, #34d399 100%); color: white; }
.inactive-card .stat-icon { background: linear-gradient(135deg, #f43f5e 0%, #fb7185 100%); color: white; }
.stat-content { flex-grow: 1; }
.stat-value { font-size: 28px; font-weight: 700; color: #36395a; margin-bottom: 5px; }
.stat-label { font-size: 14px; color: #6b7280; font-weight: 500; }
/* 控制面板 */
.control-panel { background-color: #ffffff; border-radius: 14px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); padding: 24px; margin-bottom: 24px; }
.panel-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #e5e7eb; padding-bottom: 16px; }
.panel-title { display: flex; align-items: center; font-size: 18px; font-weight: 600; color: #36395a; }
.title-icon { margin-right: 10px; color: #6366f1; font-size: 20px; }
.panel-actions { display: flex; gap: 15px; align-items: center; }
.view-toggle { background-color: #f3f4f6; border-radius: 8px; overflow: hidden; }
.toggle-btn { height: 36px; color: #6b7280; border: none; }
.toggle-btn:hover { color: #36395a; background-color: #e5e7eb; }
.search-form { display: flex; flex-wrap: wrap; gap: 15px; align-items: flex-start; }
.search-input, .search-select { width: 220px; background-color: #f9fafb; border-radius: 8px; }
.search-input :deep(.el-input__wrapper), .search-select :deep(.el-input__wrapper) { background-color: #f9fafb; box-shadow: 0 0 0 1px #e5e7eb inset; border-radius: 8px; }
.search-input :deep(.el-input__inner), .search-select :deep(.el-input__inner) { color: #36395a; }
.search-input :deep(.el-input__prefix-inner i), .search-select :deep(.el-select__prefix i) { color: #6366f1; }
.search-actions { display: flex; gap: 10px; }
.btn-query { background: linear-gradient(135deg, #6366f1 0%, #818cf8 100%); border: none; box-shadow: 0 4px 10px rgba(99, 102, 241, 0.2); }
.btn-query:hover { box-shadow: 0 6px 15px rgba(99, 102, 241, 0.3); transform: translateY(-2px); }
.btn-reset { background-color: #f9fafb; border-color: #e5e7eb; color: #6b7280; }
.btn-reset:hover { border-color: #d1d5db; color: #36395a; background-color: #f3f4f6; }
.add-button { background: linear-gradient(135deg, #10b981 0%, #34d399 100%); border: none; padding: 10px 20px; box-shadow: 0 4px 10px rgba(16, 185, 129, 0.2); }
.add-button:hover { box-shadow: 0 6px 15px rgba(16, 185, 129, 0.3); transform: translateY(-2px); }
/* 表格视图 */
.data-table-container { background-color: #ffffff; border-radius: 14px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); padding: 24px; margin-bottom: 24px; }
.table-action-bar { display: flex; justify-content: space-between; margin-bottom: 20px; }
.left-actions { display: flex; gap: 12px; }
.action-btn { border-radius: 8px; transition: all 0.3s ease; font-weight: 500; height: 40px; }
.dark-table { background-color: transparent; color: #36395a; border-radius: 10px; overflow: hidden; }
.dark-table :deep(.el-table__header) { background-color: #f8fafc; }
.dark-table :deep(.el-table__header-wrapper) { border-radius: 8px 8px 0 0; overflow: hidden; }
.dark-table :deep(th.el-table__cell) { background-color: #f8fafc !important; color: #36395a; font-weight: 600; border-bottom: 2px solid #e5e7eb; padding: 16px 0; }
.dark-table :deep(.el-table__row) { background-color: #ffffff; color: #36395a; }
.dark-table :deep(.el-table__row:hover > td) { background-color: #f1f5f9; }
.dark-table :deep(.el-table__body tr:nth-child(even)) { background-color: #f9fafb; }
.dark-table :deep(.el-table__body) { border: none; }
.dark-table :deep(.el-table__cell) { border-bottom: 1px solid #e5e7eb; padding: 12px 0; }
.dark-table :deep(.el-table__empty-block) { background-color: #ffffff; }
.dark-table :deep(.el-table__empty-text) { color: #6b7280; }
.machine-name { display: flex; align-items: center; justify-content: center; gap: 8px; }
.status-tag { border-radius: 6px; font-weight: 500; padding: 4px 8px; }
.table-actions { display: flex; justify-content: center; gap: 10px; }
.circle-btn { width: 36px; height: 36px; padding: 0; font-size: 14px; border: none; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); }
.edit-btn { background-color: #6366f1; }
.delete-btn { background-color: #f43f5e; }
.circle-btn:hover { transform: translateY(-3px); box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15); }
.time-info { display: flex; flex-direction: column; align-items: center; }
.time-detail { font-size: 12px; color: #6b7280; margin-top: 5px; }
/* 卡片视图 */
.cards-container { margin-bottom: 24px; }
.cards-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 24px; }
.machine-card { background: #ffffff; border-radius: 14px; overflow: hidden; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); transition: transform 0.3s ease, box-shadow 0.3s ease; display: flex; flex-direction: column; }
.machine-card:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1); }
.card-header { display: flex; justify-content: space-between; align-items: center; padding: 16px 20px; border-bottom: 1px solid #f3f4f6; background-color: #f9fafb; }
.card-title { font-size: 16px; font-weight: 600; color: #36395a; }
.card-tag { font-size: 12px; border-radius: 6px; padding: 4px 8px; }
.card-body { padding: 20px; flex-grow: 1; }
.card-info { display: flex; flex-direction: column; gap: 12px; }
.info-item { display: flex; border-bottom: 1px dashed #f3f4f6; padding-bottom: 8px; }
.info-item:last-child { border-bottom: none; }
.info-label { width: 80px; color: #6b7280; flex-shrink: 0; font-weight: 500; }
.info-value { color: #36395a; flex-grow: 1; }
.remark-text { max-height: 60px; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; }
.card-actions { display: flex; justify-content: space-between; padding: 16px; border-top: 1px solid #f3f4f6; background-color: #f9fafb; }
.card-btn { flex: 1; margin: 0 5px; font-size: 13px; padding: 8px 0; font-weight: 500; }
/* 分页控件 */
.pagination-container { display: flex; justify-content: flex-end; padding-top: 20px; }
.pagination-container :deep(.el-pagination.is-background .el-pager li:not(.is-disabled)) { background-color: #ffffff; color: #36395a; border: 1px solid #e5e7eb; border-radius: 6px; margin: 0 2px; }
.pagination-container :deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) { background-color: #6366f1; border-color: #6366f1; }
.pagination-container :deep(.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev) { background-color: #ffffff; color: #36395a; border: 1px solid #e5e7eb; border-radius: 6px; }
/* 对话框 */
.bright-dialog :deep(.el-dialog) { border-radius: 14px; overflow: hidden; }
.bright-dialog :deep(.el-dialog__header) { background: linear-gradient(90deg, #6366f1, #818cf8); color: #ffffff; padding: 20px; }
.bright-dialog :deep(.el-dialog__title) { color: #ffffff; font-weight: 600; font-size: 18px; }
.bright-dialog :deep(.el-dialog__close) { color: rgba(255, 255, 255, 0.8); }
.bright-dialog :deep(.el-dialog__close:hover) { color: #ffffff; transform: rotate(90deg); }
.bright-dialog :deep(.el-dialog__body) { background-color: #ffffff; padding: 30px; color: #36395a; }
.bright-dialog :deep(.el-dialog__footer) { background-color: #f9fafb; padding: 16px 20px; border-top: 1px solid #f3f4f6; }
.dialog-content :deep(.el-form-item__label) { color: #36395a; font-weight: 500; }
.dialog-content :deep(.el-input__wrapper), .dialog-content :deep(.el-textarea__inner) { background-color: #f9fafb; color: #36395a; box-shadow: 0 0 0 1px #e5e7eb inset; border-radius: 8px; }
.dialog-content :deep(.el-input__inner), .dialog-content :deep(.el-textarea__inner) { color: #36395a; }
.dialog-content :deep(.el-radio__label) { color: #36395a; }
.status-radio :deep(.el-radio__input.is-checked .el-radio__inner) { border-color: #10b981; background: #10b981; }
.status-radio :deep(.el-radio__input.is-checked+.el-radio__label) { color: #10b981; }
</style>
