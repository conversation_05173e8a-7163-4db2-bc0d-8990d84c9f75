import request from '@/utils/request'

// 查询分类列表
export function listCategory(query) {
  return request({
    url: '/platform/category/list',
    method: 'get',
    params: query
  })
}

// 查询分类详细
export function getCategory(categoryId) {
  return request({
    url: '/platform/category/' + categoryId,
    method: 'get'
  })
}

// 新增分类
export function addCategory(data) {
  return request({
    url: '/platform/category',
    method: 'post',
    data: data
  })
}

// 修改分类
export function updateCategory(data) {
  return request({
    url: '/platform/category',
    method: 'put',
    data: data
  })
}

// 删除分类
export function delCategory(categoryId, isForceDelete = false) {
  return request({
    url: '/platform/category/' + categoryId,
    method: 'delete',
    params: { isForceDelete: isForceDelete }
  })
}

//获取分类信息
export function getCategorys(data) {
  return request({
    url: '/platform/category/categoryIdsAndName',
    method: 'get',
    params:{
      categoryId:data
    },
  })
}

// 检查分类下是否有资源
export function checkCategoryHasResources(categoryId) {
  return request({
    url: '/platform/category/checkResources/' + categoryId,
    method: 'get'
  }).then(response => response.data);
}

