<script setup>
import { listGoods, getGoods, delGoods, addGoods, updateGoods } from "@/api/platform/goods";
import { useRoute, useRouter } from "vue-router";
const props = defineProps({
  categorys: {
    type: Object,
    default: () => { return {} }
  }
})
const { proxy } = getCurrentInstance();
const route = useRoute()
const goodsList = ref([]);
const open = ref(false);
const loading = ref(true);
const total = ref(0);
const title = ref("");
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: null,
    goodsName: null,
    goodsInteractionId: [],
    goodsQuestionsId: [],
  },
  rules: {
    goodsInteractionId: [
      { required: true, message: "请选择互动分类", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (value.length <= 0) {
            return callback(new Error("请选择互动分类"))
          } else {
            callback()
          }
        }, trigger: "blur"
      }
    ],
    goodsQuestionsId: [
      { required: true, message: "请选择问答分类", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (value.length <= 0) {
            return callback(new Error("请选择互动分类"))
          } else {
            callback()
          }
        }
      }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);
async function getList() {
  loading.value = true;
  queryParams.value.projectId = route.params.projectId
  const response = await listGoods(queryParams.value)
  goodsList.value = response.rows;
  loading.value = false;
}
function handleAdd() {
  open.value = true;
  title.value = "添加产品管理";
}
getList()

function submitForm() {
  proxy.$refs["goodsRef"].validate((valid, fields) => {
    if (valid) {
      if (form.value.goodsId != null) {
        updateGoods(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        form.value.projectId = parseInt(route.params.projectId)
        addGoods(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    } else {
      for (let field in fields) {
        fields[field].forEach(i => {
          proxy.$modal.msgError(i.message)
        })
      }
    }
  });
}

async function handleUpdate(row) {
  const res = await getGoods(row.goodsId).then(res => res.data);
  form.value = res
  open.value = true;
  title.value = "修改产品管理";
}

function handleDelete(row) {
  const _goodsIds = row.goodsId;
  proxy.$modal.confirm('是否确认删除产品名称为"' + row.goodsName + '"的数据项？').then(function () {
    return delGoods(_goodsIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  })
}
</script>
<template>
  <el-card class="product-panel" body-style="overflow-y: auto; height:calc(100% - 50px); padding: 0;">
    <template #header>
      <div class="product-header">
        <div class="product-title">
          <div class="main-title"><i class="el-icon-shopping-cart-full"></i> 产品管理</div>
          <div class="sub-title">管理直播间推广产品</div>
        </div>
        <el-button type="primary" class="add-button" @click="handleAdd">
          <i class="el-icon-plus"></i>创建产品
        </el-button>
      </div>
    </template>
    
    <div class="product-body">
      <el-empty description="暂无产品数据" v-if="goodsList.length <= 0" />
      
      <div v-loading="loading" class="product-grid-container" v-else>
        <div class="product-grid">
          <div v-for="goods in goodsList" :key="goods.goodsId" class="product-card-wrapper">
            <div class="product-card">
              <div class="product-card-header">
                <div class="product-name">{{ goods.goodsName }}</div>
                <el-tag size="small" class="product-tag">产品</el-tag>
              </div>
              
              <div class="product-card-content">
                <div class="content-section">
                  <div class="section-row">
                    <span class="section-label"><i class="el-icon-magic-stick"></i> 互动：</span>
                    <div class="tags-container">
                      <el-tag 
                        v-for="(item, index) in goods.goodsInteractionId" 
                        :key="`inter-${index}`"
                        class="category-tag" 
                        size="small"
                        type="success" 
                        effect="plain"
                      >
                        {{ categorys[item] }}
                      </el-tag>
                      <span class="no-data" v-if="goods.goodsInteractionId.length === 0">暂无</span>
                    </div>
                  </div>
                  
                  <div class="section-row">
                    <span class="section-label"><i class="el-icon-chat-dot-square"></i> 问答：</span>
                    <div class="tags-container">
                      <el-tag 
                        v-for="(item, index) in goods.goodsQuestionsId" 
                        :key="`ques-${index}`"
                        class="category-tag" 
                        size="small"
                        type="info" 
                        effect="plain"
                      >
                        {{ categorys[item] }}
                      </el-tag>
                      <span class="no-data" v-if="goods.goodsQuestionsId.length === 0">暂无</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="product-card-footer">
                <el-button type="primary" size="small" @click="handleUpdate(goods)">
                  <i class="el-icon-edit"></i> 编辑
                </el-button>
                <el-button type="danger" size="small" @click="handleDelete(goods)">
                  <i class="el-icon-delete"></i> 删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <el-dialog 
      :title="title" 
      v-model="open" 
      width="800px" 
      append-to-body 
      @update:modelValue="form = {}" 
      destroy-on-close
      class="product-dialog"
    >
      <el-form ref="goodsRef" :model="form" :rules="rules" label-width="120px" class="product-form">
        <el-form-item label="产品名称" prop="goodsName">
          <el-input v-model="form.goodsName" placeholder="请输入产品名称" class="custom-input" />
        </el-form-item>
        
        <el-form-item label="产品互动分类" prop="goodsInteractionId">
          <div class="category-select-container">
            <div class="category-description">
              <i class="el-icon-connection"></i> 会主动触发所选分类下的音频
            </div>
            <el-checkbox-group v-model="form.goodsInteractionId" class="checkbox-group">
              <el-checkbox 
                v-for="(k, v) in categorys" 
                :key="`form-inter-${v}`" 
                :label="k" 
                :value="+v" 
                class="category-checkbox"
              />
            </el-checkbox-group>
            <div v-if="JSON.stringify(categorys)=='{}'" class="no-category-data">
              <i class="el-icon-warning"></i> 暂无分类数据
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="产品问答分类" prop="goodsQuestionsId">
          <div class="category-select-container">
            <div class="category-description">
              <i class="el-icon-chat-line-round"></i> 弹幕中涉及所选分类关键词时会触发该分类下的音频
            </div>
            <el-checkbox-group v-model="form.goodsQuestionsId" class="checkbox-group">
              <el-checkbox 
                v-for="(k, v) in categorys" 
                :key="`form-ques-${v}`" 
                :label="k" 
                :value="+v" 
                class="category-checkbox"
              />
            </el-checkbox-group>
            <div v-if="JSON.stringify(categorys)=='{}'" class="no-category-data">
              <i class="el-icon-warning"></i> 暂无分类数据
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" class="custom-textarea" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open = false" class="cancel-btn">取 消</el-button>
          <el-button type="primary" @click="submitForm" class="submit-btn">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </el-card>
</template>

<style lang="scss" scoped>
.product-panel { height: 80vh; border-radius: 8px; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06); border: 1px solid #e8eaec; }
.product-header { display: flex; justify-content: space-between; align-items: center; padding: 12px 16px; background: linear-gradient(to right, #fffbf0 0%, #fff8ec 100%); border-bottom: 1px solid #f3e6d0; }
.product-title { display: flex; flex-direction: column; }
.main-title { font-size: 16px; font-weight: 600; color: #303133; display: flex; align-items: center; gap: 6px; }
.main-title i { font-size: 16px; color: #e6a23c; background-color: rgba(230, 162, 60, 0.1); padding: 4px; border-radius: 4px; }
.sub-title { font-size: 12px; color: #606266; margin-top: 2px; }
.add-button { padding: 6px 12px; border-radius: 4px; font-weight: 500; transition: all 0.2s; background: #e6a23c; border: none; }
.add-button:hover { background: #ebb563; transform: translateY(-2px); box-shadow: 0 2px 6px rgba(230, 162, 60, 0.25); }
.product-body { padding: 12px; background-color: #f8f8fc; }
.product-grid-container { min-height: 200px; transition: all 0.3s ease; }
.product-grid { display: flex; flex-wrap: wrap; gap: 16px; }
.product-card-wrapper { position: relative; margin-bottom: 5px; }
.product-card { 
  width: 270px; 
  border-radius: 6px; 
  background: white; 
  box-shadow: 0 2px 8px rgba(0,0,0,0.04); 
  transition: all 0.25s ease; 
  height: 100%; 
  display: flex; 
  flex-direction: column; 
  border: 1px solid #ebeef5; 
  position: relative;
  overflow: hidden;
}
.product-card:hover { 
  transform: translateY(-3px); 
  box-shadow: 0 6px 12px rgba(0,0,0,0.08); 
  border-color: #f5dab1; 
}
.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #e6a23c;
  opacity: 0.6;
}
.product-card-header { 
  padding: 10px 12px; 
  border-bottom: 1px solid #ebeef5; 
  display: flex; 
  justify-content: space-between; 
  align-items: center; 
  background: linear-gradient(to right, #fdf8ef, #fff9f0);
}
.product-name { 
  font-size: 14px; 
  font-weight: 600; 
  color: #303133; 
  overflow: hidden; 
  text-overflow: ellipsis; 
  white-space: nowrap; 
  flex: 1; 
}
.product-tag { 
  font-size: 11px; 
  padding: 0 6px; 
  height: 20px; 
  line-height: 18px; 
  color: #fff; 
  background-color: #e6a23c; 
  border: none;
  border-radius: 10px;
}
.product-card-content { padding: 10px 12px; flex: 1; background-color: #ffffff; }
.content-section { display: flex; flex-direction: column; gap: 10px; }
.section-row { display: flex; align-items: flex-start; margin-bottom: 2px; }
.section-label { 
  font-size: 12px; 
  font-weight: 500; 
  color: #606266; 
  white-space: nowrap; 
  display: flex; 
  align-items: center; 
  gap: 4px; 
  width: 55px; 
  background: rgba(230, 162, 60, 0.05);
  padding: 2px 4px;
  border-radius: 3px;
}
.section-label i { font-size: 12px; color: #e6a23c; }
.tags-container { flex: 1; display: flex; flex-wrap: wrap; gap: 4px; align-items: center; }
.category-tag { 
  margin: 1px 2px; 
  font-size: 11px; 
  padding: 0 6px; 
  height: 18px; 
  line-height: 16px; 
  border-radius: 9px;
  border: none;
}
.category-tag.el-tag--success { 
  background-color: rgba(103, 194, 58, 0.1); 
  color: #67c23a; 
}
.category-tag.el-tag--info { 
  background-color: rgba(144, 147, 153, 0.1); 
  color: #909399; 
}
.no-data { 
  font-size: 11px; 
  color: #909399; 
  font-style: italic; 
  display: inline-block;
  padding: 2px 6px;
  background: #f5f7fa;
  border-radius: 3px;
}
.product-card-footer { 
  display: flex; 
  justify-content: space-between; 
  gap: 8px; 
  padding: 8px 12px; 
  border-top: 1px solid #ebeef5; 
  background: #f8f9fc; 
}
.product-card-footer .el-button {
  flex: 1;
  font-size: 12px;
  padding: 6px 0;
  border-radius: 4px;
}
.product-card-footer .el-button--primary {
  background-color: #e6a23c;
  border-color: #e6a23c;
}
.product-card-footer .el-button--primary:hover {
  background-color: #ebb563;
  border-color: #ebb563;
  box-shadow: 0 2px 6px rgba(230, 162, 60, 0.25);
}
.product-card-footer .el-button--danger {
  background-color: #fff;
  border-color: #f56c6c;
  color: #f56c6c;
}
.product-card-footer .el-button--danger:hover {
  background-color: #f56c6c;
  color: #fff;
}

/* 对话框样式保持不变 */
.category-select-container { border: 1px solid #e4e7ed; border-radius: 8px; padding: 16px; background: #f9fafc; }
.category-description { font-size: 13px; margin-bottom: 16px; color: #606266; display: flex; align-items: center; gap: 6px; }
.category-description i { color: #e6a23c; font-size: 16px; }
.checkbox-group { display: flex; flex-wrap: wrap; gap: 12px; }
.category-checkbox { background: white; border-radius: 6px; padding: 8px 12px; margin: 0 !important; transition: all 0.2s; }
.category-checkbox:hover { background: #fdf6ec; }
.no-category-data { color: #f56c6c; font-size: 13px; margin-top: 16px; display: flex; align-items: center; gap: 6px; }
.dialog-footer { display: flex; justify-content: center; gap: 24px; padding-top: 10px; }
</style>