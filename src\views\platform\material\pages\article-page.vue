<script setup>
import { addArticle, delArticle, generateArticle, listArticle, updateArticle, batchAddArticles } from "@/api/platform/article";
import { listSound } from "@/api/platform/sound";
import { createTask, getTask, listTask } from "@/api/platform/task";
import modal from "@/plugins/modal";
import useUserStore from '@/store/modules/user';
import { ElMessage } from "element-plus";
import { toRefs, watch, nextTick } from "vue";
import { useRoute, useRouter } from 'vue-router';
import { DocumentCopy, Check, Edit, Delete, Mic, MagicStick, Download, Select } from '@element-plus/icons-vue'
import SoundSelect from '@/components/SoundSelect/index.vue'
import { inject } from 'vue'

const route = useRoute();
const router = useRouter()
const props = defineProps({
  categoryId: {
    type: Number,
  }
})

const isFixed = ref(false);
const capsulePosition = ref({ x: 0, y: 0 });
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });

const userStore = useUserStore()
const audioButtonRef = ref(null)
const navbarRef = inject('navbarRef')

// 计算初始位置
function initCapsulePosition() {
  const pageWidth = window.innerWidth;
  capsulePosition.value = {
    x: (pageWidth - 200) / 2,
    y: 80
  };
}

function handleDragStart(e) {
  if (e.target.classList.contains('capsule-btn')) return;
  isDragging.value = true;
  dragOffset.value = {
    x: e.clientX - capsulePosition.value.x,
    y: e.clientY - capsulePosition.value.y
  };
  e.preventDefault();
}

function handleDrag(e) {
  if (!isDragging.value) return;

  // 计算新位置
  let newX = e.clientX - dragOffset.value.x;
  let newY = e.clientY - dragOffset.value.y;

  // 限制范围
  const maxWidth = window.innerWidth - 200;
  const maxHeight = window.innerHeight - 60;

  newX = Math.max(0, Math.min(maxWidth, newX));
  newY = Math.max(0, Math.min(maxHeight, newY));

  capsulePosition.value = { x: newX, y: newY };
}

function handleDragEnd() {
  isDragging.value = false;
}

function handleScroll(e) {
  const mainActions = document.querySelector('.main-actions');
  if (mainActions) {
    const mainActionsRect = mainActions.getBoundingClientRect();
    const tabsContent = document.querySelector('.tabs-content');
    if (tabsContent) {
      const tabsRect = tabsContent.getBoundingClientRect();

      // 计算剩余可滚动高度
      const remainingHeight = tabsContent.scrollHeight - (tabsContent.scrollTop + tabsContent.clientHeight);

      // 检查mainActions是否完全离开可视区域
      const isMainActionsOutOfView = mainActionsRect.bottom < tabsRect.top;

      // 如果mainActions完全离开可视区域,保持悬浮状态
      if (isMainActionsOutOfView) {
        isFixed.value = true;
      } else {
        // 否则,只有当剩余高度足够时才允许切换状态
        if (remainingHeight > 100) {
          isFixed.value = mainActionsRect.top < (tabsRect.top + 20);
        } else {
          // 如果剩余高度不足且mainActions还部分可见,保持非悬浮状态
          isFixed.value = false;
        }
      }
    }
  }
}

onMounted(() => {
  initCapsulePosition();
  isFixed.value = false;
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', handleDragEnd);
  window.addEventListener('resize', initCapsulePosition);

  // 滚动监听
  const tabsContent = document.querySelector('.tabs-content');
  if (tabsContent) {
    tabsContent.addEventListener('scroll', handleScroll);
    // 初始化时触发一次滚动检查
    handleScroll();
  }
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', handleDragEnd);
  window.removeEventListener('resize', initCapsulePosition);

  const tabsContent = document.querySelector('.tabs-content');
  if (tabsContent) {
    tabsContent.removeEventListener('scroll', handleScroll);
  }
});

const { proxy } = getCurrentInstance();
const articleList = ref([]);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const drawer = ref(false);
const isSelectAll = ref(false);
const showTextToAudio = ref(false);

//是否编辑框出现
const isUpdateShow = ref(false);
function clickUpdate(val) {
  val.isEdit = true;
}
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    categoryId: null,
    content: null,
  },
  rules: {
    categoryId: [{ required: true, message: "分类ID不能为空", trigger: "blur" }],
    content: [{ required: true, message: "内容不能为空", trigger: "blur" }],
  },
});

const { queryParams, rules } = toRefs(data);
const addForm = ref({
  categoryId: null,
  content: null,
})

const selectedSound = ref('')

/** 查询文案列表 */
function getList() {
  queryParams.value.categoryId = props.categoryId;
  addForm.value.categoryId = props.categoryId;
  aiCategoryId.value = props.categoryId
  loading.value = true;
  console.log(articleList.value);

  listArticle(queryParams.value).then((response) => {
    articleList.value = response.rows;
    articleList.value.forEach(item => {
      item.isEdit = false
      item.isCopy = false
    });
    total.value = response.total;
    loading.value = false;
  });
}
//重置抽屉
function resetDrawer() {
  addForm.value = {
    categoryId: props.categoryId,
    content: exceedContent.value ? exceedContent.value : null,
  };
  proxy.resetForm("drawerRef");
  drawer.value = false;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  aiCategoryId.value = selection.length === 0 ? aiCategoryId.value : selection[0].categoryId
  articles.value = selection.map(item => ({
    articleId: item.articleId,
    content: item.content
  }));
  ids.value = selection.map((item) => item.articleId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}


/** 修改按钮操作 */
function handleUpdate(row) {
  // 清除所有空格
  if (row.content) {
    row.content = row.content.replace(/\s/g, '');
  }
  if (!row.content || row.content.length > 500) {
    ElMessage({ message: row.content ? '内容不能超过500个字符' : '内容不能为空', type: 'warning' });
    return;
  }
  let query = {
    articleId: row.articleId,
    content: row.content,
  }
  updateArticle(query).then((response) => {
    proxy.$modal.msgSuccess("编辑成功");
    row.isEdit = false;
    getList();
  });
}


/** 删除按钮操作 */
function handleDelete(row) {
  const _articleIds = row.articleId || ids.value;
  delArticle(_articleIds).then(() => {
    getList();
    ids.value = [];
    isSelectAll.value = false;
    proxy.$modal.msgSuccess("文案删除成功");
  });
}

/** 导出文案操作 文本txt*/
function handleExportTxt() {
  const x = route.params.projectId
  proxy.download('platform/article/exportTxt', {
    projectId: x
  }, `article_${new Date().getTime()}.txt`)
}

// 右侧抽屉部分
function openDrawer() {
  if (addForm.value.categoryId) {
    drawer.value = true;
  } else {
    proxy.$modal.msgError("请您先选择文案分类，或者创建一个");
    return;
  }
}
const batchType = ref(true)
const exceedContent = ref('')
//抽屉中ctrl + enter提交
function keyDown(e) {
  if (e.ctrlKey && e.keyCode == 13) confirmClick()
}

//抽屉中按钮提交
function confirmClick() {
  console.log(batchType.value)
  if (!batchType.value) {
    let content = addForm.value.content;
    let regex = /([^.!?。！？]+[.!?。！？])/g;
    let sentences = content.match(regex);
    if (sentences) {
      batchAdd(sentences, item => item.trim());
    }

  } else {
    let content = addForm.value.content.split("\n")
    batchAdd(content, item => item.trim());
  }
}
// 批量添加
function batchAdd(items, trimItem) {
  exceedContent.value = '';
  const validItems = [];
  items.forEach(item => {
    let trimContent = trimItem(item);
    if (trimContent.length > 500) {
      exceedContent.value += trimContent + '\n';
    } else {
      validItems.push({ ...addForm.value, content: trimContent });
    }
  });
  if (validItems.length > 0) {
    batchAddArticles(validItems).then((response) => {
      if (response.code === 200) {
        ElMessage({ message: '批量新增成功', plain: true, grouping: true, type: 'success' });
        getList();
        resetDrawer();
      }
    })
  }
  if (exceedContent.value) {
    ElMessage({ message: '部分文案超过500个字符，未提交', type: 'warning' });
    addForm.value.content = exceedContent.value;
  }
}

//主页中enter提交
function submit() {
  add(addForm.value)
}
function add(row) {
  if (!props.categoryId || props.categoryId == 0 || props.categoryId == -1) {
    proxy.$modal.msgError("请您先选择文案分类，或者创建一个");
    return
  }
  // 清除所有空格
  if (row.content) {
    row.content = row.content.replace(/\s/g, '');
  }
  if (!row.content || row.content.length > 100) {
    ElMessage({ message: row.content ? '内容不能超过100个字符' : '内容不能为空', type: 'warning' });
    return;
  }
  addArticle(row).then((response) => {
    ElMessage({ message: '新增成功', plain: true, grouping: true, type: 'success', })
    getList();
  });

  resetDrawer();
}
//批量提交类型切换
function selectBatchType(type) {
  batchType.value = (type === 'line');
  confirmClick()
}
const copyIcon = ref('DocumentCopy');
const successIcon = ref('Check')
function copyTextSuccess(row) {
  row.isCopy = true
  ElMessage.success('复制成功');
  setTimeout(() => {
    row.isCopy = false
  }, 1000);
}
const aiFlag = ref(false)
const articles = ref([])
const aiArticles = ref([])
const aiLoading = ref(true)
const aiCategoryId = ref(0)
const aiContent = ref([])
function openAI() {
  if (articles.value.length !== 0) {
    aiContent.value = [];
    for (let i = 0; i < articles.value.length; i++) {
      aiContent.value.push(articles.value[i].content);
    }
    aiFlag.value = true
    generateData(aiContent.value)
  } else {
    aiLoading.value = false
    ElMessage({ message: '请您选择一条文案或者多条，使用智能改写文案！', type: 'warning' });
  }
}
function addAIArticle(row) {
  if (!row.isAdd) {
    aiArticles.value = aiArticles.value.filter(item => item.content !== row.content);
    row.isAdd = true
    aiArticles.value.push(row);
    aiContent.value.push(row.content)
    if (aiContent.value.length > 10) {
      aiContent.value = aiContent.value.slice(-10);
    }
    let content = { content: row.content, categoryId: aiCategoryId.value }
    add(content)
  } else {
    ElMessage({ message: '当前文案已采纳', type: 'warning' });
  }

}
function closeAIDialog(flag) {
  aiArticles.value = []
  getList()
  aiFlag.value = false
}
function addPlusArticle() {
  let count = 0;
  aiArticles.value.forEach(item => {
    if (item.isAdd) {
      count++;
    } else {
      item.isAdd = true;
      let content = { content: item.content, categoryId: aiCategoryId.value };
      aiContent.value.push(item.content)
      if (aiContent.value.length > 10) {
        aiContent.value = aiContent.value.slice(-10);
      }
      add(content);
    }
  });
  if (count === aiArticles.value.length) {
    ElMessage({ message: '所有文案都已经采纳', type: 'warning' });
  }
}
// 生成智能文案
function generateData(data) {
  if (data.length === 0) {
    aiLoading.value = false
    modal.msgWarning('请选择较长或语义丰富的文案')
    return
  }
  aiLoading.value = true
  aiArticles.value = []
  generateArticle(data).then(res => {
    if (res.rows.length === 0) {
      aiLoading.value = false
      modal.msgWarning('请选择较长或语义丰富的文案')
    }
    res.rows.forEach(item => {
      aiArticles.value.push({ content: item, isCopy: false, isAdd: false })
    });
    aiLoading.value = false
  }).catch(error => {
    aiLoading.value = false
    modal.msgWarning('请选择较长或语义丰富的文案')
  });
}

// 文案转音频
const handleAudioConvert = async () => {
  if (articles.value.length === 0) {
    ElMessage({ message: '请您选择一条或多条文案使用该功能！', type: 'warning' });
    return;
  }

  if (!selectedSound.value) {
    ElMessage({ message: '请先选择声音', type: 'warning' });
    return;
  }

  try {
    // 创建任务
    const res = await createTask(selectedSound.value, articles.value);

    // 添加到任务列表并打开面板
    if (navbarRef.value) {
      // 先打开任务列表面板
      navbarRef.value.openTaskHistory();

      // 延迟添加新任务，等待面板完全打开后再添加
      setTimeout(() => {
        navbarRef.value.addNewTask({
          taskId: res.data,
          taskStatus: 0, // 初始状态为待处理
          modelName: selectedSound.value,
          createTime: new Date().toISOString(),
          isNew: true
        });
      }, 500);
    } else {
      console.error('未找到 Navbar 组件引用');
    }

    // 清除选中的文案
    ids.value = [];
    handleSelectionChange([]);
    isSelectAll.value = false;

    // 开始轮询任务状态
    startTask(res.data);

  } catch (error) {
    console.error(error);
    ElMessage.error('创建任务失败');
  }
}

// 修改轮询逻辑
function startTask(id, interval = 5000) {
  const polling = setInterval(() => {
    getTask(id).then(res => {
      if (res.data.taskStatus === 2 || res.data.taskStatus === 3) {
        clearInterval(polling);
      }
    }).catch(error => {
      console.error(error);
      clearInterval(polling);
    });
  }, interval);
}

//处理声音模型展示
function handleSound(id) {
  for (const item of sounds.value) {
    if (item.value == id) {
      return item.label;
    }
  }
  return '训练声音';
}

watch(() => props.categoryId, (newId) => {
  if (props.categoryId !== undefined || props.categoryId == -1) getList();
}, { immediate: true });

const handleCardSelect = (item) => {
  const index = ids.value.indexOf(item.articleId);
  if (index === -1) {
    ids.value.push(item.articleId);
  } else {
    ids.value.splice(index, 1);
  }
  handleSelectionChange(articleList.value.filter(article =>
    ids.value.includes(article.articleId)
  ));
};

/** 全选按钮操作 */
const handleSelectAll = () => {
  isSelectAll.value = !isSelectAll.value;
  if (isSelectAll.value) {
    ids.value = articleList.value.map(item => item.articleId);
  } else {
    ids.value = [];
  }
  handleSelectionChange(articleList.value.filter(article =>
    ids.value.includes(article.articleId)
  ));
}

</script>
<template>
  <div style="position: relative;">
    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" @submit.prevent="handleQuery">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="搜索内容" prop="content" label-width="80px">
            <el-input v-model="queryParams.content" placeholder="请输入内容" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">
              <span class="button-text">搜索</span>
            </el-button>
            <el-button icon="Refresh" @click="resetQuery">
              <span class="button-text">重置</span>
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="20">
      <el-col :span="16">
        <el-form :model="addForm" label-width="80px" ref="drawerRef" @submit.prevent="submit">
          <el-form-item label="文案内容" prop="content">
            <el-input v-model="addForm.content" placeholder="请输入文案内容,enter可直接提交" maxlength="100" show-word-limit />
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="8">
        <div class="action-buttons">
          <el-button type="warning" plain icon="DocumentAdd" @click="submit">
            <span class="button-text">新增</span>
          </el-button>
          <el-button type="primary" plain icon="Plus" @click="openDrawer">
            <span class="button-text">批量新增</span>
          </el-button>
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">
            <span class="delete-button-text">批量删除</span>
          </el-button>
        </div>
      </el-col>
    </el-row>

    <div class="content-wrapper">
      <div class="floating-capsule" :class="{ 'show': isFixed, 'dragging': isDragging }" :style="{
        left: capsulePosition.x + 'px',
        top: capsulePosition.y + 'px'
      }">
        <SoundSelect v-model="selectedSound" class="capsule-sound-select" />
        <button ref="audioButtonRef" class="capsule-btn" @click="handleAudioConvert" data-action="audio"
          data-tooltip="文案转音频">
          <el-icon>
            <Mic />
          </el-icon>
        </button>
        <button class="capsule-btn" @click="openAI()" data-action="ai" data-tooltip="智能改写文案">
          <el-icon>
            <MagicStick />
          </el-icon>
        </button>
        <button class="capsule-btn" @click="handleExportTxt" data-action="export" data-tooltip="导出文案">
          <el-icon>
            <Download />
          </el-icon>
        </button>
        <button class="capsule-btn" @click="handleSelectAll" data-action="select" data-tooltip="全选/取消全选">
          <el-icon><Select /></el-icon>
        </button>
        <div class="drag-handle" @mousedown.prevent="handleDragStart"></div>
      </div>

      <el-row :gutter="20" class="action-buttons-row" :class="{ 'hide': isFixed }">
        <el-col :span="24">
          <div class="main-actions">
            <div class="action-group">
              <SoundSelect v-model="selectedSound" class="main-sound-select" />
              <button ref="audioButtonRef" class="custom-button" @click="handleAudioConvert">
                <el-icon>
                  <Mic />
                </el-icon>
                <span>文案转音频</span>
              </button>
            </div>
            <el-tooltip class="item" effect="dark" content="选择现有文案，让AI为您生成类似的优化版本" placement="top">
              <el-button plain @click="openAI()">
                <el-icon>
                  <MagicStick />
                </el-icon>
                <span>智能改写文案</span>
              </el-button>
            </el-tooltip>
            <el-button type="primary" plain @click="handleExportTxt">
              <el-icon>
                <Download />
              </el-icon>
              <span>导出文案</span>
            </el-button>
            <el-button type="primary" plain @click="handleSelectAll">
              <el-icon><Select /></el-icon>
              <span>{{ isSelectAll ? '取消全选' : '全选' }}</span>
            </el-button>
          </div>
        </el-col>
      </el-row>

      <div class="card-container" v-loading="loading">
        <div class="card-grid">
          <div v-for="(item, index) in articleList" :key="item.articleId" class="article-card" :class="{
            'selected': ids.includes(item.articleId),
            'editing': item.isEdit
          }" @click="!item.isEdit && handleCardSelect(item)">
            <div class="card-corner">
              <span class="corner-id">#{{ item.articleId }}</span>
            </div>
            <div class="card-content">
              <div class="article-text" v-if="!item.isEdit">{{ item.content }}</div>
              <el-input v-else v-model="item.content" type="textarea" resize="vertical" @click.stop @mousedown.stop
                @keydown.enter.native="handleUpdate(item)" maxlength="500" show-word-limit :rows="4"
                class="edit-textarea" />
            </div>
            <div class="card-footer">
              <div class="action-buttons">
                <button class="action-btn copy-btn" :class="{ 'success': item.isCopy }"
                  @click.stop="copyTextSuccess(item)" v-copyText="item.content">
                  <component :is="!item.isCopy ? DocumentCopy : Check" class="action-icon" />
                </button>
                <button v-if="!item.isEdit" class="action-btn edit-btn" @click.stop="clickUpdate(item)">
                  <Edit class="action-icon" />
                </button>
                <button v-else class="action-btn save-btn" @click.stop="handleUpdate(item)">
                  <Check class="action-icon" />
                </button>
                <button class="action-btn delete-btn" @click.stop="handleDelete(item)">
                  <Delete class="action-icon" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" style="background: #fff;" />

      <el-drawer v-model="drawer" size="39%" modal close-on-click-modal>
        <template #header>
          <h4>新增文案</h4>
        </template>
        <template #default>
          <el-form :model="addForm" :rules="rules" label-width="80px" ref="drawerRef">
            <el-form-item label="文案内容" prop="content" @keydown.enter.native="keyDown">
              <el-input v-model="addForm.content" type="textarea" :rows="30"
                placeholder="请输入文案内容,按行提交输入完数据点击按行提交即可，按句提交切记每个文案后面需要有中英文符号。？！都支持，ctrl+enter可直接提交" />
            </el-form-item>
          </el-form>
        </template>
        <template #footer>
          <div style="flex: auto">
            <el-button type="primary" @click="selectBatchType('line')">
              按行提交
            </el-button>
            <el-button type="success" @click="selectBatchType('point')">
              按句提交</el-button>
            <el-button @click="drawer = false">取消</el-button>
          </div>
        </template>
      </el-drawer>

      <el-dialog v-model="aiFlag" @update:modelValue="closeAIDialog" title="智能文案">
        <el-table v-loading="aiLoading" element-loading-text="生成中..." :data="aiArticles" max-height="440px">
          <template #empty>
            <el-empty description="暂无数据" v-if="aiArticles.length === 0" />
          </template>
          <el-table-column label="内容" align="left" prop="content" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="240">
            <template #default="scope">
              <el-button link type="warning" :icon="!scope.row.isCopy ? copyIcon : successIcon"
                v-copyText="scope.row.content" @click="copyTextSuccess(scope.row)">复制</el-button>
              <el-button link type="primary" :icon="!scope.row.isAdd ? 'DocumentAdd' : 'CircleCheck'"
                @click="addAIArticle(scope.row)">采纳</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: center; padding-bottom: 10px;margin-top: 10px;">
          <el-button type="primary" icon="Plus" @click="addPlusArticle">
            全部采纳
          </el-button>
          <el-button type="success" plain icon="Refresh" @click="generateData(aiContent)">
            重新生成
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-loading-spinner .circular) {
  height: 39px;
  width: 39px;
}

.mb8 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

.article-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .article-col {
    display: flex;
    align-items: center;

    &.input-col {
      flex: 1;
    }

    &.button-col {
      flex: none;
      display: flex;
      align-items: center;
    }
  }

  .article-input {
    width: 100%;
    transition: width 0.3s ease;
  }

  .article-button {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;

    .button-text {
      margin-left: 5px;
    }
  }
}

@media (max-width: 1584px) {
  .button-text {
    display: none;
  }

  .delete-button-text {
    display: none;
  }

  .batch-button-text {
    display: none;
  }

  .article-button {
    padding: 0 10px;
  }
}

@media (max-width: 1320px) {
  .button-text {
    display: none;
  }

  .add-button-text {
    display: none;
  }

  .search-button-text {
    display: none;
  }

  .batch-button-text {
    display: none;
  }

  .article-button {
    padding: 0 10px;
  }
}

.custom-button {
  position: relative;
  top: 2px;
  height: 2.15rem;
  margin-right: 10px;
  width: 10rem;
  padding: 0.75rem;
  line-height: 0.5rem;
  text-align: left;
  background-color: #262626;
  color: #fafafa;
  font-weight: 700;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transform-origin: left;
  transition: all 500ms;
}

.custom-button::before {
  content: '';
  position: absolute;
  width: 3rem;
  height: 3rem;
  right: 0.25rem;
  top: 0.25rem;
  z-index: 10;
  background-color: #8b5cf6;
  border-radius: 9999px;
  filter: blur(1rem);
  transition: all 500ms;
}

.custom-button::after {
  content: '';
  position: absolute;
  width: 5rem;
  height: 5rem;
  right: 2rem;
  top: 0.75rem;
  z-index: 10;
  background-color: #fda4af;
  border-radius: 9999px;
  filter: blur(1rem);
  transition: all 500ms;
}

.custom-button:hover {
  border-color: #fda4af;
  color: #fda4af;
}

.custom-button:hover::before {
  right: 3rem;
  bottom: -2rem;
  filter: blur(2rem);
  box-shadow: 20px 20px 20px 30px #a21caf;
}

.custom-button:hover::after {
  right: -2rem;
}

.card-container {
  margin: 20px 0;
  overflow-x: hidden;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  padding: 12px;
  margin: 0 4px;
}

.article-card {
  position: relative;
  padding: 24px;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border-radius: 16px;
  overflow: hidden;

  background: linear-gradient(135deg,
      rgba(64, 158, 255, 0.05) 0%,
      rgba(64, 158, 255, 0.02) 50%,
      rgba(255, 255, 255, 0.95) 100%),
    linear-gradient(45deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(242, 247, 255, 0.9) 100%);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(64, 158, 255, 0.08);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);

  .card-corner {
    position: absolute;
    top: 12px;
    left: 16px;
    z-index: 2;

    .corner-id {
      color: #409EFF;
      font-weight: 600;
      font-size: 0.95em;
      opacity: 0.85;
      letter-spacing: 0.5px;
    }
  }

  .card-content {
    flex: 1;
    padding: 16px;
    padding-top: 40px;
    position: relative;
    z-index: 2;

    .article-text {
      line-height: 1.7;
      color: #1f2937;
      word-break: break-word;
      font-size: 0.95em;
    }

    :deep(.edit-textarea) {
      .el-textarea__inner {
        min-height: 120px !important;
        padding: 12px;
        font-size: 0.95em;
        line-height: 1.7;
        border-radius: 8px;
        resize: none;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:focus {
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
        }
      }

      .el-input__count {
        background: transparent;
        padding: 2px 6px;
        font-size: 0.85em;
      }
    }
  }

  .card-footer {
    .action-buttons {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      opacity: 0;
      transform: translateY(10px);
      transition: opacity 0.3s ease, transform 0.3s ease;
    }

    .action-btn {
      position: relative;
      width: 32px;
      height: 32px;
      padding: 0;
      border: none;
      border-radius: 8px;
      background: transparent;
      cursor: pointer;
      opacity: 0;
      transform: translateY(10px);
      transition: all 0.2s ease;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: 8px;
        background: currentColor;
        opacity: 0.1;
        transition: opacity 0.2s ease;
      }

      &:hover::before {
        opacity: 0.15;
      }

      &:active::before {
        opacity: 0.2;
      }

      .action-icon {
        width: 1em;
        height: 1em;
        font-size: 16px;
        transition: transform 0.2s ease;
      }

      &:hover .action-icon {
        transform: scale(1.1);
      }

      &.copy-btn {
        color: #67c23a;

        &.success {
          color: #67c23a;

          .action-icon {
            transform: scale(1.1);
          }
        }
      }

      &.edit-btn {
        color: #409EFF;
      }

      &.save-btn {
        color: #409EFF;
      }

      &.delete-btn {
        color: #F56C6C;
      }
    }
  }

  &:hover {
    .action-buttons {
      opacity: 1;
      transform: translateY(0);
    }

    .action-btn {
      opacity: 1;
      transform: translateY(0);

      &:nth-child(1) {
        transition-delay: 0.1s;
      }

      &:nth-child(2) {
        transition-delay: 0.15s;
      }

      &:nth-child(3) {
        transition-delay: 0.2s;
      }

      &:nth-child(4) {
        transition-delay: 0.25s;
      }
    }
  }

  &.selected {
    background: linear-gradient(145deg,
        rgba(64, 158, 255, 0.12) 0%,
        rgba(64, 158, 255, 0.06) 100%);
    border-color: rgba(64, 158, 255, 0.3);
    box-shadow:
      0 0 0 2px rgba(64, 158, 255, 0.2),
      0 8px 16px -4px rgba(64, 158, 255, 0.15);

    .card-corner .corner-id {
      color: #2b88ff;
      font-weight: 700;
    }

    .card-content .article-text {
      color: #1a365d;
    }

    &::before {
      content: '';
      position: absolute;
      top: 12px;
      right: 12px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #409EFF;
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
      opacity: 0;
      transform: scale(0.5);
      animation: selectCircle 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    }

    &::after {
      content: "✓";
      position: absolute;
      top: 12px;
      right: 12px;
      width: 20px;
      height: 20px;
      color: white;
      font-size: 14px;
      line-height: 20px;
      text-align: center;
      opacity: 0;
      transform: scale(0.8);
      animation: selectCheck 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s forwards;
      z-index: 2;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    transition: none;

    .card-footer {
      transition: none;
    }

    &:hover {
      transform: none;
    }
  }

  @media screen and (max-width: 768px) {
    .card-grid {
      grid-template-columns: 1fr;
    }
  }

  &.editing {
    cursor: default;

    &:hover {
      transform: none;
      box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.05),
        0 2px 4px -1px rgba(0, 0, 0, 0.03),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    }
  }
}

@keyframes selectCircle {
  from {
    opacity: 0;
    transform: scale(0.5);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes selectCheck {
  from {
    opacity: 0;
    transform: scale(0.8);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media screen and (max-width: 768px) {
  .card-footer {
    .action-buttons {
      justify-content: center;
    }

    .action-btn {
      width: 28px;
      height: 28px;

      .action-icon {
        font-size: 14px;
      }
    }
  }
}

@media (prefers-reduced-motion: reduce) {

  .action-buttons,
  .action-btn {
    transition: none;
  }
}

.action-card {
  position: relative;
  margin-bottom: 20px;
  padding: 16px 24px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
  overflow: hidden;

  &.fixed {
    position: sticky;
    top: 0;
    margin: 0 -12px;
    padding: 12px 36px;
    border-radius: 0 0 16px 16px;
    background: rgba(255, 255, 255, 0.98);
    box-shadow:
      0 4px 12px -2px rgba(0, 0, 0, 0.06),
      0 2px 6px -1px rgba(0, 0, 0, 0.04);

    .action-card-content {
      max-width: 1200px;
      margin: 0 auto;
      opacity: 0;
      transform: translateY(-100%);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      &.minimized {
        opacity: 0;
        transform: translateY(-100%);
      }
    }

    .float-buttons {
      opacity: 1;
      transform: translateY(0);
      pointer-events: auto;
    }
  }
}

.action-card-content {
  display: flex;
  align-items: center;
  gap: 12px;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;

  .el-button {
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
    transform: translate3d(0, 0, 0);

    &:hover {
      transform: translate3d(0, -2px, 0);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
  }
}

.float-buttons {
  position: absolute;
  top: 50%;
  right: 36px;
  transform: translateY(50%);
  display: flex;
  gap: 12px;
  opacity: 0;
  pointer-events: none;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: transform, opacity;

  &.show {
    opacity: 1;
    transform: translateY(-50%);
    pointer-events: auto;
  }
}

.float-btn {
  position: relative;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(145deg, #ffffff, #f5f5f5);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 2px 4px rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.8),
        rgba(255, 255, 255, 0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 8px 12px -4px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      inset 0 2px 4px rgba(255, 255, 255, 0.8);

    &::before {
      opacity: 1;
    }

    .float-label {
      opacity: 1;
      transform: translateX(0);
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow:
      0 2px 4px -1px rgba(0, 0, 0, 0.1),
      0 1px 2px -1px rgba(0, 0, 0, 0.05);
  }

  .float-icon {
    font-size: 18px;
    line-height: 1;
  }

  .float-label {
    position: absolute;
    left: calc(100% + 8px);
    top: 50%;
    transform: translateX(-10px) translateY(-50%);
    white-space: nowrap;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    pointer-events: none;
  }

  &.audio-btn {
    background: linear-gradient(145deg, #8b5cf6, #7c3aed);
    color: white;
  }

  &.ai-btn {
    background: linear-gradient(145deg, #3b82f6, #2563eb);
    color: white;
  }

  &.export-btn {
    background: linear-gradient(145deg, #10b981, #059669);
    color: white;
  }

  &.select-btn {
    background: linear-gradient(145deg, #f59e0b, #d97706);
    color: white;
  }
}

@keyframes slideDown {
  from {
    transform: translate3d(0, -100%, 0);
    opacity: 0;
  }

  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

.action-buttons-row {
  margin-bottom: 20px;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  &.hide {
    opacity: 0;
    pointer-events: none;
    margin: 0;
    height: 0;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1), margin 0.3s ease, height 0.3s ease;

    .main-actions {

      .custom-button,
      .el-button {
        opacity: 0;
        transform: scale(0.8) translateY(-40px);

        @for $i from 1 through 4 {
          &:nth-child(#{$i}) {
            transition-delay: #{0.05 * (4 - $i)}s;
          }
        }
      }
    }
  }

  .main-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0 12px;

    .custom-button,
    .el-button {
      position: relative;
      display: flex;
      align-items: center;
      gap: 8px;
      height: 2.15rem;
      padding: 0 1.25rem;
      opacity: 1;
      transform: scale(1) translateY(0);
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    }
  }
}

.floating-capsule {
  position: fixed;
  display: flex;
  align-items: center;
  gap: 7px;
  padding: 7px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 22px;
  box-shadow:
    0 4px 20px -2px rgba(0, 0, 0, 0.1),
    0 2px 8px -1px rgba(0, 0, 0, 0.06);
  opacity: 0;
  visibility: hidden;
  transition: all 0.25s ease;
  z-index: 2000;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  cursor: default;
  user-select: none;
  transform: scale(0.95);
  will-change: transform, opacity;

  .drag-handle {
    position: relative;
    width: 35px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
    margin-left: 4px;
    border-radius: 12px;
    transition: background 0.2s ease;

    &::before {
      content: '';
      width: 3.5px;
      height: 22px;
      border-radius: 2px;
      background: rgba(0, 0, 0, 0.15);
      box-shadow:
        -6px 0 0 rgba(0, 0, 0, 0.15),
        6px 0 0 rgba(0, 0, 0, 0.15);
      transition: all 0.2s ease;
    }

    &::after {
      content: '拖动调整位置';
      position: absolute;
      bottom: calc(100% + 12px);
      left: 50%;
      transform: translateX(-50%) translateY(5px);
      background: rgba(0, 0, 0, 0.75);
      color: white;
      font-size: 13px;
      padding: 7px 12px;
      border-radius: 7px;
      white-space: nowrap;
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s ease;
      backdrop-filter: blur(4px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      pointer-events: none;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.04);

      &::before {
        background: rgba(0, 0, 0, 0.25);
        box-shadow:
          -6px 0 0 rgba(0, 0, 0, 0.25),
          6px 0 0 rgba(0, 0, 0, 0.25);
      }

      &::after {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
      }
    }
  }

  &.dragging {
    .drag-handle {
      cursor: grabbing;
      background: rgba(0, 0, 0, 0.06);

      &::before {
        background: rgba(0, 0, 0, 0.3);
        box-shadow:
          -6px 0 0 rgba(0, 0, 0, 0.3),
          6px 0 0 rgba(0, 0, 0, 0.3);
      }

      &::after {
        opacity: 0;
        visibility: hidden;
      }
    }
  }

  &:hover {
    transform: scale(1);
  }

  &.dragging {
    transition: none;
    cursor: grabbing;
    transform: scale(0.98);

    .capsule-btn {
      pointer-events: none;
      transform: scale(0.95);
    }
  }

  &.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1);

    .capsule-btn {
      opacity: 1;
      transform: scale(1);

      @for $i from 1 through 4 {
        &:nth-child(#{$i}) {
          transition-delay: #{0.05 * $i}s;
        }
      }
    }
  }

  .capsule-btn {
    position: relative;
    width: 43px;
    height: 43px;
    padding: 0;
    border: none;
    border-radius: 14px;
    background: transparent;
    color: #666;
    cursor: pointer;
    opacity: 0;
    transform: scale(0.8);
    transition: transform 0.2s ease, color 0.2s ease;
    overflow: visible;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 14px;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    &::after {
      content: attr(data-tooltip);
      position: absolute;
      bottom: calc(100% + 12px);
      left: 50%;
      transform: translateX(-50%) translateY(5px);
      background: rgba(0, 0, 0, 0.75);
      color: white;
      font-size: 13px;
      padding: 7px 12px;
      border-radius: 7px;
      white-space: nowrap;
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s ease;
      backdrop-filter: blur(4px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      pointer-events: none;
    }

    &:hover {
      color: white;
      transform: scale(1.05);

      &::before {
        opacity: 1;
      }

      &::after {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
      }

      .el-icon {
        transform: scale(1.05);
      }
    }

    &:active {
      transform: scale(0.95);
    }

    .el-icon {
      position: relative;
      z-index: 1;
      font-size: 20px;
      transition: transform 0.2s ease;
    }

    &[data-action="audio"] {
      &::before {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
      }

      &:hover {
        box-shadow: 0 4px 12px rgba(124, 58, 237, 0.2);
      }
    }

    &[data-action="ai"] {
      &::before {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
      }

      &:hover {
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
      }
    }

    &[data-action="export"] {
      &::before {
        background: linear-gradient(135deg, #10b981, #059669);
      }

      &:hover {
        box-shadow: 0 4px 12px rgba(5, 150, 105, 0.2);
      }
    }

    &[data-action="select"] {
      &::before {
        background: linear-gradient(135deg, #f59e0b, #d97706);
      }

      &:hover {
        box-shadow: 0 4px 12px rgba(217, 119, 6, 0.2);
      }
    }

    &[data-action="text-audio"] {
      &::before {
        background: linear-gradient(135deg, #67c23a, #529b2e);
      }

      &:hover {
        box-shadow: 0 4px 12px rgba(82, 155, 46, 0.2);
      }
    }
  }
}

.content-wrapper {
  position: relative;
  min-height: 200px;

  .action-buttons-row {
    margin-bottom: 20px;
    opacity: 1;
    transform: translateY(0);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    &.hide {
      opacity: 0;
      pointer-events: none;
      margin: 0;
      height: 0;
      overflow: hidden;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1), margin 0.3s ease, height 0.3s ease;

      .main-actions {

        .custom-button,
        .el-button {
          opacity: 0;
          transform: scale(0.8) translateY(-40px);

          @for $i from 1 through 4 {
            &:nth-child(#{$i}) {
              transition-delay: #{0.05 * (4 - $i)}s;
            }
          }
        }
      }
    }
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;

  .sound-select {
    width: 180px;
  }
}

.action-group {
  display: flex;
  align-items: center;
  gap: 12px;

  .main-sound-select {
    width: 180px;
    position: relative;
    top: 2px;
  }
}

.capsule-sound-select {
  width: 150px;
  margin-right: 8px;

  :deep(.select-trigger) {
    height: 40px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 0.95);
      border-color: rgba(64, 158, 255, 0.3);
    }

    &.is-active {
      background: #fff;
      border-color: #409eff;
    }
  }
}
</style>