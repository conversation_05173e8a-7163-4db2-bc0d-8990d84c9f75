/**
 * 批量媒体处理工具
 * 用于优化大量媒体资源的加载和处理性能
 */

import type { Timeline } from '../types/videoEdit';
import { getMediaInfo, batchGetMediaInfos } from '../api/media';

export interface MediaProcessingResult {
  success: boolean;
  processedCount: number;
  failedCount: number;
  errors: string[];
  timeline?: Timeline;
  processingTime: number;
}

export class BatchMediaProcessor {
  private static instance: BatchMediaProcessor;
  
  static getInstance(): BatchMediaProcessor {
    if (!BatchMediaProcessor.instance) {
      BatchMediaProcessor.instance = new BatchMediaProcessor();
    }
    return BatchMediaProcessor.instance;
  }

  /**
   * 批量处理Timeline中的媒体资源
   */
  async processTimelineMedia(timeline: Timeline): Promise<MediaProcessingResult> {
    const startTime = performance.now();

    const result: MediaProcessingResult = {
      success: false,
      processedCount: 0,
      failedCount: 0,
      errors: [],
      processingTime: 0
    };

    try {
      // 收集所有需要处理的MediaId
      const mediaIds = this.collectMediaIds(timeline);
      
      if (mediaIds.size === 0) {
        result.success = true;
        result.timeline = timeline;
        result.processingTime = performance.now() - startTime;
        return result;
      }

      // 使用批量接口获取媒体信息
      const mediaUrlMap = await this.batchGetMediaUrls(Array.from(mediaIds));

      // 更新Timeline中的FileUrl
      const updatedTimeline = this.updateTimelineWithUrls(timeline, mediaUrlMap);

      // 统计结果
      result.processedCount = mediaUrlMap.size;
      result.failedCount = mediaIds.size - mediaUrlMap.size;
      result.success = result.failedCount === 0;
      result.timeline = updatedTimeline;
      result.processingTime = performance.now() - startTime;

      return result;

    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : String(error));
      result.processingTime = performance.now() - startTime;
      return result;
    }
  }

  /**
   * 收集Timeline中所有需要FileUrl的MediaId
   */
  private collectMediaIds(timeline: Timeline): Set<string> {
    const mediaIds = new Set<string>();

    // 收集视频轨道中的MediaId
    timeline.VideoTracks?.forEach(track => {
      track.VideoTrackClips.forEach(clip => {
        if (clip.MediaId && clip.Type !== 'Text' && !(clip as any).FileUrl) {
          mediaIds.add(clip.MediaId);
        }
      });
    });

    // 收集音频轨道中的MediaId
    timeline.AudioTracks?.forEach(track => {
      track.AudioTrackClips.forEach(clip => {
        if (clip.MediaId && !(clip as any).FileUrl) {
          mediaIds.add(clip.MediaId);
        }
      });
    });

    return mediaIds;
  }

  /**
   * 批量获取媒体URL
   */
  private async batchGetMediaUrls(mediaIds: string[]): Promise<Map<string, string>> {
    const urlMap = new Map<string, string>();

    try {
      // 尝试使用批量接口
      if (mediaIds.length > 3) {
        try {
          const batchResponse = await batchGetMediaInfos({
            mediaIds ,
            additionType: 'FileInfo'
          });

          if (batchResponse?.MediaInfos) {
            batchResponse.MediaInfos.forEach(mediaInfo => {
              const mediaId = mediaInfo.MediaId;
              const fileUrl = mediaInfo.FileInfoList?.[0]?.FileBasicInfo?.FileUrl;
              
              if (mediaId && fileUrl) {
                urlMap.set(mediaId, fileUrl);
              }
            });
          }

          // 处理批量接口未覆盖的MediaId
          const missingIds = mediaIds.filter(id => !urlMap.has(id));
          if (missingIds.length > 0) {
            await this.fillMissingUrls(missingIds, urlMap);
          }

        } catch (batchError) {
          await this.fillMissingUrls(mediaIds, urlMap);
        }
      } else {
        await this.fillMissingUrls(mediaIds, urlMap);
      }

    } catch (error) {
      console.error('获取媒体URL失败:', error);
    }

    return urlMap;
  }

  /**
   * 使用单独接口填充缺失的URL
   */
  private async fillMissingUrls(mediaIds: string[], urlMap: Map<string, string>): Promise<void> {
    const promises = mediaIds.map(async (mediaId) => {
      try {
        const response = await getMediaInfo({ mediaId });
        const fileUrl = response?.MediaInfo?.FileInfoList?.[0]?.FileBasicInfo?.FileUrl;
        
        if (fileUrl) {
          urlMap.set(mediaId, fileUrl);
        }
      } catch (error) {
        // 静默失败
      }
    });

    await Promise.all(promises);
  }

  /**
   * 更新Timeline中的FileUrl
   */
  private updateTimelineWithUrls(timeline: Timeline, urlMap: Map<string, string>): Timeline {
    const updatedTimeline = JSON.parse(JSON.stringify(timeline)); // 深拷贝

    // 更新视频轨道
    updatedTimeline.VideoTracks?.forEach((track: any) => {
      track.VideoTrackClips.forEach((clip: any) => {
        if (clip.MediaId && urlMap.has(clip.MediaId)) {
          clip.FileUrl = urlMap.get(clip.MediaId);
        }
      });
    });

    // 更新音频轨道
    updatedTimeline.AudioTracks?.forEach((track: any) => {
      track.AudioTrackClips.forEach((clip: any) => {
        if (clip.MediaId && urlMap.has(clip.MediaId)) {
          clip.FileUrl = urlMap.get(clip.MediaId);
        }
      });
    });

    return updatedTimeline;
  }

  /**
   * 预加载媒体资源
   */
  async preloadMediaAssets(mediaUrls: string[]): Promise<{
    loaded: number;
    failed: number;
    loadTime: number;
  }> {
    const startTime = performance.now();

    let loaded = 0;
    let failed = 0;

    const preloadPromises = mediaUrls.map(async (url) => {
      try {
        // 根据URL类型选择预加载方式
        if (this.isVideoUrl(url)) {
          await this.preloadVideo(url);
        } else if (this.isAudioUrl(url)) {
          await this.preloadAudio(url);
        }
        loaded++;
      } catch (error) {
        failed++;
      }
    });

    await Promise.all(preloadPromises);

    const loadTime = performance.now() - startTime;

    return { loaded, failed, loadTime };
  }

  private isVideoUrl(url: string): boolean {
    return /\.(mp4|avi|mov|mkv|webm)$/i.test(url);
  }

  private isAudioUrl(url: string): boolean {
    return /\.(mp3|wav|ogg|aac|flac)$/i.test(url);
  }

  private preloadVideo(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.preload = 'metadata';
      video.onloadedmetadata = () => resolve();
      video.onerror = () => reject(new Error('Video preload failed'));
      video.src = url;
    });
  }

  private preloadAudio(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const audio = document.createElement('audio');
      audio.preload = 'metadata';
      audio.onloadedmetadata = () => resolve();
      audio.onerror = () => reject(new Error('Audio preload failed'));
      audio.src = url;
    });
  }
}

// 导出单例实例
export const batchMediaProcessor = BatchMediaProcessor.getInstance();
