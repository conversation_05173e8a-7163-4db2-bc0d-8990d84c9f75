<template>
  <div class="version-section">
    <h4 class="section-title">选择合成版本</h4>
    <div class="version-grid">
      <div 
        v-for="version in versions" 
        :key="version.value" 
        class="version-card" 
        :class="{ active: modelValue === version.value }" 
        @click="selectVersion(version.value)"
      >
        <div class="version-icon">
          <el-icon><VideoPlay /></el-icon>
        </div>
        <h5 class="version-name">{{ version.name }}</h5>
        <p class="version-desc">{{ version.description }}</p>
        <div class="version-features">
          <span v-for="feature in version.features" :key="feature" class="feature-tag">
            {{ feature }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { VideoPlay } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

const versions = [
  {
    value: 'M',
    name: 'M版',
    description: '标准版本，支持基础对话合成',
    features: ['标准画质', '快速合成', '阈值调节']
  },
  {
    value: 'V',
    name: 'V版',
    description: '增强版本，支持AI模型选择',
    features: ['AI增强', '模型选择', '优化效果']
  },
  {
    value: 'H',
    name: 'H版',
    description: '高清版本，提供最佳画质',
    features: ['高清画质', '优质效果', '默认配置']
  }
]

const selectVersion = (version) => {
  emit('update:modelValue', version)
}
</script>

<style scoped>
.version-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.version-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.version-card {
  padding: 20px;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;

  &:hover {
    border-color: #409eff;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(64, 158, 255, 0.15);
  }

  &.active {
    border-color: #67c23a;
    background: linear-gradient(135deg, rgba(103, 194, 58, 0.05) 0%, rgba(103, 194, 58, 0.1) 100%);
    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.2);
  }
}

.version-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  color: white;
  font-size: 24px;
}

.version-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.version-desc {
  font-size: 14px;
  color: #666;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.version-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag {
  padding: 4px 8px;
  background: #f0f9ff;
  color: #409eff;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.version-card.active .feature-tag {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}
</style>
