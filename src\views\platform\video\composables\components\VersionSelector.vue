<template>
  <div class="version-section">
    <h4 class="section-title">选择合成版本</h4>
    <div class="version-grid">
      <div
        v-for="version in versions"
        :key="version.value"
        class="version-card"
        :class="{ active: modelValue === version.value }"
        @click="handleVersionSelect(version.value)"
      >
        <div class="version-icon">
          <el-icon><VideoPlay /></el-icon>
        </div>
        <h5 class="version-name">{{ version.name }}</h5>
        <p class="version-desc">{{ version.description }}</p>
        <div class="version-features">
          <span v-for="feature in version.features" :key="feature" class="feature-tag">
            {{ feature }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { VideoPlay } from '@element-plus/icons-vue'
import { listWymodel } from '@/api/platform/model'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'version-changed'])

const versions = [
  {
    value: 'M',
    name: 'M版',
    description: '标准版本，支持基础对话合成',
    features: ['标准画质', '快速合成', '阈值调节']
  },
  {
    value: 'V',
    name: 'V版',
    description: '增强版本，支持AI模型选择',
    features: ['AI增强', '模型选择', '优化效果']
  },
  {
    value: 'H',
    name: 'H版',
    description: '高清版本，提供最佳画质',
    features: ['高清画质', '优质效果', '默认配置']
  }
]

// 加载可用模型
const loadAvailableModels = async () => {
  try {
    const response = await listWymodel()
    if (response && response.rows) {
      const availableModels = response.rows.filter(model => model.modelStatus === 1)
      return availableModels
    }
    return []
  } catch (error) {
    console.error('加载模型失败:', error)
    ElMessage.error('加载模型失败')
    return []
  }
}

// 处理版本选择
const handleVersionSelect = async (version) => {
  emit('update:modelValue', version)

  // 根据版本执行相应的业务逻辑
  const versionData = { version }

  if (version === 'M') {
    // M版默认配置
    versionData.bboxShiftValue = 0
  } else if (version === 'V') {
    // V版需要加载模型
    versionData.model = ''
    const models = await loadAvailableModels()
    if (models.length > 0) {
      versionData.model = models[0].modelCode
    }
    versionData.availableModels = models
  }

  // 通知父组件版本变更及相关数据
  emit('version-changed', versionData)
}
</script>

<style lang="scss" scoped>
.version-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
  }
}

.version-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.version-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  &:hover {
    border-color: #667eea;
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.2);

    &::before {
      transform: scaleX(1);
    }
  }

  &.active {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);

    &::before {
      transform: scaleX(1);
    }

    .version-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
  }
}

.version-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #667eea;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.version-name {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.version-desc {
  font-size: 14px;
  color: #666;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.version-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  color: #667eea;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

@media (max-width: 768px) {
  .version-grid {
    grid-template-columns: 1fr;
  }
}
</style>
