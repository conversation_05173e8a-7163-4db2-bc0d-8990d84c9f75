.audio-player {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid rgba(59, 130, 246, 0.1);
  border-radius: 20px;
  position: fixed;
  bottom: 16px;
  left: calc(var(--sidebar-width) + 32px);
  width: calc((100% - var(--sidebar-width) - 64px - 80px) * 0.6 - 1px);
  z-index: 10;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* 响应侧边栏状态 */
:global(.hideSidebar) .audio-player {
  --sidebar-width: 54px;
}

:global(.openSidebar) .audio-player {
  --sidebar-width: 200px;
}

/* 默认状态 */
.audio-player {
  --sidebar-width: 200px;
}

/* 顶部进度条区域 */
.progress-section {
  width: 100%;
  padding: 16px 24px 8px;
}

.progress-container {
  position: relative;
  height: 24px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.progress-container:hover {
  transform: scaleY(1.2);
}

.progress-track {
  width: 100%;
  height: 6px;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-radius: 8px;
  position: relative;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 8px;
  transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border: 3px solid #3b82f6;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3), 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.progress-thumb:hover {
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4), 0 3px 8px rgba(0, 0, 0, 0.15);
}

/* 底部控制区域 */
.controls-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px 16px;
  height: 56px;
}

/* 左侧区域：喇叭图标和时间 */
.left-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.volume-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 12px;
  color: #3b82f6;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.volume-icon:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.time-display {
  font-size: 14px;
  color: #475569;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  font-weight: 600;
  min-width: 90px;
  padding: 6px 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

/* 中间控制按钮区域 */
.control-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 0 0 auto;
}

.control-btn {
  width: 40px;
  height: 40px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 50%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.control-btn:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.play-btn {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.play-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4), 0 4px 12px rgba(0, 0, 0, 0.15);
}

.play-btn:active {
  transform: translateY(-1px) scale(1.02);
}

/* 右侧区域：倍速按钮 */
.right-section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

.speed-btn {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid rgba(148, 163, 184, 0.2);
  color: #475569;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.speed-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s ease;
}

.speed-btn:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  color: #0369a1;
  border-color: rgba(3, 105, 161, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(3, 105, 161, 0.2);
}

.speed-btn:hover::before {
  left: 100%;
}

/* 加载状态 */
.loading-spinner {
  animation: spin 1.5s linear infinite;
  font-size: 18px;
  color: #3b82f6;
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3));
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 音频播放器入场动画 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.audio-player {
  animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 进度条脉冲动画 */
@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 2px 16px rgba(59, 130, 246, 0.5);
  }
}

.progress-fill {
  animation: pulse 2s ease-in-out infinite;
}

/* 禁用状态 */
.play-btn:disabled,
.speed-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.play-btn:disabled:hover,
.speed-btn:disabled:hover {
  background: transparent;
  color: inherit;
}

/* 错误信息 */
.error-message {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: #f56565;
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
}

/* 章节速览预览框样式 */
.chapter-preview {
  position: fixed;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  max-width: 300px;
  min-width: 200px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  pointer-events: none;
  transform: translateX(-50%) translateY(-100%);
  animation: fadeInDown 0.2s ease-out;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.preview-time {
  font-size: 12px;
  color: #3b82f6;
  font-weight: 600;
  font-family: "Courier New", monospace;
}

.preview-speaker {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.preview-content {
  font-size: 14px;
  line-height: 1.4;
  color: #f8fafc;
  word-break: break-word;
  /* 限制为两行文字 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-arrow {
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid rgba(0, 0, 0, 0.9);
}

/* 预览框动画 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-100%) translateY(0);
  }
}

/* 进度条悬浮增强 */
.progress-container:hover .progress-track {
  height: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15);
}

.progress-container:hover .progress-fill {
  box-shadow: 0 2px 12px rgba(59, 130, 246, 0.4);
}
