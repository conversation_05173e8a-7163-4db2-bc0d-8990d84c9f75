<template>
  <div class="app-container consumption-container">
    <!-- 概览信息区域 - 更现代的卡片设计 -->
    <div class="overview-section">
      <div class="section-title">
        <el-icon class="icon"><DataAnalysis /></el-icon>
        <span>算力消费概览</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :lg="6" :md="12" :sm="24">
          <div class="stat-card total-consumption">
            <div class="stat-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statisticsData.total_consumption || 0 }}</div>
              <div class="stat-label">总算力消费</div>
            </div>
            <div class="stat-chart">
              <div class="progress-circle">
                <div class="inner"></div>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :lg="6" :md="12" :sm="24">
          <div class="stat-card active-users">
            <div class="stat-icon">
              <el-icon><Avatar /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statisticsData.active_users || 0 }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-info">
              <el-progress :percentage="Math.min((statisticsData.active_users || 0) / 10 * 100, 100)" :show-text="false" :stroke-width="4" />
            </div>
          </div>
        </el-col>
        
        <el-col :lg="6" :md="12" :sm="24">
          <div class="stat-card month-consumption">
            <div class="stat-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statisticsData.month_consumption || 0 }}</div>
              <div class="stat-label">本月消费</div>
            </div>
            <div class="stat-trend">
              <div class="trend-icon" :class="{'trend-up': statisticsData.month_consumption > statisticsData.week_consumption}">
                <el-icon><ArrowUp v-if="statisticsData.month_consumption > statisticsData.week_consumption" />
                <ArrowDown v-else /></el-icon>
              </div>
              <div class="trend-text">环比上月</div>
            </div>
          </div>
        </el-col>
        
        <el-col :lg="6" :md="12" :sm="24">
          <div class="stat-card today-consumption">
            <div class="stat-icon">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statisticsData.today_consumption || 0 }}</div>
              <div class="stat-label">今日消费</div>
            </div>
            <div class="stat-extra">
              <div class="extra-item">
                <div class="extra-label">本周消费</div>
                <div class="extra-value">{{ statisticsData.week_consumption || 0 }}</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="module-info">
        <el-col :lg="24">
          <div class="module-card">
            <div class="module-title">最常用模块</div>
            <div class="module-content">
              <el-tag effect="dark" size="large" color="#6366f1">{{ statisticsData.most_used_module || '暂无数据' }}</el-tag>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- 搜索表单 - 更简约现代的设计 -->
    <div class="search-section">
      <div class="section-title">
        <el-icon class="icon"><Search /></el-icon>
        <span>筛选条件</span>
      </div>
      
      <el-form :model="queryParams" ref="queryRef" :inline="true" class="search-form">
        <el-form-item label="用户名称">
          <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="消费模块">
          <el-input v-model="queryParams.consumptionTitle" placeholder="请输入消费模块" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="操作业务">
          <el-select v-model="queryParams.consumptionStaus" placeholder="请选择状态" clearable style="width: 180px">
            <el-option v-for="dict in platform_consumption_consumption_staus" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" :icon="Search" @click="handleQuery">查询</el-button>
          <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 表格内容区域 - 更现代的数据展示 -->
    <div class="data-section">
      <div class="section-title">
        <el-icon class="icon"><List /></el-icon>
        <span>消费明细</span>
        <div class="action-buttons">
          <el-button type="warning" plain :icon="Download" size="small" @click="handleExport">导出数据</el-button>
          <el-button type="success" plain :icon="RefreshRight" size="small" @click="handleRefreshCache">刷新缓存</el-button>
        </div>
      </div>
      
      <div class="table-container">
        <el-table 
          v-loading="loading" 
          :data="consumptionList" 
          stripe 
          border 
          class="consumption-table"
          :header-cell-style="tableHeaderStyle"
          :row-class-name="tableRowClassName">
          <el-table-column label="日志编号" align="center" prop="consumptionId" min-width="80" />
          <el-table-column label="算力用户" align="center" prop="userName" min-width="120">
            <template #default="scope">
              <div class="user-cell">
                <el-avatar :size="24" :icon="User" class="user-avatar" />
                <span>{{ scope.row.userName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="消费模块" align="center" prop="consumptionTitle" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <el-tooltip :content="scope.row.consumptionTitle" placement="top">
                <span class="module-name">{{ scope.row.consumptionTitle }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="操作业务" align="center" prop="consumptionStaus" min-width="100">
            <template #default="scope">
              <dict-tag 
                :options="platform_consumption_consumption_staus" 
                :value="scope.row.consumptionStaus" 
                class="operation-tag" />
            </template>
          </el-table-column>
          <el-table-column label="消费或充值算力点" align="center" prop="consumptionHashrate" min-width="140">
            <template #default="scope">
              <div class="hashrate-cell" :class="scope.row.consumptionStaus === '0' ? 'hashrate-add' : 'hashrate-minus'">
                <el-icon class="hashrate-icon">
                  <ArrowUp v-if="scope.row.consumptionStaus === '0'" />
                  <ArrowDown v-else />
                </el-icon>
                <span class="hashrate-value">{{ scope.row.consumptionStaus === '0' ? '+' : '-' }}{{ scope.row.consumptionHashrate }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="剩余算力点" align="center" prop="consumptionResidue" min-width="120">
            <template #default="scope">
              <div class="residue-cell">
                {{ scope.row.consumptionResidue }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="充值或消费时间" align="center" prop="createTime" min-width="170">
            <template #default="scope">
              <div class="time-cell">
                <el-icon><Clock /></el-icon>
                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页控件 -->
      <div class="pagination-container">
        <pagination 
          v-show="total>0" 
          :total="total" 
          v-model:page="queryParams.pageNum" 
          v-model:limit="queryParams.pageSize" 
          @pagination="getList" 
          :pageSizes="[10, 20, 50, 100]" 
          layout="total, sizes, prev, pager, next, jumper"
          background />
      </div>
    </div>
  </div>
</template>

<script setup name="Consumption">
import { listConsumption, refreshCache, getStatistics } from "@/api/platform/consumption";
import { ElMessage } from 'element-plus';
import { 
  TrendCharts, Avatar, Calendar, Timer, Search, Refresh, List, 
  Download, RefreshRight, User, Clock, DataAnalysis,
  ArrowUp, ArrowDown
} from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const { platform_consumption_consumption_staus } = proxy.useDict("platform_consumption_consumption_staus");

const consumptionList = ref([]);
const loading = ref(true);
const total = ref(0);
const dateRange = ref([]);
const statisticsData = ref({
  total_consumption: 0,
  active_users: 0,
  month_consumption: 0,
  today_consumption: 0,
  week_consumption: 0,
  most_used_module: ''
});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: null,
    consumptionTitle: null,
    consumptionStaus: null,
    params: {}
  }
});
const { queryParams } = toRefs(data);

// 表格样式
const tableHeaderStyle = {
  background: '#f0f2f5',
  color: '#303133',
  fontWeight: 'bold',
  fontSize: '14px',
  height: '50px'
};

// 表格行样式
function tableRowClassName({row, rowIndex}) {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
}

/** 查询用户算力点数变化记录列表 */
function getList() {
  loading.value = true;
  
  if (dateRange.value && dateRange.value.length > 0) {
    queryParams.value.params = {
      beginTime: dateRange.value[0],
      endTime: dateRange.value[1]
    };
  } else {
    queryParams.value.params = {};
  }
  
  listConsumption(queryParams.value).then(response => {
    consumptionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

/** 获取统计数据 */
function getStatisticsData() {
  getStatistics().then(response => {
    if (response.code === 200) {
      statisticsData.value = response.data;
    }
  }).catch(error => {
    console.error('获取统计数据失败:', error);
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('platform/consumption/export', {
    ...queryParams.value
  }, `算力消费记录_${new Date().getTime()}.xlsx`);
  ElMessage.success('正在导出数据，请稍后...');
}

/** 刷新算力缓存 */
function handleRefreshCache() {
  loading.value = true;
  refreshCache().then(() => {
    getList();
    getStatisticsData();
    proxy.$modal.msgSuccess("刷新缓存成功");
  }).finally(() => {
    loading.value = false;
  });
}

onMounted(() => {
  getList();
  getStatisticsData();
});
</script>

<style lang="scss" scoped>
// 整体容器
.consumption-container { padding: 16px; background-color: #f5f7fa; min-height: calc(100vh - 84px); }

// 通用区域样式
.overview-section, .search-section, .data-section { background-color: #fff; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08); }

// 区域标题样式
.section-title { display: flex; align-items: center; margin-bottom: 20px; font-size: 18px; font-weight: 600; color: #303133; }
.section-title .icon { margin-right: 8px; font-size: 20px; color: #409eff; }
.section-title .action-buttons { margin-left: auto; }

// 统计卡片样式
.stat-card { height: 140px; padding: 20px; border-radius: 8px; display: flex; position: relative; margin-bottom: 20px; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04); transition: all 0.3s; overflow: hidden; }
.stat-card:hover { transform: translateY(-5px); box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); }
.stat-icon { font-size: 24px; display: flex; align-items: center; justify-content: center; width: 56px; height: 56px; border-radius: 28px; margin-right: 16px; color: white; }
.stat-content { flex: 1; }
.stat-value { font-size: 28px; font-weight: bold; line-height: 1.2; margin-bottom: 8px; }
.stat-label { font-size: 14px; color: #606266; }

// 各种统计卡片的样式
.total-consumption { background: linear-gradient(135deg, #36d1dc, #5b86e5); color: white; }
.total-consumption .stat-icon { background-color: rgba(255, 255, 255, 0.2); }
.total-consumption .stat-label { color: rgba(255, 255, 255, 0.7); }
.total-consumption .progress-circle { position: absolute; right: -20px; top: -20px; width: 120px; height: 120px; border-radius: 50%; background: rgba(255, 255, 255, 0.1); display: flex; align-items: center; justify-content: center; }
.total-consumption .progress-circle .inner { width: 80px; height: 80px; border-radius: 50%; background: rgba(255, 255, 255, 0.15); }

.active-users { background: linear-gradient(135deg, #ff9a9e, #fad0c4); color: white; }
.active-users .stat-icon { background-color: rgba(255, 255, 255, 0.2); }
.active-users .stat-label { color: rgba(255, 255, 255, 0.7); }
.active-users .stat-info { position: absolute; bottom: 20px; left: 20px; right: 20px; }

.month-consumption { background: linear-gradient(135deg, #a1c4fd, #c2e9fb); color: white; }
.month-consumption .stat-icon { background-color: rgba(255, 255, 255, 0.2); }
.month-consumption .stat-label { color: rgba(255, 255, 255, 0.7); }
.month-consumption .stat-trend { position: absolute; bottom: 20px; right: 20px; display: flex; align-items: center; }
.month-consumption .trend-icon { display: flex; align-items: center; justify-content: center; width: 24px; height: 24px; border-radius: 12px; margin-right: 6px; background-color: rgba(255, 255, 255, 0.2); }
.month-consumption .trend-text { font-size: 12px; }
.month-consumption .trend-up { color: #67c23a; }

.today-consumption { background: linear-gradient(135deg, #fbc2eb, #a6c1ee); color: white; }
.today-consumption .stat-icon { background-color: rgba(255, 255, 255, 0.2); }
.today-consumption .stat-label { color: rgba(255, 255, 255, 0.7); }
.today-consumption .stat-extra { position: absolute; bottom: 20px; right: 20px; }
.today-consumption .extra-item { text-align: right; }
.today-consumption .extra-label { font-size: 12px; opacity: 0.7; }
.today-consumption .extra-value { font-weight: bold; }

// 模块信息卡片
.module-info { margin-top: 10px; }
.module-card { background-color: white; padding: 16px; border-radius: 8px; display: flex; align-items: center; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04); }
.module-title { font-weight: bold; margin-right: 20px; color: #606266; }

// 搜索表单样式
.search-form { display: flex; flex-wrap: wrap; gap: 16px; }
.search-form :deep(.el-form-item) { margin-bottom: 16px; margin-right: 0; }
.search-form :deep(.el-form-item__label) { font-weight: 500; }
.search-form :deep(.el-input__inner) { border-radius: 4px; }
.search-buttons { margin-left: auto; }

// 表格样式
.table-container { margin-bottom: 20px; border-radius: 8px; overflow: hidden; }
.consumption-table { width: 100%; }
.consumption-table :deep(.even-row) { background-color: #fafafa; }
.consumption-table :deep(.odd-row) { background-color: #fff; }
.consumption-table :deep(.el-table__row:hover) { background-color: #f0f9eb !important; }

// 表格内各种单元格样式
.user-cell { display: flex; align-items: center; justify-content: center; }
.user-avatar { margin-right: 8px; background-color: #e6f1fe; }
.module-name { font-size: 13px; }
.operation-tag { padding: 4px 8px; font-size: 12px; }
.hashrate-cell { display: flex; align-items: center; justify-content: center; padding: 4px 8px; border-radius: 4px; }
.hashrate-add { color: #67C23A; background-color: rgba(103, 194, 58, 0.1); }
.hashrate-minus { color: #F56C6C; background-color: rgba(245, 108, 108, 0.1); }
.hashrate-icon { margin-right: 4px; }
.residue-cell { font-weight: bold; }
.time-cell { display: flex; align-items: center; justify-content: center; font-size: 13px; }
.time-cell .el-icon { margin-right: 4px; font-size: 16px; color: #909399; }

// 分页样式
.pagination-container { display: flex; justify-content: flex-end; }
.pagination-container :deep(.el-pagination) { padding: 0; font-weight: normal; }
.pagination-container :deep(.el-pagination__total) { margin-right: 12px; }
.pagination-container :deep(.el-pagination__sizes) { margin-right: 12px; }
.pagination-container :deep(.el-pagination .el-select .el-input) { margin: 0 12px 0 0; }
.pagination-container :deep(.el-pagination .el-select .el-input .el-input__inner) { height: 28px; }
.pagination-container :deep(.el-pagination .btn-prev) { padding-right: 12px; }
.pagination-container :deep(.el-pagination .btn-next) { padding-left: 12px; }
.pagination-container :deep(.el-pagination .el-pager li) { margin: 0 2px; border-radius: 2px; }
</style>
