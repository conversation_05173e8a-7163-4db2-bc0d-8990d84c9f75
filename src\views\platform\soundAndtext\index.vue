<template>
  <div class="sound-and-text-app-container">
    <div class="two-column-layout">
      <!-- 文本转音频区域 - 占50%宽度 -->
      <div class="text-audio-container-wrapper">
        <el-card class="text-audio-container">
          <div class="content-area">
            <div class="form-title">文本转音频</div>
            
            <el-form :model="form" :rules="rules" ref="formRef" class="audio-form">
              <!-- 文本输入 -->
              <div class="form-section">
                <div class="section-label">
                  <el-icon><EditPen /></el-icon>
                  <span>输入文本</span>
                </div>
                <el-form-item prop="text">
                  <el-input
                    v-model="form.text"
                    type="textarea"
                    :rows="6"
                    placeholder="请输入要转换为音频的文本内容..."
                    maxlength="1000"
                    show-word-limit
                    resize="none"
                    class="text-input"
                  />
                </el-form-item>
              </div>

              <!-- 声音选择按钮式布局，带滚动条 -->
              <div class="form-section">
                <div class="section-header">
                  <div class="section-label">
                    <el-icon><Microphone /></el-icon>
                    <span>选择声音</span>
                  </div>
                  <div class="sound-type-switch-right">
                    <el-radio-group v-model="soundType" size="small">
                      <el-radio-button label="system">系统音色</el-radio-button>
                      <el-radio-button label="builtIn">内置音色</el-radio-button>
                    </el-radio-group>
                  </div>
                </div>
                
                <el-form-item prop="modelName">
                  <!-- 修改：移除固定宽度，使用相对单位 -->
                  <div class="sound-btn-container">
                    <el-scrollbar class="sound-btn-scrollbar">
                      <div class="sound-btn-list">
                        <button
                          type="button"
                          v-for="(sound, idx) in currentSoundList"
                          :key="sound.value"
                          class="sound-btn"
                          :class="{
                            selected: isSelectionMode ? selectedSounds.includes(sound.value) : form.modelName === sound.value
                          }"
                          :style="{ background: sound.color }"
                          @click="isSelectionMode ? toggleSoundSelection(sound) : selectSound(sound)"
                        >
                          <span class="sound-btn-label">{{ sound.label }}</span>
                          <el-icon v-if="isSelectionMode && selectedSounds.includes(sound.value)" class="selected-icon">
                            <Check />
                          </el-icon>
                        </button>
                      </div>
                    </el-scrollbar>
                  </div>
                </el-form-item>
              </div>

              <div class="form-actions">
                <el-button
                  v-if="!isSelectionMode"
                  type="primary"
                  @click="handleSubmit"
                  :loading="loading"
                  :disabled="!canSubmit"
                  class="submit-btn"
                >
                  {{ loading ? '转换中...' : '开始转换' }}
                </el-button>
                <el-button
                  v-if="isSelectionMode"
                  type="success"
                  @click="confirmSoundSelection"
                  :disabled="selectedSounds.length === 0"
                  class="submit-btn"
                >
                  <el-icon><Check /></el-icon>
                  确认选择 ({{ selectedSounds.length }})
                </el-button>
              </div>
            </el-form>
          </div>
        </el-card>
      </div>
      
      <!-- 音频列表区域 - 占50%宽度 -->
      <div class="audio-list-container-wrapper">
        <el-card class="audio-list-container">
          <AudioList
            style="width: 100%; height: 100%;"
            :version-key="versionKey"
            :isSelectionMode="isSelectionMode"
            :maxCount="maxCount"
            :returnPath="returnPath"
          />
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { EditPen, Microphone, Check } from '@element-plus/icons-vue'
import { createTextToAudio, createYuyin } from '@/api/platform/task'
import AudioList from './components/AudioList.vue'
import { listSound } from '@/api/platform/sound'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const versionKey = route.query.versionKey || null

// 选择模式相关
const isSelectionMode = ref(false)
const maxCount = ref(0)
const returnPath = ref('')
const selectedSounds = ref([])
const formRef = ref()
const loading = ref(false)
const form = reactive({
  text: '',
  modelName: ''
})

const soundType = ref('system')
const systemSoundList = ref([])// 系统声音列表
const builtInSoundList = ref([ // 内置声音列表
  { label: '知媛', value: 'zhiyuan', color: '#409EFF' },
  { label: '知悦', value: 'zhiyue', color: '#67C23A' },
  { label: '知莎', value: 'zhistella', color: '#E6A23C' },
  { label: '知达', value: 'zhida', color: '#F56C6C' },
  { label: '艾琪', value: 'aiqi', color: '#909399' },
  { label: '艾诚', value: 'aicheng', color: '#626aef' },
  { label: '艾佳', value: 'aijia', color: '#409EFF' },
  { label: '思琪', value: 'siqi', color: '#67C23A' },
  { label: '思佳', value: 'sijia', color: '#E6A23C' },
  { label: '马树', value: 'mashu', color: '#F56C6C' },
  { label: '悦儿', value: 'yuer', color: '#909399' },
  { label: '若兮', value: 'ruoxi', color: '#626aef' },
  { label: '艾达', value: 'aida', color: '#409EFF' },
  { label: '思诚', value: 'sicheng', color: '#67C23A' },
  { label: '宁儿', value: 'ninger', color: '#E6A23C' },
  { label: '小云', value: 'xiaoyun', color: '#F56C6C' },
  { label: '小刚', value: 'xiaogang', color: '#409EFF' },
  { label: '瑞琳', value: 'ruilin', color: '#67C23A' },
]);

const canSubmit = computed(() => {
  return form.text.trim() && form.modelName && !loading.value
})

const rules = {
  text: [
    { required: true, message: '请输入文本内容', trigger: 'blur' },
    { min: 1, max: 1000, message: '文本长度应在1-1000个字符之间', trigger: 'blur' }
  ],
  modelName: [
    { required: true, message: '请选择声音模型', trigger: 'change' }
  ]
}

// element-plus主题色
const elColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#626aef', '#1abc9c', '#e67e22', '#e84393', '#00b894'
]

// 当前显示的声音列表
const currentSoundList = computed(() => {
  return soundType.value === 'system' ? systemSoundList.value : builtInSoundList.value
})

const fetchSounds = async () => {
  try {
    const res = await listSound({ soundStatus: '2', pageNum: 1, pageSize: 1000 })
    if (res.total > 0) {
      systemSoundList.value = res.rows.map((item, idx) => ({
        label: item.soundName,
        value: item.soundId,
        color: elColors[idx % elColors.length]
      }))
    } else {
      systemSoundList.value = []
    }
  } catch (error) {
    systemSoundList.value = []
    console.error('获取声音列表失败:', error)
  }
}

// 选择声音
const selectSound = (sound) => {
  form.modelName = sound.value
}

// 切换声音选择（选择模式）
const toggleSoundSelection = (sound) => {
  const index = selectedSounds.value.indexOf(sound.value)
  if (index > -1) {
    selectedSounds.value.splice(index, 1)
  } else {
    if (maxCount.value > 0 && selectedSounds.value.length >= maxCount.value) {
      ElMessage.warning(`最多只能选择${maxCount.value}个声音`)
      return
    }
    selectedSounds.value.push(sound.value)
  }
}

// 确认声音选择
const confirmSoundSelection = () => {
  if (selectedSounds.value.length === 0) {
    ElMessage.warning('请至少选择一个声音')
    return
  }

  // 获取选中的声音数据
  const selectedVoiceData = currentSoundList.value.filter(sound =>
    selectedSounds.value.includes(sound.value)
  ).map(sound => ({
    soundId: sound.value, // 对于内置音色，这是英文标识符（如zhiyuan）
    soundName: soundType.value === 'builtIn' ? sound.value : sound.label, // 内置音色使用英文标识符作为名称
    soundRef: '',
    // 根据声音类型设置createBy：内置音色是admin，系统音色是system
    createBy: soundType.value === 'builtIn' ? 'admin' : 'system',
    createTime: new Date().toISOString()
  }))

  // 跳转回对话合成界面，携带选择结果
  router.push({
    path: returnPath.value,
    query: {
      selectedData: encodeURIComponent(JSON.stringify(selectedVoiceData)),
      type: 'voices'
    }
  })
}

// 初始化选择模式
const initSelectionMode = () => {
  const { from, mode, returnPath: queryReturnPath, maxCount: queryMaxCount } = route.query

  if (from === 'dialogueSynthesis' && mode === 'select' && queryReturnPath) {
    isSelectionMode.value = true
    returnPath.value = queryReturnPath
    maxCount.value = parseInt(queryMaxCount) || 0
  }
}

onMounted(() => {
  initSelectionMode()
  fetchSounds()
})

// 监听声音类型变化，重置选择
watch(soundType, () => {
  form.modelName = ''
})

// 前端代码修改
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    // 根据声音类型选择不同的API
    const isSystemSound = soundType.value === 'system'
    const apiMethod = isSystemSound ? createTextToAudio : createYuyin
    // 准备参数 - 关键修改：确保传递voice参数
    const params = isSystemSound ? { text: form.text, model_name: form.modelName, category_id: '',}
      : { text: form.text, voice_id: form.modelName,voice: form.modelName }    // 添加voice参数，明确指定发音人
    await apiMethod(params)
    ElMessage.success(`${isSystemSound ? '系统' : '内置'}语音合成创建成功！`)
    // 清空表单
    form.text = ''
    form.modelName = ''
    formRef.value.resetFields()
  } catch (error) {
    console.error('创建文本转音频任务失败:', error)
    ElMessage.error('创建任务失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.sound-and-text-app-container {
  height: 84vh;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;
  
  @media (max-width: 768px) {
    padding: 12px;
  }
  
  @media (max-width: 480px) {
    padding: 8px;
  }
}

.two-column-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;  // 平等分配两列
  gap: 24px;
  height: 100%;
  width: 100%;
  margin: 0 auto;
  
  // 超大屏幕 (>1600px)
  @media (min-width: 1601px) {
    gap: 32px;
  }
  
  // 大屏幕 (1200px - 1600px)
  @media (max-width: 1600px) and (min-width: 1200px) {
    gap: 24px;
  }
  
  // 中等屏幕 (900px - 1199px)
  @media (max-width: 1199px) and (min-width: 900px) {
    gap: 20px;
  }
  
  // 小屏幕 (<900px) - 垂直布局
  @media (max-width: 899px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

// 新增：两个容器的包装器，确保平等布局
.text-audio-container-wrapper, .audio-list-container-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.text-audio-container {
  flex: 1;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(102, 126, 234, 0.08);
  border: none;
  background: #fff;
  display: flex;
  flex-direction: column;
  
  .content-area {
    padding: 32px 28px 24px 28px;
    display: flex;
    flex-direction: column;
    height: 100%;
    
    @media (max-width: 899px) {
      padding: 24px 20px 20px 20px;
    }
    
    @media (max-width: 480px) {
      padding: 20px 16px 16px 16px;
    }
  }
  
  .form-title {
    font-size: 22px;
    font-weight: 700;
    color: #3b3f5c;
    margin-bottom: 28px;
    letter-spacing: 1px;
    text-align: center;
    flex-shrink: 0;
    
    @media (max-width: 899px) {
      font-size: 20px;
      margin-bottom: 24px;
    }
    
    @media (max-width: 480px) {
      font-size: 18px;
      margin-bottom: 20px;
    }
  }
  
  .audio-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
    flex: 1;
    
    @media (max-width: 899px) {
      gap: 20px;
    }
  }
  
  .form-section {
    width: 100%;
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px 18px 12px 18px;
    box-shadow: 0 1px 4px rgba(102, 126, 234, 0.04);
    border: 1px solid #e8eaed;
    transition: all 0.3s ease;
    margin-bottom: 8px;
    
    @media (max-width: 899px) {
      padding: 16px 14px 10px 14px;
    }
    
    @media (max-width: 480px) {
      padding: 14px 12px 8px 12px;
    }
    
    &:hover {
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
      border-color: #667eea;
    }
  }
  
  // 新增样式：声音选择区域的标题行样式
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    @media (max-width: 899px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
      margin-bottom: 12px;
    }
  }
  
  .section-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom:5px;
    color: #2c3e50;
    
    @media (max-width: 899px) {
      font-size: 15px;
    }
    
    .el-icon {
      color: #667eea;
      font-size: 18px;
      
      @media (max-width: 899px) {
        font-size: 16px;
      }
    }
  }
  
  // 新增样式：右侧声音类型切换样式
  .sound-type-switch-right {
    .el-radio-button__inner {
      padding: 6px 14px;
      font-size: 13px;
      border-radius: 4px !important;
      background-color: #f5f7fa;
      color: #606266;
      
      @media (max-width: 480px) {
        padding: 4px 10px;
        font-size: 12px;
      }
    }
    
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background-color: #409EFF;
      color: white;
      box-shadow: -1px 0 0 0 #409EFF;
    }
  }
  
  // 隐藏原有的声音类型切换
  .sound-type-switch {
    display: none;
  }
  
  // 修改：移除固定宽度，使用相对单位
  .sound-btn-container {
    width: 100%;
    // 设置一个足够包含最多按钮的高度
    height: 140px;
    overflow: auto;
  }
  
  .sound-btn-scrollbar {
    // 移除原来的max-height限制
    height: 100%;
    padding-bottom: 2px;
    margin: 0 2px;
    --el-scrollbar-bg-color: #f8fafc;
    --el-scrollbar-thumb-bg-color: #dcdfe6;
    --el-scrollbar-thumb-hover-bg-color: #b3b3b3;
  }
  
  .sound-btn-list {
    display: grid;
    // 修改：使用媒体查询动态调整列数
    grid-template-columns: repeat(auto-fill, minmax(105px, 1fr));
    gap: 16px;
    width: 100%;
    padding: 4px 0;
    box-sizing: border-box;
    justify-items: center;
    align-items: center;
    
    @media (max-width: 899px) {
      gap: 12px;
    }
    
    @media (max-width: 480px) {
      gap: 10px;
      grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    }
  }
  
  .sound-btn {
    // 修改：使用相对单位
    width: 100%;
    min-width: 80px;
    height: 48px;
    border-radius: 10px;
    border: 2px solid transparent;
    color: #fff;
    font-size: 17px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    outline: none;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 0;
    
    @media (max-width: 899px) {
      font-size: 15px;
      height: 44px;
    }
    
    @media (max-width: 480px) {
      font-size: 14px;
      height: 40px;
    }
    
    &:hover, &.selected {
      border-color: #409EFF;
      color: #409EFF;
      background: #fff !important;
    }
    
    .sound-btn-label {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 90%;
      text-align: center;
    }

    .selected-icon {
      position: absolute;
      top: -8px;
      right: -8px;
      width: 20px;
      height: 20px;
      background: #409EFF;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      border: 2px solid white;
      box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
      z-index: 10;
    }
  }
  
  .text-input :deep(.el-textarea__inner) {
    border: 2px solid #e8eaed;
    border-radius: 8px;
    padding: 16px;
    font-size: 14px;
    line-height: 1.6;
    resize: none;
    transition: all 0.3s ease;
    background: #fafbfc;
    
    @media (max-width: 899px) {
      padding: 14px;
      font-size: 13px;
    }
    
    @media (max-width: 480px) {
      padding: 12px;
    }
    
    &:focus {
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    flex-shrink: 0;
    
    @media (max-width: 899px) {
      margin-top: 20px;
    }
  }
  
  .submit-btn {
    padding: 12px 32px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 200px;
    
    @media (max-width: 899px) {
      min-width: 160px;
      padding: 10px 24px;
    }
    
    @media (max-width: 480px) {
      min-width: 140px;
      padding: 10px 20px;
      font-size: 14px;
    }
    
    &:hover:not(.is-disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }
    
    &:active:not(.is-disabled) {
      transform: translateY(0);
    }
    
    &.is-loading {
      background: #a0aec0;
    }
    
    &.is-disabled {
      background: #e2e8f0;
      color: #a0aec0;
      cursor: not-allowed;
    }
  }
}

.audio-list-container {
  flex: 1;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(102, 126, 234, 0.08);
  border: none;
  background: #fff;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  
  :deep(.el-card__body) {
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}
</style>    