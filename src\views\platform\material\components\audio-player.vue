<template>
  <div class="player-container">
    <audio 
      ref="audioElement"
      :src="audioUrl"
      @timeupdate="updateTime"
      @loadedmetadata="setDuration"
      @ended="isPlaying = false"
    ></audio>
    
    <div class="player-wrapper">
      <!-- 圆形进度条和播放按钮 -->
      <div class="circular-progress" 
           :class="{ 'is-playing': isPlaying }" 
           @click="togglePlay">
        <svg class="progress-ring" width="50" height="50">
          <circle
            class="progress-ring-circle-bg"
            stroke-width="2"
            fill="transparent"
            r="23"
            cx="25"
            cy="25"
          />
          <circle
            class="progress-ring-circle"
            stroke-width="2"
            fill="transparent"
            r="23"
            cx="25"
            cy="25"
            :style="{
              strokeDasharray: `${circumference} ${circumference}`,
              strokeDashoffset: dashOffset
            }"
          />
        </svg>
        <div class="play-button">
          <div v-if="!isPlaying" class="play-icon"></div>
          <div v-else class="pause-icon"></div>
        </div>
      </div>

      <!-- 进度条和时间 -->
      <div class="controls">
        <div class="time current">{{ formattedCurrentTime }}</div>
        <div class="progress-bar-wrapper"
          @mousedown="startDrag"
          @mousemove="onDrag"
          @mouseup="endDrag"
          @mouseleave="endDrag"
          ref="progressBar"
        >
          <div class="progress-bar">
            <div class="progress-bar-inner" :style="{ width: `${progress}%` }"></div>
          </div>
        </div>
        <div class="time total">{{ formattedDuration }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onBeforeUnmount, computed } from 'vue';
import { downloadAudio } from "@/api/platform/audio";

const props = defineProps({
    audioSrc: {
        type: String,
        required: true
    }
});

const audioElement = ref(null);
const isPlaying = ref(false);
const progress = ref(0);
const duration = ref(0);
const formattedCurrentTime = ref('0:00');
const formattedDuration = ref('0:00');
const audioUrl = ref('');
const progressBar = ref(null);
const isDragging = ref(false);

// 圆形进度条计算
const circumference = computed(() => 2 * Math.PI * 23);
const dashOffset = computed(() => circumference.value - (progress.value / 100) * circumference.value);

// 原有功能保持不变
function togglePlay() {
    if (isPlaying.value) {
        audioElement.value.pause();
    } else {
        audioElement.value.play();
    }
    isPlaying.value = !isPlaying.value;
}

function updateTime() {
    if (audioElement.value && !isDragging.value) {
        const currentTime = audioElement.value.currentTime;
        progress.value = (currentTime / audioElement.value.duration) * 100;
        formattedCurrentTime.value = formatTime(currentTime);
    }
}

function setDuration() {
    if (audioElement.value) {
        duration.value = audioElement.value.duration;
        formattedDuration.value = formatTime(duration.value);
    }
}

function formatTime(time) {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

// 新的进度条拖拽实现
function startDrag(e) {
    isDragging.value = true;
    updateProgressFromEvent(e);
}

function onDrag(e) {
    if (isDragging.value) {
        updateProgressFromEvent(e);
    }
}

function endDrag(e) {
    if (isDragging.value) {
        isDragging.value = false;
        updateProgressFromEvent(e);
        if (audioElement.value) {
            audioElement.value.currentTime = (progress.value / 100) * audioElement.value.duration;
            if (isPlaying.value) {
                audioElement.value.play();
            }
        }
    }
}

function updateProgressFromEvent(e) {
    const rect = progressBar.value.getBoundingClientRect();
    const x = e.clientX - rect.left;
    progress.value = Math.max(0, Math.min(100, (x / rect.width) * 100));
}

function commonDownload(item) {
    downloadAudio(item).then(res => {
        audioUrl.value = URL.createObjectURL(new Blob([res]))
    })
}

watch(() => props.audioSrc, (newSrc) => {
    if (newSrc) {
        commonDownload(newSrc);
    }
}, { immediate: true })

onBeforeUnmount(() => {
    if (audioUrl.value) {
        URL.revokeObjectURL(audioUrl.value);
    }
})
</script>

<style lang="scss" scoped>
.player-container {
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.player-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
}

.circular-progress {
  position: relative;
  width: 50px;
  height: 50px;
  cursor: pointer;
  
  &.is-playing {
    .play-button {
      background: #3b82f6;
      box-shadow: 0 6px 15px -3px rgba(59, 130, 246, 0.25),
                 0 3px 8px -2px rgba(59, 130, 246, 0.15);
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      
      .pause-icon {
        &::before,
        &::after {
          background: white;
        }
      }
    }
    
    .progress-ring-circle {
      stroke: #3b82f6;
      stroke-width: 2.5px;
      filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.3));
    }
    
    .progress-ring-circle-bg {
      stroke: rgba(59, 130, 246, 0.1);
    }
  }
  
  &:hover {
    .play-button {
      transform: translate(-50%, -50%) scale(1.08);
      background: #3b82f6;
      box-shadow: 0 6px 15px -3px rgba(59, 130, 246, 0.25),
                 0 3px 8px -2px rgba(59, 130, 246, 0.15);
      
      .play-icon {
        border-left-color: white;
      }
      
      .pause-icon {
        &::before,
        &::after {
          background: white;
        }
      }
    }
    
    .progress-ring-circle {
      stroke: #3b82f6;
      filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.3));
    }
  }
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle-bg {
  stroke: #e2e8f0;
  transition: all 0.3s ease;
}

.progress-ring-circle {
  stroke: #94a3b8;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(1);
  width: 36px;
  height: 36px;
  background: #f8fafc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
             0 2px 4px -1px rgba(0, 0, 0, 0.06);
  will-change: transform, box-shadow;
}

.play-icon {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8px 0 8px 12px;
  border-color: transparent transparent transparent #64748b;
  margin-left: 2px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center center;
}

.pause-icon {
  width: 12px;
  height: 14px;
  display: flex;
  gap: 3px;
  
  &::before,
  &::after {
    content: '';
    width: 3px;
    height: 100%;
    background: #64748b;
    border-radius: 2px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center center;
  }
}

.controls {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.time {
  font-size: 13px;
  font-weight: 500;
  color: #64748b;
  min-width: 45px;
  
  &.current {
    text-align: right;
  }
  
  &.total {
    text-align: left;
  }
}

.progress-bar-wrapper {
  flex: 1;
  cursor: pointer;
  padding: 10px 0;
}

.progress-bar {
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  
  &:hover {
    height: 6px;
    
    .progress-bar-inner {
      background: #3b82f6;
      filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.5));
    }
  }
  
  &-inner {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background: #94a3b8;
    border-radius: 2px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 6px 15px -3px rgba(59, 130, 246, 0.25),
                0 3px 8px -2px rgba(59, 130, 246, 0.15);
  }
  50% {
    box-shadow: 0 6px 20px -3px rgba(59, 130, 246, 0.35),
                0 3px 12px -2px rgba(59, 130, 246, 0.25);
  }
}
</style>   