/**
 * 时间轴轨道处理工具类
 * 负责处理轨道数据转换、排序等功能
 */

export interface ProcessedTrack {
  originalIndex: number;
  trackId: number; // 添加轨道ID字段
  clips: any[];
  uiIndex?: number;
  isHighlighted?: boolean;
  isPreviewTrack?: boolean;
}

export interface ProcessedClip {
  name: string;
  start: number;
  duration: number;
}

/**
 * 处理轨道数据，将原始轨道数据转换为显示用的数据结构
 */
export function processTrackData(tracks: any[], clipMapper: (clip: any) => any): ProcessedTrack[] {
  return tracks.map((track, index) => {
    console.log(`📋 处理轨道 - 数组索引:${index}, 轨道ID:${track.Id}, 轨道类型:${track.Type}`);
    return {
      originalIndex: index, // 使用数组索引作为原始索引（用于UI显示顺序）
      trackId: track.Id || index, // 轨道的实际ID（用于渲染层级排序）
      clips: track.clips ? track.clips.map(clipMapper) : [],
      uiIndex: index
    };
  });
}

/**
 * 轨道显示排序函数 - 按照视频编辑软件习惯排序
 * 字幕轨道和视频轨道显示在上方，轨道序号大的在上面（图层层级高）
 */
export function sortTracksForDisplay(tracks: ProcessedTrack[]): ProcessedTrack[] {
  // 深拷贝避免修改原数组
  const sortedTracks = [...tracks];
  
  console.log('🎨 Timeline显示排序前:', tracks.map(track => ({
    originalIndex: track.originalIndex,
    trackId: track.trackId,
    轨道信息: `ID${track.trackId}`,
    片段数量: track.clips?.length || 0
  })));
  
  // 🔧 修复：按照trackId（实际轨道ID）降序排列，ID大的在上面
  // 这样才能正确反映阿里云的轨道层级关系
  const result = sortedTracks.sort((a, b) => {
    return b.trackId - a.trackId;
  });
  
  console.log('🎨 Timeline显示排序后:', result.map(track => ({
    originalIndex: track.originalIndex,
    trackId: track.trackId,
    轨道信息: `ID${track.trackId}`,
    片段数量: track.clips?.length || 0
  })));
  
  return result;
}


/**
 * 处理视频轨道数据
 */
export function processVideoTracks(timeline: any) {
  if (!timeline?.VideoTracks) {
    return [];
  }
  
  // 从VideoTracks中筛选出Type为"Video"的轨道（排除Subtitle类型）
  const allTracks = timeline.VideoTracks || [];
  const rawTracks = allTracks.filter((track: any) => track.Type === 'Video' || !track.Type);
  
  // 调试信息：输出原始轨道数据
  console.log('📊 视频轨道原始数据:', rawTracks.map((track: any) => ({
    Id: track.Id,
    Type: track.Type,
    轨道名称: `视频轨道${track.Id}`,
    片段数量: track.VideoTrackClips?.length || 0
  })));
  
  // 检查每个轨道的结构并修正clips属性名
  const normalizedTracks = rawTracks.map((track: any, index: number) => {
    // 获取正确的clips数组 - 根据实际数据结构
    let clips = [];
    if (track.VideoTrackClips) {
      clips = track.VideoTrackClips;
    } else if (track.clips) {
      clips = track.clips;
    }
    
    // 返回标准化的轨道对象
    return {
      ...track,
      clips: clips
    };
  });
  
  const clipMapper = (clip: any) => {
    // 兼容不同的属性命名方式（大写或小写）
    const timelineIn = clip.TimelineIn ?? clip.timelineIn ?? 0;
    const timelineOut = clip.TimelineOut ?? clip.timelineOut ?? 0;
    
    return {
      name: clip.Title || clip.title || clip.FileName || clip.fileName || clip.MediaId || clip.mediaId,
      start: timelineIn,
      duration: timelineOut - timelineIn,
    };
  };
  
  return processTrackData(normalizedTracks, clipMapper);
}

/**
 * 处理音频轨道数据
 */
export function processAudioTracks(timeline: any) {
  const rawTracks = timeline?.AudioTracks || [];
  
  // 标准化轨道数据结构
  const normalizedTracks = rawTracks.map((track: any, index: number) => {
    let clips = [];
    if (track.AudioTrackClips) {
      clips = track.AudioTrackClips;
    } else if (track.clips) {
      clips = track.clips;
    }
    
    return {
      ...track,
      clips: clips
    };
  });
  
  const clipMapper = (clip: any) => {
    // 兼容不同的属性命名方式（大写或小写）
    const timelineIn = clip.TimelineIn ?? clip.timelineIn ?? 0;
    const timelineOut = clip.TimelineOut ?? clip.timelineOut ?? 0;
    
    return {
      name: clip.Title || clip.title || clip.FileName || clip.fileName || clip.MediaId || clip.mediaId || '未命名',
      start: timelineIn,
      duration: timelineOut - timelineIn,
    };
  };
  
  return processTrackData(normalizedTracks, clipMapper);
}

/**
 * 处理字幕轨道数据
 */
export function processSubtitleTracks(timeline: any) {
  if (!timeline?.VideoTracks) {
    return [];
  }
  
  const allTracks = timeline.VideoTracks;
  const rawTracks = allTracks.filter((track: any) => track.Type === 'Subtitle');
  
  // 标准化轨道数据结构 - 字幕轨道的clips在VideoTrackClips中
  const normalizedTracks = rawTracks.map((track: any, index: number) => {
    const clips = track.VideoTrackClips || [];
    return { ...track, clips };
  });
  
  const clipMapper = (clip: any) => {
    const timelineIn = clip.TimelineIn ?? 0;
    const timelineOut = clip.TimelineOut ?? 0;
    
    return {
      name: clip.Content || clip.Title || '字幕',
      start: timelineIn,
      duration: timelineOut - timelineIn,
    };
  };
  
  return processTrackData(normalizedTracks, clipMapper);
}
