<script setup>
import { onMounted } from 'vue';
</script>
<template>
    <div class="container">
        <div id="movableDiv" class="filter"></div>
        <div class="card">
            <div>
                <el-icon>
                    <Platform />
                </el-icon>
            </div>
            <div style="font-size: 0.3em;margin-top: -20px;">
                直播控制台
            </div>
        </div>
    </div>

</template>
<style scoped lang="scss">
$line-width: 50%;
$border-radius: 2rem;
$card-size: 200px;
$logo-size: calc($card-size * 0.3);

@keyframes re-move {
    from {
        top: 0;
        left: 0;
    }

    25% {
        top: 0;
        left: 100%;
    }

    50% {
        top: 100%;
        left: 100%;
    }

    75% {
        top: 100%;
        left: 0;
    }

    100% {
        top: 0;
        left: 0;
    }
}

.filter {
    animation: re-move 2s linear infinite;
    top: 0;
    left: 0;
    position: absolute;
    width: $line-width;
    height: $line-width;
    aspect-ratio: 1;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    pointer-events: none;
    border-radius: 50%;
    transform: translate(-50%, -50%) scaleY(-1);
    z-index: 100;
    --_m1: radial-gradient(rgb(255 255 255 / 1) 0%,
            rgb(255 255 255 / 0.5) 40%,
            rgb(255 255 255 / 0) 65%,
            rgb(255 255 255 / 0) 100%);
    --_m: radial-gradient(rgb(0 0 0 / 0.9) 0%,
            rgb(0 0 0 / 0.5) 40%,
            rgb(0 0 0 / 0) 65%,
            rgb(0 0 0 / 0) 100%);
    -webkit-mask-box-image: var(--_m1);
    mask-border: var(--_m1);
    mix-blend-mode: overlay;
    -webkit-backdrop-filter: brightness(4) url(#filter);
    backdrop-filter: brightness(4) url(#filter);
}

.container {
    box-shadow: 0 0 20px 5px black;
    overflow: hidden;
    position: relative;
    border-radius: $border-radius;
    width: $card-size;
    height: $card-size;

    &:active {
        transform: translateY(5px);
        box-shadow: 0 0 10px 5px black;
    }
}

.card {
    width: 100%;
    aspect-ratio: 1;
    position: relative;
    z-index: unset;
    background-color: black;
    border-radius: $border-radius;
    -webkit-backdrop-filter: blur(1rem);
    backdrop-filter: blur(1rem);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: $logo-size;
    flex-direction: column;

    &:after {
        content: "";
        position: absolute;
        inset: 0;
        border-radius: $border-radius;
        z-index: -1;
        background-image: linear-gradient(122.12deg, #166496 0%, #961664 100%);
        --b: 0.5;
        filter: brightness(var(--b));
        transition: all calc(1s / 16 * 4) ease-in-out;
        --border: 4;
        --alpha: 0;
        --gradient: linear-gradient(122.12deg, #166496 0%, #961664 100%);
        --bg-size: calc(100% + (2px * var(--border)));
        background: var(--gradient) center center/var(--bg-size) var(--bg-size);
        border: calc(var(--border) * 1px) solid transparent;
        -webkit-mask: linear-gradient(hsl(0 0% 100%/var(--alpha)), hsl(0 0% 100%/var(--alpha))), linear-gradient(white, white);
        mask: linear-gradient(hsl(0 0% 100%/var(--alpha)), hsl(0 0% 100%/var(--alpha))), linear-gradient(white, white);
        -webkit-mask-clip: padding-box, border-box;
        mask-clip: padding-box, border-box;
        -webkit-mask-composite: source-in, xor;
        mask-composite: intersect;
    }

    &:hover:after {
        --b: 1;
        isolation: isolate;
    }

}
</style>
