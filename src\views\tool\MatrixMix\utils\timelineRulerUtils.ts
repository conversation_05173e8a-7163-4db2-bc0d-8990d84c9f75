/**
 * 时间轴标尺工具类
 * 负责处理时间标尺相关功能
 */

import { formatRulerTime } from './timeUtils';

export interface RulerMarker {
  second: number;
  showLabel: boolean;
}

/**
 * 生成时间标尺标记
 */
export function generateRulerMarkers(videoDurationSeconds: number): RulerMarker[] {
  if (videoDurationSeconds <= 0) return [];
  
  const markers: RulerMarker[] = [];
  const totalDurationInSeconds = Math.ceil(videoDurationSeconds);
  const totalMarkers = totalDurationInSeconds + 20;

  for (let i = 0; i < totalMarkers; i++) {
    markers.push({
      second: i,
      showLabel: i % 5 === 0 // 每5秒显示一个标签
    });
  }
  
  return markers;
}

export { formatRulerTime } from './timeUtils';

/**
 * 计算播放头位置
 */
export function calculatePlayheadPosition(currentTime: number, PIXELS_PER_SECOND: number = 50): number {
  return currentTime * PIXELS_PER_SECOND;
}

/**
 * 处理时间轴点击，计算点击时间
 */
export function calculateClickTime(
  event: MouseEvent,
  rulerElement: HTMLElement,
  PIXELS_PER_SECOND: number = 50
): number {
  const rect = rulerElement.getBoundingClientRect();
  const clickX = event.clientX - rect.left;
  return Math.max(0, clickX / PIXELS_PER_SECOND);
}
