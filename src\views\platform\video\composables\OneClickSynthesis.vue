<template>
  <div class="one-click-synthesis">
    <div class="panel-header">
      <div class="header-info">
        <h3 class="panel-title">
          <el-icon class="panel-icon"><VideoCamera /></el-icon>
          一键合成
        </h3>
        <p class="panel-desc">选择合成版本并配置相关参数，点击开始合成即可完成整个流程</p>
      </div>
    </div>

    <div class="synthesis-content"></div>
      <!-- 版本选择组件 -->
      <VersionSelector
        v-model="synthesisConfig.version"
        @version-changed="handleVersionChanged"
      />

      <!-- 版本配置组件 -->
      <VersionConfig
        :version="synthesisConfig.version"
        :config="synthesisConfig"
        :available-models="availableModels"
        @update:config="updateConfig"
        @config-changed="handleConfigChanged"
      />

      <!-- 合成预览组件 -->
      <SynthesisPreview
        :digital-humans="props.digitalHumans"
        :dialogue-content="props.dialogueContent"
        :version="synthesisConfig.version"
        @preview-updated="handlePreviewUpdated"
      />

      <!-- 视频信息配置组件 -->
      <VideoInfoForm
        :video-info="videoInfo"
        :digital-humans="props.digitalHumans"
        :dialogue-content="props.dialogueContent"
        @update:video-info="updateVideoInfo"
        @info-changed="handleInfoChanged"
      />

      <!-- 合成控制组件 -->
      <SynthesisControl
        :synthesis-status="synthesisStatus"
        :version="synthesisConfig.version"
        :model="synthesisConfig.model"
        :digital-humans-count="props.digitalHumans.length"
        :dialogue-count="props.dialogueContent.length"
        :video-title="videoInfo.title"
        :config="synthesisConfig"
        @start-synthesis="handleStartSynthesis"
        @force-start="handleForceStart"
        @status-changed="handleStatusChanged"
      />
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { VideoCamera } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { dialogueSynthesis, getAutoSynthesisStatus } from '@/api/platform/video'

// 导入子组件
import VersionSelector from './components/VersionSelector.vue'
import VersionConfig from './components/VersionConfig.vue'
import SynthesisPreview from './components/SynthesisPreview.vue'
import VideoInfoForm from './components/VideoInfoForm.vue'
import SynthesisControl from './components/SynthesisControl.vue'
const props = defineProps({
  digitalHumans: {
    type: Array,
    default: () => []
  },
  dialogueContent: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['synthesis-complete'])
const availableModels = ref([])
const synthesisConfig = reactive({
  version: 'M',
  bboxShiftValue: 0,
  model: ''
})

const videoInfo = reactive({
  title: '',
  description: ''
})

const synthesisStatus = reactive({
  isRunning: false,
  isCompleted: false,
  hasError: false,
  title: '',
  message: '',
  progress: 0
})

const dialogueGroupId = ref('')
const finalResult = ref(null)

// 定时器
let statusCheckTimer = null

// 内置音色列表
const builtinVoices = ['zhiyuan', 'zhiyue', 'zhistella', 'zhida', 'aiqi', 'aicheng', 'aijia', 'siqi', 'sijia', 'mashu', 'yuer', 'ruoxi', 'aida', 'sicheng', 'ninger', 'xiaoyun', 'xiaogang', 'ruilin']

// 计算属性 - 简化版，主要用于验证
const canStart = computed(() => {
  const hasVersion = !!synthesisConfig.version
  const hasEnoughHumans = props.digitalHumans.length >= 2
  const hasDialogue = props.dialogueContent.length > 0
  const hasModel = synthesisConfig.version !== 'V' || !!synthesisConfig.model
  const notRunning = !synthesisStatus.isRunning
  const hasTitle = !!videoInfo.title.trim()

  return hasVersion && hasEnoughHumans && hasDialogue && hasModel && notRunning && hasTitle
})

// 子组件事件处理方法
const handleVersionChanged = (versionData) => {
  console.log('版本变更:', versionData)

  // 更新配置
  synthesisConfig.version = versionData.version

  if (versionData.bboxShiftValue !== undefined) {
    synthesisConfig.bboxShiftValue = versionData.bboxShiftValue
  }

  if (versionData.model !== undefined) {
    synthesisConfig.model = versionData.model
  }

  if (versionData.availableModels) {
    availableModels.value = versionData.availableModels
  }
}

const updateConfig = (newConfig) => {
  Object.assign(synthesisConfig, newConfig)
}

const handleConfigChanged = (configData) => {
  console.log('配置变更:', configData)
  // 可以在这里添加配置变更的额外处理逻辑
}

const updateVideoInfo = (newVideoInfo) => {
  Object.assign(videoInfo, newVideoInfo)
}

const handleInfoChanged = (infoData) => {
  console.log('视频信息变更:', infoData)
  // 可以在这里添加信息变更的额外处理逻辑
}

const handlePreviewUpdated = (previewData) => {
  console.log('预览数据更新:', previewData)
  // 可以在这里添加预览数据更新的处理逻辑
}

const handleStartSynthesis = async (validationDetails) => {
  console.log('开始合成，验证详情:', validationDetails)
  await startOneClickSynthesis()
}

const handleForceStart = async (validationDetails) => {
  console.log('强制开始合成，验证详情:', validationDetails)

  // 确保有基本的标题
  if (!videoInfo.title.trim()) {
    videoInfo.title = `数字人对话视频_${new Date().toLocaleString('zh-CN')}`
  }

  await startOneClickSynthesis()
}

const handleStatusChanged = (statusData) => {
  console.log('状态变更:', statusData)
  // 可以在这里添加状态变更的处理逻辑
}

// 一键合成主方法
const startOneClickSynthesis = async () => {
  if (!canStart.value) {
    ElMessage.warning('请完成所有配置后再开始合成')
    return
  }

  try {
    // 重置状态
    resetSynthesisStatus()

    // 只在标题为空时初始化，保留用户输入的标题
    if (!videoInfo.title.trim()) {
      initVideoInfo()
    } else {
      // 更新描述但保留用户输入的标题
      updateVideoDescription()
    }

    synthesisStatus.isRunning = true
    synthesisStatus.title = '开始合成'
    synthesisStatus.message = '正在准备合成任务...'
    synthesisStatus.progress = 10

    // 验证数据
    validateSynthesisData()

    // 构建合成参数
    const synthesisParams = buildSynthesisParams()

    // 调用后端一键合成接口
    synthesisStatus.message = '正在提交合成任务...'
    synthesisStatus.progress = 20

    const response = await dialogueSynthesis(synthesisParams)

    if (response && response.data) {
      dialogueGroupId.value = response.data.dialogueGroupId

      synthesisStatus.title = '合成任务已提交'
      synthesisStatus.message = response.data.message || '正在处理合成任务...'
      synthesisStatus.progress = 30

      // 开始监控合成状态
      startStatusMonitoring()

      ElMessage.success('合成任务已提交，正在处理...')
      emit('synthesis-complete', response.data)
    } else {
      throw new Error('合成响应异常')
    }
  } catch (error) {
    console.error('一键合成失败:', error)
    synthesisStatus.isRunning = false
    synthesisStatus.hasError = true
    synthesisStatus.title = '合成失败'
    synthesisStatus.message = error.message || '未知错误'
    ElMessage.error('合成失败: ' + (error.message || '请稍后重试'))
  }
}

// 验证合成数据
const validateSynthesisData = () => {
  if (!synthesisConfig.version) {
    throw new Error('请选择合成版本')
  }

  if (props.digitalHumans.length < 2) {
    throw new Error('至少需要选择2个数字人')
  }

  if (props.dialogueContent.length === 0) {
    throw new Error('请添加对话内容')
  }

  if (synthesisConfig.version === 'V' && !synthesisConfig.model) {
    throw new Error('V版需要选择AI模型')
  }

  if (!videoInfo.title.trim()) {
    throw new Error('请输入视频标题')
  }

  // 验证对话内容
  props.dialogueContent.forEach((dialogue, index) => {
    if (!dialogue.text || dialogue.text.trim() === '') {
      throw new Error(`第${index + 1}条对话内容不能为空`)
    }
    if (!dialogue.speaker) {
      throw new Error(`第${index + 1}条对话缺少发言人`)
    }
  })

  // 验证数字人配置
  props.digitalHumans.forEach((human, index) => {
    if (!human.name || human.name.trim() === '') {
      throw new Error(`第${index + 1}个数字人缺少名称`)
    }
    if (!human.voiceId && !human.voiceName) {
      throw new Error(`第${index + 1}个数字人缺少声音配置`)
    }
  })
}

// 重置合成状态
const resetSynthesisStatus = () => {
  // 直接重置所有状态，提供清爽的重新开始体验
  synthesisStatus.isRunning = false
  synthesisStatus.isCompleted = false
  synthesisStatus.hasError = false
  synthesisStatus.title = ''
  synthesisStatus.message = ''
  synthesisStatus.progress = 0
  finalResult.value = null
}

// 构建合成参数
const buildSynthesisParams = () => {
  return {
    version: synthesisConfig.version,
    autoSynthesis: true, // 启用自动合成
    enableSubtitles: true, // 启用字幕
    title: videoInfo.title,
    description: videoInfo.description,
    digitalHumans: props.digitalHumans.map((human, index) => {
      // 验证必要字段
      if (!human.avatarAddress) {
        throw new Error(`数字人${index + 1}缺少形象地址`)
      }
      if (!human.name || human.name.trim() === '') {
        throw new Error(`数字人${index + 1}缺少名称`)
      }
      if (!human.voiceName) {
        throw new Error(`数字人${index + 1}缺少声音名称`)
      }

      // 处理声音类型和ID
      let voiceType = human.voiceType || 'builtin'
      let voiceId = null

      if (voiceType === 'system') {
        voiceId = parseInt(human.voiceId)
        if (!voiceId || voiceId <= 0) {
          throw new Error(`数字人${index + 1}的系统声音ID无效`)
        }
      } else if (voiceType === 'builtin') {
        if (!builtinVoices.includes(human.voiceName)) {
          throw new Error(`数字人${index + 1}使用了不支持的内置音色: ${human.voiceName}`)
        }
      }

      // 构建规范的数字人配置
      return {
        id: human.id,
        name: human.name.trim(),
        avatarAddress: human.avatarAddress,
        avatarName: human.avatarName,
        voiceType: voiceType,
        voiceId: voiceId,
        voiceName: human.voiceName
      }
    }),
    dialogueContent: props.dialogueContent.map((dialogue, index) => {
      // 验证对话内容
      if (!dialogue.text || dialogue.text.trim() === '') {
        throw new Error(`对话${index + 1}的文本内容不能为空`)
      }

      if (!dialogue.speaker) {
        throw new Error(`对话${index + 1}缺少发言人配置`)
      }

      // 验证发言人是否存在于数字人配置中
      const humanExists = props.digitalHumans.some(human =>
        human.id === dialogue.speaker
      )

      if (!humanExists) {
        throw new Error(`对话${index + 1}的发言人配置错误: ${dialogue.speaker}`)
      }

      // 构建规范的对话数据
      return {
        id: index + 1,
        speaker: dialogue.speaker, // 格式为 "human_imageId"
        speakerName: dialogue.speakerName || `数字人${index + 1}`,
        text: dialogue.text.trim(),
        order: index + 1 // 确保顺序字段始终有值，从1开始
      }
    }),
    bboxShiftValue: synthesisConfig.version === 'M' ? synthesisConfig.bboxShiftValue : null,
    model: synthesisConfig.version === 'V' ? synthesisConfig.model : null
  }
}

// 开始状态监控
const startStatusMonitoring = () => {
  // 立即检查一次
  checkAutoSynthesisStatus()

  // 每10秒检查一次状态
  statusCheckTimer = setInterval(() => {
    if (!synthesisStatus.isCompleted && !synthesisStatus.hasError) {
      checkAutoSynthesisStatus()
    } else {
      stopStatusMonitoring()
    }
  }, 10000)
}

// 停止状态监控
const stopStatusMonitoring = () => {
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer)
    statusCheckTimer = null
  }
}

// 检查自动合成状态
const checkAutoSynthesisStatus = async () => {
  if (!dialogueGroupId.value) {
    return
  }

  try {
    const response = await getAutoSynthesisStatus(dialogueGroupId.value)

    if (response.code === 200 && response.data) {
      const status = response.data

      // 更新进度
      updateSynthesisProgress(status)

      // 检查是否完成 - 必须所有任务完成且云剪辑完成
      const allTasksCompleted = status.totalTasks > 0 && status.completedTasks === status.totalTasks
      const isFullyCompleted = (status.overallStatus === 'FULLY_COMPLETED') ||
                               (status.overallStatus === 'DIALOGUE_COMPLETED' && allTasksCompleted && status.alreadyClipped)

      if (isFullyCompleted) {
        synthesisStatus.isRunning = false
        synthesisStatus.isCompleted = true
        synthesisStatus.title = '合成完成'
        synthesisStatus.message = '数字人对话视频合成完成！'
        synthesisStatus.progress = 100

        finalResult.value = {
          dialogueGroupId: dialogueGroupId.value,
          finalVideoUrl: status.clipInfo?.finalVideoUrl,
          clipInfo: status.clipInfo
        }

        stopStatusMonitoring()
        ElMessage.success('数字人对话视频合成完成！')
      } else if (status.overallStatus === 'FAILED') {
        synthesisStatus.isRunning = false
        synthesisStatus.hasError = true
        synthesisStatus.title = '合成失败'
        synthesisStatus.message = status.message || '合成过程中出现错误'

        stopStatusMonitoring()
        ElMessage.error('合成失败: ' + (status.message || '请稍后重试'))
      } else {
        // 更新状态信息
        synthesisStatus.title = getStatusTitle(status)
        synthesisStatus.message = status.message || getStatusMessage(status)
      }
    } else {
      throw new Error(response.msg || '检查状态失败')
    }
  } catch (error) {
    console.error('检查自动合成状态失败:', error)
    // 不立即停止监控，可能是网络问题
  }
}

// 更新合成进度 - 简化版本
const updateSynthesisProgress = (status) => {
  // 检查是否所有任务都完成
  const allTasksCompleted = status.totalTasks > 0 && status.completedTasks === status.totalTasks

  // 完成状态
  if (status.overallStatus === 'FULLY_COMPLETED' ||
      (status.overallStatus === 'DIALOGUE_COMPLETED' && allTasksCompleted && status.alreadyClipped)) {
    synthesisStatus.progress = 100
    return
  }

  // 计算基础进度
  let progress = 20
  if (status.totalTasks && status.completedTasks !== undefined) {
    const taskProgress = (status.completedTasks / status.totalTasks) * 70
    progress += taskProgress

    // 如果所有任务完成但未云剪辑，显示90%
    if (allTasksCompleted && !status.alreadyClipped) {
      progress = 90
    }
  }

  synthesisStatus.progress = Math.min(progress, 95)
}

// 获取状态标题 - 简化版本
const getStatusTitle = (status) => {
  const allTasksCompleted = status.totalTasks > 0 && status.completedTasks === status.totalTasks

  if (status.overallStatus === 'FULLY_COMPLETED' ||
      (status.overallStatus === 'DIALOGUE_COMPLETED' && allTasksCompleted && status.alreadyClipped)) {
    return '合成完成'
  }
  if (status.overallStatus === 'FAILED') {
    return '合成失败'
  }
  return '处理中'
}

// 获取状态消息 - 简化版本
const getStatusMessage = (status) => {
  // 检查是否所有任务都完成
  const allTasksCompleted = status.totalTasks > 0 && status.completedTasks === status.totalTasks

  // 完成状态
  if (status.overallStatus === 'FULLY_COMPLETED' ||
      (status.overallStatus === 'DIALOGUE_COMPLETED' && allTasksCompleted && status.alreadyClipped)) {
    return '数字人对话视频合成完成！'
  }

  // 处理中状态 - 优先使用根级别任务状态
  if (status.totalTasks !== undefined && status.completedTasks !== undefined) {
    const { totalTasks, completedTasks, failedTasks = 0 } = status

    if (failedTasks > 0) {
      return `处理中，已完成 ${completedTasks}/${totalTasks}，${failedTasks} 个失败`
    }

    // 所有任务完成但未云剪辑
    if (allTasksCompleted && !status.alreadyClipped) {
      return `所有视频合成完成(${completedTasks}/${totalTasks})，正在进行云剪辑...`
    }

    // 部分任务完成
    if (completedTasks < totalTasks) {
      return `视频合成中，已完成 ${completedTasks}/${totalTasks}`
    }

    return `处理中，已完成 ${completedTasks}/${totalTasks}`
  }

  return '正在处理中...'
}

// 初始化视频信息
const initVideoInfo = () => {
  const currentTime = new Date().toLocaleString('zh-CN')
  videoInfo.title = `数字人对话视频_${currentTime}`
  updateVideoDescription()
}

// 更新视频描述（保留标题）
const updateVideoDescription = () => {
  // 动态生成描述，确保获取到最新的数据
  const dialogueCount = props.dialogueContent?.length || 0
  const humanCount = props.digitalHumans?.length || 0
  videoInfo.description = `包含${dialogueCount}条对话的数字人视频，参与数字人：${humanCount}个`
}

// 生命周期
onMounted(() => {
  // 初始化视频信息
  initVideoInfo()

  // V版的模型加载现在由 VersionSelector 组件处理
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopStatusMonitoring()
})
</script>

<style lang="scss" scoped>
.one-click-synthesis {
  .panel-header {
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    color: white;

    .header-info {
      .panel-title {
        font-size: 24px;
        font-weight: 700;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .panel-icon {
          font-size: 28px;
        }
      }

      .panel-desc {
        font-size: 14px;
        margin: 0;
        opacity: 0.9;
        line-height: 1.5;
      }
    }
  }

  .synthesis-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .one-click-synthesis {
    .synthesis-content {
      gap: 16px;
    }
  }
}
</style>
