<template>
  <div class="config-builder">
    <div class="builder-header">
      <div class="header-info">
        <h4 class="builder-title">
          <el-icon class="title-icon"><Setting /></el-icon>
          模板配置构建器
        </h4>
        <p class="builder-subtitle">选择预设类型快速生成模板配置</p>
      </div>
      <div class="header-actions">
        <el-button size="small" @click="previewConfig" :disabled="!hasValidConfig">
          <el-icon><View /></el-icon>
          预览
        </el-button>
        <el-button size="small" @click="copyConfig" :disabled="!hasValidConfig">
          <el-icon><DocumentCopy /></el-icon>
          复制
        </el-button>
      </div>
    </div>

    <div class="builder-content">
      <el-form label-position="top" class="config-form">
        <div class="form-section">
          <el-form-item label="模板预设类型" class="preset-selector">
            <el-select
              v-model="selectedPreset"
              placeholder="请选择一个预设来开始配置"
              @change="onPresetChange"
              class="preset-select"
            >
              <el-option
                v-for="preset in presetOptions"
                :key="preset.value"
                :label="preset.label"
                :value="preset.value"
              >
                <div class="preset-option">
                  <el-icon class="preset-icon"><component :is="preset.icon" /></el-icon>
                  <div class="preset-info">
                    <span class="preset-name">{{ preset.label }}</span>
                    <span class="preset-desc">{{ preset.description }}</span>
                  </div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <!-- 动态表单区域 -->
        <div v-if="selectedPreset" class="form-section">
          <div class="section-header">
            <h5 class="section-title">配置参数</h5>
            <el-tag :type="getPresetTagType(selectedPreset)" size="small">
              {{ getPresetLabel(selectedPreset) }}
            </el-tag>
          </div>

          <div class="preset-description">
            <el-icon class="desc-icon"><InfoFilled /></el-icon>
            {{ descriptions[selectedPreset] }}
          </div>

          <!-- 水印模板表单 -->
          <div v-if="selectedPreset === 'watermark'" class="preset-form">
            <el-form-item label="水印图片ID" class="form-item">
              <el-input
                v-model="watermarkForm.imageId"
                placeholder="请输入水印素材的MediaId"
                class="form-input"
              >
                <template #prefix>
                  <el-icon><Picture /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <div class="form-row">
              <el-form-item label="宽度 (px)" class="form-item">
                <el-input
                  v-model="watermarkForm.width"
                  placeholder="200"
                  class="form-input"
                >
                  <template #append>px</template>
                </el-input>
              </el-form-item>
              <el-form-item label="高度 (px)" class="form-item">
                <el-input
                  v-model="watermarkForm.height"
                  placeholder="60"
                  class="form-input"
                >
                  <template #append>px</template>
                </el-input>
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="X坐标" class="form-item">
                <el-input
                  v-model="watermarkForm.x"
                  placeholder="40"
                  class="form-input"
                >
                  <template #append>px</template>
                </el-input>
              </el-form-item>
              <el-form-item label="Y坐标" class="form-item">
                <el-input
                  v-model="watermarkForm.y"
                  placeholder="40"
                  class="form-input"
                >
                  <template #append>px</template>
                </el-input>
              </el-form-item>
            </div>
          </div>

          <!-- 片头片尾模板表单 -->
          <div v-if="selectedPreset === 'opening_ending'" class="preset-form">
            <el-form-item label="片头视频ID" class="form-item">
              <el-input
                v-model="openingEndingForm.openingMediaId"
                placeholder="请输入片头视频素材的MediaId"
                class="form-input"
              >
                <template #prefix>
                  <el-icon><VideoPlay /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="片尾视频ID" class="form-item">
              <el-input
                v-model="openingEndingForm.endingMediaId"
                placeholder="请输入片尾视频素材的MediaId"
                class="form-input"
              >
                <template #prefix>
                  <el-icon><VideoPlay /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </div>

          <!-- 静音模板说明 -->
          <div v-if="selectedPreset === 'mute'" class="preset-form">
            <el-alert
              title="静音模板配置"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                此模板会自动对所有传入的视频进行静音处理，无需额外配置参数。
                视频素材列表将在合成时通过 ClipsParam 的 "$VideoArray" 字段传入。
              </template>
            </el-alert>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Setting,
  View,
  DocumentCopy,
  Picture,
  VideoPlay,
  InfoFilled,
  Headset,
  MagicStick
} from '@element-plus/icons-vue';

// --- Props & Emits ---
const props = defineProps<{
  config?: string;
}>();

const emit = defineEmits<{
  (e: 'update:config', value: string): void;
}>();

// --- 内部状态 ---
type PresetType = 'watermark' | 'mute' | 'opening_ending' | '';
const selectedPreset = ref<PresetType>('');

// --- 表单数据模型 ---
const watermarkForm = reactive({ imageId: '', width: '200', height: '60', x: '40', y: '40' });
const openingEndingForm = reactive({ openingMediaId: '', endingMediaId: '' });

// 预设选项
const presetOptions = [
  {
    label: '视频添加水印',
    value: 'watermark',
    description: '为视频添加图片水印',
    icon: Picture
  },
  {
    label: '视频轨道整体静音',
    value: 'mute',
    description: '对视频进行静音处理',
    icon: Headset
  },
  {
    label: '增加片头片尾',
    value: 'opening_ending',
    description: '添加片头和片尾视频',
    icon: VideoPlay
  }
];

const descriptions: Record<Exclude<PresetType, ''>, string> = {
  watermark: '此预设将在主视频轨道上添加一个图片水印。主视频素材ID需在合成时通过 ClipsParam 的 "$Video" 字段传入。',
  mute: '此预设将对所有传入的视频进行静音处理。视频素材列表需在合成时通过 ClipsParam 的 "$VideoArray" 字段传入。',
  opening_ending: '此预设将在视频素材列表的开始和结束位置分别插入一个固定的片头和片尾视频。'
};

// 计算属性
const hasValidConfig = computed(() => {
  return selectedPreset.value !== '';
});

// 获取预设标签类型
const getPresetTagType = (preset: string) => {
  const typeMap: Record<string, string> = {
    watermark: 'primary',
    mute: 'warning',
    opening_ending: 'success'
  };
  return typeMap[preset] || 'info';
};

// 获取预设标签文本
const getPresetLabel = (preset: string) => {
  const option = presetOptions.find(opt => opt.value === preset);
  return option?.label || '';
};


// --- 方法 ---

// 预览配置
const previewConfig = () => {
  generateConfig();
  ElMessage.success('配置已生成，请查看下方结果');
};

// 复制配置
const copyConfig = async () => {
  try {
    const configText = generateConfigText();
    await navigator.clipboard.writeText(configText);
    ElMessage.success('配置已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败，请手动复制');
  }
};

const onPresetChange = () => {
  // 清理旧的表单数据(如果需要)
  generateConfig();
};

// 生成配置文本
const generateConfigText = (): string => {
  const configObject = generateConfigObject();
  return JSON.stringify(configObject, null, 2);
};

// 生成配置对象
const generateConfigObject = (): object => {
  let configObject: object = {};

  switch (selectedPreset.value) {
    case 'watermark':
      configObject = {
        VideoTracks: [{ VideoTrackClips: [{ MediaId: "$Video" }] }],
        ImageTracks: [{
          ImageTrackClips: [{
            ImageId: watermarkForm.imageId,
            Width: Number(watermarkForm.width) || undefined,
            Height: Number(watermarkForm.height) || undefined,
            X: `$X:${watermarkForm.x || 40}`,
            Y: `$Y:${watermarkForm.y || 40}`,
            TimelineIn: "$TimelineIn:0",
            TimelineOut: "$TimelineOut:NULL"
          }]
        }]
      };
      break;

    case 'mute':
      configObject = {
        VideoTracks: [{
          VideoTrackClips: [{
            Sys_Type: "ArrayItems",
            Sys_ArrayObject: "$VideoArray",
            Sys_Template: {
              MediaId: "$MediaId",
              Effects: [{ Type: "Volume", Gain: "0" }]
            }
          }]
        }]
      };
      break;

    case 'opening_ending':
      configObject = {
        VideoTracks: [{
          VideoTrackClips: [
            { MediaId: openingEndingForm.openingMediaId },
            {
              Sys_Type: "ArrayItems",
              Sys_ArrayObject: "$VideoArray",
              Sys_Template: { MediaId: "$MediaId" }
            },
            { MediaId: openingEndingForm.endingMediaId }
          ]
        }]
      };
      break;
  }

  return configObject;
};

const generateConfig = () => {
  const configText = generateConfigText();
  emit('update:config', configText);
};

// 监听所有表单变化，自动更新Config
watch([() => props.config, selectedPreset, watermarkForm, openingEndingForm], () => {
  // 当外部的config变化时，需要尝试解析它并填充表单
  // 这个逻辑将在后续实现（用于编辑功能）
  // 目前，我们只确保内部变化能正确生成config
  generateConfig();
}, { deep: true });

onMounted(() => {
  // 初始化时生成一次
  generateConfig();
})

</script>

<style lang="scss" scoped>
.config-builder {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.builder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;

  .header-info {
    .builder-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
      margin: 0 0 4px 0;

      .title-icon {
        color: #667eea;
      }
    }

    .builder-subtitle {
      font-size: 13px;
      color: #64748b;
      margin: 0;
    }
  }

  .header-actions {
    display: flex;
    gap: 8px;

    .el-button {
      border-radius: 6px;
      font-size: 12px;

      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

.builder-content {
  padding: 24px;
}

.config-form {
  .form-section {
    background: #f8fafc;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e2e8f0;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .preset-selector {
    margin-bottom: 0;

    .preset-select {
      width: 100%;

      :deep(.el-input__wrapper) {
        border-radius: 8px;
      }
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #374151;
      margin: 0;
    }
  }

  .preset-description {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 12px 16px;
    background: #e0f2fe;
    border: 1px solid #0891b2;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 13px;
    color: #0c4a6e;
    line-height: 1.5;

    .desc-icon {
      color: #0891b2;
      margin-top: 2px;
      flex-shrink: 0;
    }
  }

  .preset-form {
    .form-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #374151;
        font-size: 13px;
      }
    }

    .form-input {
      :deep(.el-input__wrapper) {
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          border-color: #9ca3af;
        }

        &.is-focus {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
      }
    }

    .form-row {
      display: flex;
      gap: 16px;

      .form-item {
        flex: 1;
      }
    }
  }
}

.preset-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;

  .preset-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f1f5f9;
    border-radius: 4px;
    color: #64748b;
    flex-shrink: 0;
  }

  .preset-info {
    flex: 1;

    .preset-name {
      display: block;
      font-weight: 500;
      color: #1e293b;
      font-size: 14px;
    }

    .preset-desc {
      display: block;
      font-size: 12px;
      color: #64748b;
      margin-top: 2px;
    }
  }
}

:deep(.el-alert) {
  border-radius: 8px;

  .el-alert__content {
    font-size: 13px;
    line-height: 1.5;
  }
}
</style>