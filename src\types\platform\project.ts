import Api, { ApiService } from "@/annotation/Api";


class BaseEntity {
    public createBy?: string;
    public createTime?: Date;
    public updateBy?: string;
    public updateTime?: Date;
    public remark?: string;
    public params: { [key: string]: string } = {};
}

/**
 * 项目对象 platform_project
 * 
 * <AUTHOR>
 * @date 2024-09-09
 */
@Api('/platform/project')
export class PlatformProject extends BaseEntity {
    public projectId?: number
    public projectTitle?: string
    constructor() { 
        super(); 
        console.log(this);
        
    }
}

export const projectService = ApiService(PlatformProject)