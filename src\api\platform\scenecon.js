import request from '@/utils/request'

// 查询场控管理列表
export function listScenecon(query) {
  return request({
    url: '/platform/scenecon/list',
    method: 'get',
    params: query
  })
}

// 查询场控管理详细
export function getScenecon(sceneconId) {
  return request({
    url: '/platform/scenecon/' + sceneconId,
    method: 'get'
  })
}

// 新增场控管理
export function addScenecon(data) {
  return request({
    url: '/platform/scenecon',
    method: 'post',
    data: data
  })
}

// 修改场控管理
export function updateScenecon(data) {
  return request({
    url: '/platform/scenecon',
    method: 'put',
    data: data
  })
}

// 删除场控管理
export function delScenecon(sceneconId) {
  return request({
    url: '/platform/scenecon/' + sceneconId,
    method: 'delete'
  })
}
