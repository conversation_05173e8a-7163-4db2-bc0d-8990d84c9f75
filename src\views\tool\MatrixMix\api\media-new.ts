/**
 * 阿里云ICE媒资管理相关API
 * 通过后端代理调用阿里云ICE SDK
 */
import request from '@/utils/request';

// ============================================================================
// 类型定义 - 根据阿里云ICE官方文档
// ============================================================================

export interface MediaBasicInfo {
  MediaId: string;
  InputURL?: string;
  MediaType: string; // "video" | "audio" | "image"
  BusinessType?: string;
  Source?: string;
  Title?: string;
  Description?: string;
  Category?: string;
  MediaTags?: string;
  CoverURL?: string;
  UserData?: string;
  Snapshots?: any[];
  Status?: string;
  TranscodeStatus?: string;
  CreateTime?: string;
  ModifiedTime?: string;
  DeletedTime?: string;
  SpriteImages?: any[];
  CateId?: number;
  Biz?: string;
  UploadSource?: string;
  CateName?: string;
  ReferenceId?: string;
}

export interface FileBasicInfo {
  FileName: string;
  FileStatus: string;
  FileType: string;
  FileSize: number;
  FileUrl: string;
  Region: string;
  FormatName: string;
  Duration: number;
  Bitrate: number;
  Width: number;
  Height: number;
  CreateTime: string;
  ModifiedTime: string;
}

export interface MediaInfo {
  MediaId: string;
  MediaBasicInfo: MediaBasicInfo;
  FileInfoList?: Array<{
    FileBasicInfo: FileBasicInfo;
    AudioStreamInfoList?: any[];
    VideoStreamInfoList?: any[];
    SubtitleStreamInfoList?: any[];
  }>;
  AiRoughData?: any;
}

export interface MediaSearchParams {
  keyword?: string;
  mediaType?: string;
  pageNo?: number;
  pageSize?: number;
  sortBy?: string;
  nextToken?: string;
  maxResults?: number;
}

export interface MediaListResponse {
  MediaInfos: MediaInfo[];
  TotalCount: number;
  NextToken?: string;
  MaxResults?: number;
}

export interface MediaUploadRequest {
  file: File;
  title?: string;
  description?: string;
}

export interface MediaUploadResponse {
  MediaId: string;
  UploadURL: string;
  UploadAuth: string;
}

export interface MediaRegisterRequest {
  inputURL: string;
  mediaMetaData: {
    Title: string;
    Description?: string;
  };
}

export interface MediaRegisterResponse {
  MediaId: string;
}

export interface BatchGetMediaInfosRequest {
  mediaIds: string[];
  additionType?: string;
}

export interface BatchGetMediaInfosResponse {
  MediaInfos: MediaInfo[];
}

export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// ============================================================================
// 兼容性类型（保持向后兼容）
// ============================================================================

export interface MediaInfoQuery {
  MediaId?: string;
  InputURL?: string;
}

export interface MediaInfoResponse {
  MediaInfo: MediaInfo;
}

export interface MediaPayload extends MediaBasicInfo {}

export interface MediaListQueryParams extends MediaSearchParams {}

export interface GetEditingProjectMaterialsRequest {
  projectId: string;
}

export interface GetEditingProjectMaterialsResponse {
  materials: any[];
}

export interface UploadAndRegisterResponse extends MediaUploadResponse {}

// ============================================================================
// API接口
// ============================================================================

/**
 * 获取媒资信息
 */
export function getMediaInfo(params: MediaInfoQuery): Promise<ApiResponse<MediaInfoResponse>> {
  const mediaId = params.MediaId;
  if (!mediaId) {
    throw new Error('MediaId is required');
  }
  
  return request({
    url: `/api/ice/media/${mediaId}`,
    method: 'get'
  }).then((res: any) => ({
    ...res,
    data: {
      MediaInfo: res.data
    }
  }));
}

/**
 * 搜索媒资
 */
export function searchMedia(params: MediaSearchParams = {}): Promise<ApiResponse<MediaListResponse>> {
  return request({
    url: '/api/ice/media/search',
    method: 'get',
    params: {
      keyword: params.keyword,
      mediaType: params.mediaType,
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 20,
      sortBy: params.sortBy || 'CreationTime:Desc',
      nextToken: params.nextToken,
      maxResults: params.maxResults
    }
  });
}

/**
 * 批量获取媒资信息
 */
export function batchGetMediaInfos(data: BatchGetMediaInfosRequest): Promise<ApiResponse<BatchGetMediaInfosResponse>> {
  return request({
    url: '/api/ice/media/batch',
    method: 'post',
    data
  });
}

/**
 * 上传媒资文件
 */
export function uploadMedia(data: MediaUploadRequest): Promise<ApiResponse<MediaUploadResponse>> {
  const formData = new FormData();
  formData.append('file', data.file);
  if (data.title) {
    formData.append('title', data.title);
  }
  if (data.description) {
    formData.append('description', data.description);
  }

  return request({
    url: '/api/ice/media/upload',
    method: 'post',
    data: formData
  });
}

/**
 * 注册媒资
 */
export function registerMedia(data: MediaRegisterRequest): Promise<ApiResponse<MediaRegisterResponse>> {
  return request({
    url: '/api/ice/media/register',
    method: 'post',
    data
  });
}

/**
 * 获取剪辑工程关联的素材
 */
export function getEditingProjectMaterials(params: GetEditingProjectMaterialsRequest): Promise<ApiResponse<GetEditingProjectMaterialsResponse>> {
  return request({
    url: `/api/ice/projects/${params.projectId}/materials`,
    method: 'get'
  });
}

// ============================================================================
// 兼容性接口（保持向后兼容）
// ============================================================================

/**
 * 获取媒资列表（兼容旧接口）
 */
export function getMediaList(params: MediaListQueryParams): Promise<ApiResponse<MediaListResponse>> {
  return searchMedia(params);
}

/**
 * 上传并注册媒资（兼容旧接口）
 */
export function uploadAndRegisterMedia(data: MediaUploadRequest): Promise<ApiResponse<UploadAndRegisterResponse>> {
  return uploadMedia(data);
}
