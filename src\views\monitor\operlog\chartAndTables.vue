<template>
<div>
    <!-- 左侧 成功次数 中间饼图 右侧失败次数 -->
    <el-row :gutter="20">
        <el-col :span="8">
            <el-card shadow="always" class="box-card hover-card">
                <template #header>
                    <span>成功次数排名</span>
                </template>
                <el-table :data="paginatedSuccessRank" stripe style="height: 370px;">
                    <el-table-column label="系统模块" prop="title" align="center" />
                    <el-table-column label="成功次数" prop="count" align="center" sortable/>
                </el-table>
                <br>
                <el-pagination v-model:current-page="successCurrentPage" :page-size="pageSize" :total="successStats.length"
                @current-change="handleSuccessPageChange" layout="prev, pager, next" background/>
            </el-card>
        </el-col>
        <el-col :span="8">
            <el-card shadow="always" class="box-card hover-card">
                <template #header>
                    <span>操作状态统计</span>
                </template>
                <div id="statusPieChart" style="width: 100%; height: 420px;"></div>
            </el-card>
        </el-col>
        <el-col :span="8">
            <el-card shadow="always" class="box-card hover-card">
                <template #header>
                    <span>失败次数排名</span>
                </template>
                <el-table :data="paginatedFailureRank" stripe style="height: 370px;">
                    <el-table-column label="系统模块" prop="title" align="center" />
                    <el-table-column label="失败次数" prop="count" align="center" sortable/>
                </el-table>
                <br>
                <el-pagination v-model:current-page="failureCurrentPage" :page-size="pageSize" :total="failureStats.length"
                @current-change="handleFailurePageChange" layout="prev, pager, next" background/>
            </el-card>
        </el-col>
    </el-row>
</div>
</template>

<script setup name="NewChartAndTables">
import { ref, computed, onMounted, watch, getCurrentInstance } from 'vue';
import * as echarts from 'echarts';
import { business } from "@/api/monitor/operlog";

const props = defineProps({
    queryParams: Object,
    dateRange: Array
});
const { proxy } = getCurrentInstance();
const successStats = ref([]);
const failureStats = ref([]);
const statusStats = ref([]);
const successCurrentPage = ref(1);
const failureCurrentPage = ref(1);
const pageSize = 8;

/** 成功请求分页 */
const paginatedSuccessRank = computed(() => {
    if (!successStats.value) return [];
    const start = (successCurrentPage.value - 1) * pageSize;
    const end = start + pageSize;
    return successStats.value.slice(start, end);
});

/** 失败请求分页 */
const paginatedFailureRank = computed(() => {
    if (!failureStats.value) return [];
    const start = (failureCurrentPage.value - 1) * pageSize;
    const end = start + pageSize;
    return failureStats.value.slice(start, end);
});

/** 查询业务信息 */
function getList() {
    business(proxy.addDateRange(props.queryParams, props.dateRange)).then(response => {
        successStats.value = response.data.successStats.map(item => ({ title: item.title, count: item.success_count })) || [];
        failureStats.value = response.data.failureStats.map(item => ({ title: item.title, count: item.failure_count })) || [];
        statusStats.value = response.data.statusStats || [];
        updateCharts();
    });
}

/** 更新图表 */
function updateCharts() {
    if (statusStats.value.length == 0) return;
    updateStatusPieChart();
}

/** 渲染饼图 */
function updateStatusPieChart() {
    const successCount = statusStats.value.find(item => item.status_desc == '成功')?.status_count || 0;
    const failureCount = statusStats.value.find(item => item.status_desc == '失败')?.status_count || 0;
    const chart = echarts.init(document.getElementById('statusPieChart'));
    chart.setOption({
        tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c} ({d}%)' },
        legend: { orient: 'vertical', left: 'left'},
        series: [ 
        { name: '操作状态', type: 'pie', radius: '60%',
            data: [
            { value: failureCount, name: '失败', itemStyle: { color: '#ec0000', }},
            { value: successCount, name: '成功', itemStyle: { color: '#67C23A' }}
            ],
            emphasis: {
            itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(4, 116, 245, 0.5)' }
            }
        }
        ]
    });
    chart.resize();
}

/** 成功次数分页 */
function handleSuccessPageChange(page) {
    successCurrentPage.value = page;
}

/** 失败次数分页 */
function handleFailurePageChange(page) {
    failureCurrentPage.value = page;
}

onMounted(() => {
    getList();
});

watch([props.queryParams, props.dateRange], () => {
    getList();
});
</script>
<style scoped>
.box-card {
    margin-bottom: 20px;
    border-radius: 15px;
    transition: transform 0.3s, box-shadow 0.3s;
}

.box-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 2px 12px rgba(4, 116, 245, 0.3);
}
</style>
