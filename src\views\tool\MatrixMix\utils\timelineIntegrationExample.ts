/**
 * 时间轴水平吸附集成示例
 * 展示如何在 Timeline.vue 中使用水平吸附功能
 */

import { calculateNewTimePosition } from './timelineDragUtils'
import { DEFAULT_TIME_SNAP_CONFIG, type TimeSnapConfig } from './timelineHorizontalSnapUtils'
import type { DragState } from './timelineDragUtils'

/**
 * 示例：在片段拖拽结束时计算吸附位置
 */
export function exampleEndClipDrag(
  event: MouseEvent,
  dragState: DragState,
  timeline: any,
  PIXELS_PER_SECOND: number = 50
) {
  // 使用增强版时间位置计算函数
  const timeCalculationResult = calculateNewTimePosition(
    event,
    dragState,
    PIXELS_PER_SECOND,
    timeline, // 传入时间轴数据用于水平吸附
    true, // 启用水平吸附
    DEFAULT_TIME_SNAP_CONFIG // 使用默认吸附配置
  );
  
  const finalTime = timeCalculationResult.time;
  const snapResult = timeCalculationResult.snapResult;
  
  // 检查是否发生了水平吸附
  if (snapResult?.isSnapped) {
    console.log('💫 水平吸附生效:', {
      原始时间: (event.clientX - dragState.startX) / PIXELS_PER_SECOND + dragState.clip!.start,
      吸附时间: finalTime,
      吸附点类型: snapResult.snapPoint?.type,
      吸附点时间: snapResult.snapPoint?.time
    });
  }
  
  return { finalTime, snapResult };
}

/**
 * 示例：自定义吸附配置
 */
export const CUSTOM_SNAP_CONFIG: TimeSnapConfig = {
  threshold: 0.3,              // 增加吸附阈值到0.3秒
  enableClipSnap: true,        // 启用片段边界吸附
  enableRulerSnap: true,       // 启用标尺吸附
  enablePlayheadSnap: true,    // 启用播放头吸附
  rulerInterval: 0.5,          // 按0.5秒间隔吸附
  stabilityThreshold: 2        // 降低稳定性要求
};

/**
 * 示例：在拖拽过程中提供实时吸附反馈
 */
export function exampleDragWithHorizontalFeedback(
  event: MouseEvent,
  dragState: DragState,
  timeline: any,
  PIXELS_PER_SECOND: number = 50,
  onSnapFeedback?: (snapResult: any) => void
) {
  // 计算实时水平吸附
  const timeResult = calculateNewTimePosition(
    event,
    dragState,
    PIXELS_PER_SECOND,
    timeline,
    true,
    CUSTOM_SNAP_CONFIG
  );
  
  // 提供吸附反馈
  if (onSnapFeedback && timeResult.snapResult) {
    onSnapFeedback(timeResult.snapResult);
  }
  
  return timeResult;
}

/**
 * 示例：集成到现有 Timeline.vue 的建议修改
 */
export const INTEGRATION_TIPS = `
// 在 Timeline.vue 的 endClipDrag 函数中：

const endClipDrag = (event: MouseEvent) => {
    // ... 现有代码 ...
    
    // 使用增强版时间位置计算（替换现有的简单计算）
    const timeCalculationResult = calculateNewTimePosition(
        event,
        currentDragState.value,
        PIXELS_PER_SECOND,
        videoEditorStore.timeline, // 提供时间轴数据
        true, // 启用水平吸附
        DEFAULT_TIME_SNAP_CONFIG // 或使用自定义配置
    );
    
    const snappedTime = timeCalculationResult.time;
    const horizontalSnapResult = timeCalculationResult.snapResult;
    
    // 可选：显示吸附反馈
    if (horizontalSnapResult?.isSnapped) {
        console.log('⚡ 水平吸附:', horizontalSnapResult.snapPoint?.type);
    }
    
    // ... 使用 snappedTime 更新片段位置 ...
};

// 在 handleClipDrag 函数中（可选的实时反馈）：
const handleClipDrag = (event: MouseEvent) => {
    // ... 现有的垂直吸附代码 ...
    
    // 添加水平吸附反馈（不强制，仅用于预览）
    const timePreview = calculateNewTimePosition(
        event,
        currentDragState.value,
        PIXELS_PER_SECOND,
        videoEditorStore.timeline,
        true,
        DEFAULT_TIME_SNAP_CONFIG
    );
    
    // 可以在这里显示水平吸附的视觉反馈
    // 例如高亮显示将要吸附的位置
};
`;
