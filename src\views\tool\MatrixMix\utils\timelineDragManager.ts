import { ref, computed } from 'vue';
import useDrag from '@/hook/drag';
import { smartTrackDragCalculation, sortTracksForDisplay, type TrackType, type DragCalculationResult } from './timelineUtils_clean';
import { processTrackData } from './timelineTrackUtils';
import { alicloudTrackManager, type TrackCreationResult } from './alicloudTrackManager';
import { reorderTrackIdsForInsertion } from './trackIdReorderer';

// 吸附点类型
interface SnapPoint {
  y: number;           // Y坐标
  trackIndex: number;  // 轨道索引
  position: 'top' | 'center' | 'bottom'; // 位置类型
  trackType: 'video' | 'audio' | 'subtitle'; // 轨道类型
}

// 吸附配置
const SNAP_THRESHOLD = 999; // 吸附阈值设置为很大的值，确保总是吸附
const TRACK_HEIGHT = 61;  // 轨道高度

/**
 * 时间轴拖拽管理器
 * 基于全局拖拽函数，在回调中进行块级限制和层级解析
 * 包含吸附功能
 */
export function createTimelineDragManager() {
  // 拖拽状态
  const isDragging = ref(false);
  const dragData = ref<any>(null);
  const dragPreview = ref<DragCalculationResult | null>(null);
  
  // 吸附辅助线状态
  const snapGuidelineY = ref<number | null>(null);
  const snapGuidelineType = ref<'top' | 'center' | 'bottom' | null>(null);
  
  // 时间轴容器相关
  const timelineContainer = ref<HTMLElement | null>(null);
  const scrollContainer = ref<HTMLElement | null>(null);
  
  // 轨道数据
  const displayTracks = ref<any[]>([]);
  const allTracks = ref<any[]>([]);
  const displayVideoTracks = ref<any[]>([]);
  const displayAudioTracks = ref<any[]>([]);
  const displaySubtitleTracks = ref<any[]>([]);
  const trackHeight = ref(TRACK_HEIGHT);
  
  /**
   * 计算片段中心线Y坐标
   * @param element 拖拽的片段元素
   * @param mouseY 当前鼠标Y坐标（相对于滚动容器）
   * @param startY 拖拽开始时的鼠标Y坐标
   * @param scrollContainer 滚动容器元素
   * @returns 片段中心线的Y坐标（相对于滚动容器内容）
   */
  function calculateClipCenterY(
    element: HTMLElement, 
    mouseY: number, 
    startY: number, 
    scrollContainer: HTMLElement
  ): number {
    // 获取片段元素的原始位置信息
    const clipRect = element.getBoundingClientRect();
    const scrollContainerRect = scrollContainer.getBoundingClientRect();
    
    // 计算拖拽的偏移量
    const dragOffsetY = mouseY - startY;
    
    // 计算片段原始中心线位置（相对于滚动容器）
    const originalClipCenterY = (clipRect.top - scrollContainerRect.top) + (clipRect.height / 2) + scrollContainer.scrollTop;
    
    // 应用拖拽偏移，得到当前片段中心线位置
    const currentClipCenterY = originalClipCenterY + dragOffsetY;
    
    return currentClipCenterY;
  }

  /**
   * 计算所有吸附点
   */
  function calculateSnapPoints(): SnapPoint[] {
    const points: SnapPoint[] = [];
    let currentY = 0;
    
    // 字幕轨道吸附点
    displaySubtitleTracks.value.forEach((track, displayIndex) => {
      const trackY = currentY + displayIndex * trackHeight.value;
      const actualTrackIndex = track.originalIndex >= 0 ? track.originalIndex : displayIndex;
      points.push(
        { y: trackY, trackIndex: actualTrackIndex, position: 'top', trackType: 'subtitle' },
        { y: trackY + trackHeight.value / 2, trackIndex: actualTrackIndex, position: 'center', trackType: 'subtitle' },
        { y: trackY + trackHeight.value, trackIndex: actualTrackIndex, position: 'bottom', trackType: 'subtitle' }
      );
    });
    
    currentY += displaySubtitleTracks.value.length * trackHeight.value;
    
    // 视频轨道吸附点
    displayVideoTracks.value.forEach((track, displayIndex) => {
      const trackY = currentY + displayIndex * trackHeight.value;
      const actualTrackIndex = track.originalIndex >= 0 ? track.originalIndex : displayIndex;
      points.push(
        { y: trackY, trackIndex: actualTrackIndex, position: 'top', trackType: 'video' },
        { y: trackY + trackHeight.value / 2, trackIndex: actualTrackIndex, position: 'center', trackType: 'video' },
        { y: trackY + trackHeight.value, trackIndex: actualTrackIndex, position: 'bottom', trackType: 'video' }
      );
    });
    
    currentY += displayVideoTracks.value.length * trackHeight.value;
    
    // 音频轨道吸附点
    displayAudioTracks.value.forEach((track, displayIndex) => {
      const trackY = currentY + displayIndex * trackHeight.value;
      const actualTrackIndex = track.originalIndex >= 0 ? track.originalIndex : displayIndex;
      points.push(
        { y: trackY, trackIndex: actualTrackIndex, position: 'top', trackType: 'audio' },
        { y: trackY + trackHeight.value / 2, trackIndex: actualTrackIndex, position: 'center', trackType: 'audio' },
        { y: trackY + trackHeight.value, trackIndex: actualTrackIndex, position: 'bottom', trackType: 'audio' }
      );
    });
    
    return points;
  }

  /**
   * 查找最近的吸附点 - 基于鼠标位置判定吸附，但返回基于片段中心线的对齐位置
   */
  function findNearestSnapPoint(mouseY: number, dragType: TrackType): SnapPoint | null {
    const allSnapPoints = calculateSnapPoints();
    const relevantPoints = allSnapPoints.filter(point => point.trackType === dragType);
    
    if (relevantPoints.length === 0) return null;
    
    let nearestPoint: SnapPoint | null = null;
    let minDistance = Infinity;
    
    // 基于鼠标位置查找最近的吸附点
    for (const point of relevantPoints) {
      const distance = Math.abs(mouseY - point.y);
      if (distance < minDistance) {
        minDistance = distance;
        nearestPoint = point;
      }
    }
    
    // 如果在吸附阈值内，返回吸附点（后续会基于片段中心线对齐）
    if (nearestPoint && minDistance <= SNAP_THRESHOLD) {
      return nearestPoint;
    }
    
    return null;
  }

  /**
   * 处理吸附逻辑 - 基于鼠标触发，片段中心线对齐
   */
  function handleSnapping(
    element: HTMLElement,
    mouseY: number, 
    startY: number,
    scrollContainer: HTMLElement,
    dragType: TrackType
  ): { snapY: number; isSnapped: boolean; snapPoint: SnapPoint | null } {
    // 基于鼠标位置查找吸附点
    const nearestSnap = findNearestSnapPoint(mouseY, dragType);
    
    if (nearestSnap) {
      // 计算片段中心线当前位置
      const clipCenterY = calculateClipCenterY(element, mouseY, startY, scrollContainer);
      
      // 计算需要的偏移量，使片段中心线对齐到吸附点
      const centerOffset = nearestSnap.y - clipCenterY;
      const snapY = mouseY + centerOffset;
      
      // 设置辅助线
      snapGuidelineY.value = nearestSnap.y;
      snapGuidelineType.value = nearestSnap.position;
      
      return {
        snapY: snapY,
        isSnapped: true,
        snapPoint: nearestSnap
      };
    } else {
      // 如果没有吸附点，保持鼠标位置
      snapGuidelineY.value = null;
      snapGuidelineType.value = null;
      
      return {
        snapY: mouseY,
        isSnapped: false,
        snapPoint: null
      };
    }
  }

  /**
   * 初始化拖拽
   */
  function initDrag(containerEl: HTMLElement, scrollEl: HTMLElement) {
    timelineContainer.value = containerEl;
    scrollContainer.value = scrollEl;
  }
  
  /**
   * 检查拖拽是否在时间轴容器内
   */
  function isInTimelineContainer(x: number, y: number): boolean {
    if (!timelineContainer.value) return false;
    
    const rect = timelineContainer.value.getBoundingClientRect();
    return x >= rect.left && 
           x <= rect.right && 
           y >= rect.top && 
           y <= rect.bottom;
  }
  
  /**
   * 获取滚动容器的位置信息
   */
  function getScrollInfo() {
    if (!scrollContainer.value) return { rect: new DOMRect(), scrollTop: 0 };
    
    return {
      rect: scrollContainer.value.getBoundingClientRect(),
      scrollTop: scrollContainer.value.scrollTop
    };
  }
  
  /**
   * 第一步：块级限制检查
   * 检查拖拽是否在允许的区域内
   */
  function checkBlockConstraints(x: number, y: number): boolean {
    // 检查是否在时间轴容器内
    if (!isInTimelineContainer(x, y)) {
      return false;
    }
    
    // 可以添加更多的块级限制
    // 比如检查是否在特定的拖拽允许区域内
    
    return true;
  }
  
  /**
   * 第二步：定位计算
   * 根据鼠标位置计算属于哪个轨道区域
   */
  function calculateTrackPosition(x: number, y: number, trackType: TrackType): DragCalculationResult | null {
    if (!checkBlockConstraints(x, y)) {
      return null;
    }
    
    const { rect, scrollTop } = getScrollInfo();
    
    // 使用现有的智能轨道计算函数
    const result = smartTrackDragCalculation({
      mouseY: y,
      trackType,
      scrollAreaRect: rect,
      scrollTop,
      trackHeight: trackHeight.value,
      displayTracks: displayTracks.value,
      allTracks: allTracks.value,
      sourceTrackIndex: dragData.value?.sourceTrackIndex,
      dragStartY: dragData.value?.startY,
      isDraggingFromLibrary: dragData.value?.isFromLibrary || false
    });
    
    return result;
  }
  
  /**
   * 第三步：预览更新
   * 根据计算结果更新拖拽预览 - 已移除预览功能
   */
  function updateDragPreview(dragResult: DragCalculationResult | null, trackType: TrackType) {
    dragPreview.value = dragResult;
    // 预览功能已移除，不再更新预览轨道
  }
  
  /**
   * 第四步：最终应用
   * 拖拽结束时应用最终的更改
   */
  function applyDragResult(dragResult: DragCalculationResult, draggedItem: any) {
    if (!dragResult.isValidDrop) return;
    
    // 根据拖拽结果执行相应的操作
    if (dragResult.isNewTrack) {
      // 创建新轨道
      handleCreateNewTrack(draggedItem, dragResult);
    } else {
      // 移动到现有轨道
      handleMoveToExistingTrack(draggedItem, dragResult);
    }
  }
  
  /**
   * 处理创建新轨道（通过回调获取timeline数据）
   */
  function handleCreateNewTrack(item: any, dragResult: DragCalculationResult) {
    // 通过回调获取timeline数据
    if (callbacks.value.onCreateTrack) {
      const result = callbacks.value.onCreateTrack(item, dragResult);
      return result;
    }
  }
  
  /**
   * 处理创建新轨道（带timeline数据）- 使用智能ID重排
   */
  function handleCreateNewTrackWithTimeline(item: any, dragResult: DragCalculationResult, timeline: any): TrackCreationResult {
    const { type } = item;
    if (!type) {
      return { success: false, error: "缺少轨道类型信息" };
    }
    
    const trackTypeMap = {
      'video': 'Video' as const,
      'audio': 'Audio' as const, 
      'subtitle': 'Subtitle' as const
    };
    const aliTrackType = trackTypeMap[type as keyof typeof trackTypeMap];
    
    // 智能ID重排 - 使用具体的拖拽位置信息
    let insertPosition: 'top' | 'bottom' | 'above' | 'below';
    let targetTrackIndex: number | undefined;
    
    if (dragResult.insertPosition === 'above') {
      insertPosition = 'above';
      targetTrackIndex = dragResult.targetTrackIndex ?? undefined;
    } else if (dragResult.insertPosition === 'below') {
      insertPosition = 'below';
      targetTrackIndex = dragResult.targetTrackIndex ?? undefined;
    } else if (dragResult.insertPosition === 'replace') {
      insertPosition = 'above';
      targetTrackIndex = dragResult.targetTrackIndex ?? undefined;
    } else if (dragResult.insertPosition === 'top') {
      insertPosition = 'top';
    } else if (dragResult.insertPosition === 'bottom') {
      insertPosition = 'bottom';
    } else {
      insertPosition = 'bottom';
    }
    
    // 执行ID重排
    const reorderResult = reorderTrackIdsForInsertion(timeline, aliTrackType, insertPosition, targetTrackIndex);
    
    if (!reorderResult.success) {
      return { success: false, error: `ID重排失败: ${reorderResult.error}` };
    }
    
    // 创建新轨道
    const newTrack: any = {
      Id: reorderResult.newTrackId,
      Type: aliTrackType
    };
    
    // 添加轨道类型特有的片段数组
    if (aliTrackType === 'Video') {
      newTrack.VideoTrackClips = [];
    } else if (aliTrackType === 'Audio') {
      newTrack.AudioTrackClips = [];
    } else if (aliTrackType === 'Subtitle') {
      newTrack.SubtitleTrackClips = [];
    }
    
    // 插入新轨道
    const tracksKey = `${aliTrackType}Tracks`;
    if (!timeline[tracksKey]) {
      timeline[tracksKey] = [];
    }
    
    timeline[tracksKey].push(newTrack);
    
    // 按ID排序确保显示顺序正确
    timeline[tracksKey].sort((a: any, b: any) => b.Id - a.Id);
    
    return {
      success: true,
      timeline,
      newTrackId: reorderResult.newTrackId.toString(),
      validationLog: [
        `智能ID重排完成，新轨道ID: ${reorderResult.newTrackId}`,
        `调整了 ${reorderResult.adjustedTracks.length} 个轨道的ID`,
        ...reorderResult.adjustedTracks.map(adj => `轨道ID: ${adj.oldId} → ${adj.newId}`)
      ]
    };
  }
  
  /**
   * 处理移动到现有轨道
   */
  function handleMoveToExistingTrack(item: any, dragResult: DragCalculationResult) {
    // 这里可以触发外部回调
    if (callbacks.value.onMoveToTrack) {
      callbacks.value.onMoveToTrack(item, dragResult);
    }
  }
  
  // 回调函数管理
  const callbacks = ref<{
    onCreateTrack?: (item: any, result: DragCalculationResult) => void;
    onMoveToTrack?: (item: any, result: DragCalculationResult) => void;
    onUpdatePreview?: (tracks: any[]) => void;
  }>({});
  
  /**
   * 设置回调函数
   */
  function setCallbacks(newCallbacks: typeof callbacks.value) {
    callbacks.value = { ...callbacks.value, ...newCallbacks };
  }
  
  /**
   * 创建拖拽克隆元素 - 无动画效果
   */
  function createDragClone(element: HTMLElement, offsetX: number, offsetY: number, clientX: number, clientY: number): HTMLElement {
    const dragClone = element.cloneNode(true) as HTMLElement;
    dragClone.style.position = 'fixed';
    dragClone.style.zIndex = '10000';
    dragClone.style.pointerEvents = 'none';
    dragClone.style.width = element.offsetWidth + 'px';
    dragClone.style.height = element.offsetHeight + 'px';
    dragClone.style.left = clientX - offsetX + 'px';
    dragClone.style.top = clientY - offsetY + 'px';
    dragClone.style.opacity = '0.8';
    dragClone.classList.add('dragging-clone');
    
    document.body.appendChild(dragClone);
    return dragClone;
  }

  /**
   * 更新拖拽克隆位置 - 无动画效果
   */
  function updateDragClone(dragClone: HTMLElement, clientX: number, clientY: number, offsetX: number, offsetY: number, canDrop: boolean) {
    dragClone.style.left = clientX - offsetX + 'px';
    dragClone.style.top = clientY - offsetY + 'px';
    dragClone.style.opacity = canDrop ? '0.8' : '0.6';
  }

  /**
   * 清理拖拽克隆
   */
  function cleanupDragClone(dragClone: HTMLElement | null) {
    if (dragClone && dragClone.parentNode) {
      dragClone.parentNode.removeChild(dragClone);
    }
  }

  /**
   * 简化的拖拽开始方法
   * @param dragData 拖拽数据 { element, clip, type, trackIndex, clipIndex }
   * @param position 初始位置 { clientX, clientY, offsetX, offsetY }
   * @param options 选项
   */
  function simpleDragStart(
    dragData: { element: HTMLElement; clip: any; type: TrackType; trackIndex: number; clipIndex: number },
    position: { clientX: number; clientY: number; offsetX: number; offsetY: number },
    options?: { sourceTrackIndex?: number; isFromLibrary?: boolean }
  ) {
    // 创建拖拽克隆
    const dragClone = createDragClone(dragData.element, position.offsetX, position.offsetY, position.clientX, position.clientY);
    
    // 原始元素保持正常显示，不虚化
    
    // 设置拖拽状态
    isDragging.value = true;
    dragPreview.value = null;
    
    return {
      dragClone,
      originalElement: dragData.element,
      dragData: {
        ...dragData,
        sourceTrackIndex: options?.sourceTrackIndex || dragData.trackIndex,
        isFromLibrary: options?.isFromLibrary || false
      }
    };
  }

  /**
   * 简化的拖拽移动方法（增强版，包含吸附）
   * @param dragClone 拖拽克隆元素
   * @param position 当前位置 { clientX, clientY, offsetX, offsetY }
   * @param containerInfo 容器信息 { scrollAreaRect, scrollTop, trackHeight }
   * @param dragData 拖拽数据
   * @param originalElement 原始元素
   * @param startY 拖拽开始的Y坐标
   */
  function enhancedDragMove(
    dragClone: HTMLElement,
    position: { clientX: number; clientY: number; offsetX: number; offsetY: number },
    containerInfo: { scrollAreaRect: DOMRect; scrollTop: number; trackHeight: number },
    dragData: any,
    originalElement: HTMLElement,
    startY: number
  ) {
    // 检查是否在容器内
    const isInContainer = position.clientX >= containerInfo.scrollAreaRect.left && 
                         position.clientX <= containerInfo.scrollAreaRect.right &&
                         position.clientY >= containerInfo.scrollAreaRect.top && 
                         position.clientY <= containerInfo.scrollAreaRect.bottom;
    
    if (!isInContainer) {
      dragPreview.value = null;
      snapGuidelineY.value = null;
      snapGuidelineType.value = null;
      updateDragClone(dragClone, position.clientX, position.clientY, position.offsetX, position.offsetY, false);
      return null;
    }
    
    // 计算相对于滚动区域的鼠标Y位置
    const relativeMouseY = position.clientY - containerInfo.scrollAreaRect.top + containerInfo.scrollTop;
    
    // 处理吸附 - 传递原始元素、鼠标位置、开始位置和滚动容器
    const snapResult = handleSnapping(
      originalElement, 
      relativeMouseY, 
      startY, 
      scrollContainer.value!, 
      dragData.type
    );
    
    // 使用智能轨道计算
    const dragResult = smartTrackDragCalculation({
      mouseY: position.clientY,
      trackType: dragData.type,
      scrollAreaRect: containerInfo.scrollAreaRect,
      scrollTop: containerInfo.scrollTop,
      trackHeight: containerInfo.trackHeight,
      displayTracks: displayTracks.value,
      allTracks: allTracks.value,
      sourceTrackIndex: dragData.sourceTrackIndex,
      isDraggingFromLibrary: dragData.isFromLibrary
    });
    
    // 更新预览状态
    dragPreview.value = dragResult;
    
    // 更新拖拽克隆样式，无动画效果
    updateDragClone(dragClone, position.clientX, position.clientY, position.offsetX, position.offsetY, dragResult.isValidDrop);
    
    return { dragResult, snapResult };
  }

  /**
   * 简化的拖拽结束方法
   * @param dragClone 拖拽克隆元素
   * @param originalElement 原始元素
   * @param finalPosition 最终位置 { clientX, clientY }
   * @param pixelsPerSecond 像素每秒
   * @param containerInfo 容器信息
   */
  function simpleDragEnd(
    dragClone: HTMLElement,
    originalElement: HTMLElement,
    finalPosition: { clientX: number; clientY: number },
    pixelsPerSecond: number,
    containerInfo: { containerRect: DOMRect; scrollLeft: number }
  ) {
    // 清理UI
    cleanupDragClone(dragClone);
    originalElement.style.opacity = '';
    
    const dragResult = dragPreview.value;
    
    // 计算最终时间位置
    let result = null;
    if (dragResult && dragResult.isValidDrop) {
      const newX = Math.max(0, finalPosition.clientX - containerInfo.containerRect.left + containerInfo.scrollLeft);
      const newStartTime = Math.max(0, newX / pixelsPerSecond);
      const snappedTime = Math.round(newStartTime * 10) / 10; // 简单吸附
      
      result = {
        isSuccess: true,
        newStartTime: snappedTime,
        targetTrackIndex: dragResult.targetTrackIndex,
        isNewTrack: dragResult.isNewTrack,
        isCrossTrackMove: dragResult.targetTrackIndex !== null && 
                         (dragResult.isNewTrack || dragResult.targetTrackIndex !== dragData.value?.sourceTrackIndex)
      };
    }
    
    // 清理状态
    isDragging.value = false;
    dragPreview.value = null;
    dragData.value = null;
    
    return result;
  }
  
  /**
   * 更新轨道数据
   */
  function updateTrackData(newDisplayTracks: any[], newAllTracks: any[], videoTracks?: any[], audioTracks?: any[], subtitleTracks?: any[]) {
    displayTracks.value = newDisplayTracks;
    allTracks.value = newAllTracks;
    if (videoTracks) displayVideoTracks.value = videoTracks;
    if (audioTracks) displayAudioTracks.value = audioTracks;
    if (subtitleTracks) displaySubtitleTracks.value = subtitleTracks;
  }
  
  /**
   * 设置轨道高度
   */
  function setTrackHeight(height: number) {
    trackHeight.value = height;
  }
  
  /**
   * 清理拖拽状态和吸附辅助线
   */
  function cleanup() {
    isDragging.value = false;
    dragPreview.value = null;
    dragData.value = null;
    snapGuidelineY.value = null;
    snapGuidelineType.value = null;
  }

  return {
    // 状态
    isDragging: computed(() => isDragging.value),
    dragPreview: computed(() => dragPreview.value),
    snapGuidelineY: computed(() => snapGuidelineY.value),
    snapGuidelineType: computed(() => snapGuidelineType.value),
    
    // 方法
    initDrag,
    updateTrackData,
    setTrackHeight,
    setCallbacks,
    calculateSnapPoints,
    findNearestSnapPoint,
    handleSnapping,
    cleanup,
    
    // 拖拽方法
    simpleDragStart,
    enhancedDragMove,
    simpleDragEnd,
    
    // 轨道创建方法
    handleCreateNewTrackWithTimeline,
    
    // 内部方法（可选暴露，用于调试）
    checkBlockConstraints,
    calculateTrackPosition,
    updateDragPreview,
    applyDragResult
  };
}

/**
 * 创建时间轴拖拽管理器的单例
 */
let timelineDragManagerInstance: ReturnType<typeof createTimelineDragManager> | null = null;

export function getTimelineDragManager() {
  if (!timelineDragManagerInstance) {
    timelineDragManagerInstance = createTimelineDragManager();
  }
  return timelineDragManagerInstance;
}
