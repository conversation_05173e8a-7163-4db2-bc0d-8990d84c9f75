<template>
  <div class="image-library">
    <!-- 图片网格展示 -->
    <div class="image-grid" v-loading="props.loading">
      <div v-if="props.loading" class="loading-container">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span>加载中...</span>
      </div>
      <div v-for="image in filteredImages" :key="image.mediaId" class="image-card" @click="selectImage(image)"
        :class="{ selected: selectedImages.includes(image.mediaId) }">
        <div class="image-container">
          <img :src="image.FileUrl" :alt="image.title || '图片'" @error="handleImageError" />
          <div class="image-overlay">
            <el-button type="primary" size="small" @click.stop="previewImage(image)">
              <el-icon>
                <View />
              </el-icon>
              预览
            </el-button>
            <el-button type="success" size="small" @click.stop="downloadImage(image)">
              <el-icon>
                <Download />
              </el-icon>
              下载
            </el-button>
            <el-button type="danger" size="small" @click.stop="deleteImage(image.mediaId)">
              <el-icon>
                <Delete />
              </el-icon>
              删除
            </el-button>
          </div>
          <!-- 格式标识 -->
          <div class="format-badge">
            {{ image.title?.split('.').pop()?.toUpperCase() || '' }}
          </div>
        </div>
        <div class="image-info">
          <div class="image-name" :title="image.title || '未命名图片'">
            {{ image.title || '未命名图片' }}
          </div>
          <div class="image-meta">
            <span class="size">{{ formatFileSize(image.size || 0) }}</span>
            <span class="status">{{ getStatusText(image.Status) }}</span>
            <span class="date">{{ new Date(image.createTime).toLocaleDateString('zh-CN') }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewDialogVisible" title="图片预览" width="80%" @close="closePreview">
      <div class="image-preview" v-if="previewImageData">
        <div class="preview-image-container">
          <img :src="previewImageData.url" :alt="previewImageData.name" />
        </div>
        <div class="preview-info">
          <h3>{{ previewImageData.name }}</h3>
          <div class="info-grid">
            <p><strong>分辨率:</strong> {{ previewImageData.width }}×{{ previewImageData.height }}</p>
            <p><strong>文件大小:</strong> {{ formatFileSize(previewImageData.size) }}</p>
            <p><strong>格式:</strong> {{ previewImageData.format }}</p>
            <p><strong>上传时间:</strong> {{ formatDateTime(previewImageData.uploadTime) }}</p>
            <p v-if="previewImageData.description"><strong>描述:</strong> {{ previewImageData.description }}</p>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="preview-footer">
          <el-button @click="closePreview">关闭</el-button>
          <el-button type="primary" @click="downloadCurrentImage">下载</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Loading, View, Download, Refresh } from '@element-plus/icons-vue'
import { formatFileSize, downloadFileByUrl } from '../../utils/commonUtils'
import { getStatusText } from '../../types/media'
import type { MediaInfo, Material } from '../../types/media'

// 定义组件的事件
const emit = defineEmits<{
  upload: [uploadParams: { file: File; MediaMetaData?: any }],
  sizeChange: [size: number],
  pageChange: [current: number],
  search: [keyword: string],
  filter: [params: any],
  refreshMediaList: [page?: number]
}>()

// 定义props
const props = defineProps<{
  mediaList?: Material[],
  loading?: boolean
}>()


const selectedImages = ref<string[]>([])
const currentPreviewImage = ref<Material | null>(null)

// 对话框控制
const previewDialogVisible = ref(false)
const previewImageData = ref<any>(null)

// 计算属性
const filteredImages = computed(() => {
  // 如果正在加载，不显示任何数据，避免显示旧数据
  if (props.loading) {
    return []
  }
  return props.mediaList || []
})

const selectImage = (image: Material) => {
  const index = selectedImages.value.indexOf(image.mediaId)
  if (index > -1) {
    selectedImages.value.splice(index, 1)
  } else {
    selectedImages.value.push(image.mediaId)
  }
}

const previewImage = (image: Material) => {
  currentPreviewImage.value = image
  previewImageData.value = {
    url: image.FileUrl,
    name: image.title || '未命名图片',
    width: '未知',
    height: '未知',
    size: image.size || 0,
    format: image.title,
    uploadTime: image.createTime,
    description: ''
  }
  previewDialogVisible.value = true
}

const closePreview = () => {
  previewDialogVisible.value = false
  previewImageData.value = null
  currentPreviewImage.value = null
}

const downloadCurrentImage = () => {
  if (currentPreviewImage.value) {
    downloadImage(currentPreviewImage.value)
  }
}

const deleteImage = async (imageId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张图片吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    ElMessage.success('删除成功')
    emit('refreshMediaList')
  } catch {
    // 用户取消删除
  }
}

const batchDelete = async () => {
  if (selectedImages.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedImages.value.length} 张图片吗？删除后无法恢复。`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    ElMessage.success('批量删除成功')
    selectedImages.value = []
    emit('refreshMediaList')
  } catch {
    // 用户取消删除
  }
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/default-image.png'
}

const downloadImage = async (image: Material) => {
  try {
    const url = image.FileUrl || image.coverUrl
    if (!url || url === '/default-image.png') {
      ElMessage.error('无法获取图片下载链接')
      return
    }

    const fileName = image.title || 'image'
    downloadFileByUrl(url, fileName)
    ElMessage.success('图片下载成功')
  } catch (error) {
    console.error('下载图片失败:', error)
    ElMessage.error('下载图片失败')
  }
}

// 监听上传成功事件
const handleMediaUploaded = (event: Event) => {
  const customEvent = event as CustomEvent
  const { category } = customEvent.detail
  if (category === 'image') {
    emit('refreshMediaList')
  }
}

onMounted(() => {
  // 监听上传成功事件
  window.addEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})
</script>

<style scoped>
.image-library {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.category-filter {
  padding: 0 16px 16px 16px;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.image-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 16px;
  overflow-y: auto;
  padding: 0 4px 16px 0;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.image-card {
  border: 2px solid transparent;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(.4, 0, .2, 1);
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  &:hover {
    border-color: #409eff;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 6px 18px rgba(64, 158, 255, 0.15);
  }

  &.selected {
    border-color: #409eff;
    background: #ecf5ff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

.image-container {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:hover .image-overlay {
    opacity: 1;
  }

  .format-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
}

.image-info {
  padding: 12px;
}

.image-name {
  font-weight: 500;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #303133;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  gap: 8px;

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }
}

.empty-state {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.image-preview {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-image-container {
  text-align: center;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;

  img {
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.preview-info {
  padding: 16px;
  background: #f9fafc;
  border-radius: 8px;

  h3 {
    margin: 0 0 16px 0;
    color: #303133;
    font-size: 18px;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;

      strong {
        color: #303133;
        margin-right: 8px;
      }
    }
  }
}

.preview-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
