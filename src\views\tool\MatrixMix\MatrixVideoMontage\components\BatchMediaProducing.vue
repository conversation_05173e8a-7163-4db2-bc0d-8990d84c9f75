<template>
  <div class="batch-media-producing">
    <!-- 顶部工作区标题 -->
    <div class="workspace-header">
      <div class="workspace-title">
        <div class="title-icon">
          <el-icon><VideoPlay /></el-icon>
        </div>
        <div class="title-content">
          <h2>AI智能批量剪辑工作台</h2>
          <p>快速配置，一键生成专业视频内容</p>
        </div>
      </div>
      <div class="workspace-actions">
        <el-button type="info" icon="Refresh" @click="resetAllConfig" size="small" plain>重置</el-button>
        <el-button type="warning" icon="FolderOpened" @click="saveConfig" size="small" plain>配置管理</el-button>
        <el-button type="primary" icon="VideoPlay" @click="handleSubmit" size="small">开始制作</el-button>
      </div>
    </div>

    <!-- 主工作区 -->
    <div class="main-workspace">
      <!-- 左侧：基础配置 -->
      <div class="left-panel">
        <!-- 基础配置 -->
        <div class="config-card">
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>基础配置</span>
          </div>
          <div class="card-content">
            <BasicConfigPanel
              :task-info="taskInfo"
              :text-config="textConfig"
              @update:task-info="taskInfo = $event"
              @update:text-config="textConfig = $event"
              class="basic-config-compact"
            />
          </div>
        </div>
      </div>

      <!-- 右侧：高级配置和模板 -->
      <div class="right-panel">
        <!-- 高级配置 -->
        <div class="config-card">
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>高级配置</span>
          </div>
          <div class="card-content">
            <AdvancedConfigPanel
              :editing-config="editingConfig"
              :output-config="outputConfig"
              @update:editing-config="editingConfig = $event"
              @update:output-config="outputConfig = $event"
              class="advanced-config-compact"
            />
          </div>
        </div>

        <!-- 模板选择 -->
        <div class="config-card">
          <div class="card-header">
            <el-icon><MagicStick /></el-icon>
            <span>模板选择</span>
            <el-button
              type="primary"
              icon="Refresh"
              size="small"
              circle
              class="refresh-btn"
              @click="fetchTemplateList"
            />
          </div>
          <div class="card-content">
            <TemplateSelector
              :template-list="templateList"
              :selected-template="selectedTemplate"
              @template-changed="handleTemplateChanged"
              class="template-selector-compact"
            />
            <div
              v-for="template in templateList"
              :key="template.id"
              class="template-card-item"
              :class="{ active: selectedTemplate === template.id }"
              @click="handleTemplateChanged(template)"
            >
              <div class="template-preview">
                <el-icon><MagicStick /></el-icon>
              </div>
              <div class="template-details">
                <div class="template-name">{{ template.name }}</div>
                <div class="template-desc">{{ template.description || '暂无描述' }}</div>
              </div>
              <div class="template-status" v-if="selectedTemplate === template.id">
                <el-icon><Check /></el-icon>
              </div>
            </div>
            <div v-if="templateList.length === 0 && !templateLoading" class="empty-state">
              <el-icon class="empty-icon"><MagicStick /></el-icon>
              <p>暂无可用模板</p>
            </div>
          </div>
        </div>

        <!-- 快速预设 -->
        <div class="config-section">
          <div class="section-header">
            <div class="section-icon">
              <el-icon><Star /></el-icon>
            </div>
            <div class="section-title">快速预设</div>
          </div>
          <div class="section-content">
            <div class="preset-grid">
              <div
                class="preset-item"
                @click="applyPreset('shortVideo')"
              >
                <div class="preset-icon short-video">
                  <el-icon><VideoPlay /></el-icon>
                </div>
                <span class="preset-label">短视频</span>
              </div>
              <div
                class="preset-item"
                @click="applyPreset('longVideo')"
              >
                <div class="preset-icon long-video">
                  <el-icon><Monitor /></el-icon>
                </div>
                <span class="preset-label">长视频</span>
              </div>
              <div
                class="preset-item"
                @click="applyPreset('highQuality')"
              >
                <div class="preset-icon high-quality">
                  <el-icon><Star /></el-icon>
                </div>
                <span class="preset-label">高质量</span>
              </div>
              <div
                class="preset-item"
                @click="applyPreset('quickMode')"
              >
                <div class="preset-icon quick-mode">
                  <el-icon><Timer /></el-icon>
                </div>
                <span class="preset-label">快速模式</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    

    <!-- 媒体素材区 - 占满一行 -->
    <div class="media-workspace">
      <div class="config-card media-card">
        <div class="card-header">
          <el-icon><Picture /></el-icon>
          <span>媒体素材</span>
        </div>
        <div class="card-content">
          <MediaManager
            v-model="mediaGroups"
            :my-media-list="myMediaList"
            @api-config-changed="handleMediaGroupsApiChange"
            @media-added="handleMediaAdded"
            class="media-manager-full"
          />
        </div>
      </div>
    </div>

    <!-- 底部状态监控 -->
    <div class="status-monitor-section">
      <div class="config-card monitor-card">
        <div class="card-header">
          <el-icon><Monitor /></el-icon>
          <span>配置状态监控</span>
        </div>
        <div class="card-content">
          <div class="monitor-stats">
            <div class="stat-item">
              <div class="stat-icon video">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ editingConfig.videoVolume }}%</div>
                <div class="stat-label">视频音量</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon speech">
                <el-icon><Microphone /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ editingConfig.speechVolume }}%</div>
                <div class="stat-label">旁白音量</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon music">
                <el-icon><Headset /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ editingConfig.enableBGM ? `${editingConfig.bgmVolume}%` : '关闭' }}</div>
                <div class="stat-label">背景音乐</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon count">
                <el-icon><Files /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ outputConfig.count }}个</div>
                <div class="stat-label">生成数量</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon time">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ outputConfig.maxDuration }}秒</div>
                <div class="stat-label">视频时长</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon resolution">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ outputConfig.resolution }}</div>
                <div class="stat-label">分辨率</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  MagicStick,
  Monitor,
  VideoPlay,
  Microphone,
  Headset,
  Files,
  Timer,
  Star,
  Refresh,
  Setting,
  ArrowDown,
  DocumentAdd,
  View,
  Document,
  Upload,
  EditPen,
  Plus,
  Delete,
  Check
} from '@element-plus/icons-vue';
import BasicConfigPanel from './BatchMediaProducingComponents/BasicConfigPanel.vue';
import MediaManager from './BatchMediaProducingComponents/MediaManager.vue';
import AdvancedConfigPanel from './BatchMediaProducingComponents/AdvancedConfigPanel.vue';
//import TemplateSelector from './BatchMediaProducingComponents/TemplateSelector.vue';
import { listTemplates } from '../../api/template';
import { submitBatchMediaProducingJob } from '../../api/batchProducing';
import { searchMedia } from '../../api/media';
import useUserStore from '@/store/modules/user';
import {BatchMediaInfoUtils} from '@/views/tool/MatrixMix/utils/batchMediaUtils'
import type {
  MediaItem,
  MediaGroupUI,
  TemplateInfo,
  MediaType,
  ConfigPreset,
  ConfigPresetType,
  TaskInfo,
  TextConfig,
  UIEditingConfig,
  UIOutputConfig
} from '../../types/batchProducing';

// 主页面数据 - 使用动态默认值
const taskInfo = ref({
  taskName: '',
  taskDescription: '',
});

const mediaGroups = ref<MediaGroupUI[]>([
  { name: '媒体组1', media: [] }
]);

// 添加调试信息
watch(mediaGroups, (newVal) => {
  console.log('📊 mediaGroups 变化:', newVal);
}, { deep: true });

// 移除硬编码的form数据，使用响应式的独立数据

// 动态数据状态
const templateLoading = ref(false);
const mediaLoading = ref(false);
const templateList = ref<TemplateInfo[]>([]);
const myMediaList = ref<MediaItem[]>([]);

const selectedTemplate = ref('');

// API配置数据 - 由各个子组件提供
const apiConfigs = ref({
  mediaGroups: [] as any[],
  editingConfig: {} as any,
  outputConfig: {} as any,
  templateConfig: null as any
});

// 响应式数据
const submitting = ref(false);

// 文本配置数据 - 动态可调整
const textConfig = ref<TextConfig>({
  titles: [''],
  speechTexts: ['']
});

// 文本配置相关方法
const addTitle = () => {
  if (textConfig.value.titles.length < 10) {
    textConfig.value.titles.push('');
  }
};

const removeTitle = (index: number) => {
  if (textConfig.value.titles.length > 1) {
    textConfig.value.titles.splice(index, 1);
  }
};

const addNarration = () => {
  if (textConfig.value.speechTexts.length < 10) {
    textConfig.value.speechTexts.push('');
  }
};

const removeNarration = (index: number) => {
  if (textConfig.value.speechTexts.length > 1) {
    textConfig.value.speechTexts.splice(index, 1);
  }
};

// 剪辑配置数据 - 使用合理的默认值，可动态调整
const editingConfig = ref<UIEditingConfig>({
  videoVolume: 50,        // 视频音量 0-100
  speechVolume: 80,       // 旁白音量 0-100
  enableBGM: true,        // 是否启用背景音乐
  bgmVolume: 30,          // 背景音乐音量 0-100
  enableSubtitle: true,   // 是否启用AI字幕
  enableTransition: true, // 是否启用镜头切换特效
  enableSpeechSync: false,// 是否启用语音识别同步字幕
  enableSmartCrop: false  // 是否启用智能裁剪
});

// 输出配置数据 - 可动态调整的输出参数
const outputConfig = ref<UIOutputConfig>({
  count: 10,              // 输出视频数量
  maxDuration: 15,        // 单个视频最大时长(秒)
  resolution: '1080x1920',// 输出分辨率
  quality: 23             // 视频质量 (CRF值，越小质量越高)
});



// 计算属性
const totalMediaCount = computed(() => {
  return mediaGroups.value.reduce((total, group) => {
    return total + group.media.length;
  }, 0);
});

const canSubmit = computed(() => {
  return taskInfo.value.taskName.trim() && 
         totalMediaCount.value > 0 && 
         selectedTemplate.value && 
         outputConfig.value.count > 0;
});

// 动态数据获取方法
const fetchTemplateList = async () => {
  templateLoading.value = true;
  try {
    const response = await listTemplates({
      pageNo: 1,
      pageSize: 50,
      type: 'Timeline',
      status: 'Available'
    });
    
    if (response.code === 200) {
      // 转换API数据格式为组件期望的格式
      templateList.value = response.data.Templates.map(template => ({
        id: template.TemplateId,
        name: template.Name,
        description: template.Status === 'Available' ? '可用模板' : '处理中模板',
        icon: 'el-icon-magic-stick'
      }));
      
      // 设置默认选中的模板
      if (templateList.value.length > 0 && !selectedTemplate.value) {
        selectedTemplate.value = templateList.value[0].id;
      }
      console.log('✅ 模板列表获取成功:', templateList.value.length, '个模板');
    } else {
      ElMessage.error(`获取模板列表失败: ${response.msg}`);
    }
  } catch (error) {
    console.error('❌ 获取模板列表失败:', error);
    ElMessage.error('获取模板列表时发生网络错误');
  } finally {
    templateLoading.value = false;
  }
};

const fetchMediaList = async () => {
  mediaLoading.value = true;
  try {
    console.log('🔄 开始获取媒体列表...');
    
    const response = await searchMedia({
      maxResults: 100,
      pageSize: 100,
      includeFileBasicInfo: true
    });

    console.log('📡 API响应数据:', response);

    // 检查响应格式 - 兼容不同的响应结构
    let mediaInfos = [];
    
    if (response.data && response.data.MediaInfos) {
      // 标准格式：{ code: 200, data: { MediaInfos: [...] } }
      mediaInfos = response.data.MediaInfos;
      console.log('✅ 使用标准响应格式');
    } else if (response.MediaInfos) {
      // 直接格式：{ MediaInfos: [...] }
      mediaInfos = response.MediaInfos;
      console.log('✅ 使用直接响应格式');
    } else if (Array.isArray(response.data)) {
      // 数组格式：{ data: [...] }
      mediaInfos = response.data;
      console.log('✅ 使用数组响应格式');
    } else if (Array.isArray(response)) {
      // 纯数组格式：[...]
      mediaInfos = response;
      console.log('✅ 使用纯数组响应格式');
    }

    console.log('📊 解析到的媒体数据:', mediaInfos);

    if (mediaInfos && mediaInfos.length > 0) {
      // 转换API数据格式为组件期望的格式
      myMediaList.value = mediaInfos.map((media, index) => {
        try {
          const fileInfo = media.FileInfoList?.[0];
          const basicInfo = fileInfo?.FileBasicInfo;
          const mediaBasicInfo = media.MediaBasicInfo;

          // 根据文件扩展名判断媒体类型
          const getMediaType = (fileName: string): MediaType => {
            if (!fileName) return 'video';
            const ext = fileName.toLowerCase().split('.').pop() || '';
            if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v'].includes(ext)) return 'video';
            if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) return 'image';
            if (['mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a'].includes(ext)) return 'audio';
            return 'video'; // 默认为视频
          };

          // 获取文件名 - 多种来源
          const fileName = basicInfo?.FileName || 
                          mediaBasicInfo?.Title || 
                          media.MediaId || 
                          `媒体文件${index + 1}`;

          // 获取文件URL - 多种来源
          const fileUrl = basicInfo?.FileUrl || 
                         mediaBasicInfo?.CoverURL || 
                         '';

          const mediaItem = {
            id: media.MediaId || `media_${index}`,
            name: fileName,
            type: getMediaType(fileName),
            url: fileUrl,
            thumbnail: mediaBasicInfo?.CoverURL || fileUrl,
            duration: parseFloat(basicInfo?.Duration || mediaBasicInfo?.Duration || '0') || 0,
            size: BatchMediaInfoUtils.formatFileSize(parseInt(basicInfo?.FileSize || '0') || 0)
          };

          console.log(`📄 处理媒体 ${index + 1}:`, mediaItem);
          return mediaItem;
        } catch (itemError) {
          console.error(`❌ 处理媒体项 ${index} 时出错:`, itemError, media);
          // 返回一个基础的媒体项，避免整个列表失败
          return {
            id: media.MediaId || `error_media_${index}`,
            name: `媒体文件${index + 1}`,
            type: 'video' as MediaType,
            url: '',
            thumbnail: '',
            duration: 0,
            size: '0 B'
          };
        }
      });

      console.log('✅ 媒体列表加载成功:', myMediaList.value.length, '个媒体');
      ElMessage.success(`成功加载 ${myMediaList.value.length} 个媒体文件`);
    } else {
      console.log('⚠️ 没有找到媒体数据');
      myMediaList.value = [];
      ElMessage.warning('暂无媒体文件，请先上传一些媒体素材');
    }
  } catch (error: any) {
    console.error('❌ 获取媒体列表失败:', error);
    
    // 详细的错误信息
    let errorMessage = '获取媒体列表失败';
    
    if (error.response) {
      // HTTP错误
      const status = error.response.status;
      const statusText = error.response.statusText;
      errorMessage = `网络请求失败 (${status}): ${statusText}`;
      
      if (status === 401) {
        errorMessage = '认证失败，请重新登录';
      } else if (status === 403) {
        errorMessage = '权限不足，无法访问媒体列表';
      } else if (status === 404) {
        errorMessage = '媒体列表接口不存在';
      } else if (status >= 500) {
        errorMessage = '服务器内部错误，请稍后重试';
      }
      
      console.error('HTTP错误详情:', error.response.data);
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络连接';
      console.error('网络错误:', error.request);
    } else if (error.message) {
      // 其他错误
      errorMessage = `请求错误: ${error.message}`;
    }
    
    ElMessage.error(errorMessage);
    myMediaList.value = [];
  } finally {
    mediaLoading.value = false;
  }
};


// 配置预设 - 提供常用的配置模板
const configPresets: Record<ConfigPresetType, ConfigPreset> = {
  // 短视频模式 - 适合抖音、快手等平台
  shortVideo: {
    editing: {
      videoVolume: 60,
      speechVolume: 85,
      enableBGM: true,
      bgmVolume: 25,
      enableSubtitle: true,
      enableTransition: true,
      enableSpeechSync: false,
      enableSmartCrop: true
    },
    output: {
      count: 20,
      maxDuration: 15,
      resolution: '1080x1920',
      quality: 25
    }
  },

  // 长视频模式 - 适合B站、YouTube等平台
  longVideo: {
    editing: {
      videoVolume: 55,
      speechVolume: 80,
      enableBGM: true,
      bgmVolume: 35,
      enableSubtitle: true,
      enableTransition: false,
      enableSpeechSync: true,
      enableSmartCrop: false
    },
    output: {
      count: 5,
      maxDuration: 120,
      resolution: '1920x1080',
      quality: 20
    }
  },

  // 高质量模式 - 追求最佳画质和音质
  highQuality: {
    editing: {
      videoVolume: 50,
      speechVolume: 75,
      enableBGM: true,
      bgmVolume: 20,
      enableSubtitle: true,
      enableTransition: true,
      enableSpeechSync: true,
      enableSmartCrop: false
    },
    output: {
      count: 3,
      maxDuration: 60,
      resolution: '1920x1080',
      quality: 18
    }
  },

  // 快速模式 - 快速生成，适合测试
  quickMode: {
    editing: {
      videoVolume: 50,
      speechVolume: 80,
      enableBGM: false,
      bgmVolume: 0,
      enableSubtitle: false,
      enableTransition: false,
      enableSpeechSync: false,
      enableSmartCrop: false
    },
    output: {
      count: 3,
      maxDuration: 10,
      resolution: '720x1280',
      quality: 30
    }
  }
};

// 应用配置预设
const applyConfigPreset = (presetName: ConfigPresetType) => {
  const preset = configPresets[presetName];
  if (preset) {
    editingConfig.value = { ...preset.editing };
    outputConfig.value = { ...preset.output };
    console.log(`✅ 已应用${presetName}配置预设`);
    ElMessage.success(`已应用${getPresetDisplayName(presetName)}配置`);
  }
};

// 获取预设显示名称
const getPresetDisplayName = (presetName: ConfigPresetType): string => {
  const names = {
    shortVideo: '短视频模式',
    longVideo: '长视频模式',
    highQuality: '高质量模式',
    quickMode: '快速模式'
  };
  return names[presetName] || presetName;
};

// 处理子组件API配置变化
const handleMediaGroupsApiChange = (config: any[]) => {
  apiConfigs.value.mediaGroups = config;
  console.log('📊 媒体组API配置更新:', config);
};



const handleTemplateApiChange = (config: any) => {
  apiConfigs.value.templateConfig = config;
  console.log('📊 模板API配置更新:', config);
};

// 重置表单 - 恢复到合理的默认值
const resetForm = () => {
  // 重置任务信息
  taskInfo.value = {
    taskName: '',
    taskDescription: ''
  };

  // 重置媒体组
  mediaGroups.value = [{
    name: '媒体组1',
    media: []
  }];

  // 重置文本配置
  textConfig.value = {
    titles: [''],
    speechTexts: ['']
  };

  // 重置剪辑配置到推荐值
  editingConfig.value = {
    videoVolume: 50,        // 视频音量适中
    speechVolume: 80,       // 旁白音量较高，确保清晰
    enableBGM: true,        // 默认启用背景音乐
    bgmVolume: 30,          // 背景音乐音量较低，不抢夺主音频
    enableSubtitle: true,   // 默认启用字幕，提升观看体验
    enableTransition: true, // 默认启用转场效果
    enableSpeechSync: false,// 语音同步字幕默认关闭
    enableSmartCrop: false  // 智能裁剪默认关闭
  };

  // 重置输出配置到常用值
  outputConfig.value = {
    count: 10,              // 默认生成10个视频
    maxDuration: 15,        // 默认15秒，适合短视频
    resolution: '1080x1920',// 默认竖屏高清
    quality: 23             // 默认标准质量
  };

  // 重置模板选择
  selectedTemplate.value = '';

  console.log('✅ 表单已重置到默认值');
  ElMessage.success('表单已重置');
};

// 配置管理 - 保存和加载自定义配置
const saveCustomConfig = () => {
  if (!taskInfo.value.taskName?.trim()) {
    ElMessage.warning('请先输入任务名称');
    return;
  }

  const configData = {
    name: `${taskInfo.value.taskName}_配置`,
    description: taskInfo.value.taskDescription,
    editingConfig: editingConfig.value,
    outputConfig: outputConfig.value,
    textConfig: textConfig.value,
    createTime: new Date().toISOString()
  };

  // 保存到本地存储
  const savedConfigs = JSON.parse(localStorage.getItem('batchMediaConfigs') || '[]');
  savedConfigs.push(configData);
  localStorage.setItem('batchMediaConfigs', JSON.stringify(savedConfigs));

  console.log('✅ 配置保存成功:', configData);
  ElMessage.success(`配置"${configData.name}"保存成功！`);
};

// 加载自定义配置
const loadCustomConfig = (configName: string) => {
  const savedConfigs = JSON.parse(localStorage.getItem('batchMediaConfigs') || '[]');
  const config = savedConfigs.find((c: any) => c.name === configName);

  if (config) {
    editingConfig.value = { ...config.editingConfig };
    outputConfig.value = { ...config.outputConfig };
    textConfig.value = { ...config.textConfig };

    console.log('✅ 配置加载成功:', config);
    ElMessage.success(`配置"${configName}"加载成功！`);
  } else {
    ElMessage.error('配置不存在');
  }
};

// 获取已保存的配置列表
const getSavedConfigs = () => {
  return JSON.parse(localStorage.getItem('batchMediaConfigs') || '[]');
};

// 预览API参数
const previewApiParams = () => {
  try {
    const inputConfig = buildInputConfig();
    const editingConfig = buildEditingConfig();
    const outputConfig = buildOutputConfig();
    const templateConfig = buildTemplateConfig();
    const userData = buildUserData();

    const apiParams = {
      InputConfig: JSON.stringify(inputConfig, null, 2),
      EditingConfig: JSON.stringify(editingConfig, null, 2),
      OutputConfig: JSON.stringify(outputConfig, null, 2),
      TemplateConfig: JSON.stringify(templateConfig, null, 2),
      UserData: JSON.stringify(userData, null, 2)
    };

    // 验证配置是否符合官方文档要求
    validateApiConfig(inputConfig, editingConfig, outputConfig, templateConfig);

    // 在控制台输出API参数供调试
    console.log('🔍 InputConfig:', inputConfig);
    console.log('🔍 EditingConfig:', editingConfig);
    console.log('🔍 OutputConfig:', outputConfig);
    console.log('🔍 TemplateConfig:', templateConfig);
    console.log('🔍 UserData:', userData);

    // 使用Element Plus的消息框显示
    ElMessage({
      message: '详细API参数已输出到控制台，请按F12查看',
      type: 'success',
      duration: 3000
    });

    console.log('🔍 API参数预览:', apiParams);
    ElMessage.success('API参数预览已打开新窗口');
  } catch (error) {
    console.error('❌ 生成API参数失败:', error);
    ElMessage.error('生成API参数失败，请检查配置');
  }
};

// 配置命令处理
const handleConfigCommand = (command: string) => {
  if (command === 'save') {
    saveCustomConfig();
  } else if (command === 'preview') {
    previewApiParams();
  } else if (command.startsWith('load:')) {
    const configName = command.substring(5);
    loadCustomConfig(configName);
  }
};

// 移除未使用的saveTemplate函数

const submitTask = async () => {
  submitting.value = true;
  try {
    // 数据验证
    if (!taskInfo.value.taskName?.trim()) {
      ElMessage.warning('请输入任务名称');
      return;
    }

    if (mediaGroups.value.every(group => group.media.length === 0)) {
      ElMessage.warning('请至少添加一个媒体文件');
      return;
    }

    if (!selectedTemplate.value) {
      ElMessage.warning('请选择一个模板');
      return;
    }

    // 构建API请求参数
    let inputConfigObj, editingConfigObj, outputConfigObj, templateConfigObj, userDataObj;

    try {
      inputConfigObj = buildInputConfig();
      editingConfigObj = buildEditingConfig();
      outputConfigObj = buildOutputConfig();
      templateConfigObj = buildTemplateConfig();
      userDataObj = buildUserData();
    } catch (buildError: any) {
      console.error('构建配置参数失败:', buildError);
      ElMessage.error(`配置参数错误: ${buildError?.message || '未知错误'}`);
      return;
    }

    // 转换为JSON字符串
    const inputConfig = JSON.stringify(inputConfigObj);
    const editingConfig = JSON.stringify(editingConfigObj);
    const outputConfig = JSON.stringify(outputConfigObj);
    const templateConfig = JSON.stringify(templateConfigObj);
    const userData = JSON.stringify(userDataObj);

    console.log('📤 提交任务数据:');
    console.log('InputConfig:', inputConfigObj);
    console.log('EditingConfig:', editingConfigObj);
    console.log('OutputConfig:', outputConfigObj);
    console.log('TemplateConfig:', templateConfigObj);
    console.log('UserData:', userDataObj);

    // 验证JSON格式
    try {
      JSON.parse(inputConfig);
      JSON.parse(editingConfig);
      JSON.parse(outputConfig);
    } catch (jsonError) {
      console.error('JSON格式验证失败:', jsonError);
      ElMessage.error('配置参数JSON格式错误');
      return;
    }

    // 调用真实的API
    const response = await submitBatchMediaProducingJob({
      inputConfig,
      editingConfig,
      outputConfig,
      userData,
      templateConfig
    });

    // 处理成功响应
    console.log('✅ 任务提交成功:', response);
    ElMessage.success(`批量成片任务提交成功！任务ID: ${response.JobId}`);

    // 可以在这里添加跳转到任务列表或任务详情页面的逻辑
    // router.push(`/matrix-mix/batch-jobs/${response.JobId}`);

  } catch (error: any) {
    console.error('❌ 任务提交失败:', error);

    // 根据错误类型提供更具体的错误信息
    let errorMessage = '任务提交失败，请重试';
    if (error?.message) {
      if (error.message.includes('参数无效')) {
        errorMessage = '参数配置无效，请检查媒体文件、输出配置等参数';
      } else if (error.message.includes('JSON格式')) {
        errorMessage = 'JSON格式错误，请检查配置参数';
      } else {
        errorMessage = `提交失败: ${error.message}`;
      }
    }

    ElMessage.error(errorMessage);
  } finally {
    submitting.value = false;
  }
};

// 构建API参数的辅助方法 - 使用子组件提供的API配置
const buildInputConfig = () => {
  // 过滤有效的标题和旁白
  const validTitles = textConfig.value.titles.filter(title => title.trim());
  const validSpeeches = textConfig.value.speechTexts.filter(speech => speech.trim());

  // 验证MediaGroupArray
  const mediaGroupArray = apiConfigs.value.mediaGroups || [];
  if (mediaGroupArray.length === 0) {
    throw new Error('至少需要一个媒体组');
  }

  // 验证每个媒体组都有素材
  const validMediaGroups = mediaGroupArray.filter(group =>
    group.MediaArray && group.MediaArray.length > 0
  );

  if (validMediaGroups.length === 0) {
    throw new Error('每个媒体组至少需要一个素材文件');
  }

  const config: any = {
    MediaGroupArray: validMediaGroups
  };

  // 只有当有有效标题时才添加TitleArray
  if (validTitles.length > 0) {
    config.TitleArray = validTitles;
  }

  // 只有当有有效旁白时才添加SpeechTextArray（全局口播模式）
  if (validSpeeches.length > 0) {
    config.SpeechTextArray = validSpeeches;
  }

  console.log('🔧 构建InputConfig:', config);
  return config;
};

// 构建剪辑配置 - 使用子组件提供的API配置
const buildEditingConfig = () => {
  const config = apiConfigs.value.editingConfig || {};

  // 确保必需的配置存在，按照官方文档格式
  const editingConfig = {
    MediaConfig: config.MediaConfig || {
      Volume: 0.5
    },
    SpeechConfig: {
      Volume: config.SpeechConfig?.Volume || 0.8,
      SpeechRate: config.SpeechConfig?.SpeechRate || 1.0,
      // 添加字幕配置，根据官方文档示例
      AsrConfig: config.SpeechConfig?.AsrConfig || {
        Alignment: "BottomCenter",
        AdaptMode: "AutoWrap",
        Font: "Alibaba PuHuiTi 2.0 65 Medium",
        SizeRequestType: "Nominal",
        Spacing: -1,
        Y: 0.8
      }
    },
    BackgroundMusicConfig: config.BackgroundMusicConfig || {
      Volume: 0.3
    },
    ProcessConfig: config.ProcessConfig || {
      SingleShotDuration: 3,
      AllowVfxEffect: false,
      AllowTransition: false,
      AlignmentMode: "AutoSpeed",
      ImageDuration: 2
    },
    ...config
  };

  console.log('🔧 构建EditingConfig:', editingConfig);
  return editingConfig;
};

// 构建输出配置 - 使用UI配置转换为API配置
const buildOutputConfig = () => {
  const uiConfig = outputConfig.value;

  // 解析分辨率
  const [width, height] = uiConfig.resolution.split('x').map(Number);

  // 根据官方文档，当前只支持OSS输出模式
  // OSS模式需要MediaURL字段，不需要StorageLocation和FileName
  const apiOutputConfig = {
    Count: uiConfig.count,
    MaxDuration: uiConfig.maxDuration,
    Width: width,
    Height: height,
    Video: {
      Crf: uiConfig.quality
    },
    GeneratePreviewOnly: false,
    // OSS输出模式：MediaURL是必填的
    MediaURL: `https://szb-pc.oss-cn-beijing.aliyuncs.com/output/output_{index}.mp4`
  };

  console.log('🔧 构建OutputConfig (OSS模式):', apiOutputConfig);
  console.log('📊 UI配置:', uiConfig);
  console.log('📊 Count值检查 - UI:', uiConfig.count, '-> API:', apiOutputConfig.Count);
  return apiOutputConfig;
};

// 构建模板配置 - 使用子组件提供的API配置
const buildTemplateConfig = () => {
  return apiConfigs.value.templateConfig;
};

// 构建用户数据配置 - 只包含基础任务信息和用户信息
const buildUserData = () => {
  // 获取当前用户信息
  const userStore = useUserStore();

  return {
    // 基础任务信息
    TaskName: taskInfo.value.taskName || '',
    TaskDescription: taskInfo.value.taskDescription || '',
    CreateTime: new Date().toISOString(),

    // 用户信息
    Username: userStore.name || '',
    UserRole: userStore.roleName || '',
    UserDept: userStore.deptName || ''
  };
};

// 验证API配置是否符合官方文档要求
const validateApiConfig = (inputConfig: any, editingConfig: any, outputConfig: any, templateConfig: any) => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 判断处理模式：全局口播 vs 分组口播
  const isGlobalSpeechMode = inputConfig.SpeechTextArray && inputConfig.SpeechTextArray.length > 0;
  const isGroupSpeechMode = !isGlobalSpeechMode && inputConfig.MediaGroupArray?.some((group: any) =>
    group.Duration || (group.SpeechTextArray && group.SpeechTextArray.length > 0)
  );

  console.log(`🎯 检测到处理模式: ${isGlobalSpeechMode ? '全局口播模式' : '分组口播模式'}`);

  // 验证InputConfig
  if (!inputConfig.MediaGroupArray || inputConfig.MediaGroupArray.length === 0) {
    errors.push('InputConfig: MediaGroupArray不能为空');
  }

  inputConfig.MediaGroupArray?.forEach((group: any, index: number) => {
    if (!group.GroupName) {
      errors.push(`InputConfig: MediaGroupArray[${index}].GroupName不能为空`);
    }
    if (!group.MediaArray || group.MediaArray.length === 0) {
      errors.push(`InputConfig: MediaGroupArray[${index}].MediaArray不能为空`);
    }
    if (!group.SplitMode) {
      errors.push(`InputConfig: MediaGroupArray[${index}].SplitMode不能为空`);
    }
  });

  // 验证OutputConfig
  if (!outputConfig.GeneratePreviewOnly) {
    // 当前只支持OSS输出模式，验证MediaURL
    console.log('🎯 当前为OSS输出模式');

    if (!outputConfig.MediaURL) {
      errors.push('OutputConfig: OSS输出模式下，MediaURL是必填的');
    } else if (!outputConfig.MediaURL.includes('{index}')) {
      errors.push('OutputConfig: MediaURL必须包含{index}占位符');
    }

    // 检查是否意外包含了VOD相关字段
    if (outputConfig.StorageLocation) {
      warnings.push('OutputConfig: 检测到StorageLocation字段，但当前只支持OSS输出模式，该字段将被忽略');
    }

    if (outputConfig.FileName) {
      warnings.push('OutputConfig: 检测到FileName字段，但当前只支持OSS输出模式，该字段将被忽略');
    }
  }

  if (!outputConfig.Width || !outputConfig.Height) {
    errors.push('OutputConfig: Width和Height是必填的');
  }

  // 验证EditingConfig
  if (!editingConfig.MediaConfig) {
    errors.push('EditingConfig: MediaConfig不能为空');
  }

  if (!editingConfig.ProcessConfig) {
    errors.push('EditingConfig: ProcessConfig不能为空');
  }

  // 🔍 关键验证：AlignmentMode和ImageDuration兼容性
  if (editingConfig.ProcessConfig) {
    const processConfig = editingConfig.ProcessConfig;

    // AlignmentMode仅在全局口播模式下生效
    if (processConfig.AlignmentMode) {
      if (!isGlobalSpeechMode) {
        warnings.push('ProcessConfig: AlignmentMode仅在全局口播模式下生效，当前为分组口播模式，该字段将被忽略');
      } else {
        console.log('✅ AlignmentMode在全局口播模式下有效');
      }
    }

    // ImageDuration在两种模式下都有效，但需要检查是否有图片素材
    if (processConfig.ImageDuration) {
      console.log(`📷 ImageDuration设置为: ${processConfig.ImageDuration}秒`);
      // 这里可以进一步检查是否有图片类型的素材
    }

    // 检查MaxDuration和FixedDuration冲突
    if (outputConfig.MaxDuration && outputConfig.FixedDuration) {
      errors.push('OutputConfig: MaxDuration和FixedDuration只能二选一');
    }

    // 分组口播模式下不支持FixedDuration
    if (isGroupSpeechMode && outputConfig.FixedDuration) {
      errors.push('OutputConfig: 分组口播模式不支持FixedDuration参数');
    }
  }

  // 验证SpeechConfig
  if (editingConfig.SpeechConfig) {
    if (inputConfig.SpeechTextArray && inputConfig.SpeechTextArray.length > 0) {
      if (!editingConfig.SpeechConfig.AsrConfig) {
        warnings.push('EditingConfig: 有口播文案但缺少AsrConfig，可能无法生成字幕');
      }
    }
  }

  // 验证TemplateConfig
  if (templateConfig && Object.keys(templateConfig).length > 0) {
    if (!templateConfig.BatchEditingTemplateIdArray || !Array.isArray(templateConfig.BatchEditingTemplateIdArray) || templateConfig.BatchEditingTemplateIdArray.length === 0) {
      errors.push('TemplateConfig: 使用模板时，BatchEditingTemplateIdArray是必填的且必须是非空数组');
    } else if (templateConfig.BatchEditingTemplateIdArray.length > 50) {
      errors.push('TemplateConfig: BatchEditingTemplateIdArray最多支持50个模板ID');
    }
  }

  // 输出结果
  if (errors.length > 0) {
    console.error('❌ 配置验证发现错误:', errors);
    ElMessage.error(`配置验证发现 ${errors.length} 个错误，请检查控制台`);
  }

  if (warnings.length > 0) {
    console.warn('⚠️ 配置验证发现警告:', warnings);
    ElMessage.warning(`配置验证发现 ${warnings.length} 个警告，请检查控制台`);
  }

  if (errors.length === 0 && warnings.length === 0) {
    console.log('✅ 配置验证完全通过');
    ElMessage.success('配置验证通过');
  }
};

// 子组件事件处理函数
const handleMediaAdded = (data: { groupIndex: number; media: any[] }) => {
  console.log('媒体添加:', data);
  // 可以在这里添加额外的逻辑，比如验证文件、统计信息等
};



const handleTemplateChanged = (template: any) => {
  console.log('模板变更:', template);

  // 设置选中的模板
  selectedTemplate.value = template.id;
  console.log('✅ 已选择模板:', template.name);

  // 这里可以根据模板类型调整配置
  // 例如：不同模板可能有不同的默认参数
};

// 监控outputConfig变化
watch(() => outputConfig.value.count, (newCount, oldCount) => {
  console.log('📊 输出数量变化:', oldCount, '->', newCount);
}, { immediate: true });

// 测试API连接
const testApiConnection = async () => {
  try {
    console.log('🔍 测试API连接...');
    console.log('API基础路径:', import.meta.env.VITE_APP_BASE_API);
    console.log('完整API地址:', `${import.meta.env.VITE_APP_BASE_API}/video/media/listMediaBasicInfo`);

    // 测试最简单的请求
    const testResponse = await searchMedia({
      pageSize: 1,
      maxResults: 1
    });

    console.log('✅ API连接测试成功:', testResponse);
    return true;
  } catch (error) {
    console.error('❌ API连接测试失败:', error)
  }
}

// 初始化数据
const initializeData = async () => {
  console.log('🚀 开始初始化数据...');

  // 并行加载模板列表和媒体列表
  await Promise.all([
    fetchTemplateList(),
    fetchMediaList()
  ]);

  console.log('✅ 数据初始化完成');
};

// 重置所有配置
const resetAllConfig = () => {
  // 重置任务信息
  taskInfo.value = {
    taskName: '',
    taskDescription: ''
  };

  // 重置文本配置
  textConfig.value = {
    titles: [''],
    speechTexts: ['']
  };

  // 重置媒体组
  mediaGroups.value = [{ name: '媒体组1', media: [] }];

  // 重置选中的模板
  selectedTemplate.value = '';

  ElMessage.success('配置已重置');
};

// 保存配置
const saveConfig = () => {
  const config = {
    taskInfo: taskInfo.value,
    textConfig: textConfig.value,
    mediaGroups: mediaGroups.value,
    selectedTemplate: selectedTemplate.value,
    editingConfig: editingConfig.value,
    outputConfig: outputConfig.value
  };

  // 这里可以保存到本地存储或发送到服务器
  localStorage.setItem('batchMediaConfig', JSON.stringify(config));
  ElMessage.success('配置已保存');
};

// 应用预设配置
const applyPreset = (presetType: string) => {
  switch (presetType) {
    case 'shortVideo':
      outputConfig.value.maxDuration = 30;
      outputConfig.value.resolution = '1080x1920';
      outputConfig.value.quality = 23;
      break;
    case 'longVideo':
      outputConfig.value.maxDuration = 300;
      outputConfig.value.resolution = '1920x1080';
      outputConfig.value.quality = 20;
      break;
    case 'highQuality':
      outputConfig.value.quality = 18;
      outputConfig.value.resolution = '1920x1080';
      break;
    case 'quickMode':
      outputConfig.value.quality = 28;
      outputConfig.value.maxDuration = 60;
      break;
  }
  ElMessage.success(`已应用${presetType}预设`);
};

// 提交任务
const handleSubmit = () => {
  // 这里可以添加提交逻辑
  ElMessage.success('开始制作视频');
};



// 组件挂载时初始化数据
initializeData();
</script>

<style scoped>
.batch-media-producing {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: calc(100vh - 120px);
  box-sizing: border-box;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 工作区头部 */
.workspace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  animation: slideInDown 0.6s ease-out;
}

.workspace-title {
  display: flex;
  align-items: center;
  gap: 16px;
  color: white;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  backdrop-filter: blur(10px);
}

.title-content h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.title-content p {
  margin: 4px 0 0 0;
  font-size: 14px;
  opacity: 0.9;
}

.workspace-actions {
  display: flex;
  gap: 12px;
}

/* 主工作区 */
.main-workspace {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
  animation: fadeIn 0.6s ease-out;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  animation: slideInLeft 0.6s ease-out;
}

.right-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  animation: slideInRight 0.6s ease-out;
}

/* 媒体工作区 */
.media-workspace {
  margin-top: 20px;
  animation: slideInUp 0.8s ease-out;
}

.media-card {
  width: 100%;
}

.media-manager-full {
  max-height: 400px;
  overflow-y: auto;
}

/* 状态监控区域 */
.status-monitor-section {
  margin-top: 20px;
  animation: slideInUp 0.8s ease-out;
}

.monitor-card {
  width: 100%;
}

/* 配置卡片样式 */
.config-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.config-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.card-header > span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-content {
  padding: 20px;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3) !important;
}

/* 快速预设样式 */
.preset-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.preset-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.preset-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.preset-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.preset-icon.short-video {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.preset-icon.long-video {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.preset-icon.high-quality {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.preset-icon.quick-mode {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.preset-label {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

/* 状态监控样式 */
.monitor-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
}

.stat-icon.video {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.speech {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.music {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.count {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.time {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-icon.resolution {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 卡片通用样式 */
:deep(.el-card) {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  margin-bottom: 16px;
}

:deep(.el-card:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* 新的配置面板样式 */
.config-panel,
.advanced-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out;
}

.config-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px 16px 0 0;
  color: white;
}

.section-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  letter-spacing: -0.3px;
}

.section-content {
  padding: 20px;
}

.config-group {
  margin-bottom: 20px;
}

.config-group:last-child {
  margin-bottom: 0;
}

.group-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.text-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.text-item .el-input,
.text-item .el-textarea {
  flex: 1;
}

.add-btn {
  width: 100%;
  height: 36px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  transition: all 0.3s ease;
}

/* 新的表单样式 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.form-input {
  border-radius: 8px;
}

/* 新的文本配置网格 */
.text-config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.text-column {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.column-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.add-button {
  width: 28px;
  height: 28px;
}

.text-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.text-item-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.text-item-row:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.item-index {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 4px;
}

.item-input {
  flex: 1;
}

.delete-button {
  width: 28px;
  height: 28px;
  flex-shrink: 0;
  margin-top: 4px;
}

.add-btn:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

/* 模板卡片特殊样式 */
.template-card {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.template-card :deep(.el-card__body) {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

.template-container {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

.template-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 8px;
}

.template-item:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
  transform: translateX(4px);
}

.template-item.active {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.1);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.template-icon {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.template-info {
  flex: 1;
  min-width: 0;
}

.template-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
  font-size: 14px;
}

.template-desc {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-check {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 18px;
  height: 18px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
}

.empty-template {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

/* 快速配置预设样式 */
.preset-card :deep(.el-card__body) {
  padding: 16px;
}

.preset-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.preset-buttons .el-button {
  height: 40px;
  font-size: 14px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.preset-buttons .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
}

.header-icon {
  font-size: 18px;
  color: #409eff;
}

/* 新的快速预设样式 */
.preset-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.preset-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preset-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.preset-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.preset-icon.short-video {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.preset-icon.long-video {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.preset-icon.high-quality {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.preset-icon.quick-mode {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.preset-label {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  text-align: center;
}

.preset-buttons .el-button {
  height: 40px;
  font-size: 14px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.preset-buttons .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 底部区域样式 */
.bottom-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

/* 监控区域样式 */
.monitor-section {
  animation: slideInLeft 0.8s ease-out;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 16px 0;
}

.monitor-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 10px;
  border-left: 4px solid #409eff;
  transition: all 0.3s ease;
}

.monitor-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

/* 监控图标样式 */
.monitor-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  flex-shrink: 0;
}

.video-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.speech-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.music-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.count-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.time-icon { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.resolution-icon { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
.quality-icon { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }

.monitor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.monitor-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.monitor-value {
  font-size: 16px;
  color: #2c3e50;
  font-weight: 600;
}

/* 提交区域样式 */
.submit-section {
  animation: slideInRight 0.8s ease-out;
}

.submit-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
}

.submit-actions .el-button {
  height: 44px;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

/* 新的操作面板样式 */
.action-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.6s ease-out;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
}

.action-buttons .el-button {
  height: 44px;
  font-size: 16px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.action-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.action-buttons .submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.action-buttons .submit-btn:hover {
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-workspace {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .monitor-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .preset-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .batch-media-producing {
    padding: 12px;
  }

  .workspace-header {
    padding: 16px;
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .workspace-actions {
    justify-content: center;
  }

  .main-workspace {
    gap: 12px;
  }

  .config-card {
    margin: 0;
  }

  .card-header {
    padding: 12px 16px;
  }

  .card-content {
    padding: 16px;
  }

  .monitor-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .preset-grid {
    grid-template-columns: 1fr;
  }

  .media-manager-full {
    max-height: 300px;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.template-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
}

.template-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.template-card:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

.template-card.active {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.template-icon {
  width: 40px;
  height: 40px;
  background: #409eff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.template-info {
  flex: 1;
}

.template-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: #6c757d;
}

.template-check {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.form-tip {
  margin-left: 10px;
  color: #6c757d;
  font-size: 12px;
}

.quality-tips {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #6c757d;
}

.bgm-slider {
  margin-top: 10px;
}

.footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
}

.status-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-text {
  color: #27ae60;
  font-weight: 500;
}

.media-count {
  color: #6c757d;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.media-dialog {
  .media-selector {
    .media-tabs {
      .media-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        max-height: 400px;
        overflow-y: auto;
      }
      
      .media-item-selectable {
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        padding: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        
        &:hover {
          border-color: #409eff;
        }
        
        &.selected {
          border-color: #409eff;
          background: rgba(64, 158, 255, 0.1);
        }
        
        .media-thumbnail {
          width: 100%;
          height: 80px;
          border-radius: 4px;
          overflow: hidden;
          background: #f8f9fa;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          
          img, video {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .audio-thumbnail {
            color: #6c757d;
            font-size: 24px;
          }
        }
        
        .media-name {
          font-size: 12px;
          color: #2c3e50;
          word-break: break-all;
        }
      }
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 配置预设区域样式 */
.preset-section {
  margin-top: 24px;
}

.preset-card {
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.preset-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
  padding: 16px;
}

.preset-buttons .el-button {
  min-width: 100px;
  height: 36px;
  font-size: 14px;
  border-radius: 6px;
}

/* 配置监控区域样式 */
.config-monitor {
  margin-top: 24px;
}

.monitor-card {
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.monitor-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  padding: 16px;
}

.monitor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.monitor-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.monitor-value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

/* 提交区域样式 */
.submit-section {
  margin-top: 24px;
}

.submit-card {
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.submit-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 20px;
}

.submit-actions .el-button {
  min-width: 120px;
  height: 44px;
  font-size: 16px;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .bottom-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .monitor-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
  }

  .text-config-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .template-card {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .batch-media-producing {
    padding: 12px;
  }

  .main-grid {
    gap: 12px;
  }

  .left-section,
  .right-section {
    gap: 12px;
  }

  .preset-buttons {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .monitor-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .monitor-item {
    padding: 12px;
  }

  .monitor-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }

  .submit-actions {
    padding: 16px;
    gap: 8px;
  }

  .submit-actions .el-button {
    height: 40px;
    font-size: 14px;
  }

  .text-config-container {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .template-card {
    height: 250px;
  }

  .template-item {
    padding: 8px;
    margin-bottom: 6px;
  }

  .template-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .template-name {
    font-size: 13px;
  }

  .template-desc {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .batch-media-producing {
    padding: 8px;
  }

  .card-header {
    font-size: 14px;
  }

  .header-icon {
    font-size: 16px;
  }

  .monitor-value {
    font-size: 14px;
  }

  .monitor-label {
    font-size: 11px;
  }

  .section-title {
    font-size: 13px;
  }

  .text-item .el-input,
  .text-item .el-textarea {
    font-size: 13px;
  }

  .template-card {
    height: 200px;
  }

  .template-container {
    padding: 12px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .batch-media-producing {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }

  :deep(.el-card) {
    background: rgba(52, 73, 94, 0.95);
    color: #ecf0f1;
  }

  .card-header {
    color: #ecf0f1;
  }

  .monitor-item {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    color: #ecf0f1;
  }

  .monitor-label {
    color: #bdc3c7;
  }

  .monitor-value {
    color: #ecf0f1;
  }
}
</style>
