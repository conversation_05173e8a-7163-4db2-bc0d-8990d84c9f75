import request from '@/utils/request'

// 查询AI模型列表
export function listWymodel(query) {
  return request({
    url: '/platform/model/list',
    method: 'get',
    params: query
  })
}

// 查询AI模型详细
export function getWymodel(modelId) {
  return request({
    url: '/platform/model/' + modelId,
    method: 'get'
  })
}

// 新增AI模型
export function addWymodel(data) {
  return request({
    url: '/platform/model',
    method: 'post',
    data: data
  })
}

// 修改AI模型
export function updateWymodel(data) {
  return request({
    url: '/platform/model',
    method: 'put',
    data: data
  })
}

// 删除AI模型
export function delWymodel(modelId) {
  return request({
    url: '/platform/model/' + modelId,
    method: 'delete'
  })
}
