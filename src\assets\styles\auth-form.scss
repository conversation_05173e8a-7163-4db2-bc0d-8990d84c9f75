// 变量定义
$primary: #4f46e5;
$primary-dark: #4338ca;
$text-primary: #1a1a1a;
$text-secondary: #4b5563;
$text-placeholder: #9ca3af;
$border-gray: #d1d5db;
$icon-gray: #6b7280;

// 过渡效果
$transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

// 阴影效果
$shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
$shadow-focus: 0 0 0 2px rgba($primary, 0.1);
$shadow-hover: 0 4px 12px rgba($primary, 0.4);

// Element Plus 变量
:root {
  --el-color-primary: #{$primary};
  --el-color-primary-dark-2: #{$primary-dark};
}

// Mixins
@mixin transition-base {
  transition: $transition-normal;
}

// 表单容器
.auth-form-wrapper {
  width: 100%;
}

// 表单标题
.form-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: 2rem;
  text-align: center;
}

// 表单样式
.auth-form {
  :deep(.el-input) {
    --el-input-bg-color: rgba(255, 255, 255, 0.8);
    --el-input-border-color: transparent;
    
    .el-input__wrapper {
      box-shadow: $shadow-sm;
      border-radius: 12px;
      padding: 0.75rem 1rem;
      @include transition-base;
      
      &:hover {
        box-shadow: $shadow-md;
      }
      
      &.is-focus {
        box-shadow: $shadow-focus;
      }
    }
    
    .el-input__inner {
      height: 1.5rem;
      font-size: 0.875rem;
      
      &::placeholder {
        color: $text-placeholder;
      }
    }
    
    .el-input__prefix {
      font-size: 1.25rem;
      color: $icon-gray;
    }
  }
}

// 验证码包装器
.captcha-wrapper {
  display: flex;
  gap: 1rem;
  
  .el-input {
    flex: 1;
  }
}

// 验证码图片
.captcha-img {
  height: 3rem;
  border-radius: 8px;
  cursor: pointer;
  @include transition-base;
  
  &:hover {
    opacity: 0.8;
  }
}

// 表单选项
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1.5rem 0;
  
  :deep(.el-checkbox) {
    --el-checkbox-font-size: 0.875rem;
    --el-checkbox-input-border-color: #{$border-gray};
    
    .el-checkbox__label {
      color: $text-secondary;
    }
  }
}

// 链接样式
.form-link {
  font-size: 0.875rem;
  color: $primary;
  text-decoration: none;
  @include transition-base;
  
  &:hover {
    color: $primary-dark;
    text-decoration: underline;
  }
}

// 提交按钮
.submit-btn {
  width: 100%;
  height: 3rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 12px;
  background: linear-gradient(135deg, $primary 0%, $primary-dark 100%);
  border: none;
  @include transition-base;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: $shadow-hover;
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 表单项间距
:deep(.el-form-item) {
  margin-bottom: 1.25rem;
}

// 注册类型选项
.register-type-options {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  justify-content: center;
}

// 验证码按钮
.captcha-btn {
  height: 3rem;
  border-radius: 12px;
  padding: 0 1.5rem;
  font-size: 0.875rem;
  background: linear-gradient(135deg, $primary 0%, $primary-dark 100%);
  border: none;
  color: white;
  @include transition-base;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: $shadow-hover;
  }
  
  &:active {
    transform: translateY(0);
  }
} 