<template>
  <component
    :is="type"
    ref="mediaRef"
    v-bind="mediaAttrs"
    :controls="controls"
    :poster="poster"
    :preload="preload"
    class="lazy-media"
  >
    <source v-if="realSrc" :src="realSrc" :type="mimeType" />
    <slot />
  </component>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'

const props = defineProps({
  src: { type: String, required: true },
  type: { type: String, default: 'video' }, // 'video' or 'audio'
  controls: { type: Boolean, default: true },
  poster: String,
  preload: { type: String, default: 'metadata' },
  mimeType: String,
  mediaAttrs: { type: Object, default: () => ({}) }
})

const realSrc = ref('')
const mediaRef = ref<HTMLElement | null>(null)
let observer: IntersectionObserver | null = null
let loadedOnce = false

const loadMedia = () => {
  if (!realSrc.value && !loadedOnce) {
    console.log('LazyMedia loading:', props.type, '-', props.src) // 调试日志
    realSrc.value = props.src
    loadedOnce = true
    
    // 可以通过事件通知父组件媒体已加载
    // emit('media-loaded', { src: props.src, type: props.type })
  }
}

// 监听src变化，重置加载状态（只有在src真正改变时）
watch(() => props.src, (newSrc, oldSrc) => {
  if (newSrc !== oldSrc && newSrc) {
    // 只有当src真正改变时才重新加载
    if (!loadedOnce) {
      loadMedia()
    }
  }
})

onMounted(() => {
  // 如果已经在视口内，直接加载
  if (mediaRef.value) {
    observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0]
        if (entry.isIntersecting && !loadedOnce) {
          loadMedia()
          // 加载完成后立即停止观察
          if (observer) {
            observer.disconnect()
            observer = null
          }
        }
      },
      { 
        threshold: 0.1,
        rootMargin: '50px' // 提前50px开始加载
      }
    )
    observer.observe(mediaRef.value)
  }
})

onBeforeUnmount(() => {
  if (observer) {
    observer.disconnect()
    observer = null
  }
})
</script>

<style scoped>
.lazy-media {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  background: #000;
}
</style> 