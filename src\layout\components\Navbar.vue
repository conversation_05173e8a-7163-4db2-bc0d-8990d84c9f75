<template>
  <div class="navbar">
    <!-- Logo区域 -->
    <div class="logo-container">
      <logo :collapse="false" />
    </div>

    <!-- 中间区域 -->
    <div class="middle-section">
      <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container"
        @toggleClick="toggleSideBar" />
      <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!settingsStore.topNav" />
      <top-nav id="topmenu-container" class="topmenu-container" v-else />
    </div>

    <!-- 右侧菜单 -->
    <div class="right-menu">
      <template v-if="appStore.device !== 'mobile'">
        <el-button style="margin-top: 2px; padding: 0px; border: 0px;" @click="jumpMessage">
          <!--小铃铛-->
          <SmallBell @view-details="viewDetails" /> 
        </el-button>
        <el-button 
          class="task-history-btn"
          :class="{ 'is-active': isTaskHistoryOpen }"
          @click="toggleTaskHistory"
        >
          <el-badge :value="taskCount" :max="99" class="task-badge">
            <el-icon><Mic /></el-icon>
          </el-badge>
        </el-button>
        <!-- <header-search id="header-search" class="right-menu-item" /> -->
        <!-- <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->
        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->
      </template>

      <div class="avatar-container">
        <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">
          <div class="avatar-wrapper">
            <img :src="userStore.avatar" class="user-avatar" />
            <el-icon><caret-bottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
              <el-dropdown-item command="setLayout">
                <span>布局设置</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <el-dialog :title="title" v-model="detailsDialogVisible" width="40%" append-to-body :show-overlay="false"
        class="custom-dialog">
        <div>
          <el-descriptions :column="1" border class="descriptions">
            <el-descriptions-item label="标题" width="50px">{{ detailForm.messageTitle }}</el-descriptions-item>
            <el-descriptions-item label="发件人" width="50px">{{ detailForm.createBy }}</el-descriptions-item>
            <el-descriptions-item label="发送方式" width="50px">
              <dict-tag :options="send_mode" :value="detailForm.sendMode" />
            </el-descriptions-item>
            <el-descriptions-item label="发送时间" width="50px">
              {{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
            </el-descriptions-item>
            <el-descriptions-item label="消息内容" width="50px">{{ detailForm.messageContent }}</el-descriptions-item>
            <el-descriptions-item label="收件人" width="50px">{{ detailForm.messageRecipient }}</el-descriptions-item>
            <el-descriptions-item label="消息类型" width="50px">
              <dict-tag :options="message_type" :value="detailForm.messageType" />
            </el-descriptions-item>
            <el-descriptions-item label="备注" width="50px">{{ detailForm.remark }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-dialog>

    </div>
  </div>
  <TaskHistory 
    ref="taskHistoryRef"
    v-model:isOpen="isTaskHistoryOpen"
  />
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import HeaderSearch from '@/components/HeaderSearch'
import Logo from './Sidebar/Logo.vue'
import useAppStore from '@/store/modules/app'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import { useRouter } from 'vue-router'
import SmallBell from '@/views/modelMessage/messageSystem/components/smallBell.vue'
import { getUpdate } from '@/api/modelMessage/messageSystem'
import { useDict } from '@/utils/dict'
import TaskHistory from '@/components/TaskHistory/index.vue'
import { RoutesAlias } from '@/router/routesAlias'
const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()
const router = useRouter()


const detailsDialogVisible = ref(false);
const detailForm = ref([]);
const title = ref("");
const { message_type, send_mode } = useDict("message_status", "message_type", "send_mode");
const isTaskHistoryOpen = ref(false)
const taskHistoryRef = ref(null)
const taskCount = ref(0)

function toggleSideBar() {
  appStore.toggleSideBar()
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      location.href = router.resolve(RoutesAlias.Home).href;
    })
  }).catch(() => { });
}

const emits = defineEmits(['setLayout'])
function setLayout() {
  emits('setLayout');
}

function jumpMessage() {
  router.push({ path: '/modelMessage/messageSystem' })
}

function viewDetails(item) {
  title.value = "信息详情"
  detailsDialogVisible.value = true;
  detailForm.value = item;
  getUpdate(item.messageId).then(response => { });
}

// 切换任务历史面板
function toggleTaskHistory() {
  isTaskHistoryOpen.value = !isTaskHistoryOpen.value
}

// 打开任务历史面板
function openTaskHistory() {
  isTaskHistoryOpen.value = true
}

// 添加新任务
function addNewTask(task) {
  if (taskHistoryRef.value) {
    taskHistoryRef.value.addNewTask(task)
    taskCount.value++
  }
}

defineExpose({
  addNewTask,
  openTaskHistory
})
</script>

<style lang="scss" scoped>
@use "@/assets/styles/variables.module.scss";

.navbar {
  height: 48px;
  overflow: hidden;
  position: relative;
  background: #ffffff;
  display: flex;
  align-items: center;
  padding: 0 16px;
  transition: all 0.3s ease;
  -webkit-app-region: drag;

  .logo-container {
    width: variables.$base-sidebar-width;
    padding: 0 12px;
    overflow: hidden;
    
    :deep(a),
    :deep(img) {
      -webkit-app-region: no-drag;
    }
  }

  .middle-section {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .hamburger-container {
    display: flex;
    align-items: center;
    height: 48px;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0 12px;
    margin-left: -12px;
    border-radius: 50%;
    -webkit-app-region: no-drag;

    &:hover {
      background: #f1f3f4;

      .svg-icon {
        color: #1a73e8;
      }
    }

    .svg-icon {
      font-size: 20px;
      color: #5f6368;
      transition: all 0.3s ease;
    }
  }

  .breadcrumb-container {
    margin-left: 8px;

    :deep(.el-breadcrumb__inner),
    :deep(.el-breadcrumb__separator) {
      -webkit-app-region: no-drag;
    }
  }

  .topmenu-container {
    position: absolute;
    display: flex;
    align-items: center;
    left: 260px;

    :deep(.el-menu-item),
    :deep(.el-sub-menu__title),
    :deep(.el-sub-menu__icon-arrow) {
      -webkit-app-region: no-drag;
    }
  }

  .right-menu {
    margin-left: auto;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 4px;

    :deep(.el-button) {
      -webkit-app-region: no-drag;
    }

    .right-menu-item {
      padding: 0 8px;
      height: 40px;
      width: 40px;
      font-size: 18px;
      color: #5f6368;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      -webkit-app-region: no-drag;

      &.hover-effect {
        cursor: pointer;

        &:hover {
          background: #f1f3f4;
          color: #1a73e8;
        }
      }
    }

    .avatar-container {
      margin-right: 18px;
      margin-left: 8px;
      -webkit-app-region: no-drag;

      .avatar-wrapper {
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        padding: 4px;
        border-radius: 50%;
        height: 32px;
        width: 32px;
        justify-content: center;

        &:hover {
          background: #f1f3f4;
        }

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          object-fit: cover;
        }

        .el-icon {
          position: absolute;
          right: -18px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
}

// 所有弹出层不可拖拽
:deep(.el-dropdown-menu),
:deep(.el-menu--popup),
:deep(.el-dialog__wrapper) {
  -webkit-app-region: no-drag;
}

.el-descriptions {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(4, 116, 245, 0.1);
}

.el-descriptions-item {
  margin-bottom: 20px;
}

.el-descriptions-item label {
  color: #409eff;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.custom-dialog {
  background-color: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.task-history-btn {
  position: relative;
  width: 40px;
  height: 40px;
  padding: 0;
  border: none;
  border-radius: 10px;
  background: transparent;
  color: #5f6368;
  transition: all 0.3s ease;
  
  &:hover,
  &.is-active {
    background: #f1f3f4;
    color: #1a73e8;
  }
  
  .el-icon {
    font-size: 20px;
  }
  
  :deep(.task-badge) {
    .el-badge__content {
      background: #f43f5e;
      border: 2px solid #fff;
      box-shadow: 0 2px 6px rgba(244, 63, 94, 0.3);
    }
  }
}
</style>