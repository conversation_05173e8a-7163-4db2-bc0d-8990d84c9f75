<template>
  <div class="avatar-step">
    <div class="panel-header">
      <div class="header-info">
        <h3 class="panel-title">
          <el-icon class="panel-icon"><Picture /></el-icon>
          选择数字人形象
        </h3>
        <p class="panel-desc">请选择参与对话的数字人形象，建议选择2-4个以获得更好的对话效果</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="goToSelectAvatars" class="select-btn">
          <el-icon><Plus /></el-icon>选择形象
        </el-button>
      </div>
    </div>

    <div class="avatar-content">
      <div class="selection-status">
        <span class="status-text">已选择 {{ internalSelectedAvatars.length }}/6 个形象</span>
        <div class="status-bar">
          <div class="status-progress" :style="{ width: `${(internalSelectedAvatars.length / 6) * 100}%` }"></div>
        </div>
      </div>

      <div v-if="internalSelectedAvatars.length === 0" class="empty-state">
        <div class="empty-icon">
          <el-icon><Picture /></el-icon>
        </div>
        <h4 class="empty-title">暂未选择任何数字人形象</h4>
        <p class="empty-desc">请选择至少2个数字人形象来开始创建对话</p>
        <el-button type="primary" @click="goToSelectAvatars" class="empty-action">
          立即选择
        </el-button>
      </div>

      <div v-else class="avatar-grid">
        <div v-for="(avatar, index) in internalSelectedAvatars" :key="avatar.imageId" class="avatar-card" :style="{ animationDelay: `${index * 0.1}s` }">
          <div class="card-preview">
            <video 
              v-if="avatarPreviews[avatar.imageAddress]" 
              :src="avatarPreviews[avatar.imageAddress]" 
              class="preview-video" 
              muted 
              loop 
              autoplay
            />
            <div v-else class="preview-loading">
              <el-icon class="loading-icon"><Loading /></el-icon>
              <span class="loading-text">加载中...</span>
            </div>
            <div class="card-overlay">
              <el-button 
                type="danger" 
                size="small" 
                circle 
                @click="removeAvatar(avatar.imageId)"
                class="remove-btn"
              >
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>
          <div class="card-info">
            <h5 class="avatar-name">{{ avatar.imageName }}</h5>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { Picture, Plus, Close, Loading } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { imageDetail } from '@/api/platform/image'

const router = useRouter()
const props = defineProps({
  selectedAvatars: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update-avatars'])

// 组件内部状态
const avatarPreviews = ref({})
const internalSelectedAvatars = ref([...props.selectedAvatars])

// 获取形象预览
const getAvatarPreview = async (imageAddress) => {
  if (!imageAddress || avatarPreviews.value[imageAddress]) {
    return avatarPreviews.value[imageAddress]
  }
  try {
    const response = await imageDetail(imageAddress)
    if (response && response.data) {
      const previewUrl = response.data.url || response.data
      avatarPreviews.value[imageAddress] = previewUrl
      return previewUrl
    }
  } catch (error) {
    console.error('获取形象预览失败:', error)
  }
  return null
}

// 加载所有形象预览
const loadAvatarPreviews = async () => {
  for (const avatar of internalSelectedAvatars.value) {
    if (avatar.imageAddress) {
      await getAvatarPreview(avatar.imageAddress)
    }
  }
}

const goToSelectAvatars = () => {
  router.push({
    path: '/szbVideo/image',
    query: {
      from: 'dialogueSynthesis',
      mode: 'select',
      returnPath: '/szbVideo/dialogueSynthesis'
    }
  })
}

const removeAvatar = (imageId) => {
  const avatar = internalSelectedAvatars.value.find(a => a.imageId === imageId)
  if (!avatar) return

  ElMessageBox.confirm(
    `确定要移除数字人形象 "${avatar.imageName}" 吗？`,
    '确认移除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = internalSelectedAvatars.value.findIndex(a => a.imageId === imageId)
    if (index > -1) {
      internalSelectedAvatars.value.splice(index, 1)
      emit('update-avatars', internalSelectedAvatars.value)
      if (internalSelectedAvatars.value.length < 2) {
        ElMessage.warning('建议至少选择2个数字人形象')
      }
    }
  }).catch(() => {})
}

// 监听props变化
watch(() => props.selectedAvatars, (newAvatars) => {
  internalSelectedAvatars.value = [...newAvatars]
  loadAvatarPreviews()
}, { immediate: true })

onMounted(() => {
  loadAvatarPreviews()
})
</script>

<style lang="scss" scoped>
.avatar-step {
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 1px solid #e9ecef;

    .header-info {
      .panel-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin: 0 0 8px 0;

        .panel-icon {
          font-size: 28px;
          color: #667eea;
        }
      }

      .panel-desc {
        font-size: 16px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .select-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
      }
    }
  }

  .selection-status {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid #e9ecef;

    .status-text {
      font-weight: 600;
      color: #333;
      font-size: 16px;
    }

    .status-bar {
      flex: 1;
      height: 8px;
      background: #e9ecef;
      border-radius: 4px;
      overflow: hidden;

      .status-progress {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 4px;
        transition: width 0.6s ease;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 2px dashed #dee2e6;

    .empty-icon {
      font-size: 64px;
      color: #dee2e6;
      margin-bottom: 20px;
    }

    .empty-title {
      font-size: 20px;
      font-weight: 600;
      color: #666;
      margin: 0 0 12px 0;
    }

    .empty-desc {
      font-size: 16px;
      color: #999;
      margin: 0 0 24px 0;
      line-height: 1.5;
    }

    .empty-action {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 12px;
      padding: 12px 32px;
      font-weight: 600;
      font-size: 16px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      }
    }
  }

  .avatar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 24px;
    margin-top: 20px;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
      gap: 20px;
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;
    }

    @media (max-width: 480px) {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 12px;
    }
  }

  .avatar-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
    }

    .card-preview {
      position: relative;
      height: 180px;
      overflow: hidden;

      @media (max-width: 768px) {
        height: 160px;
      }

      @media (max-width: 480px) {
        height: 140px;
      }

      .preview-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .preview-loading {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #999;

        .loading-icon {
          font-size: 32px;
          margin-bottom: 8px;
          animation: spin 2s linear infinite;
        }

        .loading-text {
          font-size: 14px;
        }
      }

      .card-overlay {
        position: absolute;
        top: 12px;
        right: 12px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover .card-overlay {
        opacity: 1;
      }

      .remove-btn {
        background: rgba(245, 108, 108, 0.9);
        border: none;
        color: white;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(245, 108, 108, 1);
          transform: scale(1.1);
        }
      }
    }

    .card-info {
      padding: 16px 16px 20px 16px;
      text-align: center;

      .avatar-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.4;
      }
    }
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
