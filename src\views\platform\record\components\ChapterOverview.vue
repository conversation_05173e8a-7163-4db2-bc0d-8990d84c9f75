<template>
  <div class="overview-container">
    <div class="content-card" v-if="overview">
      <div class="card-header">
        <div class="header-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="currentColor" stroke-width="1.5"/>
            <path d="M6.5 2H20v20l-5.5-6-5.5 6V2Z" stroke="currentColor" stroke-width="1.5"/>
          </svg>
        </div>
        <h3 class="card-title">章节概览</h3>
      </div>
      <div class="card-content">
        <p class="overview-text">{{ overview }}</p>
      </div>
    </div>

    <div v-if="!overview" class="empty-state">
      <div class="empty-illustration">
        <svg viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="60" cy="60" r="50" fill="#f8fafc" stroke="#e2e8f0" stroke-width="2"/>
          <path d="M40 45h40v30H40z" fill="#cbd5e1" opacity="0.5"/>
          <path d="M45 50h30v2H45zm0 6h25v2H45zm0 6h35v2H45z" fill="#64748b"/>
        </svg>
      </div>
      <h4 class="empty-title">暂无内容</h4>
      <p class="empty-message">章节信息正在生成中，请稍后刷新查看</p>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  overview: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
.overview-container {
  animation: fadeInUp 0.6s ease-out;
}

.content-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f3f4f6;
  background: #fafbfc;
}

.header-icon {
  width: 24px;
  height: 24px;
  color: #6366f1;
  flex-shrink: 0;

  svg {
    width: 100%;
    height: 100%;
  }
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  letter-spacing: -0.01em;
}

.card-content {
  padding: 24px;
}

.overview-text {
  font-size: 15px;
  line-height: 1.6;
  color: #4b5563;
  margin: 0;
  font-weight: 400;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 48px 32px;
  background: #ffffff;
  border-radius: 16px;
  border: 2px dashed #e5e7eb;
  transition: all 0.3s ease;

  &:hover {
    border-color: #d1d5db;
    background: #fafbfc;
  }
}

.empty-illustration {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  opacity: 0.7;

  svg {
    width: 100%;
    height: 100%;
  }
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-message {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
