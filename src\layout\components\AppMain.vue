<template>
  <section class="app-main">
    <el-scrollbar>
      <router-view v-slot="{ Component, route }">
        <transition name="fade-transform" mode="out-in">
          <keep-alive :include="cachedViews">
            <component :is="Component" :key="route.path" />
          </keep-alive>
        </transition>
      </router-view>
    </el-scrollbar>
  </section>
</template>

<script setup>
import useTagsViewStore from '@/store/modules/tagsView'

const tagsViewStore = useTagsViewStore()
const cachedViews = computed(() => tagsViewStore.cachedViews)
</script>

<style lang="scss" scoped>
.app-main {
  width: 100%;
  position: relative;
  overflow: hidden;
  
  .el-scrollbar {
    height: 100%;
    
    :deep(.el-scrollbar__bar) {
      &.is-horizontal {
        display: none;
      }
      
      &.is-vertical {
        width: 6px;
        right: 2px;
        
        .el-scrollbar__thumb {
          background: rgba(144, 147, 153, 0.3);
          border-radius: 6px;
          
          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }
      }
    }
  }
}

.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>