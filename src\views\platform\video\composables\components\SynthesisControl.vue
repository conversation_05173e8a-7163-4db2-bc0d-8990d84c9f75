<template>
  <div class="synthesis-control">
    <div class="control-buttons">
      <el-button
        type="primary"
        size="large"
        @click="handleStart"
        :loading="synthesisStatus.isRunning"
        :disabled="!canStart"
        class="synthesis-btn"
      >
        <el-icon><VideoPlay /></el-icon>
        {{ getSynthesisButtonText() }}
      </el-button>

      <!-- 调试信息和备用按钮 -->
      <div v-if="!canStart && isDevelopment" class="debug-info">
        <el-alert
          title="按钮不可用原因"
          type="warning"
          :closable="false"
          style="margin-top: 10px;"
        >
          <template #default>
            <div style="font-size: 12px;">
              <p>版本选择: {{ version ? '✓' : '✗' }}</p>
              <p>数字人数量: {{ digitalHumansCount }}/2 {{ digitalHumansCount >= 2 ? '✓' : '✗' }}</p>
              <p>对话内容: {{ dialogueCount }} {{ dialogueCount > 0 ? '✓' : '✗' }}</p>
              <p>AI模型: {{ version !== 'V' || model ? '✓' : '✗' }}</p>
              <p>运行状态: {{ !synthesisStatus.isRunning ? '✓' : '✗' }}</p>
              <p>视频标题: "{{ videoTitle }}" {{ videoTitle.trim() ? '✓' : '✗' }}</p>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 备用强制开始按钮（仅开发环境） -->
      <el-button
        v-if="isDevelopment"
        type="danger"
        size="small"
        @click="handleForceStart"
        :loading="synthesisStatus.isRunning"
        style="margin-top: 10px;"
      >
        强制开始（调试）
      </el-button>
    </div>

    <!-- 合成状态显示 -->
    <div v-if="synthesisStatus.message" class="synthesis-status">
      <div class="status-card" :class="getStatusClass()">
        <div class="status-icon">
          <el-icon v-if="synthesisStatus.isRunning"><Loading /></el-icon>
          <el-icon v-else-if="synthesisStatus.isCompleted"><CircleCheck /></el-icon>
          <el-icon v-else-if="synthesisStatus.hasError"><CircleClose /></el-icon>
          <el-icon v-else><InfoFilled /></el-icon>
        </div>
        <div class="status-content">
          <h5 class="status-title">{{ synthesisStatus.title }}</h5>
          <p class="status-message">{{ synthesisStatus.message }}</p>
          <div v-if="synthesisStatus.progress" class="status-progress">
            <el-progress 
              :percentage="synthesisStatus.progress" 
              :status="synthesisStatus.isCompleted ? 'success' : (synthesisStatus.hasError ? 'exception' : null)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, reactive, onUnmounted } from 'vue'
import { VideoPlay, InfoFilled, Loading, CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { dialogueSynthesis, getAutoSynthesisStatus } from '@/api/platform/video'

const props = defineProps({
  digitalHumans: {
    type: Array,
    default: () => []
  },
  dialogueContent: {
    type: Array,
    default: () => []
  },
  synthesisConfig: {
    type: Object,
    required: true
  },
  videoInfo: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['synthesis-complete'])

// 内置音色列表
const builtinVoices = ['zhiyuan', 'zhiyue', 'zhistella', 'zhida', 'aiqi', 'aicheng', 'aijia', 'siqi', 'sijia', 'mashu', 'yuer', 'ruoxi', 'aida', 'sicheng', 'ninger', 'xiaoyun', 'xiaogang', 'ruilin']

// 合成状态管理
const synthesisStatus = reactive({
  isRunning: false,
  isCompleted: false,
  hasError: false,
  title: '',
  message: '',
  progress: 0
})

const dialogueGroupId = ref('')
const finalResult = ref(null)
let statusCheckTimer = null

// 安全检查开发环境
const isDevelopment = computed(() => {
  try {
    return typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development'
  } catch (e) {
    return false
  }
})

const canStart = computed(() => {
  const hasVersion = !!props.synthesisConfig.version
  const hasEnoughHumans = props.digitalHumans.length >= 2
  const hasDialogue = props.dialogueContent.length > 0
  const hasModel = props.synthesisConfig.version !== 'V' || !!props.synthesisConfig.model
  const notRunning = !synthesisStatus.isRunning
  const hasTitle = !!props.videoInfo.title.trim()

  return hasVersion && hasEnoughHumans && hasDialogue && hasModel && notRunning && hasTitle
})

const getSynthesisButtonText = () => {
  if (props.synthesisStatus.isRunning) {
    return '合成中...'
  } else if (props.synthesisStatus.isCompleted) {
    return '重新合成'
  } else {
    return '开始合成'
  }
}

const getStatusClass = () => {
  if (props.synthesisStatus.isCompleted) return 'status-success'
  if (props.synthesisStatus.hasError) return 'status-error'
  if (props.synthesisStatus.isRunning) return 'status-running'
  return 'status-info'
}

// 重置合成状态
const resetSynthesisStatus = () => {
  synthesisStatus.isRunning = false
  synthesisStatus.isCompleted = false
  synthesisStatus.hasError = false
  synthesisStatus.title = ''
  synthesisStatus.message = ''
  synthesisStatus.progress = 0
  finalResult.value = null
}

// 验证合成数据
const validateSynthesisData = () => {
  if (!props.synthesisConfig.version) {
    throw new Error('请选择合成版本')
  }

  if (props.digitalHumans.length < 2) {
    throw new Error('至少需要选择2个数字人')
  }

  if (props.dialogueContent.length === 0) {
    throw new Error('请添加对话内容')
  }

  if (props.synthesisConfig.version === 'V' && !props.synthesisConfig.model) {
    throw new Error('V版需要选择AI模型')
  }

  if (!props.videoInfo.title.trim()) {
    throw new Error('请输入视频标题')
  }

  // 验证对话内容
  props.dialogueContent.forEach((dialogue, index) => {
    if (!dialogue.text || dialogue.text.trim() === '') {
      throw new Error(`第${index + 1}条对话内容不能为空`)
    }
    if (!dialogue.speaker) {
      throw new Error(`第${index + 1}条对话缺少发言人`)
    }
  })

  // 验证数字人配置
  props.digitalHumans.forEach((human, index) => {
    if (!human.name || human.name.trim() === '') {
      throw new Error(`第${index + 1}个数字人缺少名称`)
    }
    if (!human.voiceId && !human.voiceName) {
      throw new Error(`第${index + 1}个数字人缺少声音配置`)
    }
  })
}

// 构建合成参数
const buildSynthesisParams = () => {
  return {
    version: props.synthesisConfig.version,
    autoSynthesis: true,
    enableSubtitles: true,
    title: props.videoInfo.title,
    description: props.videoInfo.description,
    digitalHumans: props.digitalHumans.map((human, index) => {
      if (!human.avatarAddress) {
        throw new Error(`数字人${index + 1}缺少形象地址`)
      }
      if (!human.name || human.name.trim() === '') {
        throw new Error(`数字人${index + 1}缺少名称`)
      }
      if (!human.voiceName) {
        throw new Error(`数字人${index + 1}缺少声音名称`)
      }

      let voiceType = human.voiceType || 'builtin'
      let voiceId = null

      if (voiceType === 'system') {
        voiceId = parseInt(human.voiceId)
        if (!voiceId || voiceId <= 0) {
          throw new Error(`数字人${index + 1}的系统声音ID无效`)
        }
      } else if (voiceType === 'builtin') {
        if (!builtinVoices.includes(human.voiceName)) {
          throw new Error(`数字人${index + 1}使用了不支持的内置音色: ${human.voiceName}`)
        }
      }

      return {
        id: human.id,
        name: human.name.trim(),
        avatarAddress: human.avatarAddress,
        avatarName: human.avatarName,
        voiceType: voiceType,
        voiceId: voiceId,
        voiceName: human.voiceName
      }
    }),
    dialogueContent: props.dialogueContent.map((dialogue, index) => {
      if (!dialogue.text || dialogue.text.trim() === '') {
        throw new Error(`对话${index + 1}的文本内容不能为空`)
      }

      if (!dialogue.speaker) {
        throw new Error(`对话${index + 1}缺少发言人配置`)
      }

      const humanExists = props.digitalHumans.some(human => human.id === dialogue.speaker)
      if (!humanExists) {
        throw new Error(`对话${index + 1}的发言人配置错误: ${dialogue.speaker}`)
      }

      return {
        id: index + 1,
        speaker: dialogue.speaker,
        speakerName: dialogue.speakerName || `数字人${index + 1}`,
        text: dialogue.text.trim(),
        order: index + 1
      }
    }),
    bboxShiftValue: props.synthesisConfig.version === 'M' ? props.synthesisConfig.bboxShiftValue : null,
    model: props.synthesisConfig.version === 'V' ? props.synthesisConfig.model : null
  }
}

const handleStart = async () => {
  if (!canStart.value) {
    ElMessage.warning('请完成所有配置后再开始合成')
    return
  }

  try {
    resetSynthesisStatus()

    synthesisStatus.isRunning = true
    synthesisStatus.title = '开始合成'
    synthesisStatus.message = '正在准备合成任务...'
    synthesisStatus.progress = 10

    validateSynthesisData()
    const synthesisParams = buildSynthesisParams()

    synthesisStatus.message = '正在提交合成任务...'
    synthesisStatus.progress = 20

    const response = await dialogueSynthesis(synthesisParams)

    if (response && response.data) {
      dialogueGroupId.value = response.data.dialogueGroupId

      synthesisStatus.title = '合成任务已提交'
      synthesisStatus.message = response.data.message || '正在处理合成任务...'
      synthesisStatus.progress = 30

      startStatusMonitoring()

      ElMessage.success('合成任务已提交，正在处理...')
      emit('synthesis-complete', response.data)
    } else {
      throw new Error('合成响应异常')
    }
  } catch (error) {
    synthesisStatus.isRunning = false
    synthesisStatus.hasError = true
    synthesisStatus.title = '合成失败'
    synthesisStatus.message = error.message || '未知错误'
    ElMessage.error('合成失败: ' + (error.message || '请稍后重试'))
  }
}

const handleForceStart = async () => {
  // 确保有基本的标题
  if (!props.videoInfo.title.trim()) {
    props.videoInfo.title = `数字人对话视频_${new Date().toLocaleString('zh-CN')}`
  }

  await handleStart()
}

// 状态监控
const startStatusMonitoring = () => {
  checkAutoSynthesisStatus()
  statusCheckTimer = setInterval(() => {
    if (!synthesisStatus.isCompleted && !synthesisStatus.hasError) {
      checkAutoSynthesisStatus()
    } else {
      stopStatusMonitoring()
    }
  }, 10000)
}

const stopStatusMonitoring = () => {
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer)
    statusCheckTimer = null
  }
}

const checkAutoSynthesisStatus = async () => {
  if (!dialogueGroupId.value) return

  try {
    const response = await getAutoSynthesisStatus(dialogueGroupId.value)

    if (response.code === 200 && response.data) {
      const status = response.data

      updateSynthesisProgress(status)

      const allTasksCompleted = status.totalTasks > 0 && status.completedTasks === status.totalTasks
      const isFullyCompleted = (status.overallStatus === 'FULLY_COMPLETED') ||
                               (status.overallStatus === 'DIALOGUE_COMPLETED' && allTasksCompleted && status.alreadyClipped)

      if (isFullyCompleted) {
        synthesisStatus.isRunning = false
        synthesisStatus.isCompleted = true
        synthesisStatus.title = '合成完成'
        synthesisStatus.message = '数字人对话视频合成完成！'
        synthesisStatus.progress = 100

        finalResult.value = {
          dialogueGroupId: dialogueGroupId.value,
          finalVideoUrl: status.clipInfo?.finalVideoUrl,
          clipInfo: status.clipInfo
        }

        stopStatusMonitoring()
        ElMessage.success('数字人对话视频合成完成！')
      } else if (status.overallStatus === 'FAILED') {
        synthesisStatus.isRunning = false
        synthesisStatus.hasError = true
        synthesisStatus.title = '合成失败'
        synthesisStatus.message = status.message || '合成过程中出现错误'

        stopStatusMonitoring()
        ElMessage.error('合成失败: ' + (status.message || '请稍后重试'))
      } else {
        synthesisStatus.title = getStatusTitle(status)
        synthesisStatus.message = status.message || getStatusMessage(status)
      }
    }
  } catch (error) {
    // 网络错误不立即停止监控
  }
}

const updateSynthesisProgress = (status) => {
  if (status.totalTasks > 0) {
    const baseProgress = 30
    const taskProgress = (status.completedTasks / status.totalTasks) * 60
    synthesisStatus.progress = Math.min(95, baseProgress + taskProgress)
  }
}

const getStatusTitle = (status) => {
  const statusMap = {
    'PENDING': '等待处理',
    'PROCESSING': '正在处理',
    'DIALOGUE_COMPLETED': '对话生成完成',
    'CLIPPING': '正在剪辑',
    'FULLY_COMPLETED': '合成完成',
    'FAILED': '合成失败'
  }
  return statusMap[status.overallStatus] || '处理中'
}

const getStatusMessage = (status) => {
  if (status.totalTasks > 0) {
    return `已完成 ${status.completedTasks}/${status.totalTasks} 个任务`
  }
  return '正在处理中...'
}

// 组件卸载时清理定时器
onUnmounted(() => {
  stopStatusMonitoring()
})
</script>

<style lang="scss" scoped>
.synthesis-control {
  margin-bottom: 40px;
  text-align: center;
}

.control-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
}

.synthesis-btn {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(103, 194, 58, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.synthesis-status {
  margin-top: 24px;
}

.status-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 20px;
  border-radius: 12px;
  border: 1px solid;
  background: white;

  &.status-running {
    border-color: #409eff;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(64, 158, 255, 0.08) 100%);
  }

  &.status-success {
    border-color: #67c23a;
    background: linear-gradient(135deg, rgba(103, 194, 58, 0.05) 0%, rgba(103, 194, 58, 0.1) 100%);
  }

  &.status-error {
    border-color: #f56c6c;
    background: linear-gradient(135deg, rgba(245, 108, 108, 0.05) 0%, rgba(245, 108, 108, 0.1) 100%);
  }

  &.status-info {
    border-color: #409eff;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(64, 158, 255, 0.1) 100%);
  }
}

.status-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 2px;

  .status-running & {
    color: #409eff;
  }

  .status-success & {
    color: #67c23a;
  }

  .status-error & {
    color: #f56c6c;
  }

  .status-info & {
    color: #409eff;
  }
}

.status-content {
  flex: 1;
}

.status-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 6px 0;
}

.status-message {
  font-size: 13px;
  color: #666;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.status-progress {
  margin-top: 12px;
}

.debug-info {
  margin-top: 10px;
}
</style>
