<template>
  <div class="synthesis-control">
    <div class="control-buttons">
      <el-button
        type="primary"
        size="large"
        @click="handleStart"
        :loading="synthesisStatus.isRunning"
        :disabled="!canStart"
        class="synthesis-btn"
      >
        <el-icon><VideoPlay /></el-icon>
        {{ getSynthesisButtonText() }}
      </el-button>

      <!-- 调试信息和备用按钮 -->
      <div v-if="!canStart && isDevelopment" class="debug-info">
        <el-alert
          title="按钮不可用原因"
          type="warning"
          :closable="false"
          style="margin-top: 10px;"
        >
          <template #default>
            <div style="font-size: 12px;">
              <p>版本选择: {{ version ? '✓' : '✗' }}</p>
              <p>数字人数量: {{ digitalHumansCount }}/2 {{ digitalHumansCount >= 2 ? '✓' : '✗' }}</p>
              <p>对话内容: {{ dialogueCount }} {{ dialogueCount > 0 ? '✓' : '✗' }}</p>
              <p>AI模型: {{ version !== 'V' || model ? '✓' : '✗' }}</p>
              <p>运行状态: {{ !synthesisStatus.isRunning ? '✓' : '✗' }}</p>
              <p>视频标题: "{{ videoTitle }}" {{ videoTitle.trim() ? '✓' : '✗' }}</p>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 备用强制开始按钮（仅开发环境） -->
      <el-button
        v-if="isDevelopment"
        type="danger"
        size="small"
        @click="handleForceStart"
        :loading="synthesisStatus.isRunning"
        style="margin-top: 10px;"
      >
        强制开始（调试）
      </el-button>
    </div>

    <!-- 合成状态显示 -->
    <div v-if="synthesisStatus.message" class="synthesis-status">
      <div class="status-card" :class="getStatusClass()">
        <div class="status-icon">
          <el-icon v-if="synthesisStatus.isRunning"><Loading /></el-icon>
          <el-icon v-else-if="synthesisStatus.isCompleted"><CircleCheck /></el-icon>
          <el-icon v-else-if="synthesisStatus.hasError"><CircleClose /></el-icon>
          <el-icon v-else><InfoFilled /></el-icon>
        </div>
        <div class="status-content">
          <h5 class="status-title">{{ synthesisStatus.title }}</h5>
          <p class="status-message">{{ synthesisStatus.message }}</p>
          <div v-if="synthesisStatus.progress" class="status-progress">
            <el-progress 
              :percentage="synthesisStatus.progress" 
              :status="synthesisStatus.isCompleted ? 'success' : (synthesisStatus.hasError ? 'exception' : null)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { VideoPlay, InfoFilled, Loading, CircleCheck, CircleClose } from '@element-plus/icons-vue'

const props = defineProps({
  synthesisStatus: {
    type: Object,
    required: true
  },
  version: {
    type: String,
    default: ''
  },
  model: {
    type: String,
    default: ''
  },
  digitalHumansCount: {
    type: Number,
    default: 0
  },
  dialogueCount: {
    type: Number,
    default: 0
  },
  videoTitle: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['start-synthesis', 'force-start'])

// 安全检查开发环境
const isDevelopment = computed(() => {
  try {
    return typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development'
  } catch (e) {
    return false
  }
})

const canStart = computed(() => {
  const hasVersion = !!props.version
  const hasEnoughHumans = props.digitalHumansCount >= 2
  const hasDialogue = props.dialogueCount > 0
  const hasModel = props.version !== 'V' || !!props.model
  const notRunning = !props.synthesisStatus.isRunning
  const hasTitle = !!props.videoTitle.trim()
  
  return hasVersion && hasEnoughHumans && hasDialogue && hasModel && notRunning && hasTitle
})

const getSynthesisButtonText = () => {
  if (props.synthesisStatus.isRunning) {
    return '合成中...'
  } else if (props.synthesisStatus.isCompleted) {
    return '重新合成'
  } else {
    return '开始合成'
  }
}

const getStatusClass = () => {
  if (props.synthesisStatus.isCompleted) return 'status-success'
  if (props.synthesisStatus.hasError) return 'status-error'
  if (props.synthesisStatus.isRunning) return 'status-running'
  return 'status-info'
}

const handleStart = () => {
  emit('start-synthesis')
}

const handleForceStart = () => {
  emit('force-start')
}
</script>

<style scoped>
.synthesis-control {
  margin-top: 32px;
}

.control-buttons {
  text-align: center;
  margin-bottom: 24px;
}

.synthesis-btn {
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(103, 194, 58, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.synthesis-status {
  margin-top: 24px;
}

.status-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 20px;
  border-radius: 12px;
  border: 1px solid;
  background: white;

  &.status-running {
    border-color: #409eff;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(64, 158, 255, 0.08) 100%);
  }

  &.status-success {
    border-color: #67c23a;
    background: linear-gradient(135deg, rgba(103, 194, 58, 0.05) 0%, rgba(103, 194, 58, 0.1) 100%);
  }

  &.status-error {
    border-color: #f56c6c;
    background: linear-gradient(135deg, rgba(245, 108, 108, 0.05) 0%, rgba(245, 108, 108, 0.1) 100%);
  }

  &.status-info {
    border-color: #409eff;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(64, 158, 255, 0.1) 100%);
  }
}

.status-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 2px;

  .status-running & {
    color: #409eff;
  }

  .status-success & {
    color: #67c23a;
  }

  .status-error & {
    color: #f56c6c;
  }

  .status-info & {
    color: #409eff;
  }
}

.status-content {
  flex: 1;
}

.status-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 6px 0;
}

.status-message {
  font-size: 13px;
  color: #666;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.status-progress {
  margin-top: 12px;
}

.debug-info {
  margin-top: 10px;
}
</style>
