<script setup>
import { computed } from 'vue';
import { stringToHSLColor } from '@/utils/geek.ts';

const props = defineProps({
  // 文本种子，用于生成图案
  text: {
    type: String,
    required: true
  },
  // 尺寸设置
  size: {
    type: [Number, String],
    default: 48
  },
  // 是否自动生成背景色
  autoBackground: {
    type: Boolean,
    default: true
  },
  // 自定义背景色
  backgroundColor: {
    type: String,
    default: ''
  },
  // 边框半径
  borderRadius: {
    type: [Number, String],
    default: 14
  },
  // 颜色配置（色彩模式）
  colorMode: {
    type: String,
    default: 'pastel' // pastel, vibrant, soft, cool, warm
  }
});

// 颜色配置预设
const colorPresets = {
  // 淡色调模式
  pastel: {
    saturation: 60,      // 主体饱和度 - 适中的饱和度使颜色柔和
    lightness: 80,       // 主体亮度 - 较高的亮度营造柔和感
    alpha: 0.9,          // 不透明度 - 轻微透明增加层次感
    elementSaturation: 70, // 元素饱和度 - 略高于主体使图案突出
    elementLightness: 50,  // 元素亮度 - 中等亮度确保可见性
    contrastMode: 'complementary' // 对比模式 - 使用互补色增加和谐对比
  },
  // 鲜艳色调模式
  vibrant: {
    saturation: 75,
    lightness: 75,
    alpha: 0.95,
    elementSaturation: 80,
    elementLightness: 55,
    contrastMode: 'triadic'
  },
  // 柔和色调模式
  soft: {
    saturation: 45,
    lightness: 85,
    alpha: 0.9,
    elementSaturation: 60,
    elementLightness: 60,
    contrastMode: 'analogous'
  },
  // 冷色调模式
  cool: {
    hueShift: 210,
    saturation: 65,
    lightness: 80,
    alpha: 0.95,
    elementSaturation: 70,
    elementLightness: 55,
    contrastMode: 'complementary'
  },
  // 暖色调模式
  warm: {
    hueShift: 30,
    saturation: 65,
    lightness: 78,
    alpha: 0.95,
    elementSaturation: 75,
    elementLightness: 55,
    contrastMode: 'complementary'
  }
};

// 获取当前颜色预设
const currentColorPreset = computed(() => {
  return colorPresets[props.colorMode] || colorPresets.pastel;
});

// 为文本种子生成颜色
const bgColor = computed(() => {
  if (!props.autoBackground || props.backgroundColor) {
    return props.backgroundColor;
  }
  
  const preset = currentColorPreset.value;
  
  try {
    const generatedColor = stringToHSLColor(props.text || '', {
      hueShift: preset.hueShift,
      saturation: preset.saturation,
      lightness: preset.lightness,
      alpha: preset.alpha
    });
    
    return generatedColor;
  } catch (error) {
    console.error('生成背景色时发生错误:', error);
    return 'hsla(210, 30%, 85%, 0.9)';
  }
});

// 获取对比色调
const getContrastHue = (baseHue, mode, index = 0) => {
  switch (mode) {
    case 'complementary': // 互补色
      return (baseHue + 180) % 360;
    case 'triadic': // 三分色
      return (baseHue + 120 * (index % 3)) % 360;
    case 'analogous': // 类似色
      return (baseHue + 30 * ((index % 5) - 2)) % 360;
    case 'monochromatic': // 单色调
      return baseHue;
    default:
      return (baseHue + 180) % 360;
  }
};

// 生成项目图标的几何图案
const generatePatternData = (text) => {
  if (!text) return null;
  
  // 将文本转换为数字种子
  const seed = Array.from(text.toString()).reduce((acc, char, i) => {
    return acc + char.charCodeAt(0) * (i + 1);
  }, 0);
  
  // 生成随机但确定性的值
  const seeder = (index) => ((seed * (index + 1)) % 100) / 100;
  
  // 确定设计风格 (0-2)
  const designStyle = seed % 3;
  
  // 获取颜色配置
  const preset = currentColorPreset.value;
  
  // 从字符串获取基础色调
  let baseHue = (seed % 360);
  if (preset.hueShift) {
    baseHue = (baseHue + preset.hueShift) % 360;
  }
  
  // 配置和数据
  const patterns = [];
  
  // 根据不同设计风格生成图案
  switch (designStyle) {
    case 0: // 流体波浪
      {
        // 波浪形状
        const waveCount = 2 + (seed % 3);
        for (let i = 0; i < waveCount; i++) {
          const amplitude = 15 + seeder(i + 10) * 20;
          const frequency = 0.5 + seeder(i + 11) * 1.5;
          const phase = seeder(i + 12) * Math.PI * 2;
          const offsetY = 20 + (i * 20) + seeder(i + 13) * 20;
          
          // 使用对比色模式获取波浪颜色
          const waveHue = getContrastHue(baseHue, preset.contrastMode, i);
          const waveOpacity = 0.4 + seeder(i + 14) * 0.2;
          
          patterns.push({
            type: 'wave',
            amplitude,
            frequency,
            phase,
            offsetY,
            opacity: waveOpacity,
            fill: `hsla(${waveHue}, 85%, 85%, 0.9)`
          });
        }
        
        // 叠加装饰圆形
        const circleCount = 2 + (seed % 3);
        for (let i = 0; i < circleCount; i++) {
          const circleHue = getContrastHue(baseHue, preset.contrastMode, i + waveCount);
          patterns.push({
            type: 'circle',
            cx: 15 + seeder(i + 20) * 70,
            cy: 15 + seeder(i + 21) * 70,
            r: 5 + seeder(i + 22) * 15,
            fill: `hsla(${circleHue}, 90%, 90%, ${0.5 + seeder(i + 23) * 0.3})`
          });
        }
      }
      break;
      
    case 1: // 简约几何设计
      {
        // 元素配色 - 使用对比色系统
        const elementHue = getContrastHue(baseHue, preset.contrastMode, 0);
        const accentHue = getContrastHue(baseHue, preset.contrastMode, 1);
        
        // 主元素颜色
        const mainFill = `hsla(${elementHue}, ${preset.elementSaturation}%, ${preset.elementLightness}%, 0.9)`;
        const mainStroke = `hsla(${elementHue}, ${preset.elementSaturation + 10}%, ${preset.elementLightness - 10}%, 0.85)`;
        
        // 装饰元素颜色
        const accentFill = `hsla(${accentHue}, ${preset.elementSaturation}%, ${preset.elementLightness + 10}%, 0.9)`;
        
        // 中心几何图形
        const centerX = 50;
        const centerY = 50;
        
        // 决定是三角形、方形、六边形或圆形
        const shapeType = seed % 4;
        
        if (shapeType === 0) { // 三角形
          const size = 35 + seeder(20) * 15;
          const rotation = seeder(21) * 360;
          
          // 等边三角形
          const h = size * Math.sqrt(3) / 2;
          const trianglePath = `
            M ${centerX}, ${centerY - h/2}
            L ${centerX + size/2}, ${centerY + h/2}
            L ${centerX - size/2}, ${centerY + h/2}
            Z
          `;
          
          patterns.push({
            type: 'path',
            d: trianglePath,
            fill: mainFill,
            stroke: mainStroke,
            strokeWidth: 1.5,
            transform: `rotate(${rotation} ${centerX} ${centerY})`
          });
          
          // 添加一个小三角形作为装饰
          const smallSize = size * 0.5;
          const smallH = smallSize * Math.sqrt(3) / 2;
          const offset = size * 0.6;
          const offsetX = centerX + Math.cos(rotation * Math.PI / 180) * offset;
          const offsetY = centerY + Math.sin(rotation * Math.PI / 180) * offset;
          
          const smallTrianglePath = `
            M ${offsetX}, ${offsetY - smallH/2}
            L ${offsetX + smallSize/2}, ${offsetY + smallH/2}
            L ${offsetX - smallSize/2}, ${offsetY + smallH/2}
            Z
          `;
          
          patterns.push({
            type: 'path',
            d: smallTrianglePath,
            fill: accentFill,
            transform: `rotate(${rotation + 180} ${offsetX} ${offsetY})`
          });
        }
        else if (shapeType === 1) { // 方形
          const size = 30 + seeder(22) * 20;
          const rotation = seeder(23) * 45; // 最多旋转45度，保持方形感
          
          // 正方形
          patterns.push({
            type: 'rect',
            x: centerX - size/2,
            y: centerY - size/2,
            width: size,
            height: size,
            fill: mainFill,
            stroke: mainStroke,
            strokeWidth: 1.5,
            transform: `rotate(${rotation} ${centerX} ${centerY})`
          });
          
          // 内部小方形作为装饰
          const innerSize = size * 0.5;
          patterns.push({
            type: 'rect',
            x: centerX - innerSize/2,
            y: centerY - innerSize/2,
            width: innerSize,
            height: innerSize,
            fill: accentFill,
            transform: `rotate(${-rotation} ${centerX} ${centerY})`
          });
        }
        else if (shapeType === 2) { // 六边形
          const size = 30 + seeder(24) * 15;
          const hexPoints = [];
          
          // 创建六边形的顶点
          for (let i = 0; i < 6; i++) {
            const angle = (i * 60) * Math.PI / 180;
            const x = centerX + size * Math.cos(angle);
            const y = centerY + size * Math.sin(angle);
            hexPoints.push(`${x},${y}`);
          }
          
          patterns.push({
            type: 'polygon',
            points: hexPoints.join(' '),
            fill: mainFill,
            stroke: mainStroke,
            strokeWidth: 1.5
          });
          
          // 内部小六边形
          const innerSize = size * 0.5;
          const innerHexPoints = [];
          
          for (let i = 0; i < 6; i++) {
            const angle = ((i * 60) + 30) * Math.PI / 180;
            const x = centerX + innerSize * Math.cos(angle);
            const y = centerY + innerSize * Math.sin(angle);
            innerHexPoints.push(`${x},${y}`);
          }
          
          patterns.push({
            type: 'polygon',
            points: innerHexPoints.join(' '),
            fill: accentFill
          });
        }
        else { // 圆形
          // 主圆
          patterns.push({
            type: 'circle',
            cx: centerX,
            cy: centerY,
            r: 30 + seeder(25) * 10,
            fill: mainFill,
            stroke: mainStroke,
            strokeWidth: 1.5
          });
          
          // 装饰小圆
          const circleCount = 2 + (seed % 2);
          for (let i = 0; i < circleCount; i++) {
            const angle = seeder(26 + i) * Math.PI * 2;
            const distance = 20 + seeder(27 + i) * 15;
            
            patterns.push({
              type: 'circle',
              cx: centerX + Math.cos(angle) * distance,
              cy: centerY + Math.sin(angle) * distance,
              r: 5 + seeder(28 + i) * 5,
              fill: accentFill
            });
          }
        }
        
        // 线条颜色 - 使用中性色调
        const lineHue = getContrastHue(baseHue, 'analogous', 2);
        const lineColor = `hsla(${lineHue}, ${preset.elementSaturation - 10}%, ${preset.elementLightness + 20}%, 0.7)`;
        
        // 简单装饰线
        const lineCount = 2 + (seed % 2);
        for (let i = 0; i < lineCount; i++) {
          const startAngle = seeder(30 + i) * Math.PI * 2;
          const endAngle = (startAngle + Math.PI * (0.5 + seeder(31 + i) * 0.5)) % (Math.PI * 2);
          
          const startDist = 40 + seeder(32 + i) * 10;
          const endDist = 40 + seeder(33 + i) * 10;
          
          const x1 = centerX + Math.cos(startAngle) * startDist;
          const y1 = centerY + Math.sin(startAngle) * startDist;
          const x2 = centerX + Math.cos(endAngle) * endDist;
          const y2 = centerY + Math.sin(endAngle) * endDist;
          
          patterns.push({
            type: 'line',
            x1, y1, x2, y2,
            stroke: lineColor,
            strokeWidth: 1.5 + seeder(34 + i),
            strokeDasharray: seed % 2 === 0 ? `${2 + seeder(35 + i) * 2} ${2 + seeder(36 + i)}` : null
          });
        }
      }
      break;
      
    case 2: // 简约花瓣设计
      {
        // 元素配色 - 使用对比色模式
        const petalBaseHue = getContrastHue(baseHue, preset.contrastMode, 0);
        
        // 主元素颜色
        const centerFill = `hsla(${getContrastHue(petalBaseHue, 'analogous', 1)}, ${preset.elementSaturation}%, ${preset.elementLightness}%, 0.9)`;
        const centerStroke = `hsla(${getContrastHue(petalBaseHue, 'analogous', 2)}, ${preset.elementSaturation - 5}%, ${preset.elementLightness - 10}%, 0.7)`;
        
        // 中心点
        const centerX = 50;
        const centerY = 50;
        
        // 形状类型
        const designVariant = seed % 4;
        
        if (designVariant === 0) { // 简化花瓣
          // 简化花瓣参数
          const petalCount = 5 + (seed % 3);
          const radius = 35;
          
          // 中心圆
          patterns.push({
            type: 'circle',
            cx: centerX,
            cy: centerY,
            r: 12,
            fill: centerFill,
            stroke: centerStroke,
            strokeWidth: 1.5
          });
          
          // 简化花瓣 - 只用椭圆形
          for (let i = 0; i < petalCount; i++) {
            const angle = (i / petalCount) * Math.PI * 2;
            const petalX = centerX + Math.cos(angle) * radius * 0.6;
            const petalY = centerY + Math.sin(angle) * radius * 0.6;
            
            // 花瓣颜色 - 使用对比色模式的色谱
            const petalHue = getContrastHue(petalBaseHue, 'analogous', i);
            const petalFill = `hsla(${petalHue}, ${preset.elementSaturation}%, ${preset.elementLightness + 10}%, 0.9)`;
            
            // 椭圆花瓣
            patterns.push({
              type: 'ellipse',
              cx: petalX,
              cy: petalY,
              rx: radius * 0.4,
              ry: radius * 0.25,
              fill: petalFill,
              transform: `rotate(${angle * 180 / Math.PI} ${petalX} ${petalY})`
            });
          }
        }
        else if (designVariant === 1) { // 简约星形
          const pointCount = 5 + (seed % 3);
          const outerRadius = 40;
          const innerRadius = outerRadius * 0.4;
          
          // 星形颜色
          const starFill = `hsla(${petalBaseHue}, ${preset.elementSaturation}%, ${preset.elementLightness}%, 0.9)`;
          const starStroke = `hsla(${getContrastHue(petalBaseHue, 'analogous', 1)}, ${preset.elementSaturation - 5}%, ${preset.elementLightness - 10}%, 0.8)`;
          
          // 中心圆颜色
          const centerStarFill = `hsla(${getContrastHue(petalBaseHue, 'analogous', 2)}, ${preset.elementSaturation}%, ${preset.elementLightness + 10}%, 0.9)`;
          
          let starPoints = '';
          for (let i = 0; i < pointCount * 2; i++) {
            const angle = (i * Math.PI) / pointCount;
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);
            starPoints += `${x},${y} `;
          }
          
          patterns.push({
            type: 'polygon',
            points: starPoints,
            fill: starFill,
            stroke: starStroke,
            strokeWidth: 1.5
          });
          
          // 中心圆
          patterns.push({
            type: 'circle',
            cx: centerX,
            cy: centerY,
            r: innerRadius * 0.8,
            fill: centerStarFill
          });
        }
        else if (designVariant === 2) { // 简约波浪圆
          const waveRadius = 35;
          const waveCount = 8 + (seed % 5);
          const amplitude = 5 + seeder(40) * 5;
          
          // 波浪圆颜色
          const waveFill = `hsla(${petalBaseHue}, ${preset.elementSaturation}%, ${preset.elementLightness}%, 0.9)`;
          const waveStroke = `hsla(${getContrastHue(petalBaseHue, 'analogous', 1)}, ${preset.elementSaturation - 5}%, ${preset.elementLightness - 10}%, 0.8)`;
          
          // 中心圆颜色
          const centerWaveFill = `hsla(${getContrastHue(petalBaseHue, 'analogous', 2)}, ${preset.elementSaturation}%, ${preset.elementLightness + 10}%, 0.9)`;
          
          let wavePath = `M `;
          
          for (let i = 0; i <= waveCount; i++) {
            const angle = (i / waveCount) * Math.PI * 2;
            const radius = waveRadius + (i % 2 === 0 ? amplitude : -amplitude);
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);
            
            if (i === 0) {
              wavePath += `${x},${y} `;
            } else {
              const prevAngle = ((i - 1) / waveCount) * Math.PI * 2;
              const ctrlAngle = (prevAngle + angle) / 2;
              const ctrlRadius = waveRadius + amplitude * 0.8;
              const ctrlX = centerX + ctrlRadius * Math.cos(ctrlAngle);
              const ctrlY = centerY + ctrlRadius * Math.sin(ctrlAngle);
              
              wavePath += `Q ${ctrlX},${ctrlY} ${x},${y} `;
            }
          }
          
          // 闭合路径
          wavePath += 'Z';
          
          patterns.push({
            type: 'path',
            d: wavePath,
            fill: waveFill,
            stroke: waveStroke,
            strokeWidth: 1.5
          });
          
          // 中心圆
          patterns.push({
            type: 'circle',
            cx: centerX,
            cy: centerY,
            r: waveRadius * 0.4,
            fill: centerWaveFill
          });
        }
        else { // 简约螺旋
          const spiralTurns = 1.5 + (seed % 2);
          const maxRadius = 40;
          
          // 螺旋颜色
          const spiralStroke = `hsla(${petalBaseHue}, ${preset.elementSaturation}%, ${preset.elementLightness}%, 0.9)`;
          
          // 中心圆颜色
          const centerSpiralFill = `hsla(${getContrastHue(petalBaseHue, 'analogous', 2)}, ${preset.elementSaturation}%, ${preset.elementLightness + 10}%, 0.9)`;
          
          // 装饰点颜色
          const dotFill = `hsla(${getContrastHue(petalBaseHue, 'analogous', 1)}, ${preset.elementSaturation}%, ${preset.elementLightness + 5}%, 0.9)`;
          
          let spiralPath = `M ${centerX},${centerY} `;
          
          // 创建螺旋
          const points = 60;
          for (let i = 1; i <= points; i++) {
            const t = i / points;
            const angle = t * Math.PI * 2 * spiralTurns;
            const radius = t * maxRadius;
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);
            
            spiralPath += `L ${x},${y} `;
          }
          
          patterns.push({
            type: 'path',
            d: spiralPath,
            fill: 'none',
            stroke: spiralStroke,
            strokeWidth: 3 + seeder(45) * 2,
            strokeLinecap: 'round'
          });
          
          // 中心圆
          patterns.push({
            type: 'circle',
            cx: centerX,
            cy: centerY,
            r: 8,
            fill: centerSpiralFill
          });
          
          // 装饰小圆点
          const dotCount = 3 + (seed % 3);
          for (let i = 0; i < dotCount; i++) {
            const t = 0.3 + (i * 0.6 / dotCount);
            const angle = t * Math.PI * 2 * spiralTurns;
            const radius = t * maxRadius;
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);
            
            patterns.push({
              type: 'circle',
              cx: x,
              cy: y,
              r: 3 + seeder(50 + i) * 2,
              fill: dotFill
            });
          }
        }
      }
      break;
  }
  
  return { designStyle, patterns };
}

// 计算项目图标的SVG内容
const patternSvg = computed(() => {
  const data = generatePatternData(props.text);
  if (!data) return '';
  
  const { designStyle, patterns } = data;

  let svg = '';
  svg += '<defs>';
  svg += `
      <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur in="SourceGraphic" stdDeviation="3" />
      </filter>
      <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur in="SourceGraphic" stdDeviation="2" result="blur" />
        <feComposite in="SourceGraphic" in2="blur" operator="over" />
      </filter>
  `;
  
  // 处理渐变定义
  patterns.filter(p => p.type === 'gradient').forEach(gradient => {
    if (gradient.gradientType === 'radial') {
      svg += `
        <radialGradient id="${gradient.id}" cx="${gradient.cx}" cy="${gradient.cy}" r="${gradient.r}" fx="${gradient.fx}" fy="${gradient.fy}">
          ${gradient.stops.map(stop => `<stop offset="${stop.offset}" stop-color="${stop.color}" />`).join('')}
        </radialGradient>
      `;
    } else {
      svg += `
        <linearGradient id="${gradient.id}" x1="0%" y1="0%" x2="100%" y2="0%">
          ${gradient.stops.map(stop => `<stop offset="${stop.offset}" stop-color="${stop.color}" />`).join('')}
        </linearGradient>
      `;
    }
  });

  svg += '</defs>';
  
  // 不再渲染SVG背景矩形，改为仅渲染图案元素
  // 可选：添加一个半透明的背景覆盖
  /* svg += `<rect x="0" y="0" width="100" height="100" fill="rgba(255,255,255,0.2)" />`; */
  
  // 渲染其他图案
  patterns.forEach(pattern => {
    if (pattern.type === 'background' || pattern.type === 'gradient') return; // 背景和渐变已经处理过
    
    switch (pattern.type) {
      case 'wave':
        // 生成波浪路径
        let wavePath = `M 0,${pattern.offsetY} `;
        for (let x = 0; x <= 100; x += 5) {
          const y = pattern.offsetY + pattern.amplitude * Math.sin(x * pattern.frequency / 100 + pattern.phase);
          wavePath += `L ${x},${y} `;
        }
        wavePath += 'L 100,100 L 0,100 Z';
        
        svg += `<path d="${wavePath}" fill="${pattern.fill}" opacity="${pattern.opacity}" />`;
        break;
        
      case 'circle':
        svg += `<circle cx="${pattern.cx}" cy="${pattern.cy}" r="${pattern.r}" fill="${pattern.fill || 'none'}" 
                ${pattern.stroke ? `stroke="${pattern.stroke}"` : ''} 
                ${pattern.strokeWidth ? `stroke-width="${pattern.strokeWidth}"` : ''}
                ${pattern.strokeDasharray ? `stroke-dasharray="${pattern.strokeDasharray}"` : ''}
                ${pattern.filter ? `filter="${pattern.filter}"` : ''} />`;
        break;
        
      case 'rounded-rect':
        svg += `<rect x="${pattern.x}" y="${pattern.y}" width="${pattern.width}" height="${pattern.height}" 
                rx="${pattern.rx}" ry="${pattern.rx}" fill="${pattern.fill}" />`;
        break;
        
      case 'rect':
        svg += `<rect x="${pattern.x}" y="${pattern.y}" width="${pattern.width}" height="${pattern.height}" 
                fill="${pattern.fill}" />`;
        break;
        
      case 'line':
        svg += `<line x1="${pattern.x1}" y1="${pattern.y1}" x2="${pattern.x2}" y2="${pattern.y2}" 
                stroke="${pattern.stroke}" stroke-width="${pattern.strokeWidth}"
                ${pattern.strokeDasharray ? `stroke-dasharray="${pattern.strokeDasharray}"` : ''} />`;
        break;
        
      case 'path':
        svg += `<path d="${pattern.d}" fill="${pattern.fill || 'none'}" 
                ${pattern.stroke ? `stroke="${pattern.stroke}"` : ''} 
                ${pattern.strokeWidth ? `stroke-width="${pattern.strokeWidth}"` : ''}
                ${pattern.strokeDasharray ? `stroke-dasharray="${pattern.strokeDasharray}"` : ''}
                ${pattern.filter ? `filter="${pattern.filter}"` : ''} />`;
        break;
        
      case 'custom-shape':
      case 'blob':
      case 'petal':
      case 'natural-petal':
        svg += `<path d="${pattern.d}" fill="${pattern.fill}" 
                ${pattern.stroke ? `stroke="${pattern.stroke}"` : ''} 
                ${pattern.strokeWidth ? `stroke-width="${pattern.strokeWidth}"` : ''}
                ${pattern.filter ? `filter="${pattern.filter}"` : ''} />`;
        break;
        
      case 'polygon':
        svg += `<polygon points="${pattern.points}" fill="${pattern.fill || 'none'}" 
                ${pattern.stroke ? `stroke="${pattern.stroke}"` : ''} 
                ${pattern.strokeWidth ? `stroke-width="${pattern.strokeWidth}"` : ''}
                ${pattern.transform ? `transform="${pattern.transform}"` : ''} />`;
        break;
        
      case 'ellipse':
        svg += `<ellipse cx="${pattern.cx}" cy="${pattern.cy}" rx="${pattern.rx}" ry="${pattern.ry}" 
                fill="${pattern.fill || 'none'}" 
                ${pattern.stroke ? `stroke="${pattern.stroke}"` : ''} 
                ${pattern.strokeWidth ? `stroke-width="${pattern.strokeWidth}"` : ''}
                ${pattern.transform ? `transform="${pattern.transform}"` : ''} />`;
        break;
    }
  });
  
  return svg;
});

const firstChar = computed(() => ('' + props.text).trim().slice(0, 1));
</script>

<template>
  <div 
    class="geometric-avatar" 
    :style="{
      width: typeof size === 'number' ? `${size}px` : size,
      height: typeof size === 'number' ? `${size}px` : size,
      backgroundColor: bgColor,
      borderRadius: typeof borderRadius === 'number' ? `${borderRadius}px` : borderRadius
    }"
    :data-bg-color="bgColor"
  >
    <svg width="100%" height="100%" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" v-html="patternSvg"></svg>
    
    <!-- 首字符 备选 -->
    <span v-if="!patternSvg" class="fallback-text">{{ firstChar }}</span>
    
    <!-- 背景渐变效果 -->
    <div class="avatar-bg"></div>
  </div>
</template>

<style lang="scss" scoped>
.geometric-avatar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: rgba(240, 240, 240, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05), inset 0 0 0 1px rgba(0, 0, 0, 0.08);
  
  svg {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
    width: 100%;
    height: 100%;
    
    &:hover {
      filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.15));
    }
  }
  
  .fallback-text {
    font-size: 45%;
    font-weight: 600;
    color: var(--el-text-color-primary);
    position: relative;
    z-index: 2;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
  }
  
  .avatar-bg {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
    background-image: radial-gradient(
      circle at 70% 30%,
      rgba(255, 255, 255, 0.4) 0%,
      rgba(255, 255, 255, 0) 60%
    );
  }
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08), inset 0 0 0 1px rgba(0, 0, 0, 0.1);
    
    .avatar-bg {
      opacity: 1;
    }
    
    svg {
      filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
    }
  }
}
</style>