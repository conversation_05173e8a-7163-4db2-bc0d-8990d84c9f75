<script setup name="Project">
import { delProject, getProject } from "@/api/platform/project";
import projectCard from "./components/project-card.vue";
import LiveCard from './components/live-card.vue';
import LiveStreamCard from './components/LiveStreamCard.vue';
import useLiveStore from '@/store/modules/live'
import { PlatformProject, projectService } from "@/types/platform/project";
import { usePage } from "@/hook";
import cache from "@/plugins/cache";
import useTagsViewStore from "@/store/modules/tagsView";
import router from '@/router'
import { useRoute } from 'vue-router'
import { onMounted, onUnmounted, computed } from 'vue';
import { fetchRecentLives } from '@/utils/liveService';
import useUserStore from '@/store/modules/user';

const route = useRoute()
const userStore = useUserStore()
const liveStore = useLiveStore()
const { proxy } = getCurrentInstance();
const page = usePage(PlatformProject)
const { queryParams, list, total, loading, form } = page;

queryParams.value.pageSize = 8

// 视图状态
const activeTab = ref('all')
const showLiveDrawer = ref(false)
const open = ref(false)
const title = ref("")
const createTimeSort = ref(0)
const popupPosition = ref({ x: 0, y: 0, position: 'right' })
const activeProjectCard = ref(null)

// 最近直播数据
const recentCreatedLives = ref([])
const recentStartedLives = ref([])
const recentLoading = ref(false)

// 计算最近直播总数
const totalRecentLives = computed(() => {
  return recentCreatedLives.value.length + recentStartedLives.value.length
})

// 获取最近直播数据
async function fetchRecentLivesData() {
  try {
    recentLoading.value = true
    const { recentCreatedLives: created, recentStartedLives: started } = await fetchRecentLives(userStore.name)
    recentCreatedLives.value = created
    recentStartedLives.value = started
  } catch (error) {
    console.error('获取最近直播失败', error)
  } finally {
    recentLoading.value = false
  }
}

// 初始化
createTimeSort.value = cache.local.get('createTimeSort') || 0
queryParams.value.params.createTimeSort = createTimeSort.value
page.handleQuery()

// 标签页选项
const tabs = [
  { key: 'all', label: '全部项目' },
  { key: 'recent', label: '最近直播' }
]

// 监听排序变化
watch(createTimeSort, (newSort) => {
  cache.local.set('createTimeSort', newSort)
  queryParams.value.params.createTimeSort = newSort
  page.handleQuery()
});

// 排序切换
function handleSort() {
  createTimeSort.value = createTimeSort.value == 0 ? 1 : 0;
}

// 取消按钮
function cancel() {
  open.value = false;
  page.resetForm()
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  page.handleQuery()
}

/** 重置按钮操作 */
function resetQuery() {
  page.resetQuery(false)
  queryParams.value.params.createTimeSort = createTimeSort.value
}

/** 新增按钮操作 */
function handleAdd() {
  page.resetForm()
  open.value = true;
  title.value = "新建项目";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  page.resetForm()
  const _projectId = row.projectId
  getProject(_projectId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "编辑项目";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["projectRef"].validate(valid => {
    if (valid) {
      if (form.value.projectId != null) {
        page.handleUpdate().then(() => {
          proxy.$modal.msgSuccess("编辑成功");
          open.value = false;
          page.handleQuery()
        });
      } else {
        page.handleAdd().then(() => {
          proxy.$modal.msgSuccess("加入成功");
          open.value = false;
          page.handleQuery()
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除项目名称为"' + form.value.projectTitle + '"的数据项？，该操作会删掉该项目下的所有信息').then(function () {
    return delProject(form.value.projectId);
  }).then((res) => {
    if (res.code == 200) {
      page.handleQuery()
      const titles = ['material', 'live', 'broadcast'];
      titles.forEach(title => {
        const obj = router.resolve({
          path: `/project/${form.value.projectId}/${title}`,
          query: { projectTitle: form.value.projectTitle }
        });
        useTagsViewStore().delView(obj);
      });
      proxy.$modal.msgSuccess("删除成功");
      open.value = false;
    }
  }).catch(() => { });
}

// 监听标签页切换
watch(activeTab, (newTab) => {
  if (newTab === 'recent') {
    fetchRecentLivesData()
  }
})

onMounted(() => {
  liveStore.projectId = null
  liveStore.projectTitle = null
  window.addEventListener('keydown', handleKeyDown)
  fetchRecentLivesData()
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown)
})

// 处理键盘事件
const handleKeyDown = (e) => {
  if (e.key === 'Escape' && showLiveDrawer.value) {
    closeLivePopup()
  }
}

// 处理项目点击
const handleProjectClick = (projectId, projectTitle, event) => {
  if (event) {
    event.stopPropagation()
  }

  // 如果已经显示,且点击的是同一个项目,则关闭弹出层
  if (showLiveDrawer.value && liveStore.projectId === projectId) {
    closeLivePopup()
    return
  }

  // 被点击的卡片元素
  const card = event?.target?.closest('.project-card')
  if (card) {
    const cardRect = card.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 弹出层尺寸
    const popupWidth = 280
    const popupHeight = 450 // 固定弹出层高度
    const gap = 20

    // 计算水平位置
    let x = 0

    // 判断右侧空间是否足够
    const rightSpace = viewportWidth - (cardRect.right + gap)
    const leftSpace = cardRect.left - gap

    if (rightSpace >= popupWidth) {
      // 右侧空间足够,在右侧显示
      x = cardRect.right + gap
    } else if (leftSpace >= popupWidth) {
      // 左侧空间足够,在左侧显示
      x = cardRect.left - gap - popupWidth
    } else {
      // 两侧空间都不够,居中显示
      x = Math.max(gap, (viewportWidth - popupWidth) / 2)
    }

    // 计算垂直位置
    let y = 0

    // 计算卡片中心点到视口顶部和底部的距离
    const cardCenterY = cardRect.top + cardRect.height / 2
    const spaceAbove = cardCenterY
    const spaceBelow = viewportHeight - cardCenterY

    // 根据上下空间决定弹出层位置
    if (spaceBelow >= spaceAbove) {
      // 下方空间更大，向下展示
      y = cardRect.top
      // 确保不会超出底部
      if (y + popupHeight + gap > viewportHeight) {
        y = viewportHeight - popupHeight - gap
      }
    } else {
      // 上方空间更大，向上展示
      y = cardRect.bottom - popupHeight
      // 确保不会超出顶部
      if (y < gap) {
        y = gap
      }
    }

    // 设置弹出位置和方向
    popupPosition.value = {
      x: x + window.scrollX,
      y: y + window.scrollY
    }

    activeProjectCard.value = card
  }

  // 先设置 store 中的项目信息
  liveStore.projectId = projectId
  liveStore.projectTitle = projectTitle
  // 再显示弹出层
  showLiveDrawer.value = true
}

// 关闭弹出层
const closeLivePopup = () => {
  showLiveDrawer.value = false
  activeProjectCard.value = null
  // 清除 store 中的项目信息
  liveStore.projectId = null
  liveStore.projectTitle = null
}

// 监听弹出层状态
watch(showLiveDrawer, (visible) => {
  if (visible) {
    document.body.classList.add('has-popup-blur')
  } else {
    document.body.classList.remove('has-popup-blur')
    activeProjectCard.value = null
  }
})

// 监听路由变化,关闭弹出层
watch(() => route.path, () => {
  if (showLiveDrawer.value) {
    closeLivePopup()
  }
})

</script>

<template>
  <div class="app-container" :class="{ 'has-popup': showLiveDrawer }" @click.self="closeLivePopup">
    <!-- 顶部统计区 -->
    <div class="stats-bar">
      <div class="stats-wrapper">
        <div v-for="tab in tabs" :key="tab.key" class="stat-item" :class="{ 'is-active': activeTab === tab.key }"
          @click="activeTab = tab.key">
          <div class="stat-icon">
            <el-icon>
              <component :is="tab.key === 'all' ? 'Grid' :
                'Timer'
                " />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">
              {{
                tab.key === 'all' ? total :
                  totalRecentLives
              }}
            </div>
            <div class="stat-label">{{ tab.label }}</div>
          </div>
          <div class="stat-indicator"></div>
        </div>
      </div>

      <div class="action-wrapper">
        <div class="search-bar">
          <el-input v-model="queryParams.projectTitle" placeholder="搜索项目..." clearable @keyup.enter="handleQuery">
            <template #prefix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>

        <div class="header-actions">
          <el-dropdown trigger="click">
            <el-button>
              <el-icon>
                <Sort />
              </el-icon>
              {{ createTimeSort == 0 ? '最早优先' : '最新优先' }}
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="createTimeSort = 0">最早优先</el-dropdown-item>
                <el-dropdown-item @click="createTimeSort = 1">最新优先</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button type="primary" @click="handleAdd">
            <el-icon>
              <Plus />
            </el-icon>创建项目
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content" @click.self="closeLivePopup">
      <!-- 项目区 -->
      <div class="projects-section" @click.self="closeLivePopup">
        <!-- 全部项目 -->
        <div v-if="activeTab === 'all'" class="projects-grid" v-loading="loading">
          <el-empty v-if="list.length === 0" description="暂无项目" />
          <template v-else>
            <projectCard v-for="item in list" :key="item.projectId" v-bind="item"
              :is-active="activeProjectCard && activeProjectCard.getAttribute('data-project-id') === String(item.projectId)"
              @update="handleUpdate(item)"
              @click-title="handleProjectClick(item.projectId, item.projectTitle, $event)" />
          </template>
        </div>

        <!-- 最近直播 -->
        <div v-else-if="activeTab === 'recent'" class="recent-lives-container" v-loading="recentLoading">
          <el-empty v-if="recentCreatedLives.length === 0 && recentStartedLives.length === 0" description="暂无最近直播" />
          <template v-else>
            <!-- 最近开播的直播 -->
            <template v-if="recentStartedLives.length > 0">
              <div class="section-title">最近开播的直播</div>
              <div class="live-streams-flex">
                <LiveStreamCard
                  v-for="item in recentStartedLives"
                  :key="item.liveId"
                  :live-name="item.liveName"
                  :project-id="item.projectId"
                  :project-title="item.projectTitle"
                  :live-id="item.liveId"
                  :create-time="item.createTime"
                  class="with-project-title"
                >
                  <template #project-title>
                    <div class="project-tag">
                      <el-icon><Folder /></el-icon>
                      {{ item.projectTitle }}
                    </div>
                  </template>
                </LiveStreamCard>
              </div>
            </template>

            <!-- 最近创建的直播 -->
            <template v-if="recentCreatedLives.length > 0">
              <div class="section-title">最近创建的直播</div>
              <div class="live-streams-flex">
                <LiveStreamCard
                  v-for="item in recentCreatedLives"
                  :key="item.liveId"
                  :live-name="item.liveName"
                  :project-id="item.projectId"
                  :project-title="item.projectTitle"
                  :live-id="item.liveId"
                  :create-time="item.createTime"
                  class="with-project-title"
                >
                  <template #project-title>
                    <div class="project-tag">
                      <el-icon><Folder /></el-icon>
                      {{ item.projectTitle }}
                    </div>
                  </template>
                </LiveStreamCard>
              </div>
            </template>
          </template>
        </div>

        <el-pagination v-if="activeTab === 'all' && list.length > 0" class="pagination" :total="total"
          v-model:current-page="queryParams.pageNum" v-model:page-size="queryParams.pageSize"
          :page-sizes="[8, 16, 24, 32]" layout="total, sizes, prev, pager, next, jumper" @size-change="page.handleQuery"
          @current-change="page.handleQuery" />
      </div>
    </div>

    <!-- 项目表单对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" :close-on-click-modal="false" destroy-on-close>
      <el-form ref="projectRef" :model="form" :rules="{
        projectTitle: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' }
        ],
      }" label-width="80px">
        <el-form-item label="项目名称" prop="projectTitle">
          <el-input v-model="form.projectTitle" placeholder="请输入项目名称" :maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
          <el-button type="danger" @click="handleDelete" v-if="form.projectId">删除项目</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 弹出式直播列表 -->
    <Transition name="popup">
      <div v-if="showLiveDrawer" class="live-popup">
        <div class="live-popup-mask" @click="closeLivePopup"></div>
        <div class="live-popup-content" :style="{
          left: `${popupPosition.x}px`,
          top: `${popupPosition.y}px`
        }" @click.stop>
          <div class="popup-header">
            <h3>
              <el-icon>
                <VideoPlay />
              </el-icon>
              {{ liveStore.projectTitle ? `${liveStore.projectTitle}` : '选择项目' }}
            </h3>
            <button class="close-btn" @click="closeLivePopup">
              <el-icon>
                <Close />
              </el-icon>
            </button>
          </div>
          <div class="popup-body">
            <LiveCard />
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<style lang="scss" scoped>
.app-container {
  padding: 16px;
  height: 100%;
  background: #f7f9fc;
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  // 顶部统计条
  .stats-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.04);
    position: sticky;
    top: 0;
    z-index: 10;
    margin: 0 0 10px 0;
    
    .stats-wrapper {
      display: flex;
      gap: 32px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 16px;
        border-radius: 12px;
        cursor: pointer;
        position: relative;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          background: rgba(var(--el-color-primary-rgb), 0.04);

          .stat-icon {
            transform: scale(1.1);
            background: var(--el-color-primary-light-8);

            .el-icon {
              color: var(--el-color-primary);
            }
          }
        }

        &.is-active {
          background: rgba(var(--el-color-primary-rgb), 0.08);

          .stat-icon {
            background: var(--el-color-primary);

            .el-icon {
              color: white;
            }
          }

          .stat-info {
            .stat-value {
              color: var(--el-color-primary);
            }

            .stat-label {
              color: var(--el-color-primary);
            }
          }

          .stat-indicator {
            transform: scaleX(1);
            opacity: 1;
          }
        }

        .stat-icon {
          width: 36px;
          height: 36px;
          border-radius: 10px;
          background: var(--el-fill-color-light);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          .el-icon {
            font-size: 18px;
            color: var(--el-text-color-secondary);
            transition: color 0.3s ease;
          }
        }

        .stat-info {
          .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            line-height: 1;
            margin-bottom: 2px;
            transition: color 0.3s ease;
          }

          .stat-label {
            font-size: 13px;
            color: var(--el-text-color-secondary);
            transition: color 0.3s ease;
          }
        }

        .stat-indicator {
          position: absolute;
          left: 16px;
          right: 16px;
          bottom: -2px;
          height: 2px;
          background: var(--el-color-primary);
          border-radius: 1px;
          transform: scaleX(0);
          opacity: 0;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          transform-origin: left center;
        }
      }
    }

    .action-wrapper {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-bar {
        width: 240px;

        .el-input {
          .el-input__wrapper {
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(8px);

            &:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            }

            &.is-focus {
              box-shadow: 0 0 0 1px var(--el-color-primary) !important;
            }
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;

        .el-button {
          border-radius: 10px;
          background: rgba(255, 255, 255, 0.8);
          backdrop-filter: blur(8px);

          &:not(.el-dropdown-selfdefine) {
            padding: 10px 16px;
          }

          &.el-button--primary {
            background: var(--el-color-primary);
          }
        }
      }
    }
  }

  // 主内容区
  .main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    min-height: 0;
    overflow-y: auto;
    
    // 项目区
    .projects-section {
      background: white;
      border-radius: 16px;
      padding: 20px;
      display: flex;
      flex-direction: column;
      gap: 20px;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, transparent 100%);
        opacity: 0.5;
        pointer-events: none;
      }

      .projects-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 16px;
        position: relative;
        z-index: 1;
      }

      .recent-lives-container {
        position: relative;
        z-index: 1;
      }

      .live-streams-flex {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        margin-bottom: 24px;
        
        :deep(.live-stream-card) {
          width: calc((100% - 32px) / 3);
          min-width: 300px;
          
          .project-tag {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            background: var(--el-fill-color-light);
            border-radius: 6px;
            font-size: 12px;
            color: var(--el-text-color-secondary);
            margin-top: 8px;
            transition: all 0.3s ease;
            
            .el-icon {
              font-size: 14px;
            }
            
            &:hover {
              background: var(--el-color-primary-light-9);
              color: var(--el-color-primary);
            }
          }
          
          @media screen and (max-width: 1200px) {
            width: calc((100% - 16px) / 2);
          }
          
          @media screen and (max-width: 640px) {
            width: 100%;
          }
        }
      }

      .pagination {
        display: flex;
        justify-content: flex-end;
        padding-top: 16px;
        border-top: 1px solid var(--el-border-color-light);
        position: relative;
        z-index: 1;
      }
    }
  }

  &.has-popup {
    .projects-section {
      .projects-grid {
        .project-card {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:not(.is-active) {
            opacity: 0.5;
            filter: blur(1px);
            pointer-events: none;
            transform: scale(0.98);
          }

          &.is-active {
            position: relative;
            z-index: 2002;
            pointer-events: auto;
            opacity: 1;
            transform: translateY(-4px) scale(1.03);
            box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
            background: white;
            border-color: var(--el-color-primary-light-3);
          }
        }
      }
    }
  }
}

// 弹出层动画
.popup-enter-active,
.popup-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  .live-popup-content {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .live-popup-mask {
    transition: opacity 0.2s ease;
  }
}

.popup-enter-from,
.popup-leave-to {
  opacity: 0;
  visibility: hidden;

  .live-popup-content {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }

  .live-popup-mask {
    opacity: 0;
  }
}

// 弹出式直播列表
.live-popup {
  position: fixed;
  inset: 0;
  z-index: 2000;
  display: flex;

  .live-popup-mask {
    position: absolute;
    inset: 0;
    pointer-events: auto;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(4px);
    opacity: 0;
    transition: opacity 0.2s ease;

    .popup-enter-active &,
    .popup-leave-active & {
      opacity: 1;
    }
  }

  .live-popup-content {
    position: fixed;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    width: 280px;
    height: 450px;
    z-index: 2001;
    overflow: hidden;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    filter: none !important;
    border: 1px solid var(--el-border-color-light);
    display: flex;
    flex-direction: column;

    .popup-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 14px 16px;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(8px);
      border-bottom: 1px solid var(--el-border-color-light);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: -1px;
        height: 1px;
        background: linear-gradient(to right,
          var(--el-border-color-light),
          var(--el-border-color),
          var(--el-border-color-light));
        opacity: 0.6;
      }

      h3 {
        margin: 0;
        font-size: 15px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--el-text-color-primary);

        .el-icon {
          font-size: 16px;
          color: var(--el-color-primary);
          transition: transform 0.2s ease;
        }

        &:hover .el-icon {
          transform: scale(1.1);
        }
      }

      .close-btn {
        width: 24px;
        height: 24px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
        background: transparent;

        &:hover {
          background: var(--el-fill-color-light);
        }

        .el-icon {
          font-size: 14px;
          color: var(--el-text-color-secondary);
        }
      }
    }

    .popup-body {
      padding: 0;
      overflow-y: auto;
      flex: 1;
      background: var(--el-bg-color-page);

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--el-border-color-lighter);
        border-radius: 4px;
      }

      &:hover::-webkit-scrollbar-thumb {
        background: var(--el-border-color);
      }
    }
  }
}

// 响应式布局
@media screen and (max-width: 1200px) {
  .app-container .main-content {
    grid-template-columns: 1fr;
  }

  .app-container .stats-bar {
    flex-direction: column;
    gap: 16px;

    .stats-wrapper {
      width: 100%;
      justify-content: center;
    }

    .action-wrapper {
      width: 100%;

      .search-bar {
        width: 100%;
      }
    }
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 16px 0 12px;
  padding-left: 12px;
  position: relative;
  grid-column: 1 / -1;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 16px;
    background: var(--el-color-primary);
    border-radius: 2px;
  }

  &:first-child {
    margin-top: 0;
  }
}
</style>