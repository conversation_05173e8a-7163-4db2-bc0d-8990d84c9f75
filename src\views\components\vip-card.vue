<script setup>
import { onMounted, computed } from 'vue'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

const formatNumber = (num) => {
  if (!num) return '0'
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 使用计算属性获取用户信息
const userInfo = computed(() => ({
  name: userStore.name,
  createTime: userStore.createTime,
  hashrateBalance: userStore.hashrateBalance
}))

// 组件挂载时获取最新用户信息
onMounted(async () => {
  try {
    await userStore.getInfo()
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
})
</script>

<template>
  <div class="card-container noselect">
    <div class="canvas">
      <div v-for="i in 25" :key="i" :class="`tracker tr-${i}`"></div>
      <div class="card-box" id="card">
        <div class="card-head">
          <div class="card-chip">
            <svg viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="gold-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#FFD700" />
                  <stop offset="100%" style="stop-color:#FFA500" />
                </linearGradient>
              </defs>
              <path
                d="M5.813 2C2.647 2 0 4.648 0 7.813v10.375C0 21.352 2.648 24 5.813 24h14.375C23.352 24 26 21.352 26 18.187V7.813C26 4.649 23.352 2 20.187 2H5.813zm0 2h14.375C22.223 4 24 5.777 24 7.813V9h-6c-.555 0-1-.445-1-1c0-.555.445-1 1-1a1 1 0 1 0 0-2c-1.645 0-3 1.355-3 3c0 1.292.844 2.394 2 2.813v4.968c-1.198.814-2 2.18-2 3.719c0 .923.293 1.781.781 2.5H10.22a4.439 4.439 0 0 0 .78-2.5c0-1.542-.802-2.905-2-3.719v-4.969c1.156-.418 2-1.52 2-2.812c0-1.645-1.355-3-3-3H6a1 1 0 0 0-.094 0a1.001 1.001 0 0 0-.093 0A1.004 1.004 0 0 0 6 7h2c.555 0 1 .445 1 1c0 .555-.445 1-1 1H2V7.812C2 5.777 3.777 4 5.813 4zM2 11h5v4H2v-4zm17 0h5v4h-5v-4zM2 17h4.5C7.839 17 9 18.161 9 19.5S7.839 22 6.5 22h-.688C3.777 22 2 20.223 2 18.187V17zm17.5 0H24v1.188C24 20.223 22.223 22 20.187 22H19.5c-1.339 0-2.5-1.161-2.5-2.5s1.161-2.5 2.5-2.5z"
                fill="url(#gold-gradient)"></path>
            </svg>
          </div>
          <div class="card-logo">
            <img src="@/assets/logo/256.svg" alt="logo" width="42" height="42">
          </div>
        </div>
        <div class="card-number-row">
          <div class="card-point-title">当前剩余算力点</div>
          <div class="card-point-value">{{ formatNumber(userInfo.hashrateBalance) }}</div>
        </div>
        <div class="card-details">
          <div class="card-holder-col">
            <span class="card-holder-title">注册人</span>
            <span class="card-holder-name">{{ userInfo.name || '暂无' }}</span>
          </div>
          <div class="card-date-col">
            <span class="card-date-title">注册时间</span>
            <span class="card-date-sub">{{ userInfo.createTime || '暂无' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.card-container,
.card-box,
.card-head,
.card-details,
.card-holder-col,
.card-date-col {
  display: flex;
}

.card-container {
  position: relative;
  width: 100%; // 修改为100%以适应容器
  max-width: 570px; // 设置最大宽度
  height: 350px;
  transition: 200ms;
}

.card-box {
  width: 100%; // 修改为100%以适应容器
  max-width: 550px; // 设置最大宽度
  min-height: 300px;
  background: linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.15)), url('@/assets/images/vip-card-bg.jpg');
  background-size: cover;
  background-position: center;
  box-shadow: 0px 0px 15px -2px rgba(0, 0, 0, 0.2);
  border-radius: 18px;
  margin: 16px;
  padding: 1.5em;
  flex-direction: column;
  justify-content: space-around;
  gap: 2em;
}

.card-head {
  justify-content: space-between;
  align-items: center;
}

.card-chip svg {
  width: 42px;
  height: 42px;
}

.card-chip svg path {
  fill: url(#gold-gradient);
}

.card-logo svg {
  width: 42px;
  height: 42px;
}

.card-number-row {
  justify-content: center;
  word-spacing: 1em;
  margin-left: 90px;
}

.card-point-title {
  font-size: 1.2em;
  position: relative;
  top: -30px;
  color: #ffffffc9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.card-point-value {
  font-size: 2.3em;
  position: relative;
  top: -10px;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}


.card-details {
  justify-content: space-between;
  text-transform: uppercase;
}

.card-holder-col {
  flex-direction: column;
  gap: 2px;
}

.card-holder-title,
.card-date-title {
  color: #ffffffc9;
  font-size: 1em;
}

.card-holder-name,
.card-date-sub {
  font-size: 1.3em;
  font-weight: 600;
  color: #ffffff;
}

.card-date-col {
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.canvas {
  perspective: 800px;
  inset: 0;
  z-index: 200;
  position: absolute;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(5, 1fr);
  gap: 0;
}

.tracker {
  position: relative;
  z-index: 200;
  width: 100%;
  height: 100%;
}

.tracker:hover {
  cursor: pointer;
}

.card-box {
  position: absolute;
  inset: 0;
  z-index: 0;
  border-radius: 18px;
  transition: 300ms;
}

.tr-1:hover~.card-box {
  transform: rotateX(20deg) rotateY(-10deg) rotateZ(0deg);
}

.tr-2:hover~.card-box {
  transform: rotateX(20deg) rotateY(-5deg) rotateZ(0deg);
}

.tr-3:hover~.card-box {
  transform: rotateX(20deg) rotateY(0deg) rotateZ(0deg);
}

.tr-4:hover~.card-box {
  transform: rotateX(20deg) rotateY(5deg) rotateZ(0deg);
}

.tr-5:hover~.card-box {
  transform: rotateX(20deg) rotateY(10deg) rotateZ(0deg);
}

.tr-6:hover~.card-box {
  transform: rotateX(10deg) rotateY(-10deg) rotateZ(0deg);
}

.tr-7:hover~.card-box {
  transform: rotateX(10deg) rotateY(-5deg) rotateZ(0deg);
}

.tr-8:hover~.card-box {
  transform: rotateX(10deg) rotateY(0deg) rotateZ(0deg);
}

.tr-9:hover~.card-box {
  transform: rotateX(10deg) rotateY(5deg) rotateZ(0deg);
}

.tr-10:hover~.card-box {
  transform: rotateX(10deg) rotateY(10deg) rotateZ(0deg);
}

.tr-11:hover~.card-box {
  transform: rotateX(0deg) rotateY(-10deg) rotateZ(0deg);
}

.tr-12:hover~.card-box {
  transform: rotateX(0deg) rotateY(-5deg) rotateZ(0deg);
}

.tr-13:hover~.card-box {
  transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
}

.tr-14:hover~.card-box {
  transform: rotateX(0deg) rotateY(5deg) rotateZ(0deg);
}

.tr-15:hover~.card-box {
  transform: rotateX(0deg) rotateY(10deg) rotateZ(0deg);
}

.tr-16:hover~.card-box {
  transform: rotateX(-10deg) rotateY(-10deg) rotateZ(0deg);
}

.tr-17:hover~.card-box {
  transform: rotateX(-10deg) rotateY(-5deg) rotateZ(0deg);
}

.tr-18:hover~.card-box {
  transform: rotateX(-10deg) rotateY(0deg) rotateZ(0deg);
}

.tr-19:hover~.card-box {
  transform: rotateX(-10deg) rotateY(5deg) rotateZ(0deg);
}

.tr-20:hover~.card-box {
  transform: rotateX(-10deg) rotateY(10deg) rotateZ(0deg);
}

.tr-21:hover~.card-box {
  transform: rotateX(-20deg) rotateY(-10deg) rotateZ(0deg);
}

.tr-22:hover~.card-box {
  transform: rotateX(-20deg) rotateY(-5deg) rotateZ(0deg);
}

.tr-23:hover~.card-box {
  transform: rotateX(-20deg) rotateY(0deg) rotateZ(0deg);
}

.tr-24:hover~.card-box {
  transform: rotateX(-20deg) rotateY(5deg) rotateZ(0deg);
}

.tr-25:hover~.card-box {
  transform: rotateX(-20deg) rotateY(10deg) rotateZ(0deg);
}

.noselect {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.card-box::before {
  content: "";
  /* background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2)); */
  width: 105%;
  height: 105%;
  border-radius: 18px;
  position: absolute;
  z-index: -1;
  transition: 200ms;
  opacity: 0;
}

.tracker:hover~.card-box::before {
  opacity: 1;
}
</style>
