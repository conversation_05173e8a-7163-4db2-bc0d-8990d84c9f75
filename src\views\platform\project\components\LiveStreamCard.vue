<script setup>
import { useRouter } from 'vue-router'
import useSoftWareStore from "@/store/modules/software"
import { updateLive } from "@/api/platform/live"

const props = defineProps({
  liveName: String,
  projectId: Number,
  projectTitle: String,
  liveId: Number,
  createTime: String
})

const router = useRouter()

// 跳转到素材页
const toMaterialPage = () => {
  router.push({
    path: `/project/${props.projectId}/material`,
    query: { projectTitle: props.projectTitle + '项目' }
  })
}

// 开始直播
const startLive = async (e) => {
  e.stopPropagation()
  try {
    await updateLive({
      liveId: props.liveId,
      projectId: props.projectId,
      liveName: props.liveName
    })
    await useSoftWareStore().openLiveWindow(props.liveId)
  } catch (error) {
    console.error('开启直播失败', error)
  }
}
</script>

<template>
  <div class="live-stream-card">
    <!-- 主要信息区 -->
    <div class="info-section">
      <div class="live-icon">
        <el-icon><VideoCamera /></el-icon>
      </div>
      <div class="text-content">
        <h3 class="live-name">{{ liveName }}</h3>
        <div class="project-info">
          <span class="label">所属项目：</span>
          <span class="value">{{ projectTitle }}</span>
        </div>
        <div class="time-info">
          <el-icon><Clock /></el-icon>
          <span>{{ createTime }}</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮区 -->
    <div class="action-section">
      <el-button 
        class="material-btn"
        @click="toMaterialPage"
      >
        <el-icon><Document /></el-icon>
        <span>素材管理</span>
      </el-button>
      
      <el-button 
        type="success" 
        class="start-btn"
        @click="startLive"
      >
        <el-icon><VideoPlay /></el-icon>
        <span>开始直播</span>
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.live-stream-card {
  background: var(--el-bg-color);
  border-radius: 16px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--el-border-color-light);

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      135deg,
      var(--el-color-primary-light-9) 0%,
      transparent 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }
  
  &:hover {
    transform: translateY(-2px);
    border-color: var(--el-color-primary-light-5);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    
    &::before {
      opacity: 0.5;
    }
    
    .info-section {
      .live-icon {
        transform: scale(1.1);
        background: var(--el-color-primary);
        
        .el-icon {
          color: white;
        }
      }

      .project-info {
        color: var(--el-color-primary);
        transform: translateX(4px);

        .el-icon {
          transform: scale(1.1);
        }
      }
    }
  }

  // 信息区域
  .info-section {
    display: flex;
    gap: 16px;
    align-items: flex-start;
    position: relative;
    z-index: 1;
    
    .live-icon {
      width: 40px;
      height: 40px;
      border-radius: 12px;
      background: var(--el-color-primary-light-8);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      flex-shrink: 0;
      
      .el-icon {
        font-size: 20px;
        color: var(--el-color-primary);
        transition: color 0.3s ease;
      }
    }
    
    .text-content {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .live-name {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
        line-height: 1.4;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .project-info {
        display: flex;
        align-items: center;
        gap: 6px;
        color: var(--el-text-color-regular);
        font-size: 13px;
        transition: all 0.3s ease;
        
        .label {
          color: var(--el-text-color-secondary);
          font-weight: normal;
        }
        
        .value {
          color: var(--el-text-color-primary);
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &:hover {
          .value {
            color: var(--el-color-primary);
          }
        }
      }
      
      .time-info {
        display: flex;
        align-items: center;
        gap: 6px;
        color: var(--el-text-color-secondary);
        font-size: 12px;
        
        .el-icon {
          font-size: 14px;
        }
        
        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  // 操作区域
  .action-section {
    display: flex;
    gap: 12px;
    margin-top: 4px;
    position: relative;
    z-index: 2;
    
    :deep(.el-button) {
      flex: 1;
      border-radius: 10px;
      height: 36px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: visible;
      
      &::before {
        content: '';
        position: absolute;
        inset: -1px;
        background: transparent;
        border-radius: inherit;
        transition: background-color 0.3s ease;
        pointer-events: none;
      }
      
      .el-icon {
        font-size: 16px;
        transition: transform 0.3s ease;
        position: relative;
      }
      
      span {
        font-size: 14px;
        font-weight: 500;
        position: relative;
      }
      
      &.material-btn {
        --el-button-bg-color: var(--el-color-primary-light-9);
        --el-button-border-color: transparent;
        --el-button-hover-bg-color: var(--el-color-primary-light-8);
        --el-button-hover-border-color: transparent;
        --el-button-hover-text-color: var(--el-color-primary-dark-2);
        --el-button-active-bg-color: var(--el-color-primary-light-7);
        color: var(--el-color-primary);
        
        &:hover {
          color: var(--el-color-primary-dark-2);
          transform: translateY(-2px);
          
          .el-icon {
            transform: scale(1.1);
          }
        }
      }
      
      &.start-btn {
        --el-button-bg-color: var(--el-color-success);
        --el-button-border-color: transparent;
        --el-button-hover-bg-color: var(--el-color-success-light-3);
        --el-button-hover-border-color: transparent;
        --el-button-hover-text-color: white;
        --el-button-active-bg-color: var(--el-color-success-dark-2);
        box-shadow: 0 2px 8px rgba(var(--el-color-success-rgb), 0.35);
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(var(--el-color-success-rgb), 0.45);
          
          .el-icon {
            transform: scale(1.1);
          }
        }
        
        &:active {
          transform: scale(0.95);
          box-shadow: 0 2px 4px rgba(var(--el-color-success-rgb), 0.25);
        }
      }
    }
  }
}
</style> 