<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { Loading } from '@element-plus/icons-vue'

interface Props {
  // 触发加载更多的阈值距离（距离底部多少像素时触发）
  threshold?: number
  // 是否正在加载
  loading?: boolean
  // 是否还有更多数据
  hasMore?: boolean
  // 根元素的标签
  tag?: string
  // 加载区域的高度
  loadingHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  threshold: 100,
  loading: false,
  hasMore: true,
  tag: 'div',
  loadingHeight: 20
})

const emit = defineEmits<{
  loadMore: []
}>()

const rootRef = ref<HTMLElement | null>(null)
const loadingTriggerRef = ref<HTMLElement | null>(null)
let observer: IntersectionObserver | null = null
let isTriggering = ref(false) // 防止重复触发
let isInitialized = ref(false) // 防止初始化时立即触发

// 处理触发器进入视口
function handleIntersection(entries: IntersectionObserverEntry[]) {
  const entry = entries[0]

  // 防止初始化时立即触发
  if (!isInitialized.value) {
    isInitialized.value = true
    return
  }

  if (entry.isIntersecting && props.hasMore && !props.loading && !isTriggering.value) {
    isTriggering.value = true
    emit('loadMore')

    // 延迟重置触发状态，防止重复触发
    setTimeout(() => {
      isTriggering.value = false
    }, 1000)
  }
}

// 初始化观察器
function initObserver() {
  if (!loadingTriggerRef.value) {
    return
  }

  observer = new IntersectionObserver(handleIntersection, {
    root: null, // 使用视口作为根
    rootMargin: `0px 0px ${props.threshold}px 0px`, // 只在底部提前触发
    threshold: 0.1
  })

  observer.observe(loadingTriggerRef.value)
}

// 清理观察器
function cleanup() {
  if (observer) {
    observer.disconnect()
    observer = null
  }
}

onMounted(() => {
  nextTick(() => {
    initObserver()
  })
})

onBeforeUnmount(() => {
  cleanup()
})
</script>

<template>
  <component :is="props.tag" ref="rootRef" class="lazy-load-container">
    <!-- 内容区域 -->
    <slot />
    
    <!-- 加载触发器 - 当这个元素进入视口时触发加载 -->
    <div
      ref="loadingTriggerRef"
      class="loading-trigger"
      :style="{ height: `${loadingHeight}px` }"
    >
      <!-- 加载中状态 -->
      <div v-if="loading" class="loading-content">
        <slot name="loading">
          <div class="default-loading">
            <el-icon class="rotating"><Loading /></el-icon>
            <span>加载更多...</span>
          </div>
        </slot>
      </div>

      <!-- 没有更多数据状态 -->
      <div v-else-if="!hasMore" class="no-more-content">
        <slot name="no-more">
          <div class="default-no-more">
            已经到底了，没有更多了
          </div>
        </slot>
      </div>

      <!-- 空白触发区域 - 当有更多数据且不在加载时显示 -->
      <div v-else class="trigger-area">
        <!-- 这个区域用于触发懒加载，通常是透明的 -->
      </div>
    </div>
  </component>
</template>

<style lang="scss" scoped>
.lazy-load-container {
  position: relative;
}

.loading-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.loading-content,
.no-more-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.default-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;

  .rotating {
    font-size: 16px;
    color: #409EFF;
    animation: rotate 1s linear infinite;
  }
}

.default-no-more {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.trigger-area {
  width: 100%;
  height: 100%;
  min-height: 20px;
  // 调试用：可以临时启用来查看触发区域
  // background: rgba(255, 0, 0, 0.1);
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
