// 最近创建 最近开播 逻辑
import { listCreateBy, listUpdateBy } from '@/api/platform/live';
import { getProject } from '@/api/platform/project';
import { getLive, updateLive } from "@/api/platform/live";
import useContext from '@/store/modules/context';
import modal from '@/plugins/modal';
import { getToken } from '@/utils/auth';

export async function fetchRecentLives(userName) {
  try {
    const [createdResponse, updatedResponse] = await Promise.all([
      listCreateBy(userName),
      listUpdateBy(userName)
    ]);

    // 获取最近创建的直播信息，并关联项目名称
    const tempRecentCreatedLives = await Promise.all(createdResponse.data.map(async live => ({
      ...live,
      projectTitle: (await getProject(live.projectId)).data.projectTitle
    })));

    // 获取最近开播的直播信息，并关联项目名称
    const createdLiveIds = new Set(tempRecentCreatedLives.map(live => live.liveId));
    const tempRecentStartedLives = await Promise.all(updatedResponse.data.map(async live => {
      if (!createdLiveIds.has(live.liveId)) {
        return {
          ...live,
          projectTitle: (await getProject(live.projectId)).data.projectTitle
        };
      }
      return null;
    })).then(lives => lives.filter(live => live !== null));

    return {
      recentCreatedLives: tempRecentCreatedLives,
      recentStartedLives: tempRecentStartedLives
    };
  } catch (error) {
    console.error('未能获取直播信息', error);
    throw error;
  }
}

export async function openLive(liveInfo = {}) {
  try {
    const r = await getLive(liveInfo.liveId);
    await updateLive(r.data); 
    useContext().send("getToken",getToken())
    await useContext().invoke('openChildLive', { ...liveInfo});
  } catch (error) {
    console.error('无法打开直播，请稍后再试。', error);
    modal.alertError('无法打开直播，请稍后再试。');
    throw error;
  }
}