/**
 * 阿里云ICE媒资管理相关API
 * 通过后端代理调用阿里云ICE SDK
 */
import request from '@/utils/request';

// ============================================================================
// 类型定义 - 根据阿里云ICE官方文档
// ============================================================================

export interface MediaBasicInfo {
  MediaId: string;
  InputURL?: string;
  MediaType: string; // "video" | "audio" | "image"
  BusinessType?: string;
  Source?: string;
  Title?: string;
  Description?: string;
  Category?: string;
  MediaTags?: string;
  CoverURL?: string;
  UserData?: string;
  Snapshots?: any[];
  Status?: string;
  TranscodeStatus?: string;
  CreateTime?: string;
  ModifiedTime?: string;
  DeletedTime?: string;
  SpriteImages?: any[];
  CateId?: number;
  Biz?: string;
  UploadSource?: string;
  CateName?: string;
  ReferenceId?: string;
}

export interface FileBasicInfo {
  FileName: string;
  FileStatus: string;
  FileType: string;
  FileSize: number;
  FileUrl: string;
  Region: string;
  FormatName: string;
  Duration: number;
  Bitrate: number;
  Width: number;
  Height: number;
  CreateTime: string;
  ModifiedTime: string;
}

export interface MediaInfo {
  MediaId: string;
  MediaBasicInfo: MediaBasicInfo;
  FileInfoList?: Array<{
    FileBasicInfo: FileBasicInfo;
    AudioStreamInfoList?: any[];
    VideoStreamInfoList?: any[];
    SubtitleStreamInfoList?: any[];
  }>;
  AiRoughData?: any;
}

export interface MediaSearchParams {
  keyword?: string;
  mediaType?: string;
  pageNo?: number;
  pageSize?: number;
  sortBy?: string;
  nextToken?: string;
  maxResults?: number;
  includeFileBasicInfo?: boolean;
  businessType?: string;
}

export interface MediaListResponse {
  MediaInfos: MediaInfo[];
  TotalCount: number;
  NextToken?: string;
  MaxResults?: number;
}

export interface MediaUploadRequest {
  file: File;
  title?: string;
  description?: string;
  category?: string;
}

export interface MediaUploadResponse {
  MediaId: string;
  UploadURL: string;
  UploadAuth: string;
}

// 分片上传相关接口
export interface ChunkUploadInitRequest {
  fileName: string;
  fileSize: number;
  category?: string;
}

export interface ChunkUploadInitResponse {
  uploadId: string;
  filePath: string;
  fileName: string;
  category: string;
}

export interface ChunkUploadRequest {
  uploadId: string;
  filePath: string;
  chunkIndex: number;
  chunk: Blob;
}

export interface ChunkUploadResponse {
  etag: string;
  partNumber: number;
  md5?: string;
}

export interface FilePartETag {
  partNumber: number;
  ETag: string; // 使用大写ETag匹配后端@JsonProperty("ETag")
  md5?: string;
}

export interface CompleteUploadRequest {
  uploadId: string;
  filePath: string;
  fileSize: number;
  fileName: string;
  category?: string;
  partETags: FilePartETag[];
}

export interface CompleteUploadResponse {
  mediaInfo: any;
  ossUrl: string;
  filePath: string;
  uploadedPath: string;
  fileName: string;
  fileSize: number;
  category: string;
  md5?: string;
}

export interface MediaRegisterRequest {
  inputURL: string;
  mediaMetaData: {
    Title: string;
    Description?: string;
  };
}

export interface MediaRegisterResponse {
  MediaId: string;
}

export interface BatchGetMediaInfosRequest {
  mediaIds: string[];
  additionType?: string;
}

export interface BatchGetMediaInfosResponse {
  MediaInfos: MediaInfo[];
}

export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// ============================================================================
// 兼容性类型（保持向后兼容）
// ============================================================================

export interface MediaInfoQuery {
  MediaId?: string;
  InputURL?: string;
}

export interface MediaInfoResponse {
  MediaInfo: MediaInfo;
}

export interface MediaPayload extends MediaBasicInfo {}

export interface MediaListQueryParams extends MediaSearchParams {}

export interface GetEditingProjectMaterialsRequest {
  projectId: string;
}

export interface GetEditingProjectMaterialsResponse {
  materials: any[];
}

export interface UploadAndRegisterResponse extends MediaUploadResponse {}

// ============================================================================
// API接口
// ============================================================================

/**
 * 获取媒资信息
 */
export function getMediaInfo(params: MediaInfoQuery): Promise<ApiResponse<MediaInfoResponse>> {
  return request({
    url: '/video/media/info',
    method: 'get',
    params: {
      mediaId: params.MediaId,
      inputURL: params.InputURL
    }
  }).then((res: any) => ({
    ...res,
    data: {
      MediaInfo: res.data
    }
  }));
}

/**
 * 搜索媒资 - 调用后端listMediaBasicInfo接口
 */
export function searchMedia(params: MediaSearchParams = {}): Promise<ApiResponse<MediaListResponse>> {
  return request({
    url: '/video/media/listMediaBasicInfo',
    method: 'get',
    params: {
      keyword: params.keyword,
      mediaType: params.mediaType,
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 20,
      sortBy: params.sortBy || 'CreationTime:Desc',
      nextToken: params.nextToken,
      maxResults: params.maxResults,
      includeFileBasicInfo: params.includeFileBasicInfo,
      businessType: params.businessType
    }
  });
}

/**
 * 获取媒资基础信息列表（兼容旧接口）
 * 直接调用 searchMedia 函数
 */
export function listMediaBasicInfo(params: MediaSearchParams = {}): Promise<ApiResponse<MediaListResponse>> {
  return searchMedia(params);
}

/**
 * 批量获取媒资信息
 */
export function batchGetMediaInfos(data: BatchGetMediaInfosRequest): Promise<ApiResponse<BatchGetMediaInfosResponse>> {
  return request({
    url: '/video/media/batchGetMediaInfos',
    method: 'get',
    params: {
      mediaIds: data.mediaIds.join(','),
      additionType: data.additionType
    }
  });
}

/**
 * 上传媒资文件
 */
export function uploadMedia(data: MediaUploadRequest): Promise<ApiResponse<MediaUploadResponse>> {
  const formData = new FormData();
  formData.append('file', data.file);

  // 添加category参数，根据后端接口要求
  const category = data.category || 'general'; // 默认为general，符合后端接口
  formData.append('category', category);

  return request({
    url: '/video/media/uploadAndRegister',
    method: 'post',
    data: formData
  });
}

/**
 * 注册媒资
 */
export function registerMedia(data: MediaRegisterRequest): Promise<ApiResponse<MediaRegisterResponse>> {
  return request({
    url: '/api/ice/media/register',
    method: 'post',
    data
  });
}

/**
 * 获取剪辑工程关联的素材
 */
export function getEditingProjectMaterials(params: GetEditingProjectMaterialsRequest): Promise<ApiResponse<GetEditingProjectMaterialsResponse>> {
  return request({
    url: `/video/media/project/${params.projectId}/materials`,
    method: 'get'
  });
}

// ============================================================================
// 兼容性接口（保持向后兼容）
// ============================================================================

/**
 * 获取媒资列表（兼容旧接口）
 */
export function getMediaList(params: MediaListQueryParams): Promise<ApiResponse<MediaListResponse>> {
  return searchMedia(params);
}

/**
 * 上传并注册媒资（兼容旧接口）
 */
export function uploadAndRegisterMedia(data: MediaUploadRequest): Promise<ApiResponse<UploadAndRegisterResponse>> {
  return uploadMedia(data);
}

// ============================================================================
// 分片上传接口
// ============================================================================

/**
 * 初始化分片上传
 */
export function initChunkUpload(data: ChunkUploadInitRequest): Promise<ApiResponse<ChunkUploadInitResponse>> {
  return request({
    url: '/video/media/initUpload',
    method: 'post',
    params: {
      fileName: data.fileName,
      fileSize: data.fileSize,
      category: data.category || 'general'
    }
  });
}

/**
 * 上传文件分片
 */
export function uploadChunk(data: ChunkUploadRequest): Promise<ApiResponse<ChunkUploadResponse>> {
  const formData = new FormData();
  formData.append('uploadId', data.uploadId);
  formData.append('filePath', data.filePath);
  formData.append('chunkIndex', data.chunkIndex.toString());
  formData.append('chunk', data.chunk);
  return request({
    url: '/video/media/uploadChunk',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      repeatSubmit: false
    }
  });
}

/**
 * 完成分片上传并注册媒资
 */
export function completeChunkUpload(data: CompleteUploadRequest): Promise<ApiResponse<CompleteUploadResponse>> {
  return request({
    url: '/video/media/completeUpload',
    method: 'post',
    params: {
      uploadId: data.uploadId,
      filePath: data.filePath,
      fileSize: data.fileSize,
      fileName: data.fileName,
      category: data.category
    },
    data: data.partETags
  });
}
