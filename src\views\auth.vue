<template>
  <div class="auth-container">
    <!-- 背景渐变 -->
    <div class="background-gradient"></div>
    
    <!-- 主要内容 -->
    <div class="auth-content">
      <!-- 左侧内容区 -->
      <div class="content-section">
        <div class="content-wrapper">
          <h1 class="main-title">
            {{ isRegisterMode ? '创建账号' : '欢迎回来' }}
            <span class="sub-title">开启智能新世界</span>
          </h1>
          <p class="description">
            {{ isRegisterMode ? '已有账号?' : '还没有账号?' }}
            <button class="switch-button" @click="switchMode">
              <span class="switch-text">{{ isRegisterMode ? '立即登录' : '立即注册' }}</span>
              <span class="switch-icon">
                <i class="el-icon-arrow-right"></i>
              </span>
            </button>
          </p>
        </div>
        <!-- 粒子效果 -->
        <div class="particle-wrapper">
          <three-background />
        </div>
      </div>

      <!-- 右侧表单区 -->
      <transition name="form-transition" mode="out-in">
        <div class="form-section" :key="isRegisterMode">
          <div class="form-card">
            <component :is="currentComponent" @switch-mode="switchMode" />
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, shallowRef, onMounted } from 'vue'
import ThreeBackground from '@/components/ThreeBackground/index.vue'
import LoginForm from './login-form.vue'
import RegisterForm from './register-form.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const isRegisterMode = ref(false)
const currentComponent = shallowRef(LoginForm)

onMounted(() => {
  isRegisterMode.value = route.path === '/register'
  currentComponent.value = isRegisterMode.value ? RegisterForm : LoginForm
})

const switchMode = () => {
  isRegisterMode.value = !isRegisterMode.value
  currentComponent.value = isRegisterMode.value ? RegisterForm : LoginForm
}
</script>

<style lang="scss" scoped>
// 变量定义
$primary-color: #4f46e5;
$primary-hover: #4338ca;
$text-primary: #1a1a1a;
$text-secondary: #666;
$transition-bezier: cubic-bezier(0.4, 0, 0.2, 1);
$card-shadow-normal: 
  0 4px 6px -1px rgba(0, 0, 0, 0.1),
  0 2px 4px -1px rgba(0, 0, 0, 0.06),
  inset 0 2px 4px rgba(255, 255, 255, 0.2);
$card-shadow-hover: 
  0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04),
  inset 0 2px 4px rgba(255, 255, 255, 0.2);

// Mixins
@mixin transition-base {
  transition: all 0.3s $transition-bezier;
}

.auth-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 背景渐变
.background-gradient {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f6f8fd 0%, #f1f1fd 100%);
  z-index: 1;
}

// 主要内容
.auth-content {
  position: relative;
  width: 100%;
  max-width: 1200px;
  height: 600px;
  display: flex;
  z-index: 2;
  padding: 0 2rem;
}

// 左侧内容区
.content-section {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  padding: 3rem;
  overflow: hidden;
}

.content-wrapper {
  position: relative;
  z-index: 2;
  max-width: 440px;
  pointer-events: none;
  
  .main-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: $text-primary;
    line-height: 1.2;
    margin-bottom: 1rem;
    
    .sub-title {
      display: block;
      font-size: 2rem;
      color: $text-secondary;
      margin-top: 0.5rem;
    }
  }
  
  .description {
    font-size: 1rem;
    color: $text-secondary;
    margin-top: 2rem;
  }
}

// 切换按钮样式
.switch-button {
  display: inline-flex;
  align-items: center;
  background: none;
  border: none;
  padding: 0.5rem 1rem;
  margin-left: 0.5rem;
  cursor: pointer;
  pointer-events: auto;
  position: relative;
  @include transition-base;
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba($primary-color, 0.1);
    border-radius: 20px;
    opacity: 0;
    transform: scale(0.8);
    @include transition-base;
  }
  
  &:hover {
    &::before {
      opacity: 1;
      transform: scale(1);
    }
    
    .switch-text {
      color: $primary-hover;
    }
    
    .switch-icon {
      transform: translateX(4px);
    }
  }
  
  .switch-text {
    color: $primary-color;
    font-weight: 500;
    font-size: 0.875rem;
    margin-right: 0.5rem;
    @include transition-base;
  }
  
  .switch-icon {
    font-size: 0.75rem;
    color: $primary-color;
    @include transition-base;
  }
}

// 粒子效果包装器
.particle-wrapper {
  position: absolute;
  inset: 0;
  z-index: 1;
  opacity: 0.6;
}

// 右侧表单区
.form-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

// 表单卡片
.form-card {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: $card-shadow-normal;
  @include transition-base;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $card-shadow-hover;
  }
}

// 表单切换动画
.form-transition-enter-active,
.form-transition-leave-active {
  transition: all 0.4s $transition-bezier;
}

.form-transition-enter-from,
.form-transition-leave-to {
  opacity: 0;
  transform: translate3d(20px, 0, 0);
}

.form-transition-leave-to {
  transform: translate3d(-20px, 0, 0);
}

// 响应式断点
$breakpoint-desktop: 1024px;
$breakpoint-tablet: 768px;

@media (max-width: $breakpoint-desktop) {
  .auth-content {
    max-width: 900px;
  }
  
  .main-title {
    font-size: 3rem;
    
    .sub-title {
      font-size: 1.75rem;
    }
  }
}

@media (max-width: $breakpoint-tablet) {
  .auth-content {
    flex-direction: column;
    height: auto;
    padding: 2rem;
  }
  
  .content-section,
  .form-section {
    padding: 2rem 0;
  }
  
  .main-title {
    font-size: 2.5rem;
    
    .sub-title {
      font-size: 1.5rem;
    }
  }
}
</style> 