<template>
  <div class="video-library">
    <!-- 内容区域 -->
    <div class="content-wrapper">
      <!-- 视频列表 -->
      <div class="video-panel">
        <div v-if="props.loading" class="loading-container">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span>加载中...</span>
        </div>

        <div v-else-if="(props.mediaList || []).length === 0" class="empty-state">
          <el-empty description="暂无数据">
            <el-button type="primary" @click="handleUpload">上传视频</el-button>
          </el-empty>
        </div>

        <div v-else class="video-grid">
          <div v-for="video in (props.mediaList || [])" :key="video.mediaId"
            :class="['video-item', { selected: selectedItems.includes(video.mediaId) }]"
            @click="toggleSelect(video.mediaId)">
            <div class="video-thumbnail" @click.stop="handlePreview(video)">
              <img :src="video.coverUrl || '/placeholder-video.jpg'" :alt="video.title" />
              <div class="video-overlay">
                <el-icon class="play-icon">
                  <VideoPlay />
                </el-icon>
              </div>
              <!-- 展示时长（如有） -->
              <div v-if="video.duration" class="duration">
                {{ formatDuration(video.duration) }}
              </div>
            </div>
            <div class="video-content">
              <div class="video-info">
                <h4 class="video-title">{{ video.title }}</h4>
                <div class="video-meta-row">
                  <span>状态：{{ getStatusText(video.Status) }}</span>
                  <span>大小：{{ formatFileSize(video.size || 0) }}</span>
                </div>
                <div class="video-meta-row">
                  <span>创建：{{ formatDate(video.createTime) }}</span>
                </div>
              </div>
              <div class="video-actions">
                <el-button size="small" type="primary" @click.stop="handlePreview(video)">
                  <el-icon>
                    <VideoPlay />
                  </el-icon>
                  预览
                </el-button>
                <el-button size="small" type="success" @click.stop="handleDownload(video)">
                  <el-icon>
                    <Download />
                  </el-icon>
                  下载
                </el-button>
                <el-button size="small" type="danger" @click.stop="handleDeleteSingle(video.mediaId)">
                  <el-icon>
                    <Delete />
                  </el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传组件 -->
    <MediaUploader v-model:visible="uploadDialogVisible" media-type="video" @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError" />

    <!-- 视频播放预览弹窗 -->
    <el-dialog v-model="previewDialogVisible" title="视频预览" width="80%" @close="closePreview">
      <div v-if="currentPreviewVideo" class="video-preview-container">
        <div class="video-player">
          <video ref="videoPlayerRef" :src="getVideoPlayUrl(currentPreviewVideo)" controls preload="metadata"
            style="width: 100%; max-height: 60vh;" @error="handleVideoError">
            您的浏览器不支持视频播放
          </video>
        </div>
        <div class="video-preview-info">
          <h3>{{ currentPreviewVideo.title }}</h3>
          <div class="preview-meta">
            <p><strong>文件大小:</strong> {{ formatFileSize(currentPreviewVideo.size || 0) }}</p>
            <p><strong>时长:</strong> {{ formatDuration(currentPreviewVideo.duration || 0) }}</p>
            <p><strong>类型:</strong> {{ currentPreviewVideo.mediaType }}</p>
            <p><strong>创建时间:</strong> {{ formatDate(currentPreviewVideo.createTime) }}</p>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="preview-footer">
          <el-button @click="closePreview">关闭</el-button>
          <el-button type="success" @click="downloadCurrentVideo">
            <el-icon>
              <Download />
            </el-icon>
            下载
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Refresh, Loading, VideoPlay, Search, Download } from '@element-plus/icons-vue'
import type { MediaInfo, Material } from '../../types/media'
import { formatDuration, formatFileSize, downloadFileByUrl } from '../../utils/commonUtils'
import { BusinessTypeMap, StatusMap, SourceMap, mapToOptions, getStatusText } from '../../types/media'
import MediaUploader from './add/MediaUploader.vue'

// 定义组件的事件和props
const emit = defineEmits<{
  refreshMediaList: [page?: number],
  search: [keyword: string],
  filter: [params: any]
}>()

// 定义props
const props = defineProps<{
  mediaList?: Material[],
  currentPage?: number,
  pageSize?: number,
  total?: number,
  loading?: boolean
}>()

// 响应式数据
const selectedItems = ref<string[]>([])
const uploadDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const currentPreviewVideo = ref<Material | null>(null)
const videoPlayerRef = ref<HTMLVideoElement>()

// 获取视频播放地址
const getVideoPlayUrl = (video: Material): string => {
  return video.FileUrl || ''
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 处理视频错误
const handleVideoError = (e: Event) => {
  console.error('视频播放错误:', e)
  ElMessage.error('视频播放失败，请检查视频文件')
}

const handleUpload = () => {
  uploadDialogVisible.value = true
}

const toggleSelect = (videoId: string) => {
  const index = selectedItems.value.indexOf(videoId)
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(videoId)
  }
}

const handlePreview = (video: Material) => {
  currentPreviewVideo.value = video
  previewDialogVisible.value = true
}

const closePreview = () => {
  previewDialogVisible.value = false
  currentPreviewVideo.value = null
  if (videoPlayerRef.value) {
    videoPlayerRef.value.pause()
    videoPlayerRef.value.currentTime = 0
  }
}

const downloadCurrentVideo = () => {
  if (currentPreviewVideo.value) {
    handleDownload(currentPreviewVideo.value)
  }
}

const handleDeleteSingle = async (videoId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个视频吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    emit('refreshMediaList')
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleDownload = (video: Material) => {
  const url = getVideoPlayUrl(video)
  if (url) {
    try {
      downloadFileByUrl(url, video.title)
      ElMessage.success('开始下载视频文件')
    } catch (error) {
      console.error('下载视频失败:', error)
      ElMessage.error('无法下载视频文件')
    }
  } else {
    ElMessage.error('无法获取视频下载链接')
  }
}

// 上传成功处理
const handleUploadSuccess = (result: any) => {
  ElMessage.success('视频上传成功！')
  uploadDialogVisible.value = false
  emit('refreshMediaList')
}

// 上传错误处理
const handleUploadError = (error: any) => {
  console.error('上传失败:', error)
  ElMessage.error(`上传失败: ${error.message || '未知错误'}`)
}

// 监听上传成功事件
const handleMediaUploaded = (event: Event) => {
  const customEvent = event as CustomEvent
  const { category } = customEvent.detail
  if (category === 'video') {
    emit('refreshMediaList')
  }
}

onMounted(() => {
  window.addEventListener('mediaUploaded', handleMediaUploaded as EventListener)
})

// 清理事件监听
onMounted(() => {
  return () => {
    window.removeEventListener('mediaUploaded', handleMediaUploaded as EventListener)
  }
})
</script>

<style lang="scss" scoped>
.video-library {
  display: flex;
  flex-direction: column;
  padding: 10px;
}


.content-wrapper {
  display: flex;
  gap: 20px;
  min-height: 0;
}

.video-panel {
  width: 100%;
  height: 555px;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  padding: 10px 18px 18px 18px;
  display: flex;
  flex-direction: column;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 5px;
    color: #666;

    .el-icon {
      font-size: 32px;
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
  }

  .video-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 14px;
    overflow-y: auto;
    align-items: flex-start;
  }

  .video-item {
    flex: 1 1 18%;
    max-width: 30%;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    height: 260px;
    border: 2px solid transparent;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(.4, 0, .2, 1);
    background: #f8fafd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    &:hover {
      border-color: #409eff;
      transform: translateY(-3px) scale(1.02);
      box-shadow: 0 6px 18px rgba(64, 158, 255, 0.10);
    }

    &.selected {
      border-color: #409eff;
      background: #ecf5ff;
    }

    .video-thumbnail {
      height: 110px;
      min-height: 110px;
      max-height: 110px;
      position: relative;
      width: 100%;
      overflow: hidden;
      border-radius: 10px 10px 0 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 10px 10px 0 0;
      }

      .video-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.22);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s;
        cursor: pointer;

        .play-icon {
          font-size: 34px;
          color: white;
          margin-bottom: 4px;
        }

        .play-text {
          color: white;
          font-size: 12px;
          font-weight: 500;
        }
      }

      &:hover .video-overlay {
        opacity: 1;
      }

      .duration {
        position: absolute;
        bottom: 8px;
        right: 8px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 2px 8px;
        border-radius: 5px;
        font-size: 13px;
      }
    }

    .video-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;
      padding: 18px 10px 16px 10px;
      min-height: 0;
    }

    .video-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      min-height: 0;

      .video-title {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .video-meta-row {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #888;
        margin-bottom: 2px;
        gap: 6px;

        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
        }
      }

      .video-description {
        font-size: 12px;
        color: #aaa;
        margin-top: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .video-actions {
      margin-top: 6px;
      padding: 0 0 4px 0;
      border-top: 1px solid #f0f0f0;
      display: flex;
      gap: 5px;

      .el-button {
        flex: 1;
        border-radius: 7px;
        font-size: 14px;
      }
    }
  }
}

.video-preview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.video-player {
  text-align: center;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-preview-info {
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;

  h3 {
    margin: 0 0 16px 0;
    color: #303133;
  }

  .preview-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;

      strong {
        color: #303133;
      }
    }
  }
}

.preview-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
