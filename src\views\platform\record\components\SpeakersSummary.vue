<template>
  <div class="speakers-container">
    <div v-if="statements" class="speakers-grid">
      <div
        v-for="(speaker, index) in formattedStatements"
        :key="index"
        class="speaker-card"
        :style="{ animationDelay: `${index * 0.1}s` }"
      >
        <div class="card-header">
          <div class="avatar-wrapper">
            <div class="speaker-avatar">
              {{ speaker.name.charAt(0) }}
            </div>
            <div class="avatar-ring"></div>
          </div>
          <div class="speaker-info">
            <h4 class="speaker-name">{{ speaker.name }}</h4>
            <span class="speaker-label">发言人</span>
          </div>
        </div>
        <div class="card-content">
          <p class="speaker-summary">{{ speaker.summary }}</p>
        </div>
      </div>
    </div>

    <div v-else class="empty-state">
      <div class="empty-icon">
        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="32" cy="32" r="28" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="2"/>
          <path d="M20 28c0-2 1-4 3-4h18c2 0 3 2 3 4v8c0 2-1 4-3 4H23l-3 3v-15z" fill="#cbd5e1"/>
          <circle cx="26" cy="32" r="2" fill="#64748b"/>
          <circle cx="32" cy="32" r="2" fill="#64748b"/>
          <circle cx="38" cy="32" r="2" fill="#64748b"/>
        </svg>
      </div>
      <h3 class="empty-title">暂无发言记录</h3>
      <p class="empty-desc">系统正在分析发言内容，请稍后查看</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  statements: {
    type: String,
    default: ''
  }
})

// 格式化发言人信息，将JSON字符串转换为数组
const formattedStatements = computed(() => {
  if (!props.statements) return []
  try {
    const statements = JSON.parse(props.statements)
    return statements.map(statement => ({
      speakerId: statement.SpeakerId,
      name: statement.SpeakerName,
      summary: statement.Summary
    }))
  } catch (error) {
    console.error('解析发言人信息失败:', error)
    return []
  }
})
</script>

<style lang="scss" scoped>
.speakers-container {
  animation: fadeIn 0.6s ease-out;
}

.speakers-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr;
}

.speaker-card {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideInUp 0.5s ease-out both;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #f3f4f6;
}

.avatar-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.speaker-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.avatar-ring {
  position: absolute;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 2px solid #6366f1;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.speaker-card:hover .avatar-ring {
  opacity: 0.3;
  transform: scale(1);
  animation: pulse 2s infinite;
}

.speaker-info {
  flex: 1;
}

.speaker-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.speaker-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.card-content {
  padding: 24px;
}

.speaker-summary {
  font-size: 15px;
  color: #4b5563;
  margin: 0;
  line-height: 1.6;
  font-weight: 400;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 60px 32px;
  background: #ffffff;
  border-radius: 12px;
  border: 2px dashed #e5e7eb;
  transition: all 0.3s ease;
  animation: fadeIn 0.6s ease-out;

  &:hover {
    border-color: #d1d5db;
    background: #fafbfc;
  }
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 24px;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;

  svg {
    width: 100%;
    height: 100%;
  }
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}
</style>
