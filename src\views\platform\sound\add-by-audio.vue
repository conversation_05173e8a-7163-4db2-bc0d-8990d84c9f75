<template>
    <el-dialog center @closed="resetForm()" :style="{ top: '110px' }">
        <el-form :model="form" :rules="rules" ref="soundRef">
            <el-form-item label="声音名称" prop="soundName">
                <el-input v-model="form.soundName" placeholder="请输入声音名称" />
            </el-form-item>
            <el-form-item label="训练音频" prop="trainAudio">
                <el-row align="middle">
                    <el-col :span="7">
                        <uploadOneFile v-model="form.trainAudio" @remove="removeTrainAudio"></uploadOneFile>
                    </el-col>
                    <el-col :span="1">
                        <el-button
                            v-if="form.trainAudio"
                            icon="Close"
                            circle
                            size="small"
                            type="danger"
                            @click="removeTrainAudio"
                            style="position: absolute; top: 12px; right: -70px;"
                        ></el-button>
                    </el-col>
                    <el-col :span="16">
                        <audio v-if="trainAudioURL" :src="trainAudioURL" controls style="height: 33px;transform: translateY(20%);"></audio>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="参考音频" prop="refAudio">
                <el-row align="middle">
                    <el-col :span="7">
                        <uploadOneFile v-model="form.refAudio" @remove="removeRefAudio"></uploadOneFile>
                    </el-col>
                    <el-col :span="1">
                        <el-button
                            v-if="form.refAudio"
                            icon="Close"
                            circle
                            size="small"
                            type="danger"
                            @click="removeRefAudio"
                            style="position: absolute; top: 12px; right: -70px;"
                        ></el-button>
                    </el-col>
                    <el-col :span="16">
                        <audio v-if="refAudioURL" :src="refAudioURL" controls style="height: 33px;transform: translateY(20%);"></audio>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="参考文本" prop="refText">
                <el-input type="textarea" v-model="form.refText" placeholder="请确保参考文本与参考音频内容完全一致，以免影响文案转音频的效果。"/>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="success" @click="submit">提交</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { uploadSoundtrainAndRef } from '@/api/platform/sound';
import modal from '@/plugins/modal';
import type { FormInstance, UploadRawFile } from 'element-plus';
import { computed, ref } from 'vue';
import uploadOneFile from './upload-one-file.vue';

const form = ref<{
    soundName: string,
    trainAudio?: UploadRawFile,
    refAudio?: UploadRawFile,
    refText: string,
    deptId: number | null,
    soundFiltration: number | null
}>({
    soundName: '',
    trainAudio: undefined,
    refAudio: undefined,
    refText: '',
    deptId: null,
    soundFiltration: null
});
const rules = ref({
    soundName: [
        { required: true, message: '请输入声音名称', trigger: 'blur' }
    ],
    trainAudio: [
        { required: true, message: '请上传训练音频', trigger: 'blur' },
        {
            validator: (rule: any, value: any, callback: any) => {
                if (value && ! /\.(wav|mp3|flac|m4a)$/i.test(value.name)) {
                    callback(new Error('训练音频必须是.wav/.mp3/.flac/.m4a格式'));
                } else {
                    callback();
                }
            },
            trigger: 'change'
        }
    ],
    refAudio: [
        { required: true, message: '请上传参考音频', trigger: 'blur' },
        {
            validator: (rule: any, value: any, callback: any) => {
                if (value && ! /\.(wav|mp3|flac|m4a)$/i.test(value.name)) {
                    callback(new Error('参考音频必须是.wav/.mp3/.flac/.m4a格式'));
                } else {
                    callback();
                }
            },
            trigger: 'change'
        }
    ],
    refText: [
        { required: true, message: '请确保参考文本与参考音频内容完全一致，以免影响文案转音频的效果。', trigger: 'blur' }
    ]
});

const emit = defineEmits();

const resetForm = () => {
    form.value = {
        soundName: '',
        trainAudio: undefined,
        refAudio: undefined,
        refText: '',
        deptId: null,
        soundFiltration: null
    };
};

const refAudioURL = computed(() => form.value.refAudio ? URL.createObjectURL(form.value.refAudio) : undefined);
const trainAudioURL = computed(() => form.value.trainAudio ? URL.createObjectURL(form.value.trainAudio) : undefined);
const soundRef = ref<FormInstance>();

const removeTrainAudio = () => {
    form.value.trainAudio = undefined;
};

const removeRefAudio = () => {
    form.value.refAudio = undefined;
};

const submit = () => {
    if (!soundRef.value) return;
    soundRef.value.validate((valid, err) => {
        if (valid) {
            const formData = new FormData();
            formData.append('soundName', form.value.soundName);
            formData.append('trainAudio', form.value.trainAudio!);
            formData.append('refAudio', form.value.refAudio!);
            formData.append('refText', form.value.refText);
            formData.append('deptId', form.value.deptId ? form.value.deptId.toString() : '');
            formData.append('soundFiltration', form.value.soundFiltration ? form.value.soundFiltration.toString() : '');
            formData.forEach((value, key) => console.log(key, value));
            uploadSoundtrainAndRef(formData).then(res => {
                modal.msgSuccess('上传成功');
                emit('uploadByAudioSuccess', res);
                resetForm();
            });
        }
    });
};
</script>    