import request from '@/utils/request'

export function uploadSoundtrain(data,param,signal) {
  return request({
    url: '/platform/sound/uploadSoundtrain',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
      'repeatSubmit': false
    },
    // headers: { 'content-type': 'application/x-www-form-urlencoded'},
    data:data,
    signal:signal,
    onUploadProgress: (event) => {
      param.file.percent = event.loaded/event.total*100
      console.log("percent",param)
			param.onProgress(param.file) 
    },
    
  })
}

export function uploadSoundref(data,param,signal) {
  return request({
    url: '/platform/sound/uploadSoundref',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
      'repeatSubmit': false
    },
    // headers: { 'content-type': 'application/x-www-form-urlencoded'},
    data:data,
    signal:signal,
    onUploadProgress: (event) => {
      param.file.percent = event.loaded/event.total*100
      console.log("percent",param)
			param.onProgress(param.file) 
    },
    
  })
}

// 添加待训练声音
export function uploadSoundtrainAndRef(data) {
  return request({
    url: '/platform/sound/uploadSoundtrainAndRef',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
      'repeatSubmit': false
    },
    data: data
  })
}

// 查询声音管理列表
export function listSound(query) {
  return request({
    url: '/platform/sound/list',
    method: 'get',
    params: query
  })
}

// 查询声音管理详细
export function getSound(soundId) {
  return request({
    url: '/platform/sound/' + soundId,
    method: 'get'
  })
}

// 新增声音管理
export function addSound(data) {
  return request({
    url: '/platform/sound',
    method: 'post',
    data: data
  })
}

// 修改声音管理
export function updateSound(data) {
  return request({
    url: '/platform/sound',
    method: 'put',
    data: data
  })
}

// 删除声音管理
export function delSound(soundId) {
  return request({
    url: '/platform/sound/' + soundId,
    method: 'delete'
  })
}

// 重新训练声音模型
export function retrainSoundModel(soundId) {
  return request({
    url: '/platform/sound/retrainSoundModel/' + soundId,
    method: 'post',
  })
}

// 审核声音模型
export function soundAudit(soundId, isApproved) {
  return request({
    url: `/platform/sound/soundAudit/${soundId}?isApproved=${isApproved}`,
    method: 'post',
  });
}

/**
 * 初始化声音模型分片上传
 */
export function initSoundUpload(data) {
  return request({
    url: '/platform/sound/initUpload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    timeout: 600000
  })
}

/**
 * 上传声音模型分片
 */
export function uploadSoundChunk(data) {
  return request({
    url: '/platform/sound/uploadChunk',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
       repeatSubmit: false,
    },
    timeout: 600000
  })
}

/**
 * 完成声音模型分片上传
 */
export function completeSoundUpload(data) {
  return request({
    url: '/platform/sound/completeUpload',
    method: 'post',
    params: {
      soundName: data.soundName,
      uploadId: data.uploadId,
      gptFilePath: data.gptFilePath,
      sovitsFilePath: data.sovitsFilePath,
      refAudioFilePath: data.refAudioFilePath,
      refText: data.refText,
      deptId: data.deptId,
      soundFiltration: data.soundFiltration
    },
    timeout: 600000
  })
}