/**
 * @file templateUtils.ts
 * @description 模板管理相关的工具函数集合
 *              包含模板解析、素材同步、配置转换、数据验证等通用功能
 */

import type { Material, TemplateInfo, UpdateTemplateRequest } from '../types/template';
import type { Timeline } from '../types/videoEdit';
import { deepCopyTimeline } from './timelineUtils';

/**
 * 解析模板配置，提取其中的可变素材信息
 * 
 * 这个函数用于从模板的JSON配置中识别并提取所有标记为"来自模板"的素材片段，
 * 将它们转换为前端可以操作的Material对象数组。
 * 
 * @param template - 包含配置信息的模板对象
 * @param template.Config - JSON字符串格式的模板配置
 * @returns Material[] - 解析出的可变素材数组
 * 
 */
export function parseTemplateConfigForMaterials(template: TemplateInfo): Material[] {
  const materials: Material[] = [];

  try {
    // 如果没有配置信息，直接返回空数组
    if (!template.Config) return materials;

    // 解析JSON配置
    const config = JSON.parse(template.Config);
    
    // 解析ClipsParam，获取可变素材配置
    // 重要：ClipsParam为空"{}"表示所有素材都不可替换
    // ClipsParam有内容表示其中的素材是可替换的
    let clipsParam: Record<string, any> = {};
    if (template.ClipsParam && template.ClipsParam.trim() !== '{}') {
      try {
        clipsParam = JSON.parse(template.ClipsParam);
        console.log('🔧 解析到ClipsParam可变素材配置:', clipsParam);
      } catch (error) {
        console.warn('ClipsParam解析失败:', error);
      }
    } else {
      console.log('📋 ClipsParam为空，表示当前模板所有素材都是不可替换的固定素材');
    }

    /**
     * 处理轨道中的片段，提取模板素材信息
     * @param clips - 片段数组
     * @param type - 素材类型（视频/图片/音频）
     * @param trackIndex - 轨道索引
     */
    const handleClips = (clips: any[], type: string, trackIndex: number) => {
      clips.forEach((clip: any, clipIndex: number) => {
        // 检查是否有有效的MediaId和Title
        if (!clip.MediaId || !clip.Title) return;

        // 生成素材ID，优先使用现有的TemplateMaterialId，否则自动生成
        const materialId = clip.TemplateMaterialId || `material_${clip.MediaId.substring(0, 8)}`;
        
        // 判断是否为可变素材的新逻辑（移除IsFromTemplate的依赖）：
        // 1. 如果ClipsParam中存在该素材ID，则认为是可变素材
        // 2. 如果片段有TemplateMaterialId且TemplateReplaceable为true，则认为是模板素材
        const hasClipsParamEntry = Object.prototype.hasOwnProperty.call(clipsParam, materialId);
        const hasTemplateMaterialId = !!clip.TemplateMaterialId;
        const isTemplateReplaceable = clip.TemplateReplaceable === true;
        
        // 素材可替换的条件：
        // - ClipsParam中存在该素材ID，或者
        // - 片段明确标记为可替换（TemplateReplaceable: true）
        const isReplaceable = hasClipsParamEntry || isTemplateReplaceable;
        
        // 是否应该纳入模板素材管理：
        // - 在ClipsParam中有配置，或者
        // - 有TemplateMaterialId且标记为可替换，或者
        // - 有MediaId以$开头（表示是模板变量）
        const shouldInclude = hasClipsParamEntry || 
                              (hasTemplateMaterialId && isTemplateReplaceable) ||
                              (clip.MediaId && clip.MediaId.startsWith('$'));

        if (shouldInclude) {
          // 提取高级设置 - 从clip中提取除了基础字段外的其他设置
          const advancedSettings: Record<string, any> = {};
          const basicFields = [
            'TemplateMaterialId', 'TemplateReplaceable', 'TemplateRemark', 
            'MediaId', 'Id', 'Title', 'TimelineIn', 'TimelineOut', 
            'Duration', 'In', 'Out', 'Type', 'VirginDuration'
          ];

          // 提取所有非基础字段作为高级设置
          Object.keys(clip).forEach(key => {
            if (!basicFields.includes(key) && clip[key] !== undefined && clip[key] !== null) {
              advancedSettings[key] = clip[key];
            }
          });

          // 创建素材对象
          materials.push({
            name: clip.Title || `${type}片段${trackIndex}-${clipIndex}`, // 显示名称
            type, // 素材类型
            replace: isReplaceable, // 是否可替换
            idNum: materialId, // 素材唯一标识
            remark: clip.TemplateRemark || '', // 使用原有备注，不凭空生成
            action: isReplaceable ? '高级设置' : '', // 只有可替换的素材才显示高级设置
            advancedSettings // 使用提取的高级设置
          });

          console.log(`🎯 识别到素材: ${clip.Title} (ID: ${materialId}, 可替换: ${isReplaceable})`);
        }
      });
    };

    // 遍历各种类型的轨道
    if (config.VideoTracks) {
      config.VideoTracks.forEach((track: any, i: number) =>
        track.VideoTrackClips && handleClips(track.VideoTrackClips, '视频', i)
      );
    }
    if (config.ImageTracks) {
      config.ImageTracks.forEach((track: any, i: number) =>
        track.ImageTrackClips && handleClips(track.ImageTrackClips, '图片', i)
      );
    }
    if (config.AudioTracks) {
      config.AudioTracks.forEach((track: any, i: number) =>
        track.AudioTrackClips && handleClips(track.AudioTrackClips, '音频', i)
      );
    }

    // **修改智能检测逻辑**：ClipsParam为空时，显示所有素材供用户选择设置
    if (materials.length === 0 && Object.keys(clipsParam).length === 0) {
      console.log('📋 ClipsParam为空，将展示所有素材供用户选择是否设为可替换');
      
      // 将所有有效的媒体片段都添加到素材管理，让用户自己决定是否可替换
      const detectAllMaterials = (clips: any[], type: string, trackIndex: number) => {
        clips.forEach((clip: any, clipIndex: number) => {
          if (clip.MediaId && clip.Title && clip.Duration > 0) {
            const materialId = `material_${clip.MediaId.substring(0, 8)}`;
            
            const advancedSettings: Record<string, any> = {};
            const basicFields = [
              'MediaId', 'Id', 'Title', 'TimelineIn', 'TimelineOut', 
              'Duration', 'In', 'Out', 'Type', 'VirginDuration'
            ];
            
            Object.keys(clip).forEach(key => {
              if (!basicFields.includes(key) && clip[key] !== undefined && clip[key] !== null) {
                advancedSettings[key] = clip[key];
              }
            });

            materials.push({
              name: clip.Title,
              type,
              replace: false, // 默认不可替换，让用户自己设置
              idNum: materialId,
              remark: clip.TemplateRemark || '', // 不生成备注，使用原有的或留空
              action: '', // 默认没有操作，用户设置为可替换后才有高级设置
              advancedSettings
            });

            console.log(`� 发现素材: ${clip.Title} (${type}, 待用户设置是否可替换)`);
          }
        });
      };

      // 对所有轨道进行素材检测，让用户自己决定是否设为可替换
      if (config.VideoTracks) {
        config.VideoTracks.forEach((track: any, i: number) =>
          track.VideoTrackClips && detectAllMaterials(track.VideoTrackClips, '视频', i)
        );
      }
      if (config.ImageTracks) {
        config.ImageTracks.forEach((track: any, i: number) =>
          track.ImageTrackClips && detectAllMaterials(track.ImageTrackClips, '图片', i)
        );
      }
      if (config.AudioTracks) {
        config.AudioTracks.forEach((track: any, i: number) =>
          track.AudioTrackClips && detectAllMaterials(track.AudioTrackClips, '音频', i)
        );
      }
    }
    if (config.AudioTracks) {
      config.AudioTracks.forEach((track: any, i: number) =>
        track.AudioTrackClips && handleClips(track.AudioTrackClips, '音频', i)
      );
    }
  } catch (e) {
    console.error('解析模板配置失败:', e);
  }

  console.log(`✅ 模板素材解析完成，共识别到 ${materials.length} 个素材:`, materials.map(m => ({
    name: m.name,
    type: m.type,
    replace: m.replace,
    idNum: m.idNum
  })));

  return materials;
}

/**
 * 将当前时间轴数据转换为模板配置格式
 * 
 * 这个函数将编辑器中的时间轴数据转换回可以保存的模板配置JSON格式。
 * 它会根据用户对素材的设置（是否可替换、备注等）更新时间轴中的相关字段，
 * 并为可替换素材生成特殊的MediaId格式。
 * 
 * @param timeline - 当前的时间轴数据对象
 * @param templateMaterials - 用户设置的模板素材数组
 * @returns string - 序列化后的模板配置JSON字符串
 * 
 * @throws {Error} 当时间线数据不存在时抛出错误
 * 
 */
export function convertTimelineToTemplateConfig(timeline: Timeline, templateMaterials: Material[]): string {
  if (!timeline) throw new Error('时间线数据不存在，无法转换为模板配置');

  // 创建时间轴的深拷贝，避免修改原始数据
  const templateConfig = deepCopyTimeline(timeline);

  // 创建素材ID到素材对象的映射，便于快速查找
  const materialMap = new Map<string, Material>();
  templateMaterials.forEach(m => m.idNum && materialMap.set(m.idNum, m));

  /**
   * 同步单个片段的素材状态
   * 根据用户在素材管理中的设置，更新片段的模板相关属性
   * @param clip - 要处理的片段对象
   */
  const syncMaterialToClip = (clip: any) => {
    if (clip.IsFromTemplate) {
      // 获取或生成素材ID
      const materialId = clip.TemplateMaterialId || `auto_${clip.MediaId?.substring(0, 6) || clip.Id}`;
      const material = materialMap.get(materialId);

      if (material) {
        // 同步素材设置到片段
        clip.TemplateReplaceable = material.replace !== false; // 是否可替换
        clip.TemplateRemark = material.remark || clip.TemplateRemark || ''; // 备注信息
        if (!clip.TemplateMaterialId) clip.TemplateMaterialId = materialId; // 设置素材ID

        // **修复**：同步高级设置 - 只复制安全的、与官方兼容的字段
        if (material.advancedSettings && Object.keys(material.advancedSettings).length > 0) {
          // 定义允许的安全字段白名单（与 user1.mp4 结构一致）
          const safeFields = ['AdaptMode', '替换视频时长大于模板槽位', '替换视频时长小于模板槽位', '替换视频缩放尺寸'];

          safeFields.forEach(field => {
            if (material.advancedSettings![field] !== undefined) {
              clip[field] = material.advancedSettings![field];
            }
          });

          // 清理可能存在的问题字段
          const problematicFields = ['LongVideoHandling', 'ShortVideoHandling', 'Adapt_type', 'Effects'];
          problematicFields.forEach(field => {
            if (clip[field] !== undefined) {
              delete clip[field];
            }
          });
        }
      }

      // 如果素材可替换，更新MediaId格式为 $素材ID:原始ID
      if (clip.TemplateReplaceable && clip.TemplateMaterialId) {
        const originalMediaId = clip.MediaId.replace(/^\$.*?:/, ''); // 提取原始MediaId
        clip.MediaId = `$${clip.TemplateMaterialId}:${originalMediaId}`; // 生成新格式
      }
    }
  };

  // 遍历所有轨道类型并同步素材状态
  if (templateConfig.VideoTracks) {
    templateConfig.VideoTracks.forEach((track: any) => {
      if (track.VideoTrackClips) {
        track.VideoTrackClips.forEach(syncMaterialToClip);
      }
    });
  }
  if (templateConfig.ImageTracks) {
    templateConfig.ImageTracks.forEach((track: any) => {
      if (track.ImageTrackClips) {
        track.ImageTrackClips.forEach(syncMaterialToClip);
      }
    });
  }
  if (templateConfig.AudioTracks) {
    templateConfig.AudioTracks.forEach((track: any) => {
      if (track.AudioTrackClips) {
        track.AudioTrackClips.forEach(syncMaterialToClip);
      }
    });
  }

  return JSON.stringify(templateConfig);
}

/**
 * 根据模板素材构建ClipsParam对象
 * 
 * ClipsParam是用于模板渲染的参数对象，它定义了每个可替换素材ID对应的实际MediaId。
 * 这个函数会遍历时间轴数据，收集所有模板素材的ID映射关系，
 * 然后根据用户设置的可替换状态生成最终的参数对象。
 * 
 * @param templateMaterials - 用户配置的模板素材数组
 * @param timeline - 当前的时间轴数据
 * @returns string - 序列化后的ClipsParam JSON字符串
 */
export function buildClipsParamFromMaterials(templateMaterials: Material[], timeline: Timeline): string {
  const clipsParam: Record<string, string> = {};
  // 创建素材ID到MediaId的映射表
  const materialIdToMediaId = new Map<string, string>();

  /**
   * 从片段数组中收集素材ID映射关系
   * @param clips - 要处理的片段数组
   */
  const collectMaterialIds = (clips: any[]) => {
    clips.forEach((clip: any) => {
      if (clip.IsFromTemplate && clip.MediaId) {
        // 提取原始MediaId（去除$前缀部分）
        const originalMediaId = clip.MediaId.replace(/^\$.*?:/, '');
        // 获取或生成素材ID
        const materialId = clip.TemplateMaterialId || `auto_${clip.MediaId?.substring(0, 6) || clip.Id}`;
        // 建立映射关系
        materialIdToMediaId.set(materialId, originalMediaId);
      }
    });
  };

  // 遍历所有轨道类型，收集素材映射
  if (timeline.VideoTracks) {
    timeline.VideoTracks.forEach((track: any) => {
      if (track.VideoTrackClips) collectMaterialIds(track.VideoTrackClips);
    });
  }
  if (timeline.ImageTracks) {
    timeline.ImageTracks.forEach((track: any) => {
      if (track.ImageTrackClips) collectMaterialIds(track.ImageTrackClips);
    });
  }
  if (timeline.AudioTracks) {
    timeline.AudioTracks.forEach((track: any) => {
      if (track.AudioTrackClips) collectMaterialIds(track.AudioTrackClips);
    });
  }

  // 根据素材设置生成ClipsParam
  templateMaterials.forEach((material) => {
    // 只处理有ID且标记为可替换的素材
    if (material.idNum && material.replace) {
      const mediaId = materialIdToMediaId.get(material.idNum);
      if (mediaId) {
        clipsParam[material.idNum] = mediaId;
      } else {
        console.warn(`警告：无法从timeline中找到可替换素材 ${material.idNum} 对应的MediaId，使用占位符`);
        clipsParam[material.idNum] = "mediaId"; // 使用占位符
      }
    }
  });

  return JSON.stringify(clipsParam);
}

/**
 * 同步模板素材与时间线数据的一致性
 * 
 * 这个函数用于检查当前的模板素材列表与时间轴中实际的模板片段是否同步。
 * 它会比较数量和ID匹配情况，判断是否需要重新同步数据。
 * 通常在保存模板前调用，确保数据一致性。
 * 
 * @param templateMaterials - 当前的模板素材数组
 * @param timeline - 当前的时间轴数据
 * @returns boolean - true表示需要同步（数据不一致），false表示数据已同步
 * 
 * ```
 */
export function syncMaterialsWithTimeline(templateMaterials: Material[], timeline: Timeline): boolean {
  if (!timeline) {
    console.warn('时间线数据不存在，无法同步');
    return false;
  }

  // 收集时间轴中所有的模板片段信息
  const timelineTemplateClips: any[] = [];

  /**
   * 从指定轨道收集模板片段信息
   * @param clips - 片段数组
   * @param type - 片段类型（视频/图片/音频）
   */
  const collectTemplateClips = (clips: any[], type: string) => {
    clips.forEach((clip: any) => {
      if (clip.IsFromTemplate) {
        timelineTemplateClips.push({
          type,
          mediaId: clip.MediaId,
          id: clip.Id,
          templateMaterialId: clip.TemplateMaterialId,
          // 生成自动ID，用于比较
          autoGeneratedId: clip.TemplateMaterialId || `auto_${clip.MediaId?.substring(0, 6) || clip.Id}`
        });
      }
    });
  };

  // 遍历所有轨道类型，收集模板片段
  if (timeline.VideoTracks) {
    timeline.VideoTracks.forEach((track: any) => {
      if (track.VideoTrackClips) collectTemplateClips(track.VideoTrackClips, '视频');
    });
  }
  if (timeline.ImageTracks) {
    timeline.ImageTracks.forEach((track: any) => {
      if (track.ImageTrackClips) collectTemplateClips(track.ImageTrackClips, '图片');
    });
  }
  if (timeline.AudioTracks) {
    timeline.AudioTracks.forEach((track: any) => {
      if (track.AudioTrackClips) collectTemplateClips(track.AudioTrackClips, '音频');
    });
  }

  // 创建ID集合用于比较
  const currentMaterialIds = new Set(templateMaterials.map(m => m.idNum));
  const timelineGeneratedIds = new Set(timelineTemplateClips.map(clip => clip.autoGeneratedId));

  // 检查数量是否匹配
  const countMatches = timelineTemplateClips.length === templateMaterials.length;
  // 检查ID集合是否匹配
  const idsMatch = timelineGeneratedIds.size === currentMaterialIds.size &&
    [...timelineGeneratedIds].every(id => currentMaterialIds.has(id));

  // 如果数量或ID不匹配，则需要同步
  const needsSync = !countMatches || !idsMatch;

  // 输出详细的不同步信息用于调试
  if (needsSync) {
    console.warn('🔄 检测到数据不同步:', {
      timelineTemplateClipsCount: timelineTemplateClips.length,
      currentMaterialsCount: templateMaterials.length,
      countMatches,
      idsMatch,
      timelineIds: Array.from(timelineGeneratedIds),
      materialIds: Array.from(currentMaterialIds)
    });
  }

  return needsSync;
}

/**
 * 构建更新模板的请求对象
 * 
 * 这个函数将所有必要的数据组合成一个完整的更新模板请求对象。
 * 它会调用配置转换函数，生成最新的模板配置，然后包装成API请求格式。
 * 
 * @param templateId - 要更新的模板ID
 * @param templateName - 模板名称
 * @param timeline - 当前的时间轴数据
 * @param templateMaterials - 模板素材配置
 * @returns UpdateTemplateRequest - 完整的更新请求对象
 * 
 * @throws {Error} 当模板ID不存在时抛出错误
 * // 返回: { TemplateId: "template123", Config: "...", Name: "我的模板" }
 * ```
 */
export function buildUpdateTemplateRequest(
  templateId: string,
  templateName: string,
  timeline: Timeline,
  templateMaterials: Material[]
): UpdateTemplateRequest {
  if (!templateId) {
    throw new Error('模板ID不存在，无法构建更新请求');
  }

  // 将时间轴数据转换为模板配置格式
  const config = convertTimelineToTemplateConfig(timeline, templateMaterials);

  return {
    TemplateId: templateId,    // 模板唯一标识
    Config: config,            // 序列化的模板配置
    Name: templateName,        // 模板显示名称
  };
}

/**
 * 验证数据完整性
 * 
 * 这个函数对模板保存前的关键数据进行完整性检查，
 * 确保所有必要的信息都已准备就绪，避免保存过程中出现错误。
 * 它会检查时间线、素材、模板信息的完整性，并识别潜在的同步问题。
 * 
 * @param timeline - 时间轴数据，可能为null
 * @param templateMaterials - 模板素材数组
 * @param templateId - 模板ID字符串
 * @param templateName - 模板名称字符串
 * @returns 包含验证结果的详细信息对象
 * 
 * @example
 * ```typescript
 * const validation = validateDataIntegrity(timeline, materials, "123", "模板名");
 * if (validation.issues.length > 0) {
 *   console.warn('发现问题:', validation.issues);
 * }
 * // 返回: { timelineExists: true, materialsCount: 3, issues: [] }
 * ```
 */
export function validateDataIntegrity(
  timeline: Timeline | null,
  templateMaterials: Material[],
  templateId: string,
  templateName: string
) {
  const result = {
    timelineExists: !!timeline,           // 时间线是否存在
    materialsCount: templateMaterials.length,  // 素材数量
    templateIdExists: !!templateId,       // 模板ID是否存在
    templateNameExists: !!templateName,   // 模板名称是否存在
    issues: [] as string[]                // 发现的问题列表
  };

  // 检查各项数据的完整性
  if (!timeline) {
    result.issues.push('时间线数据不存在');
  }
  if (templateMaterials.length === 0) {
    result.issues.push('模板素材数据为空');
  }
  if (!templateId) {
    result.issues.push('编辑模板ID不存在');
  }
  if (!templateName) {
    result.issues.push('模板名称不存在');
  }

  // 如果时间线存在，检查数据同步状态
  if (timeline && syncMaterialsWithTimeline(templateMaterials, timeline)) {
    result.issues.push('模板素材与时间线数据不同步');
  }

  return result;
}

/**
 * 将单个素材的设置同步回时间轴数据
 * 
 * 这个函数用于将前端 templateMaterials 中的修改同步到 timeline 数据中，
 * 确保高级设置、备注、可替换状态等修改能够反映到实际的时间轴片段中。
 * 
 * @param timeline - 时间轴数据
 * @param material - 要同步的素材
 * @returns boolean - 是否成功找到并同步了对应的片段
 * 
 * @example
 * ```typescript
 * const material = { idNum: "c8eb82", replace: true, remark: "新备注" };
 * const success = syncSingleMaterialToTimeline(timeline, material);
 * if (success) {
 *   console.log('素材设置已同步到时间轴');
 * }
 * ```
 */
export function syncSingleMaterialToTimeline(timeline: Timeline, material: Material): boolean {
  if (!timeline || !material.idNum) {
    console.warn('时间轴或素材ID不存在，无法同步');
    return false;
  }

  let found = false;

  /**
   * 同步单个片段的素材状态
   * @param clip - 要处理的片段对象
   */
  const syncClip = (clip: any) => {
    // 匹配逻辑：优先使用 TemplateMaterialId，否则生成自动ID
    const clipMaterialId = clip.TemplateMaterialId || `auto_${clip.MediaId?.substring(0, 6) || clip.Id}`;

    if (clipMaterialId === material.idNum) {
      // 同步素材设置到片段
      clip.TemplateReplaceable = material.replace !== false; // 是否可替换
      clip.TemplateRemark = material.remark || ''; // 备注信息

      // 如果原来没有 TemplateMaterialId，则设置它
      if (!clip.TemplateMaterialId) {
        clip.TemplateMaterialId = material.idNum;
      }

      // 标记为来自模板（如果还没有标记的话）
      if (!clip.IsFromTemplate) {
        clip.IsFromTemplate = true;
      }

      // **修复**：安全地同步高级设置 - 只复制允许的字段
      if (material.advancedSettings && Object.keys(material.advancedSettings).length > 0) {
        // 定义允许的安全字段白名单（与 user1.mp4 结构一致）
        const safeFields = ['AdaptMode', '替换视频时长大于模板槽位', '替换视频时长小于模板槽位', '替换视频缩放尺寸'];

        safeFields.forEach(field => {
          if (material.advancedSettings![field] !== undefined) {
            clip[field] = material.advancedSettings![field];
          }
        });

        // 清理可能存在的问题字段
        const problematicFields = ['LongVideoHandling', 'ShortVideoHandling', 'Adapt_type', 'Effects'];
        problematicFields.forEach(field => {
          if (clip[field] !== undefined) {
            delete clip[field];
          }
        });
      }

      // **新增**：如果素材可替换，更新MediaId格式
      if (clip.TemplateReplaceable && clip.TemplateMaterialId) {
        const originalMediaId = clip.MediaId.replace(/^\$.*?:/, ''); // 提取原始MediaId
        const newMediaId = `$${clip.TemplateMaterialId}:${originalMediaId}`;
        if (clip.MediaId !== newMediaId) {
          clip.MediaId = newMediaId;
        }
      }

      found = true;
    }
  };

  // 遍历所有轨道类型并同步
  if (timeline.VideoTracks) {
    timeline.VideoTracks.forEach((track: any) => {
      if (track.VideoTrackClips) {
        track.VideoTrackClips.forEach(syncClip);
      }
    });
  }

  if (timeline.AudioTracks) {
    timeline.AudioTracks.forEach((track: any) => {
      if (track.AudioTrackClips) {
        track.AudioTrackClips.forEach(syncClip);
      }
    });
  }

  if (timeline.ImageTracks) {
    timeline.ImageTracks.forEach((track: any) => {
      if (track.ImageTrackClips) {
        track.ImageTrackClips.forEach(syncClip);
      }
    });
  }

  if (timeline.SubtitleTracks) {
    timeline.SubtitleTracks.forEach((track: any) => {
      if (track.SubtitleTrackClips) {
        track.SubtitleTrackClips.forEach(syncClip);
      }
    });
  }

  if (!found) {
    console.warn(`未能在时间轴中找到素材 ${material.idNum} 对应的片段`);
  }

  return found;
}

/**
 * @function refreshMaterialsFromTimeline
 * @description 从时间轴重新解析模板素材，用于时间轴操作后的数据同步
 * @param {Timeline} timeline - 当前时间轴数据
 * @returns {Material[]} 重新解析的素材数组
 */
export function refreshMaterialsFromTimeline(timeline: Timeline): Material[] {
  if (!timeline) {
    console.warn('时间轴数据不存在，无法刷新素材');
    return [];
  }

  const materials: Material[] = [];

  /**
   * 从片段数组中提取模板素材
   */
  const extractMaterials = (clips: any[], type: string) => {
    clips.forEach((clip: any, index: number) => {
      if (clip.IsFromTemplate) {
        const materialId = clip.TemplateMaterialId || `auto_${clip.MediaId?.substring(0, 6) || clip.Id}`;

        // **修复**: 正确提取高级设置
        const advancedSettings: Record<string, any> = {};
        const basicFields = ['IsFromTemplate', 'TemplateMaterialId', 'TemplateReplaceable', 'TemplateRemark', 'MediaId', 'Id', 'Title'];

        // 提取所有非基础字段作为高级设置
        Object.keys(clip).forEach(key => {
          if (!basicFields.includes(key) && clip[key] !== undefined && clip[key] !== null) {
            advancedSettings[key] = clip[key];
          }
        });

        materials.push({
          name: clip.Title || `${type}片段${index}`,
          type,
          replace: clip.TemplateReplaceable || false,
          idNum: materialId,
          remark: clip.TemplateRemark || '',
          action: clip.TemplateReplaceable ? '高级设置' : '',
          advancedSettings // **修复**: 使用提取的高级设置
        });
      }
    });
  };

  // 遍历所有轨道类型
  if (timeline.VideoTracks) {
    timeline.VideoTracks.forEach((track: any) => {
      if (track.VideoTrackClips) extractMaterials(track.VideoTrackClips, '视频');
    });
  }
  if (timeline.AudioTracks) {
    timeline.AudioTracks.forEach((track: any) => {
      if (track.AudioTrackClips) extractMaterials(track.AudioTrackClips, '音频');
    });
  }
  if (timeline.ImageTracks) {
    timeline.ImageTracks.forEach((track: any) => {
      if (track.ImageTrackClips) extractMaterials(track.ImageTrackClips, '图片');
    });
  }

  console.log(`🔄 从时间轴重新解析得到 ${materials.length} 个模板素材`);
  return materials;
}

/**
 * 构建"另存为模板"的请求数据
 * 专门用于将当前的时间轴转换为新模板，与更新现有模板不同
 * 会自动检测可变素材并生成相应的模板结构
 * 
 * @param timeline - 当前的时间轴数据
 * @param templateName - 新模板的名称
 * @param coverUrl - 封面图片URL（可选）
 * @param previewMediaId - 预览媒体ID（可选）
 * @returns AddTemplateRequest - 添加模板的请求对象
 */
export function buildSaveAsTemplateRequest(
  timeline: Timeline,
  templateName: string,
  coverUrl?: string,
  previewMediaId?: string | null
): any {
  if (!timeline) {
    throw new Error('时间线数据不存在，无法创建模板');
  }

  // 深拷贝时间轴数据，避免修改原数据
  const templateConfig = deepCopyTimeline(timeline);
  
  // 收集所有媒体ID和可变素材信息
  const allMediaIds = new Set<string>();
  const variableClips: Array<{
    clip: any,
    type: 'video' | 'audio' | 'image',
    trackIndex: number,
    clipIndex: number
  }> = [];

  /**
   * 处理轨道中的片段，收集媒体ID并标识可变素材
   * @param clips - 片段数组
   * @param type - 媒体类型
   * @param trackIndex - 轨道索引
   */
  const processClips = (clips: any[], type: 'video' | 'audio' | 'image', trackIndex: number) => {
    clips.forEach((clip: any, clipIndex: number) => {
      // 收集所有有效的媒体ID
      if (clip.MediaId && !clip.MediaId.startsWith('$')) {
        allMediaIds.add(clip.MediaId);
        
        // 更完善的可变素材检测规则：
        // 1. 有有效的MediaId（非占位符、非空）
        // 2. 有标题或文件名（确保可识别）
        // 3. 有时长（确保是有效媒体）
        // 4. 不是已经标记为模板的素材（避免重复处理）
        const hasValidMediaId = clip.MediaId && clip.MediaId.trim() !== '';
        const hasTitle = clip.Title || clip.FileName;
        const hasDuration = clip.Duration && clip.Duration > 0;
        const notFromTemplate = !clip.IsFromTemplate;
        
        if (hasValidMediaId && hasTitle && hasDuration && notFromTemplate) {
          variableClips.push({
            clip,
            type,
            trackIndex,
            clipIndex
          });
        }
      }
    });
  };

  // 遍历所有轨道收集信息，确保处理所有轨道类型
  // 视频轨道
  if (templateConfig.VideoTracks) {
    templateConfig.VideoTracks.forEach((track: any, trackIndex: number) => {
      if (track.VideoTrackClips && Array.isArray(track.VideoTrackClips)) {
        processClips(track.VideoTrackClips, 'video', trackIndex);
      }
    });
  }

  // 音频轨道
  if (templateConfig.AudioTracks) {
    templateConfig.AudioTracks.forEach((track: any, trackIndex: number) => {
      if (track.AudioTrackClips && Array.isArray(track.AudioTrackClips)) {
        processClips(track.AudioTrackClips, 'audio', trackIndex);
      }
    });
  }

  // 图片轨道
  if (templateConfig.ImageTracks) {
    templateConfig.ImageTracks.forEach((track: any, trackIndex: number) => {
      if (track.ImageTrackClips && Array.isArray(track.ImageTrackClips)) {
        processClips(track.ImageTrackClips, 'image', trackIndex);
      }
    });
  }

  // 字幕轨道（如果存在的话，也可能包含媒体文件）
  if (templateConfig.SubtitleTracks) {
    templateConfig.SubtitleTracks.forEach((track: any, trackIndex: number) => {
      if (track.SubtitleTrackClips && Array.isArray(track.SubtitleTrackClips)) {
        track.SubtitleTrackClips.forEach((clip: any, clipIndex: number) => {
          if (clip.MediaId && !clip.MediaId.startsWith('$')) {
            allMediaIds.add(clip.MediaId);
          }
        });
      }
    });
  }

  // 构建 RelatedMediaids
  const relatedMediaIds = {
    video: [] as string[],
    audio: [] as string[],
    image: [] as string[]
  };

  // 构建 Variables
  const variables: Record<string, any> = {};

  // 处理可变素材，生成模板占位符
  variableClips.forEach(({ clip, type, trackIndex, clipIndex }) => {
    // 生成更清晰的素材ID，包含类型和位置信息
    const materialId = `${type}_${trackIndex}_${clipIndex}_${Date.now().toString().slice(-6)}`;
    
    // 保存原始MediaId用于恢复
    const originalMediaId = clip.MediaId;
    
    // 标记为模板素材，设置完整的模板属性
    clip.IsFromTemplate = true;
    clip.TemplateMaterialId = materialId;
    clip.TemplateReplaceable = true;
    clip.TemplateRemark = clip.Title || clip.FileName || `${type}素材`;
    
    // 保留原始素材的重要属性
    const originalFileName = clip.FileName;
    const originalTitle = clip.Title;
    const originalDuration = clip.Duration;
    
    // 修改MediaId为模板格式: $素材ID:原始MediaId
    clip.MediaId = `$${materialId}:${originalMediaId}`;
    
    // 添加到相关媒体ID，按类型分类
    relatedMediaIds[type].push(originalMediaId);
    
    // 添加到变量定义，包含更完整的元数据
    variables[materialId] = {
      Type: type === 'video' ? 'Video' : type === 'audio' ? 'Audio' : 'Image',
      DefaultValue: originalMediaId,
      Title: originalTitle,
      FileName: originalFileName,
      Duration: originalDuration,
      Description: `${type}素材 - ${originalTitle || originalFileName || '未命名'}`
    };
    
    console.log(`🔄 处理可变素材: ${materialId} -> ${originalMediaId} (${type})`);
  });

  // 构建最终的模板请求，确保包含所有必要字段
  const templateRequest = {
    Name: templateName,
    Type: 'Timeline',
    Config: JSON.stringify(templateConfig),
    CoverUrl: coverUrl || undefined,
    PreviewMedia: previewMediaId || undefined,
    
    // 只有在有相关媒体ID时才添加该字段
    RelatedMediaids: Object.values(relatedMediaIds).some(arr => arr.length > 0)
      ? JSON.stringify(relatedMediaIds)
      : undefined,
    
    // 只有在有变量定义时才添加该字段
    Variables: Object.keys(variables).length > 0 
      ? JSON.stringify(variables)
      : undefined,
      
    Source: 'WebSDK',
    Version: '1.0.0', // 添加版本信息用于追踪
    CreatedAt: new Date().toISOString(), // 添加创建时间
    
    // 添加统计信息用于调试
    Statistics: {
      TotalMediaIds: allMediaIds.size,
      VariableClips: variableClips.length,
      VideoTracks: templateConfig.VideoTracks?.length || 0,
      AudioTracks: templateConfig.AudioTracks?.length || 0,
      ImageTracks: templateConfig.ImageTracks?.length || 0
    }
  };

  console.log('🎯 构建另存为模板请求 (已优化):', {
    templateName,
    totalMediaIds: allMediaIds.size,
    variableClipsCount: variableClips.length,
    relatedMediaIds,
    variablesCount: Object.keys(variables).length,
    statistics: templateRequest.Statistics
  });

  return templateRequest;
}

/**
 * 智能检测时间轴中的可变素材
 * 根据素材特征自动判断哪些片段应该作为可替换的模板素材
 * 
 * @param timeline - 时间轴数据
 * @returns 检测到的可变素材信息数组
 */
export function detectVariableMaterials(timeline: Timeline): Array<{
  mediaId: string;
  title: string;
  type: 'video' | 'audio' | 'image';
  trackIndex: number;
  clipIndex: number;
  duration: number;
  timelineIn: number;
}> {
  const variableMaterials: Array<{
    mediaId: string;
    title: string;
    type: 'video' | 'audio' | 'image';
    trackIndex: number;
    clipIndex: number;
    duration: number;
    timelineIn: number;
  }> = [];

  if (!timeline) return variableMaterials;

  /**
   * 检测片段是否应该作为可变素材
   * @param clip - 片段对象
   * @param type - 媒体类型
   * @param trackIndex - 轨道索引
   * @param clipIndex - 片段索引
   */
  const detectClip = (clip: any, type: 'video' | 'audio' | 'image', trackIndex: number, clipIndex: number) => {
    // 更完善的检测规则（与 buildSaveAsTemplateRequest 保持一致）：
    // 1. 有有效的MediaId（非占位符、非空）
    // 2. 有标题或文件名（确保可识别）
    // 3. 有时长（确保是有效媒体）
    // 4. 不是已经标记为模板的素材（避免重复处理）
    const hasValidMediaId = clip.MediaId && 
                           !clip.MediaId.startsWith('$') && 
                           clip.MediaId.trim() !== '';
    const hasTitle = clip.Title || clip.FileName;
    const hasDuration = clip.Duration && clip.Duration > 0;
    const notFromTemplate = !clip.IsFromTemplate;
    
    if (hasValidMediaId && hasTitle && hasDuration && notFromTemplate) {
      variableMaterials.push({
        mediaId: clip.MediaId,
        title: clip.Title || clip.FileName || `${type}片段${trackIndex}-${clipIndex}`,
        type,
        trackIndex,
        clipIndex,
        duration: clip.Duration || 0,
        timelineIn: clip.TimelineIn || 0
      });
    }
  };

  // 遍历所有轨道检测可变素材
  if (timeline.VideoTracks) {
    timeline.VideoTracks.forEach((track: any, trackIndex: number) => {
      if (track.VideoTrackClips) {
        track.VideoTrackClips.forEach((clip: any, clipIndex: number) => {
          detectClip(clip, 'video', trackIndex, clipIndex);
        });
      }
    });
  }

  if (timeline.AudioTracks) {
    timeline.AudioTracks.forEach((track: any, trackIndex: number) => {
      if (track.AudioTrackClips) {
        track.AudioTrackClips.forEach((clip: any, clipIndex: number) => {
          detectClip(clip, 'audio', trackIndex, clipIndex);
        });
      }
    });
  }

  if (timeline.ImageTracks) {
    timeline.ImageTracks.forEach((track: any, trackIndex: number) => {
      if (track.ImageTrackClips) {
        track.ImageTrackClips.forEach((clip: any, clipIndex: number) => {
          detectClip(clip, 'image', trackIndex, clipIndex);
        });
      }
    });
  }

  console.log(`🔍 检测到 ${variableMaterials.length} 个可变素材:`, variableMaterials);
  return variableMaterials;
}
