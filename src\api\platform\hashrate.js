import request from '@/utils/request'

// 查询用户算力点数列表
export function listHashrate(query) {
  return request({
    url: '/platform/hashrate/list',
    method: 'get',
    params: query
  })
}

// 查询用户算力点数详细
export function getHashrate(hashrateId) {
  return request({
    url: '/platform/hashrate/' + hashrateId,
    method: 'get'
  })
}

// 新增用户算力点数
export function addHashrate(data) {
  return request({
    url: '/platform/hashrate',
    method: 'post',
    data: data
  })
}

// 修改用户算力点数
export function updateHashrate(data) {
  return request({
    url: '/platform/hashrate',
    method: 'put',
    data: data
  })
}

// 删除用户算力点数
export function delHashrate(hashrateId) {
  return request({
    url: '/platform/hashrate/' + hashrateId,
    method: 'delete'
  })
}

// 充值用户算力点数
export function topHashrate(data) {
  return request({
    url: '/platform/hashrate/top',
    method: 'post',
    data: data
  })
}

// 强制删除算力用户 消费 充值记录
export function deleteByUserName(userName) {
  return request({
    url: `/platform/hashrate/deleteByUserName/${userName}`, 
    method: 'delete'
  })
}

//初始化用户
export function initializeUserHashrate(userName) {
  return request({
    url: `/platform/hashrate/initializeUserHashrate/${userName}`, 
    method: 'post'
  })
}

// 批量创建卡号
export function generateAndExport(count, balance) {
  return request({
    url: `/platform/hashrate/generateAndExport/${count}/${balance}`, 
    method: 'post'
  })
}