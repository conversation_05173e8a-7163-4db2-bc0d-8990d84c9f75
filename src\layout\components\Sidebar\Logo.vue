<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <h1 v-else class="sidebar-title">{{ title }}</h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <h1 class="sidebar-title">{{ title }}</h1>
      </router-link>
    </transition>
  </div>
</template>

<script setup>
import logoImg from '@/assets/logo/logo.png'
// import useSettingsStore from '@/store/modules/settings'

const props = defineProps({
  collapse: {
    type: Boolean,
    required: true
  }
})

// const settingsStore = useSettingsStore()
const title = ref("数智宝")
const logo = ref(logoImg)
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: transparent;
  text-align: center;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 12px;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      transition: all 0.3s ease;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #1f2937;
      font-weight: 600;
      font-size: 16px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
      vertical-align: middle;
      transition: all 0.3s ease;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin: 0 auto;
    }
    
    .sidebar-title {
      display: none;
    }
  }
}

// 暗色主题适配
:deep(.theme-dark) {
  .sidebar-logo-container {
    .sidebar-title {
      color: #e5e7eb;
    }
  }
}
</style>