import { Directive } from "vue"

const DISTANCE = 100
const DURATION = 300
const map = new WeakMap()
const ob = new IntersectionObserver(entries => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const animation = map.get(entry.target)
            animation && animation.play()
            ob.unobserve(entry.target)
        }

    })
})
function isBelowViewport(el: Element) {
    const rect = el.getBoundingClientRect()
    return rect.top > (window.innerHeight || document.documentElement.clientHeight)
}
const vSlideIn: Directive = {
    mounted(el) {
        if (isBelowViewport(el)) {
            return
        }
        const animation = el.animate([
            { transform: `translateY(${DISTANCE}px)`, opacity: 0.5 },
            { transform: `translateY(0)`, opacity: 1 },
        ], {
            duration: DURATION,
            easing: 'ease-out',
            fill: 'forwards'
        })
        map.set(el, animation)
        ob.observe(el)
    },

    unmounted(el) {
        ob.unobserve(el)
    }
}
export default vSlideIn