<template>
  <div class="batch-create-card">
    <el-form :model="form" ref="formRef" class="batch-create-form">
      <div class="card-header">
        <h2>批量创建卡号</h2>
      </div>
      
      <el-form-item label="数量" prop="count" :rules="[{ required: true, message: '请输入数量', trigger: 'blur' }]">
        <el-input-number 
          v-model="form.count" 
          :min="1" 
          :max="10000" 
          :step="1" 
          class="form-input" 
          @input="validateCount"
        />
      </el-form-item>

      <el-form-item label="金额" prop="balance" :rules="[{ required: true, message: '请输入金额', trigger: 'blur' }]">
        <el-input-number 
          v-model="form.balance" 
          :min="1" 
          :max="1000000" 
          :step="1" 
          precision="0" 
          class="form-input" 
          @input="validateBalance"
        />
      </el-form-item>

      <el-row justify="center" class="button-row">
        <el-button type="primary" @click="saveData" :loading="saveLoading">保存</el-button>
        <el-button @click="closeDialog" class="cancel-btn">取消</el-button>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { generateAndExport } from "@/api/platform/hashrate";

const form = ref({
  count: 1,
  balance: 100,
});

const saveLoading = ref(false);
const formRef = ref(null);
const emit = defineEmits(['close', 'save', 'createSuccess']);

// Validate count input (ensure it's a positive integer)
function validateCount(value) {
  if (value < 1) {
    form.value.count = 1; // Reset to 1 if less than 1
  }
}

// Validate balance input (ensure it's a positive integer)
function validateBalance(value) {
  if (value < 1) {
    form.value.balance = 1; // Reset to 1 if less than 1
  }
}

async function saveData() {
  if (saveLoading.value) return;
  saveLoading.value = true;

  formRef.value?.validate(valid => {
    if (valid) {
      generateAndExport(form.value.count, form.value.balance).then(() => {
        emit('createSuccess'); 
        emit('close'); 
      }).catch((error) => {
        console.error("创建失败，请检查参数", error);
      }).finally(() => {
        saveLoading.value = false;
      });
    } else {
      saveLoading.value = false;
    }
  });
}

function closeDialog() {
  emit('close');  // 触发关闭事件
}
</script>    

<style scoped>
.batch-create-card {
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
}

.card-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.batch-create-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.el-form-item {
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 14px;
}

.el-button {
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 4px;
}

.button-row {
  justify-content: space-between;
  margin-top: 20px;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cancel-btn:hover {
  background-color: #e5e5e5;
}

.el-form-item label {
  font-weight: bold;
  color: #666;
  margin-bottom: 8px;
}

.el-input-number {
  width: 100%;
  border-radius: 4px;
  font-size: 14px;
}

.el-button:focus {
  box-shadow: none;
}

.el-button.primary {
  background-color: #409EFF;
  border-color: #409EFF;
}

.el-button.primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}
</style>
