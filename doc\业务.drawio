<mxfile host="65bd71144e">
    <diagram id="ikI7OD0KdpA0BQHBg2io" name="第 1 页">
        <mxGraphModel dx="1079" dy="779" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="4" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2" target="3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="9" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="24" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2" target="22">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="40" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2" target="37">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="47" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2" target="46">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="文案管理" style="html=1;fillColor=#ffe6cc;strokeColor=#FF3333;strokeWidth=5;" vertex="1" parent="1">
                    <mxGeometry x="145" y="90" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="36" style="edgeStyle=none;html=1;" edge="1" parent="1" source="3" target="34">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="44" style="edgeStyle=none;html=1;" edge="1" parent="1" source="3" target="41">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="智能文案改写&lt;br&gt;对接通义千问" style="html=1;strokeWidth=6;" vertex="1" parent="1">
                    <mxGeometry x="460" y="90" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="7" style="edgeStyle=none;html=1;" edge="1" parent="1" source="5" target="6">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="23" style="edgeStyle=none;html=1;" edge="1" parent="1" source="5" target="22">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="音频管理" style="html=1;fillColor=#ffe6cc;strokeColor=#000000;strokeWidth=8;" vertex="1" parent="1">
                    <mxGeometry x="145" y="180" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="10" style="edgeStyle=none;html=1;" edge="1" parent="1" source="6" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="声音训练" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="304" y="180" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="21" style="edgeStyle=none;html=1;" edge="1" parent="1" source="8" target="18">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="33" style="edgeStyle=none;html=1;" edge="1" parent="1" source="8" target="19">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="声音推理" style="html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
                    <mxGeometry x="460" y="180" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="14" style="edgeStyle=none;html=1;" edge="1" parent="1" source="11" target="12">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="25" style="edgeStyle=none;html=1;" edge="1" parent="1" source="11" target="22">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="视频管理" style="html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
                    <mxGeometry x="145" y="260" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="15" style="edgeStyle=none;html=1;" edge="1" parent="1" source="12" target="13">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="形象训练" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="304" y="260" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="20" style="edgeStyle=none;html=1;" edge="1" parent="1" source="13" target="19">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="形象推理" style="html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
                    <mxGeometry x="460" y="260" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="实景无人播" style="html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
                    <mxGeometry x="712" y="180" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="28" style="edgeStyle=none;html=1;" edge="1" parent="1" source="19" target="27">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="32" style="edgeStyle=none;html=1;" edge="1" parent="1" source="19" target="31">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="35" style="edgeStyle=none;html=1;" edge="1" parent="1" source="19" target="34">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="数字人" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="590" y="220" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="视频混剪" style="html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
                    <mxGeometry x="5" y="180" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="29" style="edgeStyle=none;html=1;" edge="1" parent="1" source="26" target="18">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="30" style="edgeStyle=none;html=1;" edge="1" parent="1" source="26" target="27">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="弹幕获取" style="html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
                    <mxGeometry x="712" y="300" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="数字人直播" style="html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
                    <mxGeometry x="590" y="390" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="31" value="数字人口播" style="html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
                    <mxGeometry x="460" y="350" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="真实时渲染&lt;br&gt;半实时渲染" style="html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
                    <mxGeometry x="590" y="90" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="43" style="edgeStyle=none;html=1;" edge="1" parent="1" source="37" target="41">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="随机文案改写" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="304" y="10" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="39" style="edgeStyle=none;html=1;" edge="1" parent="1" source="38" target="37">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="随机词管理" style="html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
                    <mxGeometry x="145" y="10" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="41" value="可控智能文案改写" style="html=1;" vertex="1" parent="1">
                    <mxGeometry x="460" y="10" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="46" value="提词器" style="html=1;strokeColor=#82b366;fillColor=#d5e8d4;" vertex="1" parent="1">
                    <mxGeometry x="5" y="90" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="54" style="edgeStyle=none;html=1;fontSize=1;" edge="1" parent="1" source="51" target="53">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="任务参数" style="html=1;strokeColor=#000000;strokeWidth=1;" vertex="1" parent="1">
                    <mxGeometry x="5" y="635" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="57" style="edgeStyle=none;html=1;fontSize=12;" edge="1" parent="1" source="53" target="55">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="58" style="edgeStyle=none;html=1;fontSize=12;" edge="1" parent="1" source="53" target="56">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;JSON&lt;/font&gt;" style="html=1;strokeColor=#000000;strokeWidth=1;fontSize=1;" vertex="1" parent="1">
                    <mxGeometry x="131" y="635" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="64" style="edgeStyle=none;html=1;fontSize=12;" edge="1" parent="1" source="55" target="65">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="66" value="转换具体信息" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;" vertex="1" connectable="0" parent="64">
                    <mxGeometry x="-0.1325" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="55" value="具体信息&lt;br&gt;参数、名称" style="html=1;strokeColor=#000000;strokeWidth=1;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="350" y="570" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="60" style="edgeStyle=none;html=1;fontSize=12;" edge="1" parent="1" source="56" target="59">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="56" value="引用信息&lt;br&gt;文件路径" style="html=1;strokeColor=#000000;strokeWidth=1;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="274" y="735.44" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="61" style="edgeStyle=none;html=1;fontSize=12;" edge="1" parent="1" source="59" target="65">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="62" value="转换具体信息" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=12;" vertex="1" connectable="0" parent="61">
                    <mxGeometry x="-0.1975" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="59" value="下载文件信息" style="html=1;strokeColor=#000000;strokeWidth=1;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="422" y="735.44" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="63" value="执行" style="html=1;strokeColor=#000000;strokeWidth=1;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="712" y="635" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="67" style="edgeStyle=none;html=1;fontSize=12;" edge="1" parent="1" source="65" target="63">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="65" value="生成任务文件" style="html=1;strokeColor=#000000;strokeWidth=1;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="580" y="635" width="110" height="50" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>