<script setup>
const props = defineProps({
  projectTitle: String,
  projectId: Number,
  createTime: String,
  remark: String,
  isActive: Boolean
})
import { stringToHSLColor } from '@/utils/geek';
import { useRouter } from 'vue-router';
import { ref, computed } from 'vue';

import GeometricAvatar from './GeometricAvatar.vue';

const isHovered = ref(false)

const router = useRouter()
const toPage = (page) => {
    router.push({
        path: `/project/${props.projectId}/${page}`,
        query: { projectTitle: props.projectTitle + '项目', id: 'article' }
    })
}

const emit = defineEmits(['clickTitle', 'update'])

// 快捷操作菜单
const quickActions = [
  { icon: 'Document', label: '文案编辑', action: () => toPage('material'), type: 'primary' },
  { icon: 'VideoCamera', label: '直播设置', action: () => toPage('live'), type: 'success' },
  { icon: 'Edit', label: '编辑信息', action: () => emit('update'), type: 'warning' }
]

// 根据项目ID确定图标颜色模式
const avatarColorMode = computed(() => {
  // 使用项目ID确定颜色模式，不同项目有不同的颜色风格
  const colorModes = ['pastel', 'vibrant', 'soft', 'cool', 'warm'];
  return colorModes[props.projectId % colorModes.length];
})
</script>

<template>
  <div 
    class="project-card"
    :class="{ 'is-active': isActive }"
    :data-project-id="projectId"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
    @click="$emit('clickTitle', $event)"
  >
    <!-- 卡片主体 -->
    <div class="card-main">
      <div class="project-icon">
        <GeometricAvatar 
          :text="projectTitle" 
          size="100%" 
          :autoBackground="true"
          borderRadius="14px"
          :colorMode="avatarColorMode"
        />
      </div>
      
      <!-- 项目信息 -->
      <div class="project-info">
        <div class="title-row">
          <h3 class="project-title">
            {{ projectTitle }}
          </h3>
        </div>
        
        <div class="project-meta">
          <div class="meta-item">
            <el-icon><Calendar /></el-icon>
            <span>{{ createTime }}</span>
          </div>
          <div v-if="remark" class="meta-item">
            <el-icon><InfoFilled /></el-icon>
            <span>{{ remark }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 快捷操作区 -->
    <div class="quick-actions" @click.stop>
      <el-button 
        v-for="action in quickActions"
        :key="action.label"
        :class="['action-btn', `is-${action.type}`]"
        @click="action.action"
        text
      >
        <el-icon><component :is="action.icon" /></el-icon>
        <span>{{ action.label }}</span>
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 文本省略
@mixin text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.project-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(12px);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  height: 180px;
  position: relative;
  cursor: pointer;
  
  &.is-active {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
    border-color: var(--el-color-primary-light-5);
    background: white;
    backdrop-filter: none;
    pointer-events: auto;
    
    &::before {
      opacity: 0.8;
    }
    
    .card-main .project-icon {
      transform: scale(1.1);
    }
    
    .quick-actions {
      border-color: transparent;
      background: white;
      
      .action-btn {
        &:hover {
          background: var(--el-fill-color-light);
          transform: translateY(-2px);
          
          .el-icon {
            transform: scale(1.1);
          }
        }
      }
    }
  }
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }
  
  &:hover:not(.is-active) {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    border-color: transparent;
    
    &::before {
      opacity: 0.5;
    }
    
    .card-main .project-icon {
      transform: scale(1.05);
    }
    
    .quick-actions {
      border-color: transparent;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(8px);
    }
  }
  
  // 卡片主体
  .card-main {
    padding: 20px 20px 12px 20px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    flex: 1;
    position: relative;
    z-index: 1;
    min-height: 0;
    overflow-y: auto;
    
    // 项目图标
    .project-icon {
      width: 48px;
      height: 48px;
      border-radius: 14px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      overflow: hidden;
      transition: all 0.3s ease;
    }
    
    // 项目信息
    .project-info {
      flex: 1;
      min-width: 0;
      
      .title-row {
        margin-bottom: 12px;
        
        .project-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
          @include text-ellipsis;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            color: var(--el-color-primary);
            transform: translateX(4px);
          }
        }
      }
      
      .project-meta {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .meta-item {
          display: flex;
          align-items: center;
          gap: 6px;
          color: var(--el-text-color-secondary);
          font-size: 13px;
          transition: all 0.3s ease;
          
          .el-icon {
            font-size: 14px;
            transition: transform 0.3s ease;
          }
          
          span {
            @include text-ellipsis;
            max-width: 200px;
          }
          
          &:hover {
            color: var(--el-text-color-primary);
            
            .el-icon {
              transform: scale(1.1);
            }
          }
        }
      }
    }
  }
  
  // 快捷操作区
  .quick-actions {
    margin-top: 0;
    padding: 12px 20px;
    height: 60px;
    display: flex;
    gap: 12px;
    background: var(--el-fill-color-light);
    border-top: 1px solid var(--el-border-color-lighter);
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    flex-wrap: wrap;
    cursor: default;
    
    .action-btn {
      flex: 1;
      min-width: 80px;
      height: 36px;
      border-radius: 10px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 13px;
      font-weight: 500;
      
      .el-icon {
        font-size: 16px;
        transition: transform 0.3s ease;
        flex-shrink: 0;
      }
      
      span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      &.is-primary {
        color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
        
        &:hover {
          background: var(--el-color-primary-light-8);
          color: var(--el-color-primary-dark-2);
        }
      }
      
      &.is-success {
        color: var(--el-color-success);
        background: var(--el-color-success-light-9);
        
        &:hover {
          background: var(--el-color-success-light-8);
          color: var(--el-color-success-dark-2);
        }
      }
      
      &.is-warning {
        color: var(--el-color-warning);
        background: var(--el-color-warning-light-9);
        
        &:hover {
          background: var(--el-color-warning-light-8);
          color: var(--el-color-warning-dark-2);
        }
      }
      
      &:hover {
        transform: translateY(-2px);
        
        .el-icon {
          transform: scale(1.1);
        }
      }
    }
  }
}
</style>